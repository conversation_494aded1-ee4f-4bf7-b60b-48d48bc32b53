package com.trs.yq.police.subject.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.conf.PersonInfoHandlerConfig;
import com.trs.yq.police.subject.utils.JsonUtil;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.StatObjectArgs;
import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * 省厅户籍信息接口查询
 *
 * <AUTHOR>
 * @date 2022/07/25
 */
@Component
@Slf4j
public class PersonInfoHandler {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private MinioClient minioClient;

    @Resource
    private PersonInfoHandlerConfig config;


    private boolean photoExist(String idNumber) {
        try {
            minioClient.statObject(StatObjectArgs.builder().bucket("photo").object(idNumber + ".jpg").build());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从省厅户籍信息接口下载人员照片
     *
     * @param idNumber 身份证号
     */
    public void downloadPersonPhoto(String idNumber) {
        if (photoExist(idNumber)) {
            log.debug("{} 人员照片已存在。", idNumber);
            return;
        }

        log.info("开始同步人员照片。身份证号: {}", idNumber);
        HttpHeaders headers = new HttpHeaders();
        headers.add("token", config.getToken());
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, String> request = new HashMap<>();
        request.put("sfzhm", idNumber);
        request.put("userCardId", config.getUserId());
        request.put("userName", config.getUserName());
        request.put("userDeptCode", config.getDeptCode());
        request.put("userDept", config.getDeptName());

        HttpEntity<Map<String, String>> entity = new HttpEntity<>(request, headers);
        ResponseEntity<String> response = restTemplate.postForEntity(config.getUrl(), entity, String.class);
        JsonNode result = JsonUtil.parseJsonNode(response.getBody());
        if (Objects.isNull(result) || result.get("data").isNull()) {
            log.info("请求省厅接口失败。参数: {}", request);
            return;
        }

        JsonNode data = result.get("data");
        if (!data.has("xp") || StringUtils.isBlank(data.get("xp").asText())) {
            log.info("省厅接口请求的照片为空。参数: {}", request);
            return;
        }

        String base64 = data.get("xp").asText();
        byte[] imageData = Base64.getDecoder().decode(base64);
        String fileName = idNumber + ".jpg";
        try {
            minioClient.putObject(PutObjectArgs.builder().bucket("photo").object(fileName)
                .stream(new ByteArrayInputStream(imageData), imageData.length, -1).build());
        } catch (Exception exception) {
            log.error("存储照片失败。照片: {}", fileName);
            return;
        }
        log.info("同步人员照片成功! 身份证号: {}", idNumber);
    }
}
