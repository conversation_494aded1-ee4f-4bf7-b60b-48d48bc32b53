package com.trs.yq.police.subject.utils;

import com.trs.yq.police.subject.exception.SystemException;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.imageio.stream.MemoryCacheImageOutputStream;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 视频格式转换工具类
 *
 * <AUTHOR>
 * @date 2021/12/14
 */
@Slf4j
public class VideoUtil {

    private static final String MP_4 = "mp4";

    private VideoUtil() {
    }

    /**
     * 视频转换为MP4格式
     *
     * @param inputFile 文件
     * @return 转换结果
     */
    public static byte[] covertVideoToMp4(MultipartFile inputFile) {
        SeekableByteArrayOutputStream outputStream = new SeekableByteArrayOutputStream();
        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile.getInputStream())) {
            grabber.start();
            try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputStream,
                    grabber.getImageWidth(),
                    grabber.getImageHeight(),
                    grabber.getAudioChannels())) {

                //如果是mp4就不转换了
                if (MP_4.equals(inputFile.getOriginalFilename())
                        && grabber.getVideoCodec() == avcodec.AV_CODEC_ID_H264
                        && grabber.getAudioCodec() == avcodec.AV_CODEC_ID_AAC) {
                    return inputFile.getBytes();
                }

                //转换成.h264的mp4
                recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
                recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC);
                recorder.setFormat("mp4");
                recorder.setFrameRate(grabber.getFrameRate());
                recorder.setSampleRate(grabber.getSampleRate());
                recorder.setVideoBitrate(grabber.getVideoBitrate());
                recorder.setAspectRatio(grabber.getAspectRatio());
                recorder.setAudioOptions(grabber.getAudioOptions());
                recorder.start();
                Frame capturedFrame;
                while ((capturedFrame = grabber.grabFrame()) != null) {
                    recorder.setTimestamp(grabber.getTimestamp());
                    recorder.record(capturedFrame);
                }
                recorder.stop();
                recorder.release();
                grabber.stop();
                grabber.release();
                return outputStream.toByteArray();
            }
        } catch (IOException e) {
            log.error("视频转换异常！", e);
            return null;
        }
    }

    /**
     * 提取视频缩略图
     *
     * @param inputStream 视频输入流
     * @param fps         码率
     * @return 缩略图字节流
     * @throws IOException IOException
     */
    public static byte[] extractImgFromVideo(InputStream inputStream, int fps)
            throws IOException {
        try (FFmpegFrameGrabber ff = new FFmpegFrameGrabber(inputStream);
             // 创建BufferedImage对象
             Java2DFrameConverter converter = new Java2DFrameConverter()) {
            ff.start();
            int ftp = ff.getLengthInFrames();
            int flag = 0;
            Frame frame = null;
            while (flag <= ftp) {
                // 获取帧
                frame = ff.grabImage();
                // 过滤前3帧，避免出现全黑图片
                if ((flag > fps) && (frame != null)) {
                    break;
                }
                flag++;
            }

            BufferedImage bi = converter.getBufferedImage(frame);

            ByteArrayOutputStream bos = new ByteArrayOutputStream();

            ImageIO.write(bi, "jpg", new MemoryCacheImageOutputStream(bos));

            ff.stop();

            return bos.toByteArray();
        } catch (FrameGrabber.Exception e) {
            throw new SystemException("export video thumbnail fail!", e);
        }
    }
}
