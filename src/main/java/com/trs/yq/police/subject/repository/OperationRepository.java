package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.OperationEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作持久化层
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Repository
public interface OperationRepository extends BaseRepository<OperationEntity, String>{

    /**
     * 根据请求方法查询操作列表，便于进一步筛选
     *
     * @param requestMethod 请求方法
     * @param service 模块名称
     * @return 操作实体
     */
    List<OperationEntity> findByRequestMethodAndService(String requestMethod, String service);
}
