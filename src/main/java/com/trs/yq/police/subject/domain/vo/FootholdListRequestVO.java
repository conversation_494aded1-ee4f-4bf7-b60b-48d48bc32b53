package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 分页查询落脚点信息参数
 *
 * <AUTHOR>
 */
@Data
@Validated
public class FootholdListRequestVO implements Serializable {

    private static final long serialVersionUID = 392535999312310990L;

    /**
     * 分页参数
     */
    @NotNull(message = "分页参数不能为空！")
    private PageParams pageParams;

    /**
     * 时间筛选条件
     */
    @NotNull
    private TimeParams timeParams;
}
