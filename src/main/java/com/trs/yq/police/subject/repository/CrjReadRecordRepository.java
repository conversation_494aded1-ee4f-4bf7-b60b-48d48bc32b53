package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjReadRecordEntity;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 11:13
 */
@Repository
public interface CrjReadRecordRepository extends BaseRepository<CrjReadRecordEntity, String> {

    /**
     * 获取查看记录
     *
     * @param recordId uuid
     * @param module   模块名
     * @param userId   用户id
     * @return {@link CrjReadRecordEntity}
     */
    Optional<CrjReadRecordEntity> findByRecordIdAndModuleAndUserId(@Param("recordId") String recordId,
        @Param("module") String module, @Param("userId") String userId);

    /**
     * 删除境外人员已读记录
     */
    @Modifying
    @Query(nativeQuery = true, value = "DELETE FROM T_PS_CRJ_READ_RECORD where MODULE='jwry' and  RECORD_ID not in (SELECT t.RECORD_ID from T_PS_CRJ_JWRY t )")
    void deleteJwryRead();
}
