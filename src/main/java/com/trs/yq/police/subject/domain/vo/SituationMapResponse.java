package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 热力图数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/12 14:07
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SituationMapResponse extends SituationMapVO {

    /**
     * 子区域的数据
     */
    private List<SituationMapVO> items;
}
