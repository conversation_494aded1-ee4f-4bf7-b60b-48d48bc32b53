package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动相关人员vo
 *
 * <AUTHOR>
 * @since 2022/1/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActivityRelatedPersonVO {
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 人员id
     */
    private String personId;
    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 相关事件名称
     */
    private String relevantEvent;
    /**
     * 事件id
     */
    private String eventId;
}
