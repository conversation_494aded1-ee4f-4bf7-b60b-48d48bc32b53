package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.EventGroupRelationEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/12/14 9:45
 */
@Repository
public interface EventGroupRelationRepository extends BaseRepository<EventGroupRelationEntity, String> {

    /**
     * 根据事件id和群体id 查找关联关系
     *
     * @param eventId 事件id
     * @param groupId 群体id
     * @return {@link EventGroupRelationEntity}
     */
    EventGroupRelationEntity findByEventIdAndGroupId(String eventId, String groupId);

    /**
     * 根据事件id和群体id 查找关联关系
     *
     * @param eventId 事件id
     * @return {@link EventGroupRelationEntity}
     */
    List<EventGroupRelationEntity> findAllByEventId(String eventId);

    /**
     * 删除事件-群体关联
     *
     * @param eventId 事件id
     */
    @Modifying
    void removeAllByEventId(String eventId);

    /**
     * 批量删除与事件关联的群体id
     *
     * @param eventId 事件id
     */
    @Modifying
    void deleteAllByEventId(String eventId);

    /**
     * 根据群体id查询关系
     *
     * @param groupId 群体id
     * @return {@link EventGroupRelationEntity}
     */
    List<EventGroupRelationEntity> findAllByGroupId(String groupId);

    /**
     * 根据群体id删除关系
     *
     * @param groupId 群体id
     */
    @Modifying
    void deleteAllByGroupId(String groupId);

    /**
     * 查询参与事件的所有群体名
     *
     * @param eventId 事件id
     * @return 群体名
     */
    @Query("select g.name from EventGroupRelationEntity egr left join GroupEntity g on g.id = egr.groupId where egr.eventId = :eventId")
    List<String> findAllGroupNameByEvent(@Param("eventId") String eventId);

    /**
     * 获取事件中关联的群体列表
     *
     * @param eventId 事件id
     * @return 群体列表
     */
    @Query("select g from GroupEntity g where exists (select 1 from EventGroupRelationEntity r where r.eventId = :eventId and r.groupId = g.id)")
    List<GroupEntity> findGroupByEvent(@NotNull @Param("eventId") String eventId);

    /**
     * 根据群体id查找事件
     *
     * @param groupId 群体id
     * @return 关联关系
     */
    @Query("select count(egr.id) from EventGroupRelationEntity egr left join EventEntity e on e.id = egr.eventId where e.deleted = false and egr.groupId = :groupId ")
    Integer countAllByGroupId(@Param("groupId") String groupId);
}
