package com.trs.yq.police.subject.domain.vo;

import javax.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 人员批量导出请求VO
 *
 * <AUTHOR>
 * @date 2021/08/11
 */
@Data
public class ExportParams {

    @NotEmpty(message = "字段列表不能为空！")
    private List<String> fieldNames;

    private List<String> ids;

    /**
     * 是否导出全部
     */
    private Boolean isAll;

    /**
     * 列表筛选条件
     */
    private ListRequestVO listParams;
    /**
     * 专题Id
     */
    @NotBlank(message = "专题id不能为空")
    private String subjectId;
}
