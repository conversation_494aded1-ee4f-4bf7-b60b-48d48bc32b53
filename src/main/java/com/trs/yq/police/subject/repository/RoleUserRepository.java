package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.RoleUserEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色-用户持久化层
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Repository
public interface RoleUserRepository extends BaseRepository<RoleUserEntity, String> {

    /**
     * 按照用户id查询角色id列表
     *
     * @param userId 用户id
     * @return 角色id列表
     */
    @SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
    @Query(value = "select role_id from T_ADMIN_ROLE_USER where user_id = :userId", nativeQuery = true)
    List<String> findRoleIdByUserId(@Param("userId") String userId);

    /**
     * 按用户id查询拥有的角色
     *
     * @param userId 用户id
     * @return 角色列表
     */
    List<RoleUserEntity> findByUserId(String userId);

    /**
     * 根据用户id查询手机号
     *
     * @param roleIdList 用户id列表
     * @return 手机号列表
     */
    @SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
    @Query(nativeQuery = true, value = "select mobilephone from (select u.* from T_USER u inner join T_ADMIN_ROLE_USER ru on u.id = ru.user_id where ru.role_id in (:roleIdList)) where mobilephone is not null group by mobilephone")
    List<String> findMobileByRoleId(List<String> roleIdList);
}
