package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.request.AppVisitListRequest;
import com.trs.yq.police.subject.domain.request.ListParamsRequest;
import com.trs.yq.police.subject.domain.vo.AppPersonalVisitListVO;
import com.trs.yq.police.subject.domain.vo.AppVisitListVO;
import com.trs.yq.police.subject.domain.vo.CrjAccommodationVO;
import com.trs.yq.police.subject.domain.vo.CrjAppAccommodationListVO;
import com.trs.yq.police.subject.domain.vo.CrjAppListRequestVO;
import com.trs.yq.police.subject.domain.vo.CrjOcrVO;
import com.trs.yq.police.subject.domain.vo.CrjResidenceDetailVO;
import com.trs.yq.police.subject.domain.vo.CrjResidenceListVO;
import com.trs.yq.police.subject.domain.vo.CrjVisitVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.service.CrjAppService;
import com.trs.yq.police.subject.service.CrjCzryService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * 出入境app接口
 *
 * <AUTHOR>
 * @since 2021/10/18
 */
@RestController
@RequestMapping("/entry-exit-app")
public class CrjAppController {

    @Resource
    private CrjAppService crjAppService;
    @Resource
    private CrjCzryService crjCzryService;


    /**
     * orc识别图片接口
     * http://***************:3001/project/4897/interface/api/132733
     *
     * @param file 图片
     * @return {@link CrjOcrVO}
     */
    @PostMapping("/ocr")
    public CrjOcrVO getIdentifyInfo(@RequestParam MultipartFile file) {
        return CrjOcrVO.getVirtualDate();
    }

    /**
     * 添加住宿信息
     * http://***************:3001/project/4897/interface/api/132757
     *
     * @param crjAccommodationVO {@link CrjAccommodationVO}
     */
    @PostMapping("/accommodation")
    public void createAccommodation(@RequestBody CrjAccommodationVO crjAccommodationVO) {
        crjAppService.createAccommodation(crjAccommodationVO);
    }

    /**
     * http://***************:3001/project/4897/interface/api/132745
     * 住宿登记信息详情
     *
     * @param id 住宿登记信息id
     * @return {@link CrjAccommodationVO}
     */
    @GetMapping("/accommodation/{id}")
    public CrjAccommodationVO getAccommodationInfo(@PathVariable String id) {
        return crjAppService.getAccommodationInfo(id);
    }

    /**
     * 住宿登记记录列表
     * http://***************:3001/project/4897/interface/api/132739
     *
     * @param crjAppListRequestVO {@link CrjAppListRequestVO}
     * @return {@link CrjAppAccommodationListVO}
     */
    @PostMapping("/accommodation/list")
    public List<CrjAppAccommodationListVO> getAccommodationList(@RequestBody CrjAppListRequestVO crjAppListRequestVO) {
        return crjAppService.getAccommodationList(crjAppListRequestVO);
    }


    /**
     * 走访信息列表
     * http://***************:3001/project/4897/interface/api/132751
     *
     * @param request 列表查询参数
     * @return 查询结果 {@link AppVisitListVO}
     */
    @PostMapping("/visit/list")
    public List<AppVisitListVO> getVisitList(@RequestBody AppVisitListRequest request) {
        return crjAppService.getVisitList(request);
    }

    /**
     * 某一人员走访信息列表
     * http://***************:3001/project/4897/interface/api/132781
     *
     * @param certificateNumber 证件号码
     * @param request 列表查询参数
     * @return 查询结果 {@link AppPersonalVisitListVO}
     */
    @PostMapping("/visit/{certificateNumber}")
    public List<AppPersonalVisitListVO> getPersonalVisitList(@PathVariable String certificateNumber, @RequestBody AppVisitListRequest request) {
        return crjAppService.getPersonalVisitList(certificateNumber, request);
    }

    /**
     * 走访信息详情
     * http://***************:3001/project/4897/interface/api/132763
     *
     * @param id 走访记录id
     * @return 记录详情 {@link CrjVisitVO}
     */
    @GetMapping("/visit/{id}")
    public CrjVisitVO getVisitDetail(@PathVariable String id) {
        return crjAppService.getVisitDetail(id);
    }

    /**
     * 走访信息新增
     * http://***************:3001/project/4897/interface/api/132769
     *
     * @param crjVisitVO 走访信息内容
     */
    @PostMapping("/visit")
    public void addVisit(@RequestBody CrjVisitVO crjVisitVO) {
        crjAppService.addVisit(crjVisitVO);
    }

    /**
     * 走访信息编辑
     * http://***************:3001/project/4897/interface/api/132775
     *
     * @param id 记录id
     * @param crjVisitVO 走访信息内容
     */
    @PutMapping("/visit/{id}")
    public void updateVisit(@PathVariable String id, @RequestBody CrjVisitVO crjVisitVO) {
        crjAppService.updateVisit(id, crjVisitVO);
    }

    /**
     * 走访信息删除 http://***************:3001/project/4897/interface/api/132787
     *
     * @param id 记录id
     */
    @DeleteMapping("/visit/{id}")
    public void deleteVisit(@PathVariable String id) {
        crjAppService.deleteVisit(id);
    }

    /**
     * 常住人员列表
     *
     * @param request 参数
     * @return 列表
     */
    @PostMapping("/residence/list")
    public PageResult<CrjResidenceListVO> getCzryList(@RequestBody ListParamsRequest request) {
        return crjCzryService.getResidenceList(request);
    }

    /**
     * 常住人员详情
     *
     * @param id 人员id
     * @return 列表
     */
    @GetMapping("/residence/detail")
    public CrjResidenceDetailVO getCzryList(@RequestParam String id) {
        return crjCzryService.getResidenceDetail(id);
    }
}
