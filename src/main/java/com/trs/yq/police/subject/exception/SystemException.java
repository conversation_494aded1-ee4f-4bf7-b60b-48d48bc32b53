package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * System Exception
 *
 * <AUTHOR>
 */
@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
public class SystemException extends RuntimeException {
    
    private static final long serialVersionUID = -335150824993665637L;

    /**
     *
     */
    public SystemException() {
        super();
    }

    /**
     * @param message 异常信息
     */
    public SystemException(String message) {
        super(message);
    }

    /**
     * @param message 异常信息
     * @param cause   异常
     */
    public SystemException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * @param cause 异常
     */
    public SystemException(Throwable cause) {
        super(cause);
    }
}
