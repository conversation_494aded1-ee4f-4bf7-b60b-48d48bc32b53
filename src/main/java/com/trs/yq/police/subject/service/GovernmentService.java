package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.GovernmentInfoVO;

/**
 * <AUTHOR>
 * @date 2021/12/23 15:58
 */
public interface GovernmentService {
    /**
     * 查询关联政府信息id
     *
     * @param module   模块
     * @param recordId 关联id
     * @return {@link GovernmentInfoVO}
     */
    GovernmentInfoVO getGovernmentByModuleAndRecordId(String module, String recordId);

    /**
     * 更新政府主管部门信息
     *
     * @param module           模块
     * @param recordId         关联id
     * @param governmentInfoVO 政府行业主管部门信息
     */
    void updateGovernmentInfo(String module, String recordId, GovernmentInfoVO governmentInfoVO);
}
