package com.trs.yq.police.subject.message;

import com.trs.yq.police.subject.constants.enums.ControlStatusEnum;
import com.trs.yq.police.subject.constants.enums.MonitorStatusEnum;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.message.startegy.ProfessionalWarningProcess;
import com.trs.yq.police.subject.message.startegy.WarningProcess;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.trs.yq.police.subject.constants.WarningConstants.*;

/**
 * 同步业务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/22 17:55
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class SyncServiceImpl implements SyncService {

    @Resource
    private PersonRepository personRepository;
    @Resource(name = "callWarningProcess")
    private WarningProcess callWarningProcess;
    @Resource(name = "batchWarningProcess")
    private WarningProcess batchWarningProcess;
    @Resource(name = "streamWarningProcess")
    private WarningProcess streamWarningProcess;
    @Resource(name = "carWarningProcess")
    private WarningProcess carWarningProcess;
    @Resource(name = "professionalWarningProcess")
    private ProfessionalWarningProcess professionalWarningProcess;

    /**
     * 同步处置状态
     *
     * @param msg 消息
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncDisposalStatus(String msg) {

    }

    /**
     * 同步布控状态
     *
     * @param msg kafka消息
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncBkStatus(String msg) {
        Map<String, String> map = JsonUtil.parseMap(msg, String.class);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String idNumber = entry.getKey();
            String status = entry.getValue();
            PersonEntity personEntity = personRepository.findByIdNumber(idNumber);
            switch (status) {
                case "start":
                    personEntity.setMonitorStatus(MonitorStatusEnum.IN_MONITOR.getCode());
                    break;
                case "end":
                    personEntity.setControlStatus(ControlStatusEnum.IN_CONTROL.getCode());
                    break;
                default:
                    break;
            }
            personRepository.save(personEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncWarning(String message) {

        // 利用策略模式重构
        final Map<String, Object> messageMap = JsonUtil.parseMap(message, Object.class);

        if (SetUtils.isEqualSet(messageMap.keySet(), Arrays.asList("plateNo", "result", "explain"))) {
            carWarningProcess.process(message);
            return;
        }

        final String warningTypeMark = "type";
        final Object warningType = messageMap.get(warningTypeMark);

        final String warningTypeStr = warningType.toString();
        if (warningType instanceof List) {
            // 如果预警类型为列表形式，说明为流式处理
            streamWarningProcess.process(message);
        } else {
            // 单个预警类型为批处理
            if (StringUtils.equals(WARNING_TYPE_JD_YXSDRY, warningTypeStr)) {
                // 隐形吸毒人员
                callWarningProcess.process(message);
            } else if (StringUtils.equals(WARNING_TYPE_JD_YXSDWD, warningTypeStr)) {
                // 隐形涉毒窝点
                batchWarningProcess.process(message);
            } else if (StringUtils.equals(WARNING_TYPE_JD_RYJJ, warningTypeStr)) {
                //吸毒人员聚集
                batchWarningProcess.process(message);
            } else {
                // 人员聚集
                streamWarningProcess.process(message);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void syncJzsdWarning(String message, String subjectId) {
        professionalWarningProcess.process(message, subjectId);
    }
}
