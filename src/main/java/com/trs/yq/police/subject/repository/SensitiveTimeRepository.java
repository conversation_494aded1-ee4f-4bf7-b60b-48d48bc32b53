package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.SensitiveTimeEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * 敏感时间节点数据库接口
 *
 * <AUTHOR>
 * @date 2021/12/28 14:16
 */
@Repository
public interface SensitiveTimeRepository extends BaseRepository<SensitiveTimeEntity, String> {

    /**
     * 分页查询敏感时间节点
     *
     * @param searchKey 模糊检索关键词
     * @param scope     范围
     * @param pageable  分页参数
     * @return {@link SensitiveTimeEntity}
     */
    @Query("select t from SensitiveTimeEntity t " +
            "where (:searchKey is null or t.name like :searchKey or t.remark like :searchKey) " +
            "and (:scope is null or t.nodeType = :scope) " +
            " order by t.upTime desc ")
    Page<SensitiveTimeEntity> getSensitivePageList(String searchKey, String scope, Pageable pageable);

    /**
     * 群体已关联敏感时间节点查询
     *
     * @param groupId 群体id
     * @param page    分页参数
     * @return {@link SensitiveTimeEntity}
     */
    @Query("select t1 from SensitiveTimeEntity  t1 join GroupTimeRelationEntity  t2 on t1.id=t2.sensitiveTimeId where t2.groupId=:groupId order by t1.startTime desc ")
    Page<SensitiveTimeEntity> findAllByGroupId(@Param("groupId") String groupId, Pageable page);

    /**
     * 群体已关联敏感事件节点
     *
     * @param groupId 群体已关联敏感时间节点
     * @return {@link SensitiveTimeEntity}
     */
    @Query("select t1 from SensitiveTimeEntity  t1 join GroupTimeRelationEntity  t2 on t1.id=t2.sensitiveTimeId where t2.groupId=:groupId")
    List<SensitiveTimeEntity> findAllByGroupId(String groupId);

    /**
     * 在时间范围内的节点
     *
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return {@link SensitiveTimeEntity}
     */
    @Query("SELECT tst FROM SensitiveTimeEntity tst " +
            "WHERE tst.endTime >= :startTime " +
            "AND tst.startTime <= :endTime " +
            " order by tst.upTime desc")
    List<SensitiveTimeEntity> findAllByTimeRange(LocalDate startTime, LocalDate endTime);

    /**
     * 根据节点有效范围查询
     *
     * @param nodeType 范围（0：默认有效 1：选择有效）
     * @return {@link SensitiveTimeEntity}
     */
    List<SensitiveTimeEntity> findAllByNodeType(Integer nodeType);
}
