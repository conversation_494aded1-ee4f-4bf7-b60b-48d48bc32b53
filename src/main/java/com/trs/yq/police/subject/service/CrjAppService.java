package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.CrjAccommodationVO;
import com.trs.yq.police.subject.domain.vo.CrjAppAccommodationListVO;
import com.trs.yq.police.subject.domain.vo.CrjAppListRequestVO;

import java.util.List;

import com.trs.yq.police.subject.domain.request.AppVisitListRequest;
import com.trs.yq.police.subject.domain.vo.AppPersonalVisitListVO;
import com.trs.yq.police.subject.domain.vo.AppVisitListVO;
import com.trs.yq.police.subject.domain.vo.CrjVisitVO;


/**
 * 出入境app接口
 *
 * <AUTHOR>
 * @since 2021/10/18
 */
public interface CrjAppService {
    /**
     * 新增住宿信息
     *
     * @param crjAccommodationVO {@link CrjAccommodationVO}
     */
    void createAccommodation(CrjAccommodationVO crjAccommodationVO);

    /**
     * 获取住宿登记信息
     *
     * @param id 住宿记录id
     * @return {@link CrjAccommodationVO}
     */
    CrjAccommodationVO getAccommodationInfo(String id);

    /**
     * 获取住宿登记列表
     *
     * @param crjAppListRequestVO {@link CrjAppListRequestVO}
     * @return {@link CrjAppAccommodationListVO}
     */
    List<CrjAppAccommodationListVO> getAccommodationList(CrjAppListRequestVO crjAppListRequestVO);
    /**
     * 走访信息列表
     *
     * @param request 列表查询参数
     * @return 查询结果 {@link AppPersonalVisitListVO}
     */
    List<AppVisitListVO> getVisitList(AppVisitListRequest request);
    /**
     * 某一人员走访信息列表
     *
     * @param certificateNo 证件号码
     * @param request 列表查询参数
     * @return 查询结果 {@link AppPersonalVisitListVO}
     */
    List<AppPersonalVisitListVO> getPersonalVisitList(String certificateNo, AppVisitListRequest request);
    /**
     * 走访信息详情
     *
     * @param id 走访记录id
     * @return 记录详情 {@link CrjVisitVO}
     */
    CrjVisitVO getVisitDetail(String id);
    /**
     * 走访信息新增
     *
     * @param crjVisitVO 走访信息内容
     */
    void addVisit(CrjVisitVO crjVisitVO);
    /**
     * 走访信息编辑
     *
     * @param id 记录id
     * @param crjVisitVO 走访信息内容
     */
    void updateVisit(String id, CrjVisitVO crjVisitVO);
    /**
     * 走访信息删除
     *
     * @param id 记录id
     */
    void deleteVisit(String id);
}
