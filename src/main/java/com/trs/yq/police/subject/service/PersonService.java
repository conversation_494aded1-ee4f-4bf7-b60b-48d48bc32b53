package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.PersonArchiveStatusEnum;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Nullable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.PERSON_ID_MISSING;

/**
 * 人员档案业务层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 14:23
 */
@Validated
public interface PersonService {


    /**
     * 创建人员信息
     *
     * @param vo 数据接收 {@link PersonBasicVO}
     * @return 人员档案信息主键
     */
    String createPerson(@NotNull(message = "人员基础信息缺失") PersonBasicVO vo);

    /**
     * 移除人员
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    void deletePerson(@NotBlank(message = "人员主键缺失") String personId, @NotBlank(message = "专题主键缺失") String subjectId);

    /**
     * 更新人员基本信息
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param vo        人员信息
     * @return 人员id
     */
    String updatePersonBasicInfo(@NotBlank(message = "人员主键缺失") String personId,
                                 @NotBlank(message = "专题主键缺失") String subjectId,
                                 @NotNull(message = "人员基础信息缺失") PersonBasicVO vo);

    /**
     * 查询人员基本信息
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 人员基本信息 {@link PersonBasicVO}
     */
    PersonBasicVO getBasicInfo(@NotBlank(message = "人员主键缺失") String personId,
                               @NotBlank(message = "专题主键缺失") String subjectId);

    /**
     * 根据身份证号码查询人员基本信息
     *
     * @param idNumber 身份证号码
     * @return 人员基本信息 {@link PersonBasicVO}
     */
    PersonBasicVO getBasicInfoByIdNumber(@NotBlank(message = "身份证号缺失") String idNumber);


    /**
     * 查询人员列表
     *
     * @param subjectId 专题id
     * @param params    查询条件
     * @return 分页查询结果 {@link PersonEntity}
     */
    PageResult<PersonEntity> getGroupPersonList(@NotBlank(message = "专题主键不可为空！") String subjectId, ListRequestVO params);

    /**
     * 批量导入人员信息
     *
     * @param importVO 导入参数
     * @return 导入结果 {@link ImportResultVO}
     */
    ImportResultVO importPerson(ImportVO importVO);

    /**
     * 下载批量导入模板
     *
     * @param subjectId  专题编号
     * @param personType 人员类别
     * @return 模板文件二进制流
     * @throws UnsupportedEncodingException url编码异常
     */
    ResponseEntity<ByteArrayResource> downloadImportTemplate(@NotBlank(message = "专题编号缺失") String subjectId, @Nullable String personType) throws UnsupportedEncodingException;

    /**
     * 下载批量导入失败文件
     *
     * @param subjectId 专题id
     * @param initialId 原始文件id
     * @return 失败记录文件
     * @throws UnsupportedEncodingException 暂不支持的URL编码
     */
    ResponseEntity<ByteArrayResource> downloadImportFailure(String subjectId, String initialId) throws UnsupportedEncodingException;

    /**
     * 查询人员类别
     *
     * @param subjectId 专题id
     * @return 人员类型
     */
    List<IdNameVO> getTypes(String subjectId);

    /**
     * 更新人员基本信息图片
     *
     * @param personId 人员id
     * @param images   图片 {@link ImageVO}
     */
    void updateBaseImages(String personId, List<ImageVO> images);

    /**
     * 校验人员是否存在
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 如果存在则返回 人物实体，如果不存在，则抛出异常 {@see ParamValidationException}
     */
    PersonEntity checkPersonExist(@NotBlank(message = PERSON_ID_MISSING) String personId, String subjectId);

    /**
     * 校验人员是否存在
     *
     * @param personId 人员id
     * @return 如果存在则返回 人物实体，如果不存在，则抛出异常 {@see ParamValidationException}
     */
    PersonEntity checkPersonExist(@NotBlank(message = PERSON_ID_MISSING) String personId);

    /**
     * 查询人员存在状态
     *
     * @param idNumber 身份证号
     * @return 人员存在状态 {@link StorageStatusVO}
     */
    StorageStatusVO getPersonStorageStatus(String idNumber);

    /**
     * 查询人员的归档状态
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 人员归档状态 {@link PersonArchiveStatusEnum}
     */
    PersonArchiveStatusEnum getPersonArchiveStatus(String personId, String subjectId);

    /**
     * 人员归档
     *
     * @param personId        人员id
     * @param subjectId       专题id
     * @param personArchiveVO vo
     */
    void archivePerson(String personId, String subjectId, PersonArchiveVO personArchiveVO);

    /**
     * 人员激活
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    void activePerson(String personId, String subjectId);

    /**
     * 查询该人员对应的群体
     *
     * @param personId   人员id
     * @param subjectId  专题id
     * @param pageParams 分页参数 {@link PageParams}
     * @return {@link GroupRelatedPersonVO}
     */
    PageResult<PersonRelatedGroupVO> getPersonRelatedGroupList(String personId, String subjectId, PageParams pageParams);

    /**
     * 查询人员关联的群体列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedGroupVO}
     */
    List<PersonRelatedGroupVO> getPersonRelatedGroupList(String personId, String subjectId);

    /**
     * 添加人员群体关联
     *
     * @param personGroupRelationVO {@link PersonGroupRelationVO}
     */
    void updatePersonGroupsRelation(PersonGroupRelationVO personGroupRelationVO);

    /**
     * 获取关联对话框中人员分页查询的结果
     *
     * @param request 请求
     * @return {@link DialogPersonListVO}
     */
    PageResult<DialogPersonListVO> getDialogPersonListVOPageResult(DialogPersonListRequestVO request);

    /**
     * 获取人员照片
     *
     * @param person 人员
     * @return {@link ImageVO}
     */
    ImageVO getPhotoFromPerson(PersonEntity person);

    /**
     * 根据人员id查询线索列表
     *
     * @param pageParams 分页参数 {@link PageParams}
     * @param personId   人员id
     * @param subjectId  专题id
     * @return {@link PersonRelatedGroupVO}
     */
    PageResult<PersonRelatedClueVO> getPersonRelatedClueList(String personId, String subjectId, PageParams pageParams);

    /**
     * 不分页查询人员关联线索列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedClueVO}
     */
    List<PersonRelatedClueVO> getPersonRelatedClueList(String personId, String subjectId);

    /**
     * [人员档案] 查询事件列表（分页）
     *
     * @param pageParams 分页参数 {@link PageParams}
     * @param personId   人员id
     * @param subjectId  专题id
     * @return {@link PersonRelatedEventVO}
     */
    PageResult<PersonRelatedEventVO> getPersonRelatedEventList(String personId, String subjectId, PageParams pageParams);

    /**
     * [人员档案] 查询事件列表（不分页）
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedEventVO}
     */
    List<PersonRelatedEventVO> getPersonRelatedEventList(String personId, String subjectId);

    /**
     * [人员档案] 更新人员事件的关联
     *
     * @param personEventsRelationVO {@link PersonEventsRelationVO}
     */
    void updatePersonEventsRelation(PersonEventsRelationVO personEventsRelationVO);

    /**
     * [人员档案] 更新人员线索的关联
     *
     * @param personCluesRelationVO {@link PersonCluesRelationVO}
     */
    void updatePersonCluesRelation(PersonCluesRelationVO personCluesRelationVO);

    /**
     * 批量删除人员（维稳）
     *
     * @param personIds 人员id
     * @param subjectId 专题id
     */
    void batchDeletePersons(String subjectId, List<String> personIds);

    /**
     * 列管
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    void controlPerson(String personId, String subjectId);

    /**
     * 取消列管
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    void cancelControlPerson(String personId, String subjectId);
}
