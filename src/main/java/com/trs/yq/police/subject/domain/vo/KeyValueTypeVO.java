package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.utils.JsonUtil;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 键值对
 *
 * <AUTHOR>
 * @date 2021/07/30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeyValueTypeVO implements Serializable {
    private static final long serialVersionUID = -6019475372869018559L;
    private String key;
    private Object value;
    private String type;

    private static final String TIME_PARAMS = "timeParams";
    private static final String ARRAY = "array";
    private static final String CASE_LABEL = "caseLabel";
    private static final String LONG_ARRAY = "longArray";

    /**
     * 从filterParams获取指定key的value
     *
     * @param filterParams 筛选参数
     * @param key          字段名
     * @param clazz        类型
     * @param <T>          类型
     * @return 结果
     */
    public static <T> T getSingleFilterParam(List<KeyValueTypeVO> filterParams, String key, Class<T> clazz) {
        for (KeyValueTypeVO vo : filterParams) {
            if (vo.getKey().equals(key) && vo.getValue() != null) {
                return JsonUtil.parseSpecificObject(vo.getValue(), clazz);
            }
        }
        return null;
    }

    /**
     * 从filterParams获取指定key-value
     *
     * @param filterParams 筛选参数
     * @param key          key
     * @return {@link KeyValueTypeVO}
     */
    public static KeyValueTypeVO get(List<KeyValueTypeVO> filterParams, String key) {
        if (key == null) {
            return null;
        }
        for (KeyValueTypeVO filterParam : filterParams) {
            if (key.equals(filterParam.getKey())) {
                return filterParam;
            }
        }
        return null;
    }

}
