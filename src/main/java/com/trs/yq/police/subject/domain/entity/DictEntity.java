package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 字典表
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_DICT")
public class DictEntity implements Serializable {

    private static final long serialVersionUID = 8164142332012636899L;
    
    @Id
    private String id;
    /**
     * 上级节点id(仅仅用于字典表维护 )
     */
    private String pid;
    /**
     * 码表类型
     */
    private String type;
    /**
     * 码值
     */
    private String code;
    /**
     * 显示名称
     */
    private String name;
    /**
     * 上级代码(同一类型码表本身码值的上下关系)
     */
    @Column(name = "pcode")
    private String pCode;
    /**
     * 对码表的说明(同一类型码表应该具有同样的说明)
     */
    @Column(name = "dictdesc")
    private String dictDesc;
    /**
     * 排序字段
     */
    @Column(name = "shownumber")
    private Integer showNumber;
    /**
     * 编码标准 如 公安部标准  国家标准等
     */
    private String standard;
    /**
     * 其他标记
     */
    private String flag;
}
