package com.trs.yq.police.subject.domain.vo;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/27 14:33
 */
@Data
public class RiskJudgeVO implements Serializable {
    private static final long serialVersionUID = 3741531229255817350L;


    /**
     * 事件编号
     */
    private String eventId;

    /**
     * 群体类别
     */
    private List<String> groupType;

    /**
     * 诉求地
     */
    private String appealPlace;

    /**
     * 行为名称
     */
    private List<String> behaviors;

    /**
     * 涉事人员规模
     */
    private Integer personCount;
    /**
     * 风险等级
     */
    private String riskLevel;
}
