package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 *落脚点居住状态枚举类
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
public enum FootholdStatusEnum {
    /**
     * 1=在住，0=未住
     */
    IS_LIVING("1", "在住"),
    NOT_LIVING("0", "未住");

    FootholdStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private final String name;

    @Getter
    private final String code;
}
