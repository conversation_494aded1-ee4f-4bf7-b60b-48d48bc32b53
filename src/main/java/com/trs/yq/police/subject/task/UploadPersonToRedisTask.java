package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.constants.enums.MonitorStatusEnum;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.PersonSubjectRelationRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;
import static com.trs.yq.police.subject.constants.WarningConstants.*;

/**
 * <AUTHOR>
 * @date 2021/09/01
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.upload.person.task.enable", havingValue = "true")
public class UploadPersonToRedisTask {

    @Value("${com.trs.redis.person.upload.key}")
    private String personUploadKey;

    @Resource(name = "assignRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private PersonSubjectRelationRepository personSubjectRelationRepository;

    @Resource
    private LabelRepository labelRepository;


    /**
     * 定时更新人员的预警类别到redis
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.upload.person.task}")
    public void updatePersonWarningType() {
        List<Map<String, Object>> relations = personSubjectRelationRepository.findSubjectIdAndIdNumber();
        relations.forEach(map -> {
            String subjectId = map.get("SUBJECT_ID").toString();
            String idNumber = map.get("ID_NUMBER").toString();
            String monitorStatus = map.get("MONITOR_STATUS").toString();
            String personId = map.get("ID").toString();
            HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
            Object obj = hashOperations.get(personUploadKey, idNumber);
            List<String> valueStr = getValueStr(subjectId, personId, monitorStatus);
            if (Objects.isNull(obj)) {
                hashOperations.put(personUploadKey, idNumber, JsonUtil.toJsonString(valueStr));
            } else {
                List<String> list = JsonUtil.parseArray(obj.toString(), String.class);
                valueStr.stream().filter(value -> !list.contains(value)).forEach(list::add);
                hashOperations.put(personUploadKey, idNumber, JsonUtil.toJsonString(list));
            }
        });
    }

    private List<String> getValueStr(String subjectId, String personId, String monitorStatus) {
        switch (subjectId) {
            case FK_SUBJECT:
                return Arrays.asList(WARNING_TYPE_FK_XLRWARY, WARNING_TYPE_FK_QTJJ, WARNING_TYPE_FK_GZRYFXYJ);
            case JD_SUBJECT:
                //吊销驾照人员才会推送预警
                LabelEntity personTypeEntity = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId)
                    .stream()
                    .filter(item -> "吊销驾照".equals(item.getName()))
                    .findAny()
                    .orElse(null);
                return Objects.nonNull(personTypeEntity)
                    ? Arrays.asList(WARNING_TYPE_JD_RYJJ, WARNING_TYPE_JD_SDRYJC, WARNING_TYPE_JD_YXSDRY,
                    WARNING_TYPE_JD_YXSDWD)
                    : Arrays.asList(WARNING_TYPE_JD_RYJJ, WARNING_TYPE_JD_YXSDRY, WARNING_TYPE_JD_YXSDWD);
            case XZ_SUBJECT:
                return Arrays.asList(WARNING_TYPE_XZ_ZDRYCJ, WARNING_TYPE_XZ_ZDRYRJ, WARNING_TYPE_XZ_RYYJ);
            //政保专题只预警布控中的人员
            case ZB_SUBJECT:
                List<String> array = new ArrayList<>();
                array.add(WARNING_TYPE_ZB_WARYJJYJ);
                array.add(WARNING_TYPE_ZB_RYYJ);
                if (MonitorStatusEnum.IN_MONITOR.getCode().equals(monitorStatus)) {
                    array.add(WARNING_TYPE_ZB_GZRYFXYJ);
                }
                return array;
            case JJ_SUBJECT:
                List<LabelEntity> types = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
                array = new ArrayList<>();
                if (types.stream().anyMatch(type -> type.getName().contains("失驾"))) {
                    array.add("jj_sjryjc");
                }
                if (types.stream().anyMatch(type -> type.getName().contains("赛摩"))) {
                    array.add(WARNING_TYPE_JJ_SMRYJJ);
                }
                array.add(WARNING_TYPE_JJ_GZRYFXYJ);
                return array;
            case WW_SUBJECT:
                return Arrays.asList(WARNING_TYPE_WW_ZDRYJJ, WARNING_TYPE_WW_ZDRYFS, WARNING_TYPE_WW_QTJJ,
                    WARNING_TYPE_WW_TLJZ,WARNING_TYPE_WW_JZSDYJ);
            case FX_SUBJECT:
                List<String> fxWarningTypes = new ArrayList<>();
                fxWarningTypes.add(WARNING_TYPE_FX_WARYJJYJ);
                fxWarningTypes.add(WARNING_TYPE_FX_RYYJ);
                if (MonitorStatusEnum.IN_MONITOR.getCode().equals(monitorStatus)) {
                    fxWarningTypes.add(WARNING_TYPE_FX_GZRYFXYJ);
                }
                return fxWarningTypes;
            default:
                return Collections.emptyList();
        }
    }
}
