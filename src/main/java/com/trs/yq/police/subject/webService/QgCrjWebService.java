package com.trs.yq.police.subject.webService;

import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import com.trs.yq.police.subject.exception.AppLogicException;
import com.trs.yq.police.subject.properties.CrjSystemProperties;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.webService.domain.CertResult;
import com.trs.yq.police.subject.webService.domain.LoginResult;
import com.trs.yq.police.subject.webService.domain.QueryArgs;
import com.trs.yq.police.subject.webService.domain.QueryResult;
import com.trs.yq.police.subject.webService.domain.QueryResult.StayInfo;
import com.trs.yq.police.subject.webService.domain.ResponseData;
import com.trs.yq.police.subject.webService.domain.StayInfoArgs;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * webService
 */
@Slf4j
@Service
public class QgCrjWebService {

    @Resource
    private CrjSystemProperties crjSystemProperties;

    private Client client;

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RemoteStorageService remoteStorageService;

    @Value("${com.trs.fastdfs.group_name}")
    private String groupName;
    @Value("${com.trs.crj.save.stay.enable:false}")
    private Boolean isSaveStay;
    private String sessionId;

    public final String UNI_METHOD = "unimethod";

    public final String LOGIN_METHOD = "loginNew";

    public final String SAVE_STAY = "jw_saveStay";

    public final String QUERY_ALL = "qg_queryJwryAll";

    private Client getClient() {
        if (Objects.isNull(client)) {
            JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
            client = dcf.createClient(crjSystemProperties.getWsdl());
        }
        return client;
    }


    /**
     * 获取证书签名
     *
     * @return 证书签名
     */
    private String getCert() {
        CertResult certResult = restTemplate.postForObject(crjSystemProperties.getCertUrl(), null,
            CertResult.class);
        log.info("获取证书，请求结果：{}", certResult);
        assert certResult != null;
        if ("1".equals(certResult.getSuccess())) {
            return certResult.getData();
        }
        throw new AppLogicException("获取证书失败!");
    }

    /**
     * 获取sessionId
     */
    private void setSessionId() {
        String cert = getCert();
        try {
            Object[] objects = getClient().invoke(LOGIN_METHOD, cert);
            log.info("登陆 响应内容：{}", objects[0]);
            LoginResult loginResult = JsonUtil.parseObject((String) objects[0], LoginResult.class);

            assert loginResult != null;
            if (loginResult.getSuccess() == 1) {
                this.sessionId = loginResult.getData();
                log.info("登陆成功:{}", loginResult.getData());
            } else {
                throw new AppLogicException("登陆失败!");
            }

        } catch (Exception e) {
            log.error("登陆失败！", e);
        }
    }


    private Object doRequest(String methodName, Object param) {
        try {
            if (StringUtils.isBlank(sessionId)) {
                setSessionId();
            }
            String paramString = JsonUtil.toJsonString(param);
            log.info("webservice 请求接口: {}", methodName);
            log.info("webservice 请求参数: {}", paramString);

            Object[] objects = getClient().invoke(UNI_METHOD, sessionId, methodName, paramString);
            log.info("webservice 响应内容：{}", objects[0]);
            ResponseData responseData = JsonUtil.parseObject((String) objects[0], ResponseData.class);
            assert responseData != null;
            if (responseData.getSuccess() == 1 && "CRJ0000".equals(responseData.getState())) {
                return responseData.getData();
            } else if (responseData.getSuccess() == 0 && "CRJ1002".equals(responseData.getState())) {
                //会话过期
                setSessionId();
                return doRequest(methodName, param);
            }
            throw new AppLogicException("webservice 接口调用失败");
        } catch (AppLogicException e) {
            log.error("", e);
            return null;
        } catch (Exception e) {
            log.error("webservice 接口调用失败", e);
            return null;
        }
    }

    /**
     * 境外人员住宿信息入库
     *
     * @param stayInfoArgs 境外人员住宿信息
     */
    public void saveStay(StayInfoArgs stayInfoArgs) {
        if(isSaveStay){
            doRequest(SAVE_STAY, stayInfoArgs);
        }

    }

    /**
     * 全国境外人员综合查询
     *
     * @param queryArgs 查询参数
     * @return 查询结果
     */
    public List<CrjJwryVisitVO> stayInfo(QueryArgs queryArgs) {

        Object o = doRequest(QUERY_ALL, queryArgs);
        QueryResult queryResult = JsonUtil.parseObject(JsonUtil.toJsonString(o), QueryResult.class);
        if (Objects.isNull(queryResult)) {
            log.info("全国境外人员综合查询结果为空！");
            return new ArrayList<>();
        }
        log.info("=================全过境外人员综合查询: {}",JsonUtil.toJsonString(queryResult));
        StayInfo[] stayInfos = queryResult.getStayinfo();
        if (Objects.isNull(stayInfos) || stayInfos.length == 0) {
            log.info("查询结果，住宿信息为空！");
            return new ArrayList<>();
        }
        StayInfo stayInfo = stayInfos[0];
        if(Objects.isNull(stayInfo)){
            return new ArrayList<>();
        }else {
            CrjJwryVisitVO visitVO = stayInfo.toVisitVO();
              try {
                  Base64.Decoder decoder = Base64.getDecoder();
                  byte[] decode = decoder.decode(stayInfo.getXp().replaceAll("^data:image/jpeg;base64,", ""));
                  String[] imageUrl = remoteStorageService.uploadFile(decode, groupName, "jpg");
                  visitVO.setAttachment(Collections.singletonList(CrjConstants.ATTACHMENT_PREFIX + imageUrl[1]));
              }catch (Exception e){
                  log.error("同步照片失败！,证件号码:{}", queryArgs.getZjhm(),e);
              }
            return Collections.singletonList(visitVO);
        }

    }

}
