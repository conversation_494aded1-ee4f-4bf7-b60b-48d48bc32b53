package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.ApprovalVO;
import com.trs.yq.police.subject.domain.vo.ApprovalListRequest;
import com.trs.yq.police.subject.domain.vo.ApprovalListVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.service.ApprovalService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 人员档案审批
 *
 * <AUTHOR>
 * @since 2021/11/29
 */
@RestController
@RequestMapping("")
public class ApprovalController {

    @Resource
    private ApprovalService approvalService;


    /**
     * [人员档案] 待我审批列表查询
     * http://192.168.200.192:3001/project/4897/interface/api/133789
     *
     * @param request 列表查询条件
     * @return 审批列表
     */
    @PostMapping("/approval-list")
    public PageResult<ApprovalListVO> getApprovalList(@RequestBody ApprovalListRequest request) {
        return approvalService.getApprovalList(request);
    }

    /**
     * [人员档案] 提交删除申请
     * http://192.168.200.192:3001/project/4897/interface/api/133795
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param deleteVO 申请删除信息
     */
    @DeleteMapping("/{personId}/submit-delete")
    public void getDeletePersonApproval(@PathVariable String personId, String subjectId, @RequestBody ApprovalVO deleteVO) {
        approvalService.submitDeletePersonApproval(personId, subjectId, deleteVO);
    }

    /**
     * [人员档案] 待我审批-同意
     * http://192.168.200.192:3001/project/4897/interface/api/133807
     *
     * @param id 审批id
     */
    @GetMapping("/approval-list/agree")
    public void agreeApproval(String id) {
        approvalService.agreeApproval(id);
    }

    /**
     * [人员档案] 待我审批-驳回
     * http://192.168.200.192:3001/project/4897/interface/api/133813
     *
     * @param id 审批id
     */
    @GetMapping("/approval-list/reject")
    public void rejectApproval(String id) {
        approvalService.rejectApproval(id);
    }
}
