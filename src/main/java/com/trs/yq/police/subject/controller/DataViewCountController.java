package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.request.CountRequestVO;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.service.DataViewCountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;

/**
 * 专题首页数据统计接口
 *
 * <AUTHOR>
 * @since 2021/9/14
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data-view")
public class DataViewCountController {

    @Resource
    private DataViewCountService dataViewCountService;

    /**
     * [专题首页-交警/刑侦] 区县分布情况 http://192.168.200.192:3001/project/4897/interface/api/131005
     *
     * @param areaDistributeVO {@link CountRequestVO}
     * @return 区县分布情况
     */
    @PostMapping("/warning/district/county")
    public List<AreaDistributeVO> getDistrictCounty(@RequestBody CountRequestVO areaDistributeVO) {
        return dataViewCountService.getAreaDistribute(areaDistributeVO);
    }

    /**
     * [专题首页-禁毒] 警综涉毒案件统计(柱状图) http://192.168.200.192:3001/project/4897/interface/api/131551
     *
     * @param requestParams {@link RequestParams}
     * @param caseType      案件类型：0（刑事案件），1（刑侦案件）
     * @return 区县分布情况
     */
    @PostMapping("/warning/drug/district/county/histogram")
    public CountDistributeCaseVO getCaseDistrictCounty(@NotEmpty(message = "类型不能为空") String caseType,
        @RequestBody RequestParams requestParams) {
        return dataViewCountService.getCaseDistrictCounty(caseType, requestParams.getTimeParams());
    }

    /**
     * [专题首页-禁毒] 警综涉毒案件统计(总数) http://192.168.200.192:3001/project/4897/interface/api/131599
     *
     * @param requestParams {@link RequestParams}
     * @return 总数
     */
    @PostMapping("/warning/drug/district/county/total")
    public List<CountTypeResponseVO> getCaseDistrictCounty(@RequestBody RequestParams requestParams) {
        return dataViewCountService.getDrugCaseTotal(requestParams.getTimeParams());
    }

    /**
     * [专题首页] 涉毒前科人员 http://192.168.200.192:3001/project/4897/interface/api/131593
     *
     * @return 前科人员地区统计
     */
    @PostMapping("/done-drug/warning/person")
    public List<CountTypeResponseVO> getDoneDrugWarningPerson() {
        return dataViewCountService.getDoneDrugWarningPerson();
    }

    /**
     * [专题首页-交警] 获取区域失驾人员数量TOP10 http://192.168.200.192:3001/project/4897/interface/api/131443
     *
     * @param requestParams 请求参数
     * @return {@link AreaCountVO}
     */
    @PostMapping("/traffic-police/warning/count/cancel-drive")
    public List<AreaCountVO> getAreaCancelDriveCount(@RequestBody RequestParams requestParams) {
        return dataViewCountService.getCancelDriveCount(requestParams.getTimeParams());
    }

    /**
     * [专题首页]获取泸州区域信息 http://192.168.200.192:3001/project/4897/interface/api/130819
     *
     * @return {@link AreaVO}
     */
    @PostMapping("/warning/heatMap/areas")
    public List<AreaVO> getAreaInfo() {
        return dataViewCountService.getAreaInfo();
    }

    /**
     * [专题首页-反恐]获取人员变化率 http://192.168.200.192:3001/project/4897/interface/api/130543
     *
     * @param subjectId 专题id
     * @return {@link PersonChangeRateVO}
     */
    @PostMapping("/index/change-rate")
    public List<PersonChangeRateVO> getPersonChangeRateVoList(@NotBlank(message = "专题id缺失") String subjectId) {
        return dataViewCountService.getPersonChangeRateVOList(subjectId);
    }

    /**
     * [专题首页] 获取热力数据
     *
     * @param heatMapRequestVO {@link HeatMapRequestVO}
     * @return {@link HeatMapVO}
     */
    @PostMapping("/warning/heatMap")
    public List<HeatMapVO> getHeatMapList(@RequestBody @Validated HeatMapRequestVO heatMapRequestVO) {
        return dataViewCountService.getHeatMap(heatMapRequestVO.getSubjectId(), heatMapRequestVO.getAreaCode(),
            heatMapRequestVO.getPersonType(), heatMapRequestVO.getTimeParams());
    }

    /**
     * [专题首页-刑侦]人员类型统计 http://192.168.200.192:3001/project/4897/interface/api/130993
     *
     * @return 统计结果
     */
    @GetMapping("/criminal-investigation/statistics/type")
    public PersonTypeCountVO countCriminalType() {
        return dataViewCountService.countType(XZ_SUBJECT);
    }

    /**
     * [专题首页-政保] 人员区县分布查询 http://192.168.200.192:3001/project/4897/interface/api/131557
     *
     * @param requestParams 请求参数
     * @return {@link DistributePersonCountVO}
     */
    @PostMapping("/political-guarantee/statistics/distribute")
    public List<DistributePersonCountVO> getDistributePersonCount(@RequestBody RequestParams requestParams) {
        return dataViewCountService.getDistributePersonCountList(requestParams.getTimeParams(), ZB_SUBJECT);
    }

    /**
     * [专题首页-政保]人员类型统计 http://192.168.200.192:3001/project/4897/interface/api/131581
     *
     * @return 统计结果
     */
    @GetMapping("/political-guarantee/statistics/type")
    public PersonTypeCountVO countPoliticalType() {
        return dataViewCountService.countTag(ZB_SUBJECT);
    }

    /**
     * [专题首页-政保] 群体统计
     *
     * @return {@link GroupPersonStatisticsVO}
     */
    @GetMapping("/political-guarantee/statistics/group")
    public List<GroupPersonStatisticsVO> statisticsPoliticalGroup() {
        return dataViewCountService.statisticsPoliticalGroup(ZB_SUBJECT);
    }

    /**
     * [专题首页-禁毒] 涉毒人员统计 http://192.168.200.192:3001/project/4897/interface/api/131575
     *
     * @return {@link CountTypeResponseVO}
     */
    @GetMapping("/anti-drug/statistics/type")
    public PersonTypeCountVO getPersonTypeCountVOList() {
        return dataViewCountService.countType(JD_SUBJECT);
    }
}
