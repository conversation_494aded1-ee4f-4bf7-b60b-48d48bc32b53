package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BattleReplyEntity;
import java.util.List;
import java.util.Map;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface BattleReplyRepository extends BaseRepository<BattleReplyEntity, String> {


    /**
     * 查询事件合成所有回复
     *
     * @param eventId 事件id
     * @return {@link BattleReplyEntity}
     */
    @Query(nativeQuery = true, value = "select * " +
        " from T_BATTLE_REPLY " +
        " where RECORDID in (SELECT br.id " +
        "                   FROM T_BATTLE_RECORD br " +
        "                   WHERE br.state = 2 " +
        "                     AND EXISTS( " +
        "                           SELECT 1 " +
        "                           FROM T_BATTLE_EVENT be " +
        "                           WHERE be.SRCTABLE = 'system.t_event' " +
        "                             AND be.KEYNAME = 'ID' " +
        "                             AND be.KEYVAL = :eventId " +
        "                             AND INSTR(br.eventids, be.ID) > 0)) " +
        " order by REPLYTIME desc ")
    List<BattleReplyEntity> getByEventId(@Param("eventId") String eventId);


    /**
     * 查询事件预案等级
     *
     * @param eventId 事件id
     * @return 事件元等级
     */
    @Query(nativeQuery = true, value = "select PLANCONFID  from T_BATTLE_PLAN where SRCTABLE='system.t_event' and KEYVAL=:eventId order by CRTTIME desc ")
    List<Map<String, String>> getEventLevel(@Param("eventId") String eventId);

}
