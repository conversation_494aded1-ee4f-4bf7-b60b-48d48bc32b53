package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警轨迹信息
 *
 * <AUTHOR>
 * @since 2021/09/06 10:24
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_TRAJECTORY")
public class WarningTrajectoryEntity implements Serializable {

    private static final long serialVersionUID = -4555602432183223933L;

    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 预警源id
     */
    private String sourceId;

    /**
     * 身份证号
     */
    private String idNumber;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 地点
     */
    private String place;

    /**
     * 地址
     */
    private String address;

    /**
     * hybase 表名
     */
    private String hybaseTable;

    /**
     * hybase主键
     */
    private String hybaseId;

    /**
     * id的类型
     */
    private String idType;

    /**
     * id的值
     */
    private String idValue;

    /**
     * 全景图url
     */
    private String imageUrl;

    /**
     * 裁剪图url
     */
    private String cropUrl;

    /**
     * 图片相似度
     */
    private String similarity;

    /**
     * 路径时间
     */
    private LocalDateTime dateTime;
    /**
     * 地区编码
     */
    private String areaCode;
    /**
     * 车辆预警信息
     */
    private String warningExplain;
    /**
     * 原始json数据
     */
    private String rawData;
}
