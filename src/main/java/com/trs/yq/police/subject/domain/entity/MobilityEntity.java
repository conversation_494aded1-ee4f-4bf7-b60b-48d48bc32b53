package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 人员流动信息表
 *
 * <AUTHOR>
 * @since 2021/7/29
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_MOBILITY")
public class MobilityEntity extends BaseEntity {

    private static final long serialVersionUID = -3989424450752212052L;

    /**
     * 人员id
     */
    private String personId;
    /**
     * 时间
     */
    private LocalDateTime moveTime;
    /**
     * 流动类型 流入=1、流出=2
     */
    private String moveType;
    /**
     * 地址
     */
    private String location;
    /**
     * 备注
     */
    private String note;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MobilityEntity that = (MobilityEntity) o;
        return personId.equals(that.personId) && moveTime.equals(that.moveTime) && moveType.equals(that.moveType) && Objects.equals(location, that.location) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(personId, moveTime, moveType, location, note);
    }
}
