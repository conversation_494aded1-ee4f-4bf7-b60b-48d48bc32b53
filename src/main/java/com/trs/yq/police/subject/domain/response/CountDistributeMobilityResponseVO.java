package com.trs.yq.police.subject.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 区县人员流动分布情况
 *
 * <AUTHOR>
 * @date 2021/9/4 14:15
 */
@Getter
@Setter
@ToString
public class CountDistributeMobilityResponseVO implements Serializable {

    private static final long serialVersionUID = -8539143770016333725L;

    /**
     * 地区代码
     */
    private String areaCode;

    /**
     * 地区名称
     */
    private String areaName;

    /**
     * 人员数量
     */
    private Long personCount;

    /**
     * 群体数量
     */
    private Long groupCount;

    /**
     * 人员增长
     */
    private Long personIncrement;
}
