package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.constants.enums.WarningTypeEnum;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.service.DataViewCountService;
import com.trs.yq.police.subject.service.DataViewService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FX_SUBJECT;

/**
 * @ClassName CounterCultsDataViewController
 * @Description 反邪专题相关接口
 * <AUTHOR>
 * @Date 2024/2/27 13:46
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/data-view/counter-cults")
@AllArgsConstructor
public class CounterCultsDataViewController {

    private final DataViewCountService dataViewCountService;

    private final DataViewService dataViewService;

    /**
     * [专题首页-反邪] 人员区县分布查询 http://***************:3001/project/4897/interface/api/131557
     *
     * @param requestParams 请求参数
     * @return {@link DistributePersonCountVO}
     */
    @PostMapping("/statistics/distribute")
    public List<DistributePersonCountVO> getDistributePersonCount(@RequestBody RequestParams requestParams) {
        return dataViewCountService.getDistributePersonCountList(requestParams.getTimeParams(), FX_SUBJECT);
    }

    /**
     * [专题首页-反邪]人员类型统计 http://***************:3001/project/4897/interface/api/131581
     *
     * @return 统计结果
     */
    @GetMapping("/statistics/type")
    public PersonTypeCountVO countPoliticalType() {
        return dataViewCountService.countTag(FX_SUBJECT);
    }

    /**
     * [专题首页-反邪] 群体统计
     *
     * @return {@link GroupPersonStatisticsVO}
     */
    @GetMapping("/statistics/group")
    public List<GroupPersonStatisticsVO> statisticsPoliticalGroup() {
        return dataViewCountService.statisticsPoliticalGroup(FX_SUBJECT);
    }

    /**
     * [专题首页-反邪]  人员预警
     * http://***************:3001/project/4897/interface/api/131365
     *
     * @param status 预警状态
     * @return {@link WarningScrollListVO}
     */
    @GetMapping("/warning/person")
    public WarningScrollListVO getWarningPersonControlVoList(@NotBlank(message = "预警状态缺失") String status) {
        return dataViewService.getWarningPersonInfoList(WarningTypeEnum.FX_RYYJ.getCode(),status);
    }

    /**
     * [专题首页-反邪-预警]查询群体危安人员聚集预警列表
     * http://***************:3001/project/4897/interface/api/131347
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/warning/group/gather")
    public WarningScrollListVO getPoliticalGatherList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getGatherWarningList(WarningTypeEnum.FX_WARYJJYJ.getCode(), status);
    }


    /**
     * [专题首页-反邪-预警]查询关注人员风险警情预警列表
     * http://***************:3001/project/4897/interface/api/131389
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/warning/focus-person")
    public WarningScrollListVO getPoliticalFocusPeopleWarningList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getFocusPersonWarningList(WarningTypeEnum.FX_GZRYFXYJ.getCode(), status);
    }


}
