package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;


/**
 * 案事件信息实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_CASE_EVENT")
public class CaseEventEntity extends BaseEntity {

    private static final long serialVersionUID = 8199825285525183066L;
    /**
     * 人员ID
     */
    private String personId;

    /**
     * 案事件名称
     */
    private String name;

    /**
     * 案事件类型
     */
    private String caseType;

    /**
     * 发生时间
     */
    private LocalDateTime happenTime;

    /**
     * 发生地点
     */
    private String happenSituation;

    /**
     * 派出所
     */
    private String policeSituation;
}
