package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.request.TrajectoryListRequest;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.PersonTrajectoryVO;

/**
 * 出入境服务层实现
 *
 * <AUTHOR>
 * @since 2021/9/17
 */
public interface HkTrajectoryService {

    /**
     * 查询轨迹数量
     *
     * @param idNumber 人员id
     * @return 数量
     */
    Integer countTrajectNum(String idNumber);

    /**
     * 查询轨迹
     *
     * @param request  请求参数
     * @param personId 人员id
     * @return 数量
     */
    PageResult<PersonTrajectoryVO> traject(String personId, TrajectoryListRequest request);
}
