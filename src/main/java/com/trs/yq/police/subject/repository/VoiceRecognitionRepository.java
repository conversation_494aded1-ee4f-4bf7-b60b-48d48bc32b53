package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.UserEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * 语音识别统计相关查询
 *
 * <AUTHOR>
 * @date 2021/10/19 15:55
 */
@Repository
public interface VoiceRecognitionRepository extends BaseRepository<UserEntity, String> {
    /**
     * 警情数量统计 八类暴力犯罪
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return java.lang.Long
     * <AUTHOR>
     */
    @Query(
            nativeQuery = true,
            value =
                    "select count(1) "
                            + "from JWZH_ASJ_JQXX t "
                            + "where t.JQZTDM not in ('04', '05') "
                            + "  and t.BJSJ_RQSJ > :beginTime "
                            + "  and t.BJSJ_RQSJ < :endTime "
                            + "  and t.CJDW_GAJGJGDM like concat(:areaCode,'%')"
                            + "  and (t.JQLBDM like '010301' "
                            + "    or t.JQLBDM like '010303' "
                            + "    or t.JQLBDM like '010401' "
                            + "    or t.JQLBDM like '010302' "
                            + "    or t.JQLBDM like '010105' "
                            + "    or t.JQLBDM like '010101' "
                            + "    or t.JQLBDM like '010405' "
                            + "    or t.JQLBDM like '010401')")
    Long countJq8(
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("areaCode") String areaCode);

    /**
     * 警情数量统计 街面六类
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return java.lang.Long
     * <AUTHOR>
     */
    @Query(
            nativeQuery = true,
            value =
                    "select count(1) "
                            + "from JWZH_ASJ_JQXX t "
                            + "where t.JQZTDM not in ('04', '05') "
                            + "  and t.BJSJ_RQSJ >:beginTime "
                            + "  and t.BJSJ_RQSJ <:endTime "
                            + "  and t.CJDW_GAJGJGDM like concat(:areaCode,'%') "
                            + "  and (t.JQLBDM like '1040203' "
                            + "    or t.JQLBDM like '1040221' "
                            + "    or t.JQLBDM like '10403%' "
                            + "    or t.JQLBDM like '10401%' "
                            + "    or t.JQLBDM like '1040202' )")
    Long countJq6(
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("areaCode") String areaCode);

    /**
     * 主城区-警情数量统计 街面六类
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return java.lang.Long
     * <AUTHOR>
     */
    @Query(
            nativeQuery = true,
            value =
                    "select count(1) "
                            + "from JWZH_ASJ_JQXX t "
                            + "where t.JQZTDM not in ('04', '05') "
                            + "  and t.BJSJ_RQSJ >:beginTime "
                            + "  and t.BJSJ_RQSJ <:endTime "
                            + "  and t.CJDW_GAJGJGDM like '510503' "
                            + "  and t.CJDW_GAJGJGDM like '510502   ' "
                            + "  and t.CJDW_GAJGJGDM like '510503' "
                            + "  and (t.JQLBDM like '1040203' "
                            + "    or t.JQLBDM like '1040221' "
                            + "    or t.JQLBDM like '10403%' "
                            + "    or t.JQLBDM like '10401%' "
                            + "    or t.JQLBDM like '1040202' )")
    Long countZcqJq6(
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime);

    /**
     * 立案(刑事案件)数量统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return java.lang.Long
     * <AUTHOR>
     * @since 2021/10/15 14:19
     */
    @Query(
            nativeQuery = true,
            value =
                    "select count(1) "
                            + "from JWZH_XSAJ_AJ t "
                            + "where "
                            + "    t.LARQ  >:beginTime"
                            + "  and  t.LARQ  <:endTime"
                            + "  and t.LADW_GAJGJGDM like concat(:areaCode,'%') "
                            + "  and (t.AJXLBDM like '20001%' "
                            + "    or t.AJXLBDM like '2000300' "
                            + "    or t.AJXLBDM like '2000300' "
                            + "    )")
    long countLaXs(
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("areaCode") String areaCode);

    /**
     * 有效警情数量统计
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(
            nativeQuery = true,
            value =
                    "select count(1) "
                            + "from JWZH_ASJ_JQXX t "
                            + "where t.JQZTDM not in ('04', '05') "
                            + "  and t.BJSJ_RQSJ >:beginTime "
                            + "  and t.BJSJ_RQSJ <:endTime "
                            + "  and t.CJDW_GAJGJGDM like concat(:areaCode,'%')")
    long countYxJq(
            @Param("beginTime") LocalDateTime beginTime,
            @Param("endTime") LocalDateTime endTime,
            @Param("areaCode") String areaCode);

    /**
     * 电信诈骗数量统计
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(nativeQuery = true, value = "with map as (select a.ASJBH as bh,regexp_substr(a.AJLBDM,'[^,]+',1,level) code from JWZH_XZAJ_AJ a " +
            "             connect by level <= nvl(regexp_count(a.AJLBDM,','),0)+1 " +
            "                    and prior a.ASJBH = a.ASJBH " +
            "                    and prior DBMS_RANDOM.VALUE is not null) " +
            " select count(1) from JWZH_XZAJ_AJ a " +
            " where a.fxasjsj > :beginTime and a.fxasjsj < :endTime " +
            " and a.xt_zxbz =0 " +
            "  and a.BADW_GAJGJGDM like concat(:areaCode,'%')  and exists(select 1 from map m where m.bh = a.ASJBH and m.code in" +
            "                                                                                            ('03040045','03040046','03040047','03040048','03040049','03040050','03040051','03040052','03040053','03040054','03040055'," +
            "                                                                                             '11000000','11000001','24000003','24000004','24000005','24000006','24000007','24000008','24000009'))")
    long countDxzp(@Param("beginTime") LocalDateTime beginTime,
                   @Param("endTime") LocalDateTime endTime,
                   @Param("areaCode") String areaCode);

    /**
     * 行政案件-涉黄
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(nativeQuery = true, value = "with map as (select a.ASJBH as bh,regexp_substr(a.AJLBDM,'[^,]+',1,level) code from JWZH_XZAJ_AJ a" +
            " connect by level <= nvl(regexp_count(a.AJLBDM,','),0)+1" +
            " and prior a.ASJBH = a.ASJBH" +
            " and prior DBMS_RANDOM.VALUE is not null)" +
            "select count(1) from JWZH_XZAJ_AJ a" +
            " where a.fxasjsj >:beginTime and a.fxasjsj < :endTime and a.xt_zxbz =0" +
            " and a.BADW_GAJGJGDM like concat(:areaCode,'%')  and exists(select 1 from map m where m.bh = a.ASJBH and m.code in" +
            "('03040045','03040046','03040047','03040048','03040049','03040050','03040051','03040052','03040053','03040054','03040055'," +
            "'11000000','11000001','24000003','24000004','24000005','24000006','24000007','24000008','24000009'))")
    long countXzSh(@Param("beginTime") LocalDateTime beginTime,
                   @Param("endTime") LocalDateTime endTime,
                   @Param("areaCode") String areaCode);

    /**
     * 行政案件-涉赌
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(nativeQuery = true, value = "with map as (select a.ASJBH as bh,regexp_substr(a.AJLBDM,'[^,]+',1,level) code from JWZH_XZAJ_AJ a" +
            " connect by level <= nvl(regexp_count(a.AJLBDM,','),0)+1" +
            " and prior a.ASJBH = a.ASJBH" +
            " and prior DBMS_RANDOM.VALUE is not null)" +
            "select count(1) from JWZH_XZAJ_AJ a" +
            " where a.fxasjsj >:beginTime and a.fxasjsj < :endTime and a.xt_zxbz =0" +
            " and a.BADW_GAJGJGDM like concat(:areaCode,'%')  and exists(select 1 from map m where m.bh = a.ASJBH and m.code in" +
            "('03040056','03040057','24000010','24000011','24000021'))")
    long countXzSd(@Param("beginTime") LocalDateTime beginTime,
                   @Param("endTime") LocalDateTime endTime,
                   @Param("areaCode") String areaCode);

    /**
     * 行政案件-涉毒
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(nativeQuery = true, value = "with map as (select a.ASJBH as bh,regexp_substr(a.AJLBDM,'[^,]+',1,level) code from JWZH_XZAJ_AJ a" +
            " connect by level <= nvl(regexp_count(a.AJLBDM,','),0)+1" +
            " and prior a.ASJBH = a.ASJBH" +
            " and prior DBMS_RANDOM.VALUE is not null)" +
            "select count(1) from JWZH_XZAJ_AJ a" +
            " where a.fxasjsj >:beginTime and a.fxasjsj < :endTime and a.xt_zxbz =0" +
            " and a.BADW_GAJGJGDM like concat(:areaCode,'%')  and exists(select 1 from map m where m.bh = a.ASJBH and m.code in" +
            "('03040058','03040059','03040061','03040062','03040063','03040065','24000001','24000002','65000001','65000002'))")
    long countXzSdp(@Param("beginTime") LocalDateTime beginTime,
                    @Param("endTime") LocalDateTime endTime,
                    @Param("areaCode") String areaCode);

    /**
     * 行政案件-殴打他人
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(nativeQuery = true, value = "with map as (select a.ASJBH as bh,regexp_substr(a.AJLBDM,'[^,]+',1,level) code from JWZH_XZAJ_AJ a" +
            " connect by level <= nvl(regexp_count(a.AJLBDM,','),0)+1" +
            " and prior a.ASJBH = a.ASJBH" +
            " and prior DBMS_RANDOM.VALUE is not null)" +
            "select count(1) from JWZH_XZAJ_AJ a" +
            " where a.fxasjsj >:beginTime and a.fxasjsj < :endTime and a.xt_zxbz =0" +
            " and a.BADW_GAJGJGDM like concat(:areaCode,'%')  and exists(select 1 from map m where m.bh = a.ASJBH and m.code in" +
            "('03030015'))")
    long countXzOdtr(@Param("beginTime") LocalDateTime beginTime,
                     @Param("endTime") LocalDateTime endTime,
                     @Param("areaCode") String areaCode);

    /**
     * 行政案件-所有
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param areaCode  地区代码
     * @return 数量
     */
    @Query(nativeQuery = true, value = "with map as (select a.ASJBH as bh,regexp_substr(a.AJLBDM,'[^,]+',1,level) code from JWZH_XZAJ_AJ a" +
            " connect by level <= nvl(regexp_count(a.AJLBDM,','),0)+1" +
            " and prior a.ASJBH = a.ASJBH" +
            " and prior DBMS_RANDOM.VALUE is not null)" +
            " select count(1) from JWZH_XZAJ_AJ a" +
            " where a.fxasjsj >:beginTime and a.fxasjsj < :endTime and a.xt_zxbz =0" +
            " and a.BADW_GAJGJGDM like concat(:areaCode,'%')  and exists(select 1 from map m where m.bh = a.ASJBH and m.code in" +
            "('03040045','03040046','03040047','03040048','03040049','03040050','03040051','03040052','03040053','03040054','03040055'," +
            "'11000000','11000001','24000003','24000004','24000005','24000006','24000007','24000008','24000009','03030015','03040056'," +
            "'03040057','24000010','24000011','24000021','03040058','03040059','03040061','03040062','03040063','03040065','24000001','24000002','65000001','65000002'))")
    long countXzAll(@Param("beginTime") LocalDateTime beginTime,
                    @Param("endTime") LocalDateTime endTime,
                    @Param("areaCode") String areaCode);
}
