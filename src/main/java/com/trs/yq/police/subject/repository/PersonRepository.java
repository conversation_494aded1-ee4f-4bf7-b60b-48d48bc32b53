package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.dto.PersonCountDTO;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 人员档案查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 14:20
 */
@Repository
public interface PersonRepository extends BaseRepository<PersonEntity, String> {

    /**
     * 通过身份证号码查询
     *
     * @param idNumber 身份证号码
     * @return 人员信息
     */
    PersonEntity findByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据人员Id查询人员群体类别
     *
     * @param personId  人员Id
     * @param subjectId 专题ID
     * @return 群体类别
     */
    @Query(nativeQuery = true, value = "select NAME " + "from T_PS_GROUP" + " where ID in (" + "   select GROUP_ID " + "   from  T_PS_PERSON_GROUP_RELATION t1 JOIN T_PS_GROUP t2 ON t1.GROUP_ID=t2.ID " + "   where t1.PERSON_ID = :personId and t2.SUBJECT_ID=:subjectId)")
    List<String> getGroupType(@Param("personId") String personId, @Param("subjectId") String subjectId);

    /**
     * 查询该地区该类别的人员数量
     *
     * @param areaCode     地区编码
     * @param personTypeId 人员类别id
     * @return 数量
     */
    @Query(nativeQuery = true, value = "select a.* from T_PS_PERSON a inner join T_PS_PERSON_LABEL_RELATION b " + "on a.ID = b.PERSON_ID " + "where a.AREA_CODE = :areaCode and b.LABEL_ID = :personTypeId")
    List<PersonEntity> getPersonByPersonTypeAndAreaCode(@Param("areaCode") String areaCode, @Param("personTypeId") String personTypeId);

    /**
     * 根据类型名称查询人员
     *
     * @param typeName 人员类别名称
     * @return 数量
     */
    @Query(nativeQuery = true, value = "SELECT * " + "FROM T_PS_PERSON p " + "WHERE p.id IN (" + "   SELECT PERSON_ID " + "   FROM T_PS_PERSON_LABEL_RELATION ptr JOIN T_PS_LABEL pt ON ptr.label_id = pt.id " + "   WHERE instr(pt.name,:typeName)>0" + ") " + "AND p.id IN ( SELECT PERSON_ID FROM T_PS_PERSON_SUBJECT_RELATION psr WHERE psr.SUBJECT_ID='2')")
    List<PersonEntity> getPersonByPersonTypeName(@Param("typeName") String typeName);

    /**
     * 根据人员Id查询人员种类
     *
     * @param personId  人员Id
     * @param subjectId 专题ID
     * @return 人员种类
     */
    @Query(nativeQuery = true, value = "select NAME " + "from T_PS_LABEL" + " where ID in (" + "   select LABEL_ID " + "   from  T_PS_PERSON_LABEL_RELATION t1 JOIN T_PS_LABEL t2 ON t1.LABEL_ID = t2.ID " + "   where t1.PERSON_ID = :personId and t2.SUBJECT_ID=:subjectId)")
    List<String> getPersonType(@Param("personId") String personId, @Param("subjectId") String subjectId);

    /**
     * 根据人员Id查询人员标签
     *
     * @param personId  人员Id
     * @param subjectId 专题ID
     * @return 人员标签
     */
    @Query(nativeQuery = true, value = "select NAME " + "from T_PS_LABEL" + " where ID in (" + "   select LABEL_ID " + "   from  T_PS_PERSON_LABEL_RELATION t1 JOIN T_PS_LABEL t2 ON t1.LABEL_ID=t2.ID " + "   where t1.PERSON_ID = :personId and t2.SUBJECT_ID=:subjectId)")
    List<String> getPersonLabel(@Param("personId") String personId, @Param("subjectId") String subjectId);


    /**
     * 根据人员id查询列表
     *
     * @param personIds 人员id列表
     * @return 人员列表
     */
    @Query("SELECT person FROM PersonEntity person WHERE person.id in (:personIds)")
    List<PersonEntity> findByIds(@Param("personIds") List<String> personIds);

    /**
     * 通过身份证检查人员是否与专题有关联关系
     *
     * @param idNumber  身份证
     * @param subjectId 专题id
     * @return 是否存在
     */
    @Query("select case when count(p.id) > 0 then true else false end " + "from PersonEntity p " + "left join PersonSubjectRelationEntity psr on psr.personId = p.id " + "where p.idNumber = :idNumber " + "and psr.subjectId = :subjectId")
    boolean checkExistByIdNumberAndSubjectId(@Param("idNumber") String idNumber, @Param("subjectId") String subjectId);

    /**
     * 检查人员是否为逃犯
     *
     * @param idNumber 身份证号
     * @return number
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(1) " + "FROM T_ALARM_ZTRY TAZ " + "WHERE TAZ.ZJHM = :idNumber " + "AND TAZ.YWZT = '审核通过'")
    long checkPersonIsEscapedCriminal(@Param("idNumber") String idNumber);

    /**
     * 检查人员是否为常控人员
     *
     * @param idNumber 身份证号
     * @return number
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(1) " + "FROM T_ALARM_CKRY TAC " + "WHERE TAC.GMSFHM = :idNumber " + "AND TAC.YXX = '有效'")
    long checkPersonIsUnderNormalizeControl(@Param("idNumber") String idNumber);

    /**
     * 检查人员是否为临控人员
     *
     * @param idNumber 身份证号
     * @return number
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(1) " + "FROM T_ALARM_BKXX TAB, T_ALARM_BKXX_RY TABR " + "WHERE TAB.BKBH = TABR.BKZJ " + "AND TABR.ZJHM = :idNumber " + "AND TAB.STATUS = 2")
    long checkPersonIsUnderTempControl(@Param("idNumber") String idNumber);

    /**
     * 更新管控人员状态
     *
     * @param id            人员id
     * @param controlStatus 人员状态
     */
    @Modifying
    @Query(nativeQuery = true, value = "update T_PS_PERSON p set p.MONITOR_STATUS = :controlStatus where p.ID = :id")
    void updateControlStatusWithId(@Param("id") String id, @Param("controlStatus") String controlStatus);

    /**
     * 查询虚拟身份与身份证号的映射关系
     *
     * @return 虚拟身份与身份证号的对应关系
     */
    @Query(nativeQuery = true, value = "(SELECT t2.id_number, t1.phone_number as value FROM t_ps_person_mobile_phone t1 JOIN t_ps_person t2 ON t1.person_id = t2.id) " + "UNION " + "(SELECT t2.id_number, t1.VIRTUAL_NUMBER as value FROM T_PS_PERSON_VIRTUAL_IDENTITY t1 JOIN t_ps_person t2 ON t1.PERSON_ID =t2.ID WHERE t1.VIRTUAL_TYPE in (1,2,3,4,5)) " + "UNION " + "(SELECT t2.id_number, t1.VEHICLE_NUMBER as value FROM T_PS_PERSON_VEHICLE t1 JOIN t_ps_person t2 ON t1.person_id = t2.id)")
    List<Map<String, Object>> findVirtualIdentities();

    /**
     * 根据人员id查询
     *
     * @param subjectId 专题id
     * @return 统计数量
     */
    @Query("select new com.trs.yq.police.subject.domain.dto.PersonCountDTO(p.controlStatus, count(p.id)) from PersonEntity p where p.id in (select personId from PersonSubjectRelationEntity where subjectId =:subjectId) group by p.controlStatus")
    List<PersonCountDTO> findAllByIdAndControlStatus(@Param("subjectId") String subjectId);

    /**
     * 统计布控人数
     *
     * @param subjectId 专题id
     * @return 统计数量
     */
    @Query("select count(p.id) from PersonEntity p where p.monitorStatus = '1' and p.id in (select personId from PersonSubjectRelationEntity where subjectId =:subjectId)")
    Long countBySubjectIdInMonitor(@Param("subjectId") String subjectId);

    /**
     * 根据专题id查询所有人员实体
     *
     * @param subjectId   专题id
     * @param typeId      人员类别id
     * @param createDept  创建单位id
     * @param searchValue 检索文字
     * @param pageable    {@link Pageable}
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 " + "FROM PersonSubjectRelationEntity t1 JOIN PersonEntity t2 ON t1.personId = t2.id " + "WHERE t1.subjectId=:subjectId " + "AND (:typeId is null or EXISTS(SELECT t3 FROM PersonLabelRelationEntity t3 WHERE t3.personId=t2.id AND t3.labelId=:typeId)) " + "AND (:searchValue is null or concat(t2.name, t2.idNumber) like concat('%',:searchValue,'%')) " + "AND (:createDept is null or t2.crDeptCode like concat(:createDept,'%') ) " + "ORDER BY t2.upTime DESC")
    Page<PersonEntity> findAllBySubjectId(@Param("subjectId") String subjectId, @Param("typeId") String typeId, @Param("createDept") String createDept, @Param("searchValue") String searchValue, Pageable pageable);

    /**
     * 查询专题下所有关联人
     *
     * @param subjectId 专题id
     * @return {@link PersonEntity}
     */
    @Query("SELECT p FROM PersonEntity p JOIN PersonSubjectRelationEntity psr ON p.id=psr.personId WHERE psr.subjectId=:subjectId")
    List<PersonEntity> findAllBySubjectId(@Param("subjectId") String subjectId);

    /**
     * 根据线索id查询所有关联的人员
     *
     * @param clueId 线索id
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 FROM CluePersonRelationEntity t1 JOIN PersonEntity t2 ON t1.personId=t2.id WHERE t1.clueId=:clueId")
    List<PersonEntity> findAllByClueId(@Param("clueId") String clueId);

    /**
     * 根据线索id查询所有关联的人员(分页)
     *
     * @param clueId   线索id
     * @param pageable {@link Pageable}
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 FROM CluePersonRelationEntity t1 JOIN PersonEntity t2 ON t1.personId=t2.id WHERE t1.clueId=:clueId ORDER BY t2.upTime DESC")
    Page<PersonEntity> findAllByClueId(String clueId, Pageable pageable);

    /**
     * 根据群体id查询所有关联的人员
     *
     * @param groupId 线索id
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 FROM PersonGroupRelationEntity t1 JOIN PersonEntity t2 ON t1.personId=t2.id WHERE t1.groupId=:groupId")
    List<PersonEntity> findAllByGroupId(@Param("groupId") String groupId);

    /**
     * 根据群体id查询所有关联的人员
     *
     * @param groupId  线索id
     * @param pageable {@link Pageable}
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 FROM PersonGroupRelationEntity t1 JOIN PersonEntity t2 ON t1.personId=t2.id WHERE t1.groupId=:groupId ORDER BY t1.activityLevel DESC,t2.upTime DESC")
    Page<PersonEntity> findAllByGroupId(String groupId, Pageable pageable);

    /**
     * 根据人员身份证号查询列表
     *
     * @param idNumbers 身份证号列表
     * @return 人员列表
     */
    @Query("SELECT person FROM PersonEntity person WHERE person.idNumber in (:idNumbers)")
    List<PersonEntity> findByIdNumbers(@Param("idNumbers") List<String> idNumbers);

    /**
     * 根据专题id 查询人员列表
     *
     * @param subjectId 专题id
     * @param endTime   结束时间
     * @return {@link PersonEntity}
     */
    @Query(nativeQuery = true, value = "SELECT x.* FROM T_PS_PERSON x join T_PS_PERSON_SUBJECT_RELATION y on x.id = y.person_id  where  x.cr_time< :endTime  and y.SUBJECT_ID= :subjectId ")
    List<PersonEntity> getPersonListBySubject(@Param("subjectId") String subjectId, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据人员类别查询所有人员身份证号
     *
     * @param typeId 人员类型id
     * @return 身份证
     */
    @Query("select t1.idNumber from PersonEntity t1 join PersonLabelRelationEntity t2 on t1.id=t2.personId where t2.labelId =:typeId")
    List<String> getIdNumbersByPersonType(String typeId);

    /**
     * 统计未分类人员数量
     *
     * @param subjectId 专题id
     * @param areaCode  区域id
     * @return 数量
     */
    @Query(nativeQuery = true, value = "SELECT p.* " + "FROM T_PS_PERSON p " + "WHERE NOT EXISTS(" + "   SELECT ptr.id " + "   FROM T_PS_PERSON_LABEL_RELATION ptr JOIN T_PS_LABEL pt ON ptr.LABEL_ID=pt.id " + "   WHERE pt.SUBJECT_ID=:subjectId " + "   AND ptr.PERSON_ID=p.ID) " + "AND EXISTS( " + "   SELECT psr.id " + "   FROM T_PS_PERSON_SUBJECT_RELATION psr " + "   WHERE psr.PERSON_ID=p.id " + "   AND psr.SUBJECT_ID=:subjectId) " + "AND (:areaCode is null or instr(p.AREA_CODE,:areaCode)>0 )")
    List<PersonEntity> findAllOtherPerson(@Param("subjectId") String subjectId, @Param("areaCode") String areaCode);

    /**
     * 获取所有身份证号
     *
     * @return 身份证号
     */
    @Query("select p.idNumber from PersonEntity p")
    List<String> findAllIdNumber();


    /**
     * 查询各专题人员增长人数
     *
     * @param subjectId 专题id
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return {@link PersonEntity}
     */
    @Query(nativeQuery = true, value = "SELECT count(*) FROM T_PS_PERSON x join T_PS_PERSON_SUBJECT_RELATION y on x.id = y.person_id  where x.cr_time> :beginTime and x.cr_time< :endTime  and y.SUBJECT_ID= :subjectId ")
    long getPersonIncrement(@Param("subjectId") String subjectId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 分页查询事件关联的人员
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 FROM EventPersonRelationEntity t1 JOIN PersonEntity t2 ON t1.personId=t2.id WHERE t1.eventId=:eventId ORDER BY t2.upTime DESC")
    Page<PersonEntity> findAllByEventId(String eventId, Pageable pageable);

    /**
     * 不分页查询事件关联的人员
     *
     * @param eventId 事件id
     * @return {@link PersonEntity}
     */
    @Query("SELECT t2 FROM EventPersonRelationEntity t1 JOIN PersonEntity t2 ON t1.personId=t2.id WHERE t1.eventId=:eventId ORDER BY t2.upTime DESC")
    List<PersonEntity> findAllByEventId(String eventId);

    /**
     * 查询专题下 电话号码匹配的人员
     *
     * @param subjectId 专题id
     * @param phone     电话号码
     * @return 人员列表
     */
    @Query("SELECT p FROM PersonEntity p " +
            "WHERE EXISTS(SELECT r.id FROM PersonSubjectRelationEntity r WHERE r.personId=p.id AND r.subjectId=:subjectId) " +
            "AND p.contactInformation LIKE concat('%',:phone,'%')")
    List<PersonEntity> findAllBySubjectIdAndContactInfo(@Param("subjectId") String subjectId, @Param("phone") String phone);


    /**
     * 根据标签名称查人
     *
     * @param labelName 标签名称
     * @return 人员列表
     */
    @Query("select t1.idNumber from PersonEntity t1 join PersonLabelRelationEntity t2 on t1.id=t2.personId " + "where t2.labelId in (SELECT l.id from LabelEntity l where l.name=:labelName)")
    List<PersonEntity> findAllByLabelName(@Param("labelName") String labelName);

    /**
     * 查询高风险预警人员和警情
     *
     * @return 人员
     */
    @Query(nativeQuery = true, value = "SELECT p.id as person_id, e.id as event_id " +
            "FROM T_PS_PERSON p ,T_BATTLE_EVENT e " +
            "WHERE p.ID IN (SELECT PERSON_ID FROM T_PS_PERSON_SUBJECT_RELATION WHERE SUBJECT_ID='6') " +
            "AND INSTR(p.CONTACT_INFORMATION,e.CALLPHONE)>0 AND e.SAVETYPE='5' ")
    List<Map<String, String>> getGfxWarningPerson();

    /**
     * 根据身份证号查询人员
     *
     * @param phoneNumber 电话号码
     * @param subjectId   专题id
     * @return 人员
     */
    @Query(nativeQuery = true, value = "SELECT p.* FROM T_PS_PERSON p WHERE INSTR(p.CONTACT_INFORMATION,:phoneNumber)>0 " +
            "and EXISTS(SELECT 1 FROM T_PS_PERSON_SUBJECT_RELATION psr WHERE psr.PERSON_ID=p.ID AND psr.SUBJECT_ID=:subjectId) " +
            "and ROWNUM<=1")
    PersonEntity findByPhoneNumber(@Param("phoneNumber") String phoneNumber,
                                   @Param("subjectId") String subjectId);
}
