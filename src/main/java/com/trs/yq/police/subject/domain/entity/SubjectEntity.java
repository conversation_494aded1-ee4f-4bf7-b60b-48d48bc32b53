package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 专题相关配置数据表，包含列表检索条件配置等。
 *
 * <AUTHOR>
 * @date 2021/07/27
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_SUBJECT")
public class SubjectEntity {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 专题名称
     */
    private String name;

    /**
     * 人员列表检索配置
     */
    private String personListFilters;

    /**
     * 批量导出人员时属性
     */
    private String personListProperty;

    /**
     * 群体列表检索配置
     */
    private String groupListFilters;

    /**
     * 批量导出群体时属性
     */
    private String groupListProperty;

    /**
     * 线索列表检索配置
     */
    private String clueListFilters;

    /**
     * 批量导出线索时属性
     */
    private String clueListProperty;

    /**
     * 预警列表检索配置
     */
    private String warningListFilters;

    /**
     * 批量导出预警时属性
     */
    private String warningListProperty;
    /**
     * 事件列表检索配置
     */
    private String eventListFilters;

    /**
     * 批量导出事件时属性
     */
    private String eventListProperty;

    /**
     * 批量导出境外人员字段
     */
    private String crjJwryListProperty;

    /**
     * 批量导出境外人员字段
     */
    private String crjSfryListProperty;

    /**
     * 常住人员导出
     */
    private String crjPermanentListProperty;
}
