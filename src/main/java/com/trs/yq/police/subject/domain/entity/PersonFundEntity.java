package com.trs.yq.police.subject.domain.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资金分析
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "T_PS_PERSON_FUND")
public class PersonFundEntity extends BaseEntity {

    private static final long serialVersionUID = -2448631038467263115L;

    private String personId;

    private String topologyGraph;

    private String attachments;

    private String remark;
}
