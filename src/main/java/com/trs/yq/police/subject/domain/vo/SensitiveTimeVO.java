package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021/12/28 14:11
 */
@Data
public class SensitiveTimeVO implements Serializable {
    private static final long serialVersionUID = -6940031691364871596L;
    private String nodeId;
    /**
     * 敏感节点名称
     */
    @NotBlank(message = "节点名称缺失！")
    private String name;
    /**
     * 敏感节点备注
     */
    private String remark;
    /**
     * 开始时间
     */
    @NotNull(message = "起始时间缺失！！")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间缺失！！")
    private LocalDate endTime;
    /**
     * 有效范围 0 默认有效 1 选择有效
     */
    private Integer nodeType;

    /**
     * 0 每年 1 固定时间
     */
    private Integer prescription;
}
