package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.EventLabelRelationEntity;
import org.springframework.stereotype.Repository;

/**
 * 线索类别关系接口
 *
 * <AUTHOR>
 * @date 2021/09/06
 */
@Repository
public interface EventLabelRelationRepository extends BaseRepository<EventLabelRelationEntity, String> {

    /**
     * 根据事件id删除所有类别关联
     *
     * @param eventId 事件id
     */
    void deleteAllByEventId(String eventId);
}
