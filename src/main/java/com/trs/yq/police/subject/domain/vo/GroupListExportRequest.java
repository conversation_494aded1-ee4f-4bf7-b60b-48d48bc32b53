package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 群体批量导出请求
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class GroupListExportRequest {

    /**
     * 导出的field
     */
    @NotEmpty(message = "fieldNames不能为空！")
    private List<String> fieldNames;

    /**
     * 导出的群体
     */
    @NotEmpty(message = "groupIds不能为空！")
    private List<String> groupIds;
}
