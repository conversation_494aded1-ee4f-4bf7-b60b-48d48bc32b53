package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.FootholdEntity;
import com.trs.yq.police.subject.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * 落脚点信息视图层
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class FootholdVO implements Serializable {

    private static final long serialVersionUID = -3169817506713941727L;

    /**
     * 落脚点信息id
     */
    private String id;
    /**
     * 开始居住时间戳
     */
    @NotNull(message = "开始居住时间不能为空！")
    private Long startTime;
    /**
     * 停止居住时间戳
     */
    private Long endTime;
    /**
     * 居住地址
     */
    @NotBlank(message = "居住地址不能为空！")
    private String address;
    /**
     * 居住状态 1=在住 0=未住
     */
    @NotBlank(message = "居住状态不能为空！")
    private String livingStatus;
    /**
     * 备注
     */
    private String note;

    /**
     * 落脚点根据entity生成vo
     *
     * @param entity entity
     * @return vo
     */
    public static FootholdVO of(FootholdEntity entity) {
        FootholdVO vo = new FootholdVO();
        vo.setId(entity.getId());
        vo.setStartTime(DateUtil.dateToUtc(entity.getStartTime()));
        if(Objects.nonNull(entity.getEndTime())) {
            vo.setEndTime(DateUtil.dateToUtc(entity.getEndTime()));
        }
        vo.setAddress(entity.getAddress());
        vo.setLivingStatus(entity.getState());
        vo.setNote(entity.getNote());
        return vo;
    }
}
