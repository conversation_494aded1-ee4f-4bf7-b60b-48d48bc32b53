package com.trs.yq.police.subject.jpa.listener;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.BaseEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.BeanUtil;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Objects;

/**
 * jpa审计自动填充数据信息补充
 *
 * <AUTHOR>
 */
public class CustomJpaAuditListener {

    /**
     * 新增之前
     *
     * @param entity 基础实体
     */
    @PrePersist
    private void postPersist(BaseEntity entity) {
        final LoginUser loginUser = AuthHelper.getCurrentUser();
        if (Objects.nonNull(loginUser)) {
            UnitRepository repository = BeanUtil.getBean(UnitRepository.class);
            UnitEntity unit = repository.findByUnitCode(loginUser.getUnitCode());
            if (Objects.nonNull(unit)) {
                entity.setCrDept(unit.getUnitName());
            } else {
                entity.setCrDept(loginUser.getUnitName());
            }
            entity.setCrDeptCode(loginUser.getUnitCode());
            entity.setCrByName(loginUser.getRealName());
            entity.setUpByName(loginUser.getRealName());
        }
    }

    /**
     * 更新之前
     *
     * @param entity 基础实体
     */
    @PreUpdate
    private void preUpdate(BaseEntity entity) {
        final LoginUser loginUser = AuthHelper.getCurrentUser();
        if (Objects.nonNull(loginUser)) {
            entity.setUpByName(loginUser.getRealName());
        }
    }
}
