package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/09/09
 */
@Data
public class ClueExportListVO implements Serializable {
    private static final long serialVersionUID = 7362197408680668560L;

    @ExcelProperty(value = "线索名称", order = 0)
    private String clueName;

    @ExcelProperty(value = "线索来源", order = 1)
    private String clueSource;

    @ExcelProperty(value = "紧急程度", order = 2)
    private String emergencyLevel;

    @ExcelProperty(value = "录入单位", order = 3)
    private String createDeptName;

    @ExcelProperty(value = "录入时间", order = 4)
    private String createTime;

    @ExcelProperty(value = "更新时间", order = 5)
    private String updateTime;

    @ExcelProperty(value = "线索级别", order = 6)
    private String clueType;

    @ExcelProperty(value = "维权方式", order = 7)
    private String method;

    @ExcelProperty(value = "维权行为", order = 8)
    private String behaviour;

    @ExcelProperty(value = "维权时间", order = 9)
    private String occurrenceTime;

    @ExcelProperty(value = "创建单位", order = 10)
    private String createDept;
}
