package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

/**
 * 出入境-常住人员-列表vo
 *
 * <AUTHOR>
 */
@Data
public class CrjResidenceListVO {

    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 国家地区
     */
    private String country;
    /**
     * 入境时间
     */
    private String entryTime;
    /**
     * 居住状态
     */
    private String livingStatus;
    /**
     * 居留许可签发日期
     */
    private String visaSignDate;
    /**
     * 居留许可有效期
     */
    private String visaValidity;
    /**
     * 工作地址
     */
    private String workAddress;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 管辖部门
     */
    private String dept;
}
