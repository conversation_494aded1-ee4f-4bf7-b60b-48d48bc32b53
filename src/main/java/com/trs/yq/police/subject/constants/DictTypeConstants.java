package com.trs.yq.police.subject.constants;

/**
 * 字典类型常量
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 9:43
 */
public class DictTypeConstants {

    private DictTypeConstants() {
    }

    /**
     * 管控状态
     */
    public static final String DICT_TYPE_CONTROL_STATUS = "ps_control_status";

    /**
     * 布控状态
     */
    public static final String DICT_TYPE_MONITOR_STATUS = "ps_monitor_status";

    /**
     * 管控级别(政保)
     */
    public static final String DICT_TYPE_CONTROL_LEVEL = "ps_zb_control_level";

    /**
     * 分配状态
     */
    public static final String DICT_TYPE_DISTRIBUTE_STATUS = "ps_distribute_status";

    /**
     * 指派状态
     */
    public static final String DICT_TYPE_ASSIGN_STATUS = "ps_assign_status";

    /**
     * 线索紧急程度
     */
    public static final String DICT_TYPE_CLUE_EMERGENCY_LEVEL = "ps_clue_emergency_level";
    /**
     * 线索公开程度
     */
    public static final String DICT_TYPE_CLUE_OPENNESS_LEVEL = "ps_clue_openness_level";
    /**
     * 线索来源
     */
    public static final String DICT_TYPE_CLUE_SOURCE = "ps_clue_source";

    /**
     * 线索来源
     */
    public static final String DICT_TYPE_WW_CLUE_SOURCE = "ps_ww_clue_source";

    /**
     * 活跃度
     */
    public static final String DICT_TYPE_ACTIVITY_LEVEL = "ps_activity_level";

    /**
     * 预警等级
     */
    public static final String DICT_TYPE_WARNING_LEVEL = "ps_warning_level";

    /**
     * 预警状态
     */
    public static final String DICT_TYPE_WARNING_STATUS = "ps_warning_status";

    /**
     * 线索来源
     */
    public static final String DICT_TYPE_EVENT_SOURCE = "ps_event_data_source";

    /**
     * 线索紧急程度
     */
    public static final String DICT_TYPE_EVENT_EMERGENCY_LEVEL = "risk_level";

    /**
     * 事件类别
     */
    public static final String DICT_TYPE_EVENT_TYPE = "ps_event_type";

    /**
     * 事件处置状态
     */
    public static final String DICT_TYPE_EVENT_DISPOSAL_STATUS = "ps_event_disposal_status";

    /**
     * 事件人员来源
     */
    public static final String DICT_TYPE_EVENT_PERSON_SOURCE = "ps_event_person_source";

    /**
     * 人员是否在控
     */
    public static final String DICT_TYPE_PERSON_IN_CONTROL = "ps_person_in_control";

}
