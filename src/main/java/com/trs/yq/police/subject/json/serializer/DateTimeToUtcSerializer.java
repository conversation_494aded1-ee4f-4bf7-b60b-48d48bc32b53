package com.trs.yq.police.subject.json.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import com.trs.yq.police.subject.utils.DateUtil;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.Temporal;

/**
 * jackson时间转时间戳反序列化器
 * <p>
 *
 * <AUTHOR>
 * @date 2020/7/8
 **/
public class DateTimeToUtcSerializer extends StdSerializer<Temporal> {

    private static final long serialVersionUID = -8913438175820400329L;

    /**
     * 构造器
     *
     * @param t Temporal对象
     */
    public DateTimeToUtcSerializer(Class<Temporal> t) {
        super(t);
    }

    /**
     * 默认构造器
     */
    public DateTimeToUtcSerializer() {
        this(null);
    }

    @Override
    public void serialize(Temporal temporal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (temporal instanceof LocalDateTime) {
            LocalDateTime dateTime = (LocalDateTime) temporal;
            jsonGenerator.writeNumber(DateUtil.dateTimeToUtc(dateTime));
        } else if (temporal instanceof LocalDate) {
            LocalDate date = (LocalDate) temporal;
            jsonGenerator.writeNumber(DateUtil.dateToUtc(date));
        } else {
            jsonGenerator.writeNull();
        }
    }
}
