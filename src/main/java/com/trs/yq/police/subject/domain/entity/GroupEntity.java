package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/07/28
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_GROUP")
public class GroupEntity extends BaseEntity {

    private static final long serialVersionUID = -5210756689481787711L;
    /**
     * 群体名称
     */
    private String name;
    /**
     * 专题id
     */
    private String subjectId;
    /**
     * 基本信息
     */
    private String basicInfo;
}
