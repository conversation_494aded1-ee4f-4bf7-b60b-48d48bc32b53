package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合成作战日志表
 *
 * <AUTHOR>
 * @since 2021/9/15
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_BATTLE_EVENTLOG")
public class BattleEventLogEntity implements Serializable {

    private static final long serialVersionUID = -3190772136122689683L;

    /**
     * 主键
     */
    @Id
    private String id;
    /**
     * 警情id
     */
    private String eventid;
    /**
     * 资源表名
     */
    private String srctable;
    /**
     * 资源中文名
     */
    private String srcname;
    /**
     * 标识字段值
     */
    private String keyval;
    /**
     * 研判数据表名
     */
    private String tablename;
    /**
     * 研判数据id
     */
    private String itemid;
    /**
     * 研判数据标题
     */
    private String itemtitle;
    /**
     * 研判数据详情
     */
    private String itemdetail;
    /**
     * 研判单位代码
     */
    private String unitcode;
    /**
     * 研判单位名称
     */
    private String unitname;
    /**
     * 研判人
     */
    private String crtby;
    /**
     * 研判人姓名
     */
    private String crtbyname;
    /**
     * 研判时间
     */
    private LocalDateTime crttime;
    /**
     * 业务名称
     */
    private String bizname;
    /**
     * 消息提示内容
     */
    private String contents;
    /**
     * 是否流转信息
     */
    private String isflow;
}
