package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrjDiseaseAreaCountVO implements Serializable {
    private static final long serialVersionUID = 6249633184082207901L;
    /**
     * 区县编码
     */
    private String areaCode;
    /**
     * 区县名称
     */
    private String areaName;
    /**
     * 统计人数
     */
    private Long personCount;
}
