package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 预警推送表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/08
 **/
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_PUSH_CONFIG")
public class WarningPushConfigEntity implements Serializable {

    private static final long serialVersionUID = -1295176854088596141L;


    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 专题id
     */
    private String warningType;

    /**
     * 区域代码分类
     */
    private String areaCode;

    /**
     * 绑定的角色id，拥有该角色的所有user都将被推送
     */
    private String roleId;

    /**
     * 是否发短信
     */
    private Integer smsFlag;

    /**
     * 是否弹窗
     */
    private Integer popFlag;
}
