package com.trs.yq.police.subject.conf;

import com.trs.yq.police.subject.common.ResponseMessage;
import com.trs.yq.police.subject.common.SkipResponseBodyAdvice;
import org.springframework.core.MethodParameter;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Arrays;

/**
 * 统一包装返回值
 *
 * <AUTHOR>
 * @since 2020/11/27 14:34
 */
@ControllerAdvice
public class CustomResponseBodyConvert implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter returnType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        // 已经封装过了就不需要再封装一次
        return Arrays.stream(returnType.getMethodAnnotations())
                .noneMatch(s -> s.annotationType().isAssignableFrom(SkipResponseBodyAdvice.class))
                && !returnType.getParameterType().equals(ResponseMessage.class);
    }

    @Override
    public Object beforeBodyWrite(
            Object o,
            @NonNull MethodParameter returnType,
            @NonNull MediaType selectedContentType,
            @NonNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
            @NonNull ServerHttpRequest request,
            @NonNull ServerHttpResponse response) {
        int status = ((ServletServerHttpResponse) response).getServletResponse().getStatus();
        if (status == HttpStatus.UNAUTHORIZED.value()) {
            return ResponseMessage.unauthorized(o);
        } else if (o instanceof ResponseMessage) {
            return o;
        } else {
            return ResponseMessage.okWithStatus(status, o);
        }
    }
}
