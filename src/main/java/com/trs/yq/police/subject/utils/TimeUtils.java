package com.trs.yq.police.subject.utils;

import com.mysql.cj.util.StringUtils;

import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @date 创建时间：2024/6/19 19:44
 * @version 1.0
 * @since 1.0
 */
public class TimeUtils {
    public static final String DEFAULT_YYYYMMDD_HHMMSS = "yyyy/MM/dd HH:mm:ss";
    public static final String DEFAULT_YYYYMMDD_HHMMSS_SSS = "yyyy/MM/dd HH:mm:ss.SSS";
    public static final String YYYYMMDD_HHMMSS = "yyyy-MM-dd HH:mm:ss";
    public static final String HHMMSS = "HH:mm:ss";
    public static final String YYYYMMDD_HHMMSS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
    public static final String HHMMSS_SSS = "HH:mm:ss.SSS";
    public static final String YYYYMMDD_HHMMSS2 = "yyyyMMddHHmmss";
    public static final String HHMMSS2 = "HHmmss";
    public static final String YYYYMMDD_HHMMSS_SSS2 = "yyyyMMddHHmmssSSS";
    public static final String YYYYMMDD_HHMMSS3 = "yyyy年MM月dd日HH时mm分ss秒";
    public static final String YYYYMMDD_HHMMSS_SSS3 = "yyyy年MM月dd日HH时mm分ss秒SSS毫秒";
    public static final String YYYYMMDD_HHMMSS4 = "yyyy.MM.dd HH:mm:ss";
    public static final String YYYYMMDD_HHMMSS_SSS4 = "yyyy.MM.dd HH:mm:ss.SSS";
    public static final String YYYYMMDD_HH = "yyyy-MM-dd HH";
    public static final String YYYYMMDD_HH1 = "yyyyMMddHH";
    public static final String YYYYMMDD_HH2 = "yyyy/MM/dd HH";
    public static final String YYYYMMDD_HH3 = "yyyy.MM.dd HH";
    public static final String YYYYMMDD = "yyyy-MM-dd";
    public static final String YYYYMMDD2 = "yyyy年MM月dd日";
    public static final String YYYYMMDD3 = "yyyy/MM/dd";
    public static final String YYYYMMDD4 = "yyyy/MM";
    public static final String YYYYMMDD5 = "yyyyMMdd";
    public static final String YYYYMMDD6 = "yyyy.MM.dd";
    public static final String DAY = "day";
    public static final String HOUR = "hour";
    public static final String MINUTE = "minute";
    public static final String SECOND = "second";
    public static final String MILLIS = "millis";
    private static final SimpleDateFormat SINA_SDF;
    public static final String[] WEEK_DAYS;
    public static String[] DEFAULT_PATTERNS;
    public static String[] TIME_PATTERNS;
    public static String[] DATE_PATTERNS;

    /**
     * 将字符串转换成日期
     *
     * @param inputDate 参数
     * @return 结果
     */
    public static String stringToString(String inputDate) {
        return dateToString(stringToDate(inputDate), "yyyy/MM/dd HH:mm:ss");
    }

    /**
     * 将字符串转换成日期
     *
     * @param inputDate 参数
     * @param outputRegex 参数
     * @return 结果
     */
    public static String stringToString(String inputDate, String outputRegex) {
        return dateToString(stringToDate(inputDate), outputRegex);
    }

    /**
     * 将字符串转换成日期
     *
     * @param inputRegex 参数
     * @param inputDate 参数
     * @param outputRegex 参数
     * @return 结果
     */
    public static String stringToString(String inputRegex, String inputDate, String outputRegex) {
        return dateToString(stringToDate(inputDate, inputRegex), outputRegex);
    }

    /**
     * 将字符串转换成日期
     *
     * @param inputDate 参数
     * @return 结果
     */
    public static Date stringToDate(String inputDate) {
        return stringToDate(inputDate, (String) null);
    }

    /**
     * 将字符串转换成日期
     *
     * @param dateString 参数
     * @param dateFormat 参数
     * @return 结果
     */
    public static Date stringToDate(String dateString, String dateFormat) {
        return StringUtils.isNullOrEmpty(dateString) ? null : parseDate(dateString, dateFormat);
    }

    /**
     * 将日期转换成字符串
     *
     * @param date 参数
     * @return 结果
     */
    public static String dateToString(Date date) {
        return dateToString(date, "yyyy/MM/dd HH:mm:ss");
    }

    /**
     * 将日期转换成字符串
     *
     * @param date 参数
     * @param timeRegex 参数
     * @return 结果
     */
    public static String dateToString(Date date, String timeRegex) {
        if (date == null) {
            return null;
        }
        timeRegex = StringUtils.isNullOrEmpty(timeRegex) ? "yyyy/MM/dd HH:mm:ss" : timeRegex;
        return (new SimpleDateFormat(timeRegex)).format(date);
    }

    /**
     * 将long转换成字符串
     *
     * @param dateLong 参数
     * @return 结果
     */
    public static String longToString(long dateLong) {
        return longToString(dateLong, (String) null);
    }

    /**
     * 将long转换成字符串
     *
     * @param dateLong 参数
     * @param dateFormat 参数
     * @return 结果
     */
    public static String longToString(long dateLong, String dateFormat) {
        return dateToString(new Date(dateLong), dateFormat);
    }

    /**
     * 获取当前时间
     *
     * @param dateFormat 参数
     * @return 结果
     */
    public static String getCurrentDate(String dateFormat) {
        return dateToString(new Date(), dateFormat);
    }

    /**
     * 获取前后数天的日期
     *
     * @param day 参数
     * @return 结果
     */
    public static String dateBefOrAft(int day) {
        return dateBefOrAft(day, "yyyy/MM/dd HH:mm:ss");
    }

    /**
     * 获取前后数天的日期
     *
     * @param day 数天
     * @param returnRegex 参数
     * @return 结果
     */
    public static String dateBefOrAft(int day, String returnRegex) {
        return dateBefOrAft(getCurrentDate(returnRegex), day, returnRegex);
    }

    /**
     * 获取前后数天的日期
     *
     * @param date 参数
     * @param day 参数
     * @param returnRegex 参数
     * @return 结果
     */
    public static String dateBefOrAft(String date, int day, String returnRegex) {
        return dateBefOrAft(date, day, (String) null, returnRegex);
    }

    /**
     * stringToDate
     *
     * @param date 参数
     * @param day 参数
     * @param timeRegex 参数
     * @param returnRegex 参数
     * @return 结果
     */
    public static String dateBefOrAft(String date, int day, String timeRegex, String returnRegex) {
        returnRegex = StringUtils.isNullOrEmpty(returnRegex) ? timeRegex : returnRegex;
        return dateBefOrAft(stringToDate(date, timeRegex), day, returnRegex);
    }

    /**
     * dateBefOrAft
     *
     * @param date 参数
     * @param day 参数
     * @param returnRegex 参数
     * @return 结果
     */
    public static String dateBefOrAft(Date date, int day, String returnRegex) {
        return dateToString(befOrAft(date, day, 5), returnRegex);
    }

    /**
     * hourDefOrAft
     *
     * @param hour 参数
     * @param timeRegex 参数
     * @return 结果
     */
    public static String hourDefOrAft(int hour, String timeRegex) {
        return hourDefOrAft(new Date(), hour, timeRegex);
    }

    /**
     * hourDefOrAft
     *
     * @param date 参数
     * @param hour 参数
     * @param timeRegex 参数
     * @return 结果
     */
    public static String hourDefOrAft(String date, int hour, String timeRegex) {
        return hourDefOrAft(date, hour, (String) null, timeRegex);
    }

    /**
     * hourDefOrAft
     *
     * @param date 参数
     * @param hour 参数
     * @param timeRegex 参数
     * @param returnRegex 参数
     * @return 结果
     */
    public static String hourDefOrAft(String date, int hour, String timeRegex, String returnRegex) {
        timeRegex = StringUtils.isNullOrEmpty(timeRegex) ? returnRegex : timeRegex;
        return hourDefOrAft(stringToDate(date, timeRegex), hour, returnRegex);
    }

    /**
     * hourDefOrAft
     *
     * @param date 参数
     * @param hour 参数
     * @param timeRegex 参数
     * @return 结果
     */
    public static String hourDefOrAft(Date date, int hour, String timeRegex) {
        return dateToString(befOrAft(date, hour, 11), timeRegex);
    }

    /**
     * befOrAft
     *
     * @param specifiedDate 参数
     * @param num 参数
     * @param field 参数
     * @return 结果
     */
    public static Date befOrAft(Date specifiedDate, int num, int field) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(specifiedDate);
        cal.add(field, num);
        return cal.getTime();
    }

    /**
     * parseDate
     *
     * @param inputDate 参数
     * @param patterns 参数
     * @return 结果
     */
    private static Date parseDate(String inputDate, String patterns) {
        SimpleDateFormat df = new SimpleDateFormat();
        String[] localDateFormats = patterns != null ? new String[]{patterns} : DEFAULT_PATTERNS;
        String[] var4 = localDateFormats;
        int var5 = localDateFormats.length;

        for (int var6 = 0; var6 < var5; ++var6) {
            String pattern = var4[var6];
            df.applyPattern(pattern);
            df.setLenient(false);
            ParsePosition pos = new ParsePosition(0);
            Date date = df.parse(inputDate, pos);
            if (pos.getIndex() == inputDate.length()) {
                return date;
            }
        }

        SINA_SDF.setLenient(false);
        ParsePosition pos = new ParsePosition(0);
        Date date = SINA_SDF.parse(inputDate, pos);
        if (pos.getIndex() == inputDate.length()) {
            return date;
        } else {
            if (patterns == null
                    || patterns.isEmpty()) {
                try {
                    return new Date(Long.valueOf(inputDate));
                } catch (NumberFormatException var10) {
                }
            }

            String msg;
            if (patterns != null
                    && patterns.length() > 0) {
                msg = String.format("%s的时间格式与传入的%s格式不匹配", inputDate, patterns);
            } else {
                msg = String.format("%s的时间格式与内置格式不匹配", inputDate);
            }

            throw new IllegalArgumentException(msg);
        }
    }

    static {
        SINA_SDF = new SimpleDateFormat("EEE MMM d HH:mm:ss Z yyyy", Locale.US);
        WEEK_DAYS = new String[]{"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        DEFAULT_PATTERNS = new String[]{"yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss.SSS", "yyyy-MM-dd HH:mm:ss.SSS", "yyyyMMddHHmmssSSS", "yyyy年MM月dd日HH时mm分ss秒SSS毫秒", "yyyy-MM-dd HH:mm:ss", "HH:mm:ss", "HHmmss", "HH:mm:ss.SSS", "yyyyMMddHHmmss", "yyyy年MM月dd日HH时mm分ss秒", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm:ss.SSS", "yyyy/MM/dd", "yyyy-MM-dd", "yyyyMMddHH", "yyyy/MM/dd HH", "yyyy.MM.dd HH", "yyyy-MM-dd HH", "yyyy年MM月dd日", "yyyy/MM", "yyyyMMdd", "yyyy.MM.dd"};
        TIME_PATTERNS = new String[]{"yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm:ss.SSS", "yyyy-MM-dd HH:mm:ss.SSS", "yyyyMMddHHmmssSSS", "yyyy年MM月dd日HH时mm分ss秒SSS毫秒", "yyyy-MM-dd HH:mm:ss", "HH:mm:ss", "HHmmss", "HH:mm:ss.SSS", "yyyyMMddHHmmss", "yyyy年MM月dd日HH时mm分ss秒", "yyyyMMddHH", "yyyy/MM/dd HH", "yyyy.MM.dd HH", "yyyy-MM-dd HH", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm:ss.SSS"};
        DATE_PATTERNS = new String[]{"yyyy-MM-dd", "yyyy年MM月dd日", "yyyy/MM/dd", "yyyy/MM", "yyyyMMdd", "yyyy.MM.dd"};
    }
}
