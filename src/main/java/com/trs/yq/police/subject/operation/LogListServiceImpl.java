package com.trs.yq.police.subject.operation;

import com.google.common.collect.ImmutableMap;
import com.trs.yq.police.subject.constants.BattleConstants;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.OperationTypeEnum;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.dto.OperationLogQueryVO;
import com.trs.yq.police.subject.domain.entity.ModuleEntity;
import com.trs.yq.police.subject.domain.entity.OperationLogEntity;
import com.trs.yq.police.subject.domain.entity.UserEntity;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.FileInfoVO;
import com.trs.yq.police.subject.domain.vo.OperationLogVo;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.WarningLogRecordVO;
import com.trs.yq.police.subject.domain.vo.WarningLogsVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 操作记录列表查询接口实现
 *
 * <AUTHOR>
 * @since 2021/10/22
 */
@Service
@Slf4j
public class LogListServiceImpl implements LogListService {

    @Resource
    private WarningRepository warningRepository;
    @Resource
    private BattleRepository battleRepository;
    @Resource
    private ModuleRepository moduleRepository;
    @Resource
    private OperationLogRepository operationLogRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;

    @Override
    public PageResult<OperationLogVo> getOperationLogList(String queryKey, OperationLogQueryVO query, Pageable pageable) {

        final Page<OperationLogEntity> page = operationLogRepository.findPage(
                queryKey,
                generateOperateModule(query.getOperateModule(), query.getSubjectId(), query.getTargetObjectType()),
                query.getTargetObjectType(),
                pageable);
        final List<OperationLogVo> operationLogVos = page.getContent()
                .stream()
                .map(OperationLogVo::new)
                .collect(Collectors.toList());
        return PageResult.of(new PageImpl<>(operationLogVos, page.getPageable(), page.getTotalElements()));
    }

    private List<OperateModule> generateOperateModule(String operateModule, String subjectId, Integer type) {

        final String operateModuleStr = StringUtils.trimToNull(operateModule);
        String typeEnum = TargetObjectTypeEnum.getTypeByCode(type);
        if (Objects.isNull(operateModuleStr)) {
            List<ModuleEntity> list = moduleRepository.findAllBySubjectIdAndType(subjectId, typeEnum);
            return list
                    .stream()
                    .filter(Objects::nonNull)
                    .map(module -> OperateModule.codeOf(module.getId()))
                    .collect(Collectors.toList());
        } else {
            return Collections.singletonList(OperateModule.codeOf(operateModule));
        }
    }

    @Override
    public WarningLogsVO getWarningOperationLogList(String warningId, String order) {
        WarningLogsVO warningLogsVO = new WarningLogsVO();
        WarningEntity warning = warningRepository.getById(warningId);
        try {
            String warningLevel = warning.getWarningLevel();
            warningLogsVO.setWarningLevel(warningLevel);
        } catch (Exception e) {
            throw new ParamValidationException("预警信息不存在，请核实！");
        }
        //签收、研判
        List<OperationLogEntity> operationLogEntityList = operationLogRepository.findAllByTargetObjectIdAndTargetObjectType(warningId, TargetObjectTypeEnum.WARNING.getCode());
        List<WarningLogRecordVO> records = operationLogEntityList.stream()
                .map(log->{
                    final WarningLogRecordVO vo = new WarningLogRecordVO(log);
                    if(Operator.JUDGE.equals(log.getOperator())&&StringUtils.isNotBlank(warning.getJudgeFileIds())){
                        final List<FileInfoVO> files = fileStorageRepository.findAllByFileIds(Arrays.asList(warning.getJudgeFileIds().split(",")))
                            .stream()
                            .map(FileInfoVO::of)
                            .collect(Collectors.toList());
                        vo.setFiles(files);
                    }
                    return vo;
                })
                .collect(Collectors.toList());
        //合成
        List<WarningLogRecordVO> composite = battleRepository.getRecord(warningId, BattleConstants.SRC_TABLE_WARNING).stream().map(result -> generateResult(OperationTypeEnum.RECORD, result)).collect(Collectors.toList());
        log.info("records:" + composite);
        records.addAll(composite);

        //指令
        List<WarningLogRecordVO> commands = battleRepository.getCommand(warningId, BattleConstants.SRC_TABLE_WARNING).stream().map(result -> generateResult(OperationTypeEnum.COMMAND, result)).collect(Collectors.toList());
        log.info("commands:" + commands);
        records.addAll(commands);

        //协作
        List<WarningLogRecordVO> demands = battleRepository.getDemand(warningId, BattleConstants.SRC_TABLE_WARNING).stream().map(result -> generateResult(OperationTypeEnum.DEMAND, result)).collect(Collectors.toList());
        log.info("demands:" + demands);
        records.addAll(demands);

        //时间顺序
        if (Sort.Direction.fromString(order).isAscending()) {
            records.sort(Comparator.comparing(WarningLogRecordVO::getLogTime));
        } else if (Sort.Direction.fromString(order).isDescending()) {
            records.sort(Comparator.comparing(WarningLogRecordVO::getLogTime).reversed());
        }
        warningLogsVO.setLogs(records);
        return warningLogsVO;
    }

    @Override
    public PageResult<OperationLogVo> getStabilityOperationLogList(String queryKey, OperationLogQueryVO query, PageParams pageParams) {
        String module = query.getOperateModule();
        List<OperationLogVo> vos;
        if (Objects.isNull(module)) {
            //查全部
            vos = operationLogRepository.findAll(
                    queryKey,
                    generateOperateModule(query.getOperateModule(), query.getSubjectId(), query.getTargetObjectType()),
                    query.getTargetObjectType())
                    .stream().map(OperationLogVo::new).collect(Collectors.toList());
            List<OperationLogVo> clueRecords   = battleRepository.getRecord(queryKey, BattleConstants.SRC_TABLE_CLUE).stream().map(result -> generateVo(BattleConstants.MODULE_CLUE_RECORD, result)).collect(Collectors.toList());
            vos.addAll(clueRecords);
            List<OperationLogVo> eventRecords  = battleRepository.getRecord(queryKey, BattleConstants.SRC_TABLE_EVENT).stream().map(result -> generateVo(BattleConstants.MODULE_EVENT_RECORD, result)).collect(Collectors.toList());
            vos.addAll(eventRecords);
            List<OperationLogVo> clueCommands  = battleRepository.getCommand(queryKey, BattleConstants.SRC_TABLE_CLUE).stream().map(result -> generateVo(BattleConstants.MODULE_CLUE_COMMAND, result)).collect(Collectors.toList());
            vos.addAll(clueCommands);
            List<OperationLogVo> eventCommands = battleRepository.getCommand(queryKey, BattleConstants.SRC_TABLE_EVENT).stream().map(result -> generateVo(BattleConstants.MODULE_EVENT_COMMAND, result)).collect(Collectors.toList());
            vos.addAll(eventCommands);
        } else {
            switch (module) {
                case BattleConstants.MODULE_CLUE_RECORD:
                    vos = battleRepository.getRecord(queryKey, BattleConstants.SRC_TABLE_CLUE).stream().map(result -> generateVo(BattleConstants.MODULE_CLUE_RECORD, result)).collect(Collectors.toList());
                    break;
                case BattleConstants.MODULE_EVENT_RECORD:
                    vos = battleRepository.getRecord(queryKey, BattleConstants.SRC_TABLE_EVENT).stream().map(result -> generateVo(BattleConstants.MODULE_EVENT_RECORD, result)).collect(Collectors.toList());
                    break;
                case BattleConstants.MODULE_CLUE_COMMAND:
                    vos = battleRepository.getCommand(queryKey, BattleConstants.SRC_TABLE_CLUE).stream().map(result -> generateVo(BattleConstants.MODULE_CLUE_COMMAND, result)).collect(Collectors.toList());
                    break;
                case BattleConstants.MODULE_EVENT_COMMAND:
                    vos = battleRepository.getCommand(queryKey, BattleConstants.SRC_TABLE_EVENT).stream().map(result -> generateVo(BattleConstants.MODULE_EVENT_COMMAND, result)).collect(Collectors.toList());
                    break;
                default:
                    vos = operationLogRepository.findAll(
                            queryKey,
                            generateOperateModule(query.getOperateModule(), query.getSubjectId(), query.getTargetObjectType()),
                            query.getTargetObjectType())
                            .stream().map(OperationLogVo::new).collect(Collectors.toList());
            }
        }
        vos.sort(Comparator.comparing(OperationLogVo::getOperateTime).reversed());
        List<OperationLogVo> result = vos.stream().skip(pageParams.getOffset())
                .limit(pageParams.getPageSize())
                .collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), vos.size(), pageParams.getPageSize());
    }

    /**
     * 处理查询结果转换为预警操作记录
     *
     * @param type 操作类型 {@link OperationTypeEnum}
     * @param record 查询结果
     * @return 预警操作记录 {@link WarningLogRecordVO}
     */
    private WarningLogRecordVO generateResult(OperationTypeEnum type, Map<String, Object> record) {
        WarningLogRecordVO vo = new WarningLogRecordVO();
        switch (type) {
            case RECORD:
                vo.setLogMessage("发起合成");break;
            case COMMAND:
                vo.setLogMessage("发布指令");break;
            case DEMAND:
                vo.setLogMessage("发起协作");break;
            default:
        }
        vo.setCreator(record.get("name").toString());
        vo.setCreateDept(record.get("unitname").toString());
        vo.setId(record.get("bh").toString());
        vo.setType(type.getCode());
        vo.setTitle(record.get("title").toString());
        final Timestamp time = (Timestamp) record.get("time");
        vo.setLogTime(DateUtil.utcToLocalDateTime(time.getTime()));
        return vo;
    }

    /**
     * 处理查询结果转换为维稳操作记录
     *
     * @param moduleCode 模块代码
     * @param record 查询结果
     * @return 预警操作记录 {@link OperationLogVo}
     */
    private OperationLogVo generateVo(String moduleCode, Map<String, Object> record) {
        OperationLogVo vo = new OperationLogVo();
        vo.setRealName(record.get("name").toString());
        vo.setUsername(userRepository.findById(record.get("username").toString()).orElse(new UserEntity()).getUsername());
        vo.setOperateModule(moduleRepository.findById(moduleCode).orElse(new ModuleEntity()).getCnName());
        switch (moduleCode) {
            case BattleConstants.MODULE_CLUE_COMMAND:
            case BattleConstants.MODULE_EVENT_COMMAND:
                vo.setOperator(Operator.COMMAND.getName());
                vo.setOverview("发布指令");
                break;
            case BattleConstants.MODULE_CLUE_RECORD:
            case BattleConstants.MODULE_EVENT_RECORD:
                vo.setOperator(Operator.RECORD.getName());
                vo.setOverview("发起合成");
                break;
            default:
        }
        final Timestamp time = (Timestamp) record.get("time");
        vo.setOperateTime(DateUtil.utcToLocalDateTime(time.getTime()));
        Map<String, String> map = ImmutableMap.of("标题",record.get("title").toString());
        vo.setDetail(JsonUtil.toJsonString(map));
        return vo;
    }
}
