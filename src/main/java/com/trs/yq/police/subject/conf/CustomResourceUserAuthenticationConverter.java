package com.trs.yq.police.subject.conf;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.token.DefaultUserAuthenticationConverter;

import java.util.Map;

/**
 * hqc 解析授权服务传递的自定义信息
 *
 * <AUTHOR>
 */
public class CustomResourceUserAuthenticationConverter extends DefaultUserAuthenticationConverter {

    @Override
    public Authentication extractAuthentication(Map<String, ?> map) {
        UsernamePasswordAuthenticationToken authentication = (UsernamePasswordAuthenticationToken) super.extractAuthentication(map);
        final String userDetail = "userDetail";
        if (map.containsKey(userDetail)) {
            authentication.setDetails(map.get("userDetail"));
        }
        return authentication;
    }
}
