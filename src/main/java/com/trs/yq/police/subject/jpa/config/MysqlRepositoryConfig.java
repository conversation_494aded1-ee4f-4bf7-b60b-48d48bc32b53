package com.trs.yq.police.subject.jpa.config;


import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 19:05
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef = "mysqlEntityManagerFactory",
    transactionManagerRef = "mysqlTransactionManager",
    basePackages = {"com.trs.yq.police.subject.mysqlDatasource.repository"})
public class MysqlRepositoryConfig {

    @Autowired
    @Qualifier("mysqlDatasource")
    private DataSource userDataSource;

    @Autowired
    private JpaProperties jpaProperties;

    @Autowired
    private HibernateProperties hibernateProperties;

    @Value("${spring.jpa.properties.hibernate.mysql-dialect}")
    private String dialect;

    /**
     * @param builder builder
     * @return {@link LocalContainerEntityManagerFactoryBean}
     */
    @Bean(name = "mysqlEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryUser(
        EntityManagerFactoryBuilder builder) {
        HashMap<String, String> map = new HashMap<>();
        map.put("hibernate.dialect", dialect);
        jpaProperties.setProperties(map);
        Map<String, Object> properties = hibernateProperties.determineHibernateProperties(
            jpaProperties.getProperties(), new HibernateSettings());
        return builder.dataSource(userDataSource).properties(properties)
            .packages("com.trs.yq.police.subject.mysqlDatasource.entity").build();
    }

    /**
     * @param builder builder
     * @return {@link PlatformTransactionManager}
     */
    @Bean(name = "mysqlTransactionManager")
    public PlatformTransactionManager transactionManagerUser(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryUser(builder).getObject()));
    }

}
