package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.vo.HazardBarNode;
import com.trs.yq.police.subject.domain.vo.HazardResult;
import com.trs.yq.police.subject.service.HazardService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 风险洞察
 *
 * <AUTHOR>
 * @date 2022/04/13
 */
@RestController
public class RiskController {

    @Resource
    private HazardService service;

    /**
     * 风险洞察预警
     *
     * @return {@link HazardResult}
     */
    @GetMapping("/battle/hazard/hazardList")
    List<HazardResult> getHazardList() {
        return service.getHazardList();
    }

    /**
     * 风险洞察柱状图
     *
     * @return {@link HazardResult}
     */
    @GetMapping("/battle/hazard/bar")
    List<HazardBarNode> getHazardBarList() {
        return service.getHazardBarList();
    }
}
