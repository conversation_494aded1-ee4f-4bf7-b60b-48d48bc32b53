package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjSfryEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/7/7 11:32
 */
@Repository
public interface CrjSfryRepository extends BaseRepository<CrjSfryEntity, String> {
    /**
     * 分页查询三非人员
     *
     * @param searchValue 模糊检索参数
     * @param deptCode    部门code
     * @param beginTime   起始时间
     * @param endTime     结束时间
     * @param status 是否发送信息
     * @param page        分页
     * @return {@link CrjSfryEntity}
     */
    @Query("select c from CrjSfryEntity c where  c.rzsj>:beginTime and c.rzsj <:endTime and (:status is null or c.isSendMessage =:status) and (:deptCode is null or c.gxdwbm like concat(:deptCode,'%')) and (:searchValue is null or concat(c.zwxm,c.ywxm,c.zjhm,c.gxdwmc,c.gxdwbm,c.address) like concat('%',:searchValue,'%') ) ")
    Page<CrjSfryEntity> findSfryPageList(@Param("searchValue") String searchValue,
                                         @Param("deptCode")String deptCode,
                                         @Param("beginTime")LocalDateTime beginTime,
                                         @Param("endTime")LocalDateTime endTime,
                                         @Param("status")Boolean status,
                                         Pageable page);
}
