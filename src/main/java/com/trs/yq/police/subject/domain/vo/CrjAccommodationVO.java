package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 出入境-住宿登记详情vo
 *
 * <AUTHOR>
 * @since 2021/9/22
 */
@Data
public class CrjAccommodationVO implements Serializable {

    private static final long serialVersionUID = 86935603542763220L;
    /**
     * 姓名
     */
    private String name;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 名
     */
    private String firstName;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 居住信息
     */
    private String livingInfo;
    /**
     * 居住旅店
     */
    private String livingInn;
    /**
     * 联系电话
     */
    private String phoneNumber;
    /**
     * 拟离开时间
     */
    private LocalDateTime departureDate;
    /**
     * 照片
     */
    private ImageVO image;
}
