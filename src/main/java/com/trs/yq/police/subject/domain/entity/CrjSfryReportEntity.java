package com.trs.yq.police.subject.domain.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/22 15:58
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_SFRY_REPORT")
public class CrjSfryReportEntity extends BaseEntity {

    /**
     * 举报uuid
     */
    private String reportId;
    /**
     * 分派单位
     */
    private String acceptor;
    /**
     * 备注
     */
    private String description;
    /**
     * 核实时间
     */
    private Date verifyTime;
    /**
     * 核实结果
     */
    private String verifyResult;
    /**
     * 是否核实
     */
    private Boolean isVerified;
    /**
     * 分派状态
     */
    private String dispatchStatus;
    /**
     * 核实人
     */
    private String verifyUser;

    private String attachment;
    /**
     * 举报时间
     */
    private Date reportTime;
}
