package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警和轨迹关联表
 *
 * <AUTHOR>
 * @date 2021/9/7 15:35
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_TRACE_RELATION")
public class WarningTraceRelationEntity implements Serializable {

    private static final long serialVersionUID = -5886451757065538763L;

    /**
     * 关系表主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 预警id
     */
    private String warningId;

    /**
     * 轨迹id
     */
    private String trajectoryId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
