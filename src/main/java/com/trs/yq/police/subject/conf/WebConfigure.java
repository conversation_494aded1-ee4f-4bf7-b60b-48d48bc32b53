package com.trs.yq.police.subject.conf;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.trs.yq.police.subject.json.CustomNullSerializerProvider;
import com.trs.yq.police.subject.json.deserializer.UtcToDateDeserializer;
import com.trs.yq.police.subject.json.deserializer.UtcToDateTimeDeserializer;
import com.trs.yq.police.subject.json.serializer.DateTimeToUtcSerializer;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 拦截器、序列化工具等配置类
 *
 * <AUTHOR>
 * @date 2021/07/14
 */
@Configuration
public class WebConfigure implements WebMvcConfigurer {
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        List<MediaType> supportMediaTypeList = Arrays.asList(
                MediaType.APPLICATION_JSON,
                MediaType.IMAGE_JPEG,
                MediaType.IMAGE_PNG,
                MediaType.IMAGE_GIF
        );

        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(supportMediaTypeList);
        converter.setDefaultCharset(StandardCharsets.UTF_8);
        converter.getObjectMapper().setSerializerProvider(new CustomNullSerializerProvider());

        SimpleModule module = new SimpleModule();
        module.addSerializer(LocalDateTime.class, new DateTimeToUtcSerializer());
        module.addSerializer(LocalDate.class, new DateTimeToUtcSerializer());
        module.addDeserializer(LocalDateTime.class, new UtcToDateTimeDeserializer());
        module.addDeserializer(LocalDate.class, new UtcToDateDeserializer());
        converter.getObjectMapper().registerModule(module).setSerializationInclusion(JsonInclude.Include.NON_NULL);

        converters.add(0, converter);
    }
}
