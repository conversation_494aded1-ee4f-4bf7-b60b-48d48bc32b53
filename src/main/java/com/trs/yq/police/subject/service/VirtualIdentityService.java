package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.VirtualIdentityVO;

import java.util.List;

/**
 * 虚拟身份类业务层接口
 *
 * <AUTHOR>
 */
public interface VirtualIdentityService {

    /**
     * 获取人员虚拟信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link VirtualIdentityVO}
     * <AUTHOR>
     */
    List<VirtualIdentityVO> getAll(String personId);

    /**
     * 删除人员的虚拟信息
     *
     * @param personId 人员id
     * @param id       人员id
     * <AUTHOR>
     */
    void deleteOneById(String personId, String id);

    /**
     * 添加人员的虚拟信息
     *
     * @param personId              人员主键
     * @param virtualIdentityEntity 虚拟信息
     * <AUTHOR>
     */
    void addOne(String personId, VirtualIdentityVO virtualIdentityEntity);

    /**
     * 更新人员的虚拟信息
     *
     * @param personId              人员主键
     * @param virtualIdentityEntity 虚拟信息
     * <AUTHOR>
     */
    void updateOne(String personId, VirtualIdentityVO virtualIdentityEntity);
}
