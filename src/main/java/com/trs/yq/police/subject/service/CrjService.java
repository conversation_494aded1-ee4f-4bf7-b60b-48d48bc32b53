package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.CountTypeResponseVO;
import com.trs.yq.police.subject.domain.vo.CrjAccommodationListRequestVO;
import com.trs.yq.police.subject.domain.vo.CrjAccommodationListVO;
import com.trs.yq.police.subject.domain.vo.CrjAccommodationVO;
import com.trs.yq.police.subject.domain.vo.CrjDiseaseAreaCountVO;
import com.trs.yq.police.subject.domain.vo.CrjSfryBaseVO;
import com.trs.yq.police.subject.domain.vo.CrjSfryListVO;
import com.trs.yq.police.subject.domain.vo.CrjVisitListRequestVO;
import com.trs.yq.police.subject.domain.vo.CrjVisitListVO;
import com.trs.yq.police.subject.domain.vo.CrjVisitVO;
import com.trs.yq.police.subject.domain.vo.CrjWarningCountVO;
import com.trs.yq.police.subject.domain.vo.CrySfryRequestVO;
import com.trs.yq.police.subject.domain.vo.GridTreeVO;
import com.trs.yq.police.subject.domain.vo.HarmfulBorderVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.UnitVO;
import com.trs.yq.police.subject.domain.vo.VisaWaiverCountVO;
import com.trs.yq.police.subject.domain.vo.WarningScrollListVO;
import java.util.List;

/**
 * 出入境服务层接口
 *
 * <AUTHOR>
 * @since 2021/9/17
 */
public interface CrjService {

    /**
     * [专题首页-出入境] 人员预警
     *
     * @param status 预警状态
     * @return 出入境人  人员预警列表
     */
    WarningScrollListVO getWarningEntryExitPerson(String status);

    /**
     * [专题首页-出入境] 走访记录列表查询
     *
     * @param crjVisitListRequestVO {@link CrjVisitListRequestVO}
     * @return {@link PageResult}
     */
    PageResult<CrjVisitListVO> getCrjVisitListVOList(CrjVisitListRequestVO crjVisitListRequestVO);

    /**
     * [专题首页-出入境] 未办理住宿登记预警
     *
     * @param status 预警状态
     * @return 出入境人  未办理住宿登记列表
     */
    WarningScrollListVO getAccommodationWarning(String status);

    /**
     * [专题首页-出入境] 免签超期预警
     *
     * @param status 预警状态
     * @return 出入境人  免签超期预警
     */
    WarningScrollListVO getOverdue(String status);

    /**
     * [专题首页-出入境] 妨害国（边）境管理人员（人）(数量统计)
     *
     * @param timeParams 时间参数
     * @return 出入境人  妨害国（边）境管理人员（人）(数量统计)
     */
    HarmfulBorderVO getHarmful(TimeParams timeParams);

    /**
     * 在泸境外人员登记情况
     *
     * @param timeParams 时间参数
     * @return 统计结果
     */
    List<CountTypeResponseVO> getRegistrationCount(TimeParams timeParams);

    /**
     * 境外人员国籍情况
     *
     * @param timeParams 时间参数
     * @return 统计结果
     */
    List<CountTypeResponseVO> getNationalityCount(TimeParams timeParams);

    /**
     * 成渝过境免签人员活动情况
     *
     * @param timeParams 时间参数
     * @return 统计结果
     */
    List<VisaWaiverCountVO> getVisaWaiverCount(TimeParams timeParams);

    /**
     * 根据时间统计出入境预警基本数据
     *
     * @param timeParams 时间范围
     * @return 统计结果
     */
    CrjWarningCountVO getWarningCount(TimeParams timeParams);

    /**
     * [专题首页-出入境] 根据走访记录Id查询走访详情信息
     *
     * @param visitId 走访记录id
     * @return {@link CrjVisitVO}
     */
    CrjVisitVO getCrjVisitVOList(String visitId);

    /**
     * 根据id返回住宿信息详情
     *
     * @param accommodationId 住宿id
     * @return 住宿详情 {@link CrjAccommodationVO}
     */
    CrjAccommodationVO getAccommodationDetail(String accommodationId);

    /**
     * [专题首页-出入境] 住宿记录列表查询
     *
     * @param crjAccommodationListRequestVO {@link CrjAccommodationListRequestVO}
     * @return {@link CrjAccommodationListVO}
     */
    PageResult<CrjAccommodationListVO> getCrjAccommodationListVOList(
        CrjAccommodationListRequestVO crjAccommodationListRequestVO);

    /**
     * [专题首页-出入境] 走访列表查询 走访部门筛选条件
     *
     * @return 走访部门list
     */
    List<UnitVO> getListUnitTree();

    /**
     * 时间筛选涉疫人员按国家统计人数
     *
     * @param timeParams 时间条件
     * @return 统计结果
     */
    List<CountTypeResponseVO> getDiseaseInflow(TimeParams timeParams);

    /**
     * 时间筛选涉疫人员按区县统计人数
     *
     * @param timeParams 时间条件
     * @return 统计结果
     */
    List<CrjDiseaseAreaCountVO> getDiseaseAreaCount(TimeParams timeParams);

    /**
     * @param requestVO 请求参数
     * @return {@link CrjSfryListVO}
     */
    PageResult<CrjSfryListVO> getSwryList(CrySfryRequestVO requestVO);

    /**
     * 获取三飞人员信息
     *
     * @param sfryId 三非人员id
     * @return {@link CrjSfryBaseVO}
     */
    CrjSfryBaseVO getSfryInfo(String sfryId);

    /**
     * @return {@link UnitVO}
     */
    List<GridTreeVO> getGridTree();

    /**
     * 发送短信
     *
     * @param sfryId          三非人员id
     * @param gridPhoneNumber 网格员电话号
     */
    void sendMsg(String sfryId, String gridPhoneNumber) throws Exception;

    /**
     * 更新网格员手机号
     *
     * @param gridId      网格id
     * @param phoneNumber 手机号
     */
    void updateGridPhoneNumber(String gridId, String phoneNumber);
}
