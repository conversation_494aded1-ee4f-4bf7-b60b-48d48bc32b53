package com.trs.yq.police.subject.task;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.repository.VoiceRecognitionRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 智能语音识别接口数据
 *
 * <AUTHOR>
 * @date 2021/12/29 17:00
 */
@Component
@Slf4j
@ConditionalOnProperty(value = "com.trs.upload.voice.task.enable", havingValue = "true")
public class UpdateVoiceDataTask {
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private VoiceRecognitionRepository voiceRecognitionRepository;
    @Value("${com.trs.redis.audio.txt.key}")
    private String voiceKeyOfRedis;
    @Value("${com.trs.redis.audio.base64.key}")
    private String voiceBase64KeyOfRedis;

    @Value("${com.trs.text.to.voice.url}")
    private String textToVoiceUrl;

    @Resource(name = "assignRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();


    /**
     * 智能语音接口缓存
     */
    @Scheduled(fixedDelay = 60 * 60 * 1000)
    public void updateVoiceData() {
        unitRepository.findByType("1")
                .stream().map(unitEntity -> {
                    KeyValueVO keyValueVO = new KeyValueVO();
                    keyValueVO.setKey(unitEntity.getUnitCode());
                    keyValueVO.setValue(unitEntity.getShortname());
                    return keyValueVO;
                }).forEach(address -> {
                    log.info("更新语音问答结果缓存,更新区域:{}", address.getValue());
                    TimeParams today = new TimeParams();
                    today.setRange("1");
                    TimeParams yesterday = new TimeParams();
                    yesterday.setRange("11");
                    //去年今天
                    TimeParams tbTime = getOneYearTimeNow();

                    final String template = "${area}接报有效警情${alarm}起，同比${tbAlarm}%，环比${hbAlarm}%，刑事类警情${crime}起，同比${tbCrime}%，环比${hbCrime}%，其中八类暴力严重犯罪${seriousCrime}起，街面“六类”侵财${invadeMoney}起（主城区${main}起），电信诈骗${telecomFraud}起；治安类警情${security}起，同比${tbSecurity}%，环比${hbSecurity}%，其中涉黄${yellowish}起，涉赌${gambling}起，涉毒${drug}起，欧打他人${fight}起。";
                    Map<String, String> data = new HashMap<>();
                    data.put("area", address.getValue());
                    long todayAlarm = voiceRecognitionRepository.countYxJq(today.getBeginTime(), today.getEndTime(), address.getKey());
                    long yesterdayAlarm = voiceRecognitionRepository.countYxJq(yesterday.getBeginTime(), yesterday.getEndTime(), address.getKey());
                    long tbAlarm = voiceRecognitionRepository.countYxJq(tbTime.getBeginTime(), tbTime.getEndTime(), address.getKey());
                    data.put("alarm", String.valueOf(todayAlarm));
                    data.put("hbAlarm", calculateTbHb(todayAlarm, yesterdayAlarm));
                    data.put("tbAlarm", calculateTbHb(todayAlarm, tbAlarm));

                    long todayCrime = voiceRecognitionRepository.countLaXs(today.getBeginTime(), today.getEndTime(), address.getKey());
                    long yesterdayCrime = voiceRecognitionRepository.countLaXs(yesterday.getBeginTime(), yesterday.getEndTime(), address.getKey());
                    long tbCrime = voiceRecognitionRepository.countLaXs(tbTime.getBeginTime(), tbTime.getEndTime(), address.getKey());
                    data.put("crime", String.valueOf(todayCrime));
                    data.put("hbCrime", calculateTbHb(todayCrime, yesterdayCrime));
                    data.put("tbCrime", calculateTbHb(todayCrime, tbCrime));

                    data.put("seriousCrime", String.valueOf(voiceRecognitionRepository.countJq8(today.getBeginTime(), today.getEndTime(), address.getKey())));
                    data.put("invadeMoney", String.valueOf(voiceRecognitionRepository.countJq6(today.getBeginTime(), today.getEndTime(), address.getKey())));
                    data.put("main", String.valueOf(voiceRecognitionRepository.countZcqJq6(today.getBeginTime(), today.getEndTime())));
                    data.put("telecomFraud", String.valueOf(voiceRecognitionRepository.countDxzp(today.getBeginTime(), today.getEndTime(), address.getKey())));

                    long todaySecurity = voiceRecognitionRepository.countXzAll(today.getBeginTime(), today.getEndTime(), address.getKey());
                    long yesterdaySecurity = voiceRecognitionRepository.countXzAll(yesterday.getBeginTime(), yesterday.getEndTime(), address.getKey());
                    long tbSecurity = voiceRecognitionRepository.countXzAll(tbTime.getBeginTime(), tbTime.getEndTime(), address.getKey());
                    data.put("security", String.valueOf(todaySecurity));
                    data.put("hbSecurity", calculateTbHb(todaySecurity, yesterdaySecurity));
                    data.put("tbSecurity", calculateTbHb(todaySecurity, tbSecurity));
                    data.put("yellowish", String.valueOf(voiceRecognitionRepository.countXzSh(today.getBeginTime(), today.getEndTime(), address.getKey())));
                    data.put("gambling", String.valueOf(voiceRecognitionRepository.countXzSd(today.getBeginTime(), today.getEndTime(), address.getKey())));
                    data.put("drug", String.valueOf(voiceRecognitionRepository.countXzSdp(today.getBeginTime(), today.getEndTime(), address.getKey())));
                    data.put("fight", String.valueOf(voiceRecognitionRepository.countXzOdtr(today.getBeginTime(), today.getEndTime(), address.getKey())));
                    String audioTxt = renderString(template, data);
                    HashOperations<String, String, Object> hashOperations = redisTemplate.opsForHash();

                    hashOperations.put(voiceKeyOfRedis, address.getKey(), audioTxt);
                    ObjectNode textToVoiceForm = objectMapper.createObjectNode();
                    textToVoiceForm.put("text", audioTxt);
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    HttpEntity<ObjectNode> voiceToTextHttpEntity = new HttpEntity<>(textToVoiceForm, headers);
                    ResponseEntity<byte[]> textToVoice = restTemplate.postForEntity(textToVoiceUrl, voiceToTextHttpEntity, byte[].class);
                    final String audioBase64Txt = Base64.getEncoder().encodeToString(textToVoice.getBody());
                    hashOperations.put(voiceBase64KeyOfRedis, address.getKey(), audioBase64Txt);

                });
    }

    /**
     * 计算同比、环比、比重
     *
     * @param bq 本期
     * @param sq 上期
     * @return java.lang.String
     * <AUTHOR>
     * @since 2021/10/21 19:19
     */
    protected static String calculateTbHb(long bq, long sq) {
        if (sq == 0) {
            return "增长0";
        }
        BigDecimal bqBigDecimal = BigDecimal.valueOf(bq);
        BigDecimal sqBigDecimal = BigDecimal.valueOf(sq);
        BigDecimal result = (bqBigDecimal.subtract(sqBigDecimal)).divide(sqBigDecimal, 4, RoundingMode.UP).multiply(BigDecimal.valueOf(100L)).setScale(2, RoundingMode.UP);
        if (result.compareTo(BigDecimal.ZERO) > 0) {
            return "增长" + result.doubleValue();
        } else if (result.compareTo(BigDecimal.ZERO) < 0) {
            return "减少" + result.abs().doubleValue();
        } else {
            return "增长0";
        }
    }

    /**
     * 根据键值对填充字符串，如("hello ${name}",{name:"xiaoming"})
     * 输出：
     *
     * @param template 模板
     * @param map      填充的数据
     * @return 填充后的字符串
     */
    public static String renderString(String template, Map<String, String> map) {
        Set<Map.Entry<String, String>> sets = map.entrySet();
        for (Map.Entry<String, String> entry : sets) {
            String regex = "\\$\\{" + entry.getKey() + "}";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(template);
            template = matcher.replaceAll(entry.getValue());
        }
        return template;
    }

    /**
     * @return 一年前的今天
     */
    public static TimeParams getOneYearTimeNow() {
        Calendar c = Calendar.getInstance();
        c.add(Calendar.YEAR, -1);
        LocalDate date = c.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        TimeParams timeParams = new TimeParams();
        timeParams.setRange("99");
        timeParams.setBeginTime(date.atStartOfDay());
        timeParams.setEndTime(date.plusDays(1).atStartOfDay());
        return timeParams;
    }
}
