package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 群体-线索关系实体类
 *
 * <AUTHOR>
 * @date 2021/9/1 15:01
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_GROUP_CLUE_RELATION")
public class GroupClueRelationEntity extends BaseEntity {
    private static final long serialVersionUID = 2626390175766396802L;
    /**
     * 群体Id
     */
    private String groupId;
    /**
     * 线索Id
     */
    private String clueId;

}
