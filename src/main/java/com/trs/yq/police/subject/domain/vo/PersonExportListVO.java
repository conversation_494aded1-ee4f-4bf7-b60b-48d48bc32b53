package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2021/08/09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PersonExportListVO extends PersonEntity {
    private static final long serialVersionUID = -6904485350980480440L;

    @ExcelProperty(value = "被依法处理情况")
    private String treatment;

    @ExcelProperty(value = "主要诉求")
    private String mainDemand;

    @ExcelProperty(value = "涉事相关群体")
    private String group;

    @ExcelProperty(value = "群体类别")
    private String groupType;

    @ExcelProperty("人员类别")
    private String personType;

    @ExcelProperty("标签")
    private String personLabel;
}
