package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 群体-线索添加查询VO
 *
 * <AUTHOR>
 * @date 2021/9/6 9:37
 */
@Setter
@Getter
@ToString
public class DialogClueListRequestVO implements Serializable {
    private static final long serialVersionUID = 8011422278748059910L;

    /**
     * 其他参数
     */
    @Data
    public static class OtherParams implements Serializable {

        private static final long serialVersionUID = -4064876198645980212L;

        /**
         * 专题id
         */
        @NotBlank(message = "专题id不能为空！")
        private String subjectId;

        /**
         * 群体类别
         */
        private String clueTypeId;

        /**
         * 录入单位
         */
        private String createDeptId;
    }

    /**
     * 其他参数
     */
    private OtherParams otherParams;

    /**
     * 模糊检索参数
     */
    private SearchParams searchParams;

    /**
     * 分页
     */
    private PageParams pageParams;
}
