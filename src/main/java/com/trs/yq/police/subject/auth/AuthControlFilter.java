package com.trs.yq.police.subject.auth;

import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.OperationEntity;
import com.trs.yq.police.subject.domain.entity.RoleUserEntity;
import com.trs.yq.police.subject.domain.params.OperationParams;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.exception.PermissionDeniedException;
import com.trs.yq.police.subject.repository.OperationRepository;
import com.trs.yq.police.subject.repository.RoleOperationRepository;
import com.trs.yq.police.subject.repository.RoleUserRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.bind.ServletRequestUtils;

import javax.annotation.Resource;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 权限控制过滤器
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Slf4j
public class AuthControlFilter implements Filter {

    @Resource
    private RoleUserRepository roleUserRepository;

    @Resource
    private OperationRepository operationRepository;

    @Resource
    private RoleOperationRepository roleOperationRepository;

    private static final String DEFAULT_SUBJECT_ID = "0";

    private static final String SERVICE = "POLICE";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest servletRequest = (HttpServletRequest) request;
        final String method = servletRequest.getMethod();
        final String url = servletRequest.getRequestURI();
        final String subjectId = ServletRequestUtils.getStringParameter(request, "subjectId", DEFAULT_SUBJECT_ID);
        OperationParams params = new OperationParams();
        params.setKey("subjectId");
        params.setValue(subjectId);
        params.setPosition("query");
        log.info("url:" + method + " " + url + " " + params);

        LoginUser user = AuthHelper.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new ParamValidationException("用户未登录！");
        }

        List<OperationEntity> operationList = getOperationByUrl(method, url);
        if(!operationList.isEmpty()) {
            //请求url对应的操作名
            String requiredOperation = checkParams(params, operationList);
            log.info("operation-name:" + requiredOperation);
            //用户所拥有的操作列表
            List<String> roleIds = roleUserRepository.findByUserId(user.getId()).stream().map(RoleUserEntity::getRoleId).collect(Collectors.toList());
            List<String> userOperations = roleOperationRepository.findOperationListByRoleIds(roleIds);
            log.info("userOperationList:"+userOperations.toString());

            //用户没有角色时，该操作未被配置权限时不校验
            if(!userOperations.isEmpty() && StringUtils.isNotBlank(requiredOperation)) {
                if (!userOperations.contains(requiredOperation)) {
                    log.info("无操作权限，请核实！");
                    throw new PermissionDeniedException("无操作权限，请核实！");
                }
            }
        }
        chain.doFilter(request, response);
    }

    /**
     * 筛选出url符合匹配结果的操作实体列表
     *
     * @param method 请求方法
     * @param requestUrl 请求中传递的实际url
     * @return 结果列表 {@link OperationEntity}
     */
    private List<OperationEntity> getOperationByUrl(String method, String requestUrl) {
        List<OperationEntity> list = operationRepository.findByRequestMethodAndService(method, SERVICE);
        List<OperationEntity> result = new ArrayList<>();
        for (OperationEntity e: list) {
            if(urlMatcher(e.getUrl(), requestUrl)) {
                result.add(e);
            }
        }
        return result;
    }

    /**
     * 筛选参数符合的
     *
     * @param params 参数
     * @param operationList 操作列表
     * @return 操作实体
     */
    private String checkParams(OperationParams params, List<OperationEntity> operationList) {
        final String key = params.getKey();
        final String value = params.getValue();
        final String position = params.getPosition();
        OperationEntity operation = new OperationEntity();
        if (params.getValue().equals(DEFAULT_SUBJECT_ID)) {
            operation = operationList.get(0);
        } else {
            for (OperationEntity o: operationList) {
                OperationParams operationParams = JsonUtil.parseObject(o.getParams(), OperationParams.class);
                if(Objects.isNull(o.getParams()) || key.equals(Objects.requireNonNull(operationParams).getKey()) && operationParams.getValue().equals(value) && position.equals(operationParams.getPosition())) {
                    operation = o;
                    log.info("params:"+operationParams);
                }
            }
        }
        return operation.getName();
    }

    /**
     * 对数据库中带通配符的url与实际请求中url进行匹配
     *
     * @param patternPath 数据库中待匹配url
     * @param requestPath 请求中传递的实际url
     * @return 匹配结果
     */
    private boolean urlMatcher(String patternPath, String requestPath) {
        if(patternPath.isEmpty() || requestPath.isEmpty()) {
            return false;
        } else {
            PathMatcher matcher = new AntPathMatcher();
            return matcher.match(patternPath, requestPath);
        }
    }

}
