package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.dto.RiskDetailDto;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.EventPersonRelationEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/12/13 17:53
 */
@Repository
public interface EventPersonRelationRepository extends BaseRepository<EventPersonRelationEntity, String> {

    /**
     * 根据事件id和人员id获取关联关系
     *
     * @param eventId  事件id
     * @param personId 人员id
     * @return {@link EventPersonRelationEntity}
     */
    EventPersonRelationEntity findByEventIdAndPersonId(String eventId, String personId);

    /**
     * 根据事件id获取关联关系
     *
     * @param eventId 事件id
     * @return {@link EventPersonRelationEntity}
     */
    List<EventPersonRelationEntity> findAllByEventId(String eventId);

    /**
     * 分页查询
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return 分页查询风险详情
     */
    @Query(
        "select new com.trs.yq.police.subject.domain.dto.RiskDetailDto(p.id, p.name, p.idNumber, concat('/dispatchWarning/getPhoto/',p.idNumber) , p.controlStatus, epr.behaviors, epr.crTime) "
            + "from EventPersonRelationEntity epr "
            + "left join PersonEntity p on p.id = epr.personId "
            + "left join EventEntity e on e.id = epr.eventId "
            + "where epr.eventId = :eventId and e.deleted = false")
    Page<RiskDetailDto> findAllByEventId(
        @Param("eventId") String eventId, Pageable pageable);

    /**
     * 删除事件-人员关联关系
     *
     * @param eventId 事件id
     */
    void removeAllByEventId(String eventId);


    /**
     * 根据人员id获取关联关系
     *
     * @param personId 人员id
     * @return {@link EventPersonRelationEntity}
     */
    List<EventPersonRelationEntity> findAllByPersonId(String personId);

    /**
     * 根据人员id 删除事件-人员关联关系
     *
     * @param personId {人员id}
     */
    void deleteAllByPersonId(String personId);

    /**
     * 根据人员id 删除事件-人员关联关系
     *
     * @param personId {人员id}
     * @param eventId  {事件id}
     */
    void deleteByPersonIdAndEventId(String personId, String eventId);

    /**
     * 根据人员id查询相关事件
     *
     * @param personId 人员id
     * @return 相关事件
     */
    @Query("select t1 from EventEntity t1, EventPersonRelationEntity t2 where t1.id = t2.eventId and t2.personId = :personId")
    List<EventEntity> findEventsByPerson(@Param("personId") String personId);

    /**
     * 根据事件id查询关联人员
     *
     * @param eventId 事件id
     * @return 相关人员
     */
    @Query("select t1 from PersonEntity t1, EventPersonRelationEntity t2 where t1.id = t2.personId and t2.eventId = :eventId")
    List<PersonEntity> findPersonByEventId(@Param("eventId") String eventId);
}
