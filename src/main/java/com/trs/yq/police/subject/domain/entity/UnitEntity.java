package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 单位实体
 *
 * <AUTHOR>
 * @date 2021/07/28
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_UNIT")
public class UnitEntity implements Serializable {

    private static final long serialVersionUID = -4660908964101086246L;
    
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 父级单位id
     */
    @Column(name = "pid")
    private String parentId;

    /**
     * 单位代码
     */
    @Column(name = "unitcode")
    private String unitCode;

    /**
     * 单位名称
     */
    @Column(name = "unitname")
    private String unitName;

    /**
     * 简称
     */
    private String shortname;

    /**
     * 行政区划
     */
    @Column(name = "areacode")
    private String areaCode;

    /**
     * 类型
     */
    private String type;

    /**
     * 警种类型
     */
    @Column(name = "policecode")
    private String policeCode;

    /**
     * 显示顺序
     */
    @Column(name = "showorder")
    private Integer showOrder;

    /**
     *
     */
    private String supid;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private LocalDateTime crtime;

    /**
     * 创建时间
     */
    private String cruser;

    /**
     *
     */
    private String signet;

    /**
     * 分管单位局领导
     */
    private String leader;

    /**
     * 上级警种单位代码
     */
    private String policepunit;

    /**
     * 子节点
     */
    @Transient
    private List<UnitEntity> children;
}

