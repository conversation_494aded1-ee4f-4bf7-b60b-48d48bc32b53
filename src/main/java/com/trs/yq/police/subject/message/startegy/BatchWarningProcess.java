package com.trs.yq.police.subject.message.startegy;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.dto.BatchWarningMessageDTO;
import com.trs.yq.police.subject.domain.dto.WarningTrajectoryListDTO;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.message.WarningPushService;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.GeoUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量预警消息处理
 *
 * <AUTHOR>
 * @date 2021/9/11 14:40
 */
@Slf4j
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class BatchWarningProcess implements WarningProcess {

    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;
    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private WarningPushService warningPushService;
    @Resource
    private PersonRepository personRepository;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void process(String message) {

        // 转换为预警对象
        final BatchWarningMessageDTO warningMessage = JsonUtil.parseObject(message, BatchWarningMessageDTO.class);

        if (Objects.isNull(warningMessage)) {
            log.error("receive error message = {}!", message);
            return;
        }

        // 构建预警id
        final String warningId = extractWarningId(warningMessage);

        // 构建轨迹id
        final List<String> trajectoryIds = extractTrajectoryIds(warningMessage.getTrajectories());

        // 绑定关系
        establishRelationShip(warningId, trajectoryIds);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.PUSH)
                .module(OperateModule.WARNING)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(warningId)
                .targetObjectType(TargetObjectTypeEnum.WARNING.getCode())
                .desc("推送预警消息")
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    /**
     * 抽取预警id
     *
     * @param warningMessage 预警信息
     * @return 预警id列表
     */
    private String extractWarningId(BatchWarningMessageDTO warningMessage) {
        WarningEntity warning = new WarningEntity();
        final String type = warningMessage.getType();
        final WarningTypeEntity warningType = warningTypeRepository.findByEnName(type);
        final List<WarningTrajectoryListDTO> trajectories = warningMessage.getTrajectories();
        warning.setWarningTime(LocalDateTime.parse(warningMessage.getWarningTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        warning.setWarningLevel(warningType.getDefaultLevel());
        warning.setWarningSource(generateWarningSource(trajectories));
        warning.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
        warning.setWarningDetails(generateWarningDetail(warningType, warningMessage));
        warning.setSubjectId(warningType.getSubjectId());
        warning.setWarningType(warningType.getId());
        final WarningTrajectoryListDTO trajectory = trajectories.get(0);
        warning.setAddress(trajectory.getAddress());
        warning.setPlace(trajectory.getPlace());
        warning.setAreaCode(GeoUtil.getDistrictCode(trajectory.getLng(), trajectory.getLat()));
        // 返回预警id
        warningRepository.save(warning);
//        warningPushService.push(warningType, warning);
        return warning.getId();
    }

    /**
     * 生成批处理预警详情
     *
     * @param warningType    预警类型
     * @param warningMessage 预警消息
     * @return 预警详情
     */
    private String generateWarningDetail(WarningTypeEntity warningType, BatchWarningMessageDTO warningMessage) {
        SpelExpressionParser parser = new SpelExpressionParser();
        EvaluationContext context = new StandardEvaluationContext();
        final Expression expression = parser.parseExpression(warningType.getContentTemplate());

        final List<WarningTrajectoryListDTO> trajectories = warningMessage.getTrajectories();
        final String subjectId = warningType.getSubjectId();
        final List<String> personIdNumbers = trajectories
                .stream()
                .map(WarningTrajectoryListDTO::getIdNumber)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        context.setVariable("place", StringUtils.trimToEmpty(trajectories.get(0).getPlace()));
        context.setVariable("personCount", warningMessage.getPersonCount());

        context.setVariable("address", trajectories.get(0).getAddress());
        context.setVariable("personType", generateMultiPersonType(personIdNumbers, subjectId));
        context.setVariable("activeTime", warningMessage.getWarningTime());
        return expression.getValue(context, String.class);
    }

    /**
     * 多人预警构建人员类别
     *
     * @param personIdNumbers 人员身份证号列表
     * @return 类别列表字符串
     */
    private String generateMultiPersonType(List<String> personIdNumbers, String subjectId) {
        List<PersonEntity> personEntities = personRepository.findByIdNumbers(personIdNumbers);
        if (Objects.isNull(personEntities) || personEntities.isEmpty()) {
            return "人员";
        } else {
            return personEntities.stream().flatMap(
                            personEntity -> personRepository.getPersonType(personEntity.getId(), subjectId)
                                    .stream()).distinct()
                    .collect(Collectors.joining("、"));
        }
    }

    /**
     * 构建预警源
     *
     * @param trajectories 轨迹
     * @return 预警源
     */
    private List<String> generateWarningSource(List<WarningTrajectoryListDTO> trajectories) {

        final List<String> trajectoryTypes = trajectories.stream().map(WarningTrajectoryListDTO::getTrajectoryType).collect(Collectors.toList());

        final List<String> tableNames = trajectorySourceRepository.findAllNameByTableNameIn(trajectoryTypes);

        return tableNames.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 抽取轨迹id
     *
     * @param trajectories 轨迹列表
     * @return 轨迹id列表
     */
    private List<String> extractTrajectoryIds(List<WarningTrajectoryListDTO> trajectories) {
        final List<WarningTrajectoryEntity> trajectoryEntities = trajectories.stream().filter(Objects::nonNull).map(trajectory -> {
            WarningTrajectoryEntity trajectoryEntity = new WarningTrajectoryEntity();
            Optional.ofNullable(trajectorySourceRepository.findByTableName(trajectory.getTrajectoryType()))
                    .ifPresent(source -> trajectoryEntity.setSourceId(source.getId()));
            trajectoryEntity.setIdNumber(trajectory.getIdNumber());
            trajectoryEntity.setLng(trajectory.getLng());
            trajectoryEntity.setLat(trajectory.getLat());
            trajectoryEntity.setPlace(trajectory.getPlace());
            trajectoryEntity.setAddress(trajectory.getAddress());
            trajectoryEntity.setHybaseTable(trajectory.getTrajectoryType());
            trajectoryEntity.setHybaseId(trajectory.getTrsId());
            trajectoryEntity.setIdType(trajectory.getIdType());
            trajectoryEntity.setIdValue(trajectory.getIdValue());
            trajectoryEntity.setImageUrl(trajectory.getImageUrl());
            trajectoryEntity.setCropUrl(trajectory.getCropUrl());
            trajectoryEntity.setSimilarity(trajectory.getSimilarity());
            trajectoryEntity.setDateTime(LocalDateTime.parse(trajectory.getStartTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
            return trajectoryEntity;
        }).collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WarningTrajectoryEntity::getIdNumber))), ArrayList::new));
        return warningTrajectoryRepository.saveAll(trajectoryEntities)
                .stream()
                .map(WarningTrajectoryEntity::getId)
                .collect(Collectors.toList());
    }

    /**
     * 绑定预警和轨迹
     *
     * @param warningId     预警id
     * @param trajectoryIds 轨迹id列表
     */
    private void establishRelationShip(String warningId, List<String> trajectoryIds) {

        final List<WarningTraceRelationEntity> relations = trajectoryIds.stream().map(trajectoryId -> {
            WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
            relation.setWarningId(warningId);
            relation.setTrajectoryId(trajectoryId);
            relation.setCreateTime(LocalDateTime.now());
            return relation;
        }).collect(Collectors.toList());

        warningTraceRelationRepository.saveAll(relations);
    }


}
