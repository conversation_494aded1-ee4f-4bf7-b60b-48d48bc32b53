package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.VehicleVO;
import com.trs.yq.police.subject.service.VehicleService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 人员车辆信息接口类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:24
 */
@RestController
@RequestMapping("/person")
public class VehicleController {
    @Resource
    private VehicleService vehicleService;

    /**
     * 获取人员车辆基本信息
     *
     * @param personId 人员Id
     * @return 车辆信息
     */
    @GetMapping("{personId}/vehicle")
    public List<VehicleVO> queryVehicleInfo(@PathVariable String personId) {
        return vehicleService.getVehicleInfo(personId);
    }

    /**
     * 添加人员车辆信息
     *
     * @param personId  人员id
     * @param vehicleVO 车辆信息
     */
    @PostMapping("/{personId}/vehicle")
    public void saveVehicleInfo(@PathVariable String personId, @RequestBody VehicleVO vehicleVO) {
        vehicleService.saveVehicle(personId, vehicleVO);
    }

    /**
     * 删除人员车辆信息
     *
     * @param personId  人员id
     * @param vehicleId 车辆id
     */
    @DeleteMapping("/{personId}/vehicle/{vehicleId}")
    public void deleteVehicle(@PathVariable String personId, @PathVariable String vehicleId) {
        vehicleService.deleteVehicle(personId, vehicleId);
    }

    /**
     * 修改车辆信息
     *
     * @param personId  人员id
     * @param vehicleVO 车辆信息
     */
    @PutMapping("/{personId}/vehicle")
    public void updateVehicle(@PathVariable String personId, @RequestBody VehicleVO vehicleVO) {
        vehicleService.updateVehicle(personId, vehicleVO);
    }

}
