package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjMessageSendEntity;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/7/28 17:32
 */
@Repository
public interface CrjMessageSendRepository extends BaseRepository<CrjMessageSendEntity, String> {

    /**
     * 根据三非人员id获取短信息
     *
     * @param sfryId 三非人员id
     * @return {@link CrjMessageSendEntity}
     */
    List<CrjMessageSendEntity> findBySfryIdOrderBySendTimeDesc(String sfryId);

    /**
     * 获取信息发送模板
     *
     * @return 模板
     */
    @Query(nativeQuery = true, value = "select content from T_PS_CRJ_MESSAGE_TEMPLATE ")
    String getMessageTemplate();
}
