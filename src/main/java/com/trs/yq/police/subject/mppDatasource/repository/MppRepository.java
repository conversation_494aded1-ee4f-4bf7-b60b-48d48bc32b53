package com.trs.yq.police.subject.mppDatasource.repository;

import com.trs.yq.police.subject.mppDatasource.entity.MppEntity;
import com.trs.yq.police.subject.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 12345 repository
 */
@Repository
public interface MppRepository extends BaseRepository<MppEntity, String> {

    /**
     * 查询飙车预警
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     */
    @Query(nativeQuery = true, value = "select uuid, content, adddate, address, areaname from public.shzy_12345rx_gdjcxx " +
            "where adddate between :beginTime and :endTime " +
            "and (content like '%飙车%' or content like '%轰鸣%' or " +
            "(content like '%车%' and (content like '%噪音%' or content like '%扰民%' or content like '%声音%' or content like '%影响休息%')))")
    List<Map<String, String>> selectRacingWarning(@Param("beginTime")String beginTime,
                                                  @Param("endTime") String endTime);
}
