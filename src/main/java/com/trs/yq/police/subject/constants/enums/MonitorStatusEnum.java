package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 * 布控状态枚举
 *
 * <AUTHOR>
 */
public enum MonitorStatusEnum {
    /**
     * 已布控
     */
    IN_MONITOR("1", "已布控"),
    /**
     * 未布控
     */
    NOT_MONITOR("0", "未布控");

    MonitorStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private final String code;

    @Getter
    private final String name;
}
