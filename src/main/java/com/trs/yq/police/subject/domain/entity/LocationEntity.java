package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/21 11:30
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_LOCATION")
public class LocationEntity implements Serializable {
    private static final long serialVersionUID = -1451470112001471311L;
    @Id
    private Integer id;
    /**
     * 名称
     */
    private String name;
    /**
     * 管辖单位
     */
    private String orgCode;
    /**
     * 地址
     */
    private String address;
    /**
     * 纬度
     */
    private Double lat;
    /**
     * 经度
     */
    private Double lng;
}
