package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.WarningTrajectoryEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 预警轨迹点
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/10 16:00
 **/
@Data
public class PointVO {

    /**
     * 轨迹时间
     */
    private LocalDateTime dateTime;
    /**
     * 经度
     */
    private String lng;
    /**
     * 维度
     */
    private String lat;

    /**
     * 构造器
     *
     * @param trajectory {@link WarningTrajectoryEntity}
     * @return {@link PointVO}
     */
    public static PointVO of(WarningTrajectoryEntity trajectory) {
        PointVO pointVO = new PointVO();
        pointVO.setDateTime(trajectory.getDateTime());
        pointVO.setLat(Optional.ofNullable(trajectory.getLat()).orElse(""));
        pointVO.setLng(Optional.ofNullable(trajectory.getLng()).orElse(""));
        return pointVO;
    }
}
