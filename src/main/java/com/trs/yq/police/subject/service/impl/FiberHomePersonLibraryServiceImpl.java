package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.trs.yq.police.subject.constants.enums.PersonAssociateAttributesEnum;
import com.trs.yq.police.subject.constants.enums.PersonRelationEnum;
import com.trs.yq.police.subject.domain.dto.PersonAssociateRelationDTO;
import com.trs.yq.police.subject.service.FiberHomePersonLibraryService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.UUID;

/**
 * 烽火通信人员主题库&要素关系库查询服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 */
@Slf4j
@Service
public class FiberHomePersonLibraryServiceImpl implements FiberHomePersonLibraryService {

    @Resource
    private RestTemplate restTemplate;

    private static final String GET_TOKEN_URL =
            "http://80.75.6.46:8080/restcloud/rest/core/auth/login";

    private static final String COMMON_QUERY_URL =
            "http://80.75.6.46:8080/restcloud/api/LZ/v1/data/search";

    @Override
    public PersonAssociateRelationDTO queryPersonLibrary(
            PersonAssociateAttributesEnum attributesEnum, String attributesValue) {
        PersonAssociateRelationDTO resultDTO = PersonAssociateRelationDTO.newInstance();
        JsonObject resultObject;
        try {
            resultObject =
                    commonQueryData(
                            new QueryAttributes(
                                    LibraryEnum.PERSON_LIBRARY, attributesEnum, attributesValue));
        } catch (Exception e) {
            log.error("查询烽火人员主题库出错", e);
            return resultDTO;
        }
        if (Objects.isNull(resultObject)) {
            return resultDTO;
        }
        log.info("FiberHome response body:{}", resultObject);
        try {
            resultDTO = fillUpDTOForPersonLibrary(resultObject);
        } catch (Exception e) {
            log.error("解析烽火人员主题库接口返回出错", e);
        }
        return resultDTO;
    }

    /**
     * 人员主题库填充结果DTO
     *
     * @param resultObject 接口查询返回的Json
     * @return DTO
     */
    private static PersonAssociateRelationDTO fillUpDTOForPersonLibrary(JsonObject resultObject) {
        JsonObject resourceInfo =
                resultObject
                        .getAsJsonObject("ResponseParam")
                        .getAsJsonArray("ResourceInfos")
                        .get(0)
                        .getAsJsonObject();
        PersonAssociateRelationDTO resultDTO = PersonAssociateRelationDTO.newInstance();
        if (resourceInfo.isJsonNull()) {
            return resultDTO;
        }
        JsonArray dataInfoOuterArray = resourceInfo.getAsJsonArray("DataInfo").getAsJsonArray();
        if (dataInfoOuterArray.size() <= 0) {
            return resultDTO;
        }
        JsonArray dataInfoArray = dataInfoOuterArray.get(0).getAsJsonArray();
        resultDTO.setRelation(
                PersonAssociateAttributesEnum.ID_NUMBER, PersonRelationEnum.OWNER, dataInfoArray.get(0).getAsString());
        PersonAssociateAttributesEnum attribute;
        PersonRelationEnum relation;
        for (int i = 1; i <= 20; i++) {
            switch (i) {
                case 1:
                    attribute = PersonAssociateAttributesEnum.CAR_NUMBER;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 2:
                    attribute = PersonAssociateAttributesEnum.PHONE_NUMBER;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 3:
                    attribute = PersonAssociateAttributesEnum.IMEI;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 4:
                    attribute = PersonAssociateAttributesEnum.IMSI;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 5:
                    attribute = PersonAssociateAttributesEnum.MAC;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 6:
                    attribute = PersonAssociateAttributesEnum.BANK_CARD;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 7:
                    attribute = PersonAssociateAttributesEnum.QQ;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 8:
                    attribute = PersonAssociateAttributesEnum.WECHAT;
                    relation = PersonRelationEnum.OWNER;
                    break;
                case 9:
                    attribute = PersonAssociateAttributesEnum.NICK_NAME;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 10:
                    attribute = PersonAssociateAttributesEnum.NAME;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 11:
                    attribute = PersonAssociateAttributesEnum.GENDER;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 12:
                    attribute = PersonAssociateAttributesEnum.NATION;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 13:
                    attribute = PersonAssociateAttributesEnum.FORMER_NAME;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 14:
                    attribute = PersonAssociateAttributesEnum.EDUCATION;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 15:
                    attribute = PersonAssociateAttributesEnum.MARITAL_STATUS;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 16:
                    attribute = PersonAssociateAttributesEnum.POLITICS_STATUS;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 17:
                    attribute = PersonAssociateAttributesEnum.RELIGIOUS_BELIEF;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 18:
                    attribute = PersonAssociateAttributesEnum.CURRENT_JOB;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 19:
                    attribute = PersonAssociateAttributesEnum.REGISTERED_RESIDENCE;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                case 20:
                    attribute = PersonAssociateAttributesEnum.CURRENT_RESIDENCE;
                    relation = PersonRelationEnum.PERSON_INNER_PARAM;
                    break;
                default:
                    continue;
            }
            String elementString = dataInfoArray.get(i).getAsString();
            if (i <= 9) {
                for (JsonElement jsonElement : new Gson().fromJson(elementString, JsonArray.class)) {
                    resultDTO.setRelation(attribute, relation, jsonElement.getAsString());
                }
            } else {
                resultDTO.setRelation(attribute, relation, elementString);
            }
        }
        return resultDTO;
    }

    @Override
    public PersonAssociateRelationDTO queryRelationLibrary(
            PersonAssociateAttributesEnum attributesEnum, String attributesValue) {
        PersonAssociateRelationDTO resultDTO = PersonAssociateRelationDTO.newInstance();
        JsonObject resultObject;
        try {
            resultObject =
                    commonQueryData(
                            new QueryAttributes(
                                    LibraryEnum.RELATION_LIBRARY, attributesEnum, attributesValue));
        } catch (Exception e) {
            log.error("查询烽火要素关系库出错", e);
            return resultDTO;
        }
        if (Objects.isNull(resultObject)) {
            return resultDTO;
        }
        try {
            resultDTO = fillUpDTOForRelationLibrary(resultObject);
        } catch (Exception e) {
            log.error("解析烽火要素关系库接口返回出错", e);
        }
        return resultDTO;
    }

    /**
     * 要素关系库填充结果DTO
     *
     * @param resultObject 接口查询返回的Json
     * @return DTO
     */
    private static PersonAssociateRelationDTO fillUpDTOForRelationLibrary(JsonObject resultObject) {
        JsonObject resourceInfo =
                resultObject
                        .getAsJsonObject("ResponseParam")
                        .getAsJsonArray("ResourceInfos")
                        .get(0)
                        .getAsJsonObject();
        PersonAssociateRelationDTO resultDTO = PersonAssociateRelationDTO.newInstance();
        if (resourceInfo.isJsonNull()) {
            return resultDTO;
        }
        JsonArray dataInfoArray = resourceInfo.getAsJsonArray("DataInfo");
        for (JsonElement jsonElement : dataInfoArray) {
            JsonArray infoArray = jsonElement.getAsJsonArray();
            resultDTO.setRelation(
                    PersonAssociateAttributesEnum.OTHERS,
                    translateRelType(infoArray.get(5).getAsString()),
                    infoArray.get(4).getAsString());
        }
        return resultDTO;
    }

    /**
     * 翻译关系类型
     *
     * @param relType 接口返回的关系类型
     * @return 关系类型中文
     */
    private static PersonRelationEnum translateRelType(String relType) {
        switch (relType) {
            case "B01":
                return PersonRelationEnum.SHACK_UP;
            case "B21":
                return PersonRelationEnum.RELATIVE;
            case "B20":
                return PersonRelationEnum.COLLEAGUE;
            case "B26":
                return PersonRelationEnum.SAME_CASE;
            case "B29":
                return PersonRelationEnum.SAME_PRISON_ROOM;
            case "B31":
                return PersonRelationEnum.VEHICLE_VIOLATION;
            case "B32":
                return PersonRelationEnum.LODGE;
            case "B42":
                return PersonRelationEnum.SAME_ORDER;
            case "B52":
                return PersonRelationEnum.SCHOOLMATE;
            case "B53":
                return PersonRelationEnum.GUARDIAN;
            case "B54":
                return PersonRelationEnum.RENTAL;
            default:
                return PersonRelationEnum.UNKNOWN;
        }
    }

    private static final String SENDER_ID = "TRS_YQ";

    private static final String GROUP_ID = "DSJJCZD";

    /**
     * 通用数据查询接口
     *
     * @param queryAttributes 作为查询参数的属性
     * @return 查询到的数据
     */
    private JsonObject commonQueryData(QueryAttributes queryAttributes) {
        HttpHeaders headers = new HttpHeaders();
        String token = getToken();
        log.info("token:{}", token);
        headers.set("identitytoken", token);
        headers.set("SenderID", SENDER_ID);
        headers.set("GroupID", GROUP_ID);
        headers.setContentType(MediaType.APPLICATION_JSON);
        String requestBody = buildRequestBody(queryAttributes);
        log.info("FiberHome request body:{}", requestBody);
        HttpEntity<String> httpEntity =
                new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> responseEntity =
                restTemplate.postForEntity(COMMON_QUERY_URL, httpEntity, String.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            return new Gson().fromJson(responseEntity.getBody(), JsonObject.class);
        }
        return null;
    }

    private static final String TARGET = "510500";

    /**
     * 构造请求参数
     *
     * @param queryAttributes 作为查询参数的属性
     * @return 请求参数json
     */
    private static String buildRequestBody(QueryAttributes queryAttributes) {
        JsonObject rootObject = new JsonObject();
        rootObject.addProperty("From", TARGET);
        rootObject.addProperty("To", TARGET);
        rootObject.addProperty("MessageSequence", UUID.randomUUID().toString());
        JsonObject requestParam = new JsonObject();
        requestParam.addProperty("Condition", buildCondition(queryAttributes));
        JsonObject otherCondition = new JsonObject();
        otherCondition.addProperty("AsyncBoolean", "0");
        requestParam.add("OtherCondition", otherCondition);
        JsonArray resourceInfos = new JsonArray();
        JsonObject resourceInfo = new JsonObject();
        resourceInfo.addProperty(
                "ResourceName", queryAttributes.getLibraryEnum().getResourceName());
        resourceInfo.add("DataItems", buildDataItems(queryAttributes.getLibraryEnum()));
        resourceInfos.add(resourceInfo);
        requestParam.add("ResourceInfos", resourceInfos);
        rootObject.add("RequestParam", requestParam);
        return rootObject.toString();
    }

    /**
     * 构造查询字段
     *
     * @param libraryEnum 要查询的库
     * @return 查询字段json数组
     */
    private static JsonArray buildDataItems(LibraryEnum libraryEnum) {
        if (libraryEnum.equals(LibraryEnum.PERSON_LIBRARY)) {
            JsonArray jsonArray = new JsonArray();
            for (String queryColumn : libraryEnum.getQueryColumn()) {
                JsonObject nameObject = new JsonObject();
                nameObject.addProperty("Name", queryColumn);
                jsonArray.add(nameObject);
            }
            return jsonArray;
        } else {
            return null;
        }
    }

    private static final String CONDITION_FORMAT = "%s=%s";

    /**
     * 构造查询条件
     *
     * @param queryAttributes 属性对象
     * @return 查询条件字符串
     */
    private static String buildCondition(QueryAttributes queryAttributes) {
        String condition = "";
        boolean isPersonLibrary =
                queryAttributes.getLibraryEnum().equals(LibraryEnum.PERSON_LIBRARY);
        switch (queryAttributes.getColumn()) {
            case ID_NUMBER:
                condition =
                        String.format(
                                CONDITION_FORMAT,
                                isPersonLibrary ? "IDNO" : "FIRST_VALUE",
                                queryAttributes.getValue());
                break;
            case PHONE_NUMBER:
                if (isPersonLibrary) {
                    condition =
                            String.format(CONDITION_FORMAT, "MOBILE", queryAttributes.getValue());
                }
                break;
            case BANK_CARD:
                if (isPersonLibrary) {
                    condition = String.format(CONDITION_FORMAT, "BANK", queryAttributes.getValue());
                }
                break;
            case IMSI:
                if (isPersonLibrary) {
                    condition = String.format(CONDITION_FORMAT, "IMSI", queryAttributes.getValue());
                }
                break;
            case MAC:
                if (isPersonLibrary) {
                    condition = String.format(CONDITION_FORMAT, "MAC", queryAttributes.getValue());
                }
                break;
            case IMEI:
                if (isPersonLibrary) {
                    condition = String.format(CONDITION_FORMAT, "IMEI", queryAttributes.getValue());
                }
                break;
            case CAR_NUMBER:
                if (isPersonLibrary) {
                    condition = String.format(CONDITION_FORMAT, "VEH", queryAttributes.getValue());
                }
                break;
            default:
                break;
        }
        return condition;
    }

    /**
     * 获取接口token凭证
     *
     * @return token值
     */
    private String getToken() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity =
                new HttpEntity<>(
                        ImmutableMap.of("userName", "tuoersi", "password", "tuoersi"), headers);
        ResponseEntity<String> responseEntity =
                restTemplate.postForEntity(GET_TOKEN_URL, entity, String.class);
        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            JsonNode responseJson = JsonUtil.parseJsonNode(responseEntity.getBody());
            if (Objects.isNull(responseJson)) {
                return null;
            }
            return responseJson.get("identitytoken").asText();
        }
        return null;
    }

    /**
     * 作为查询条件的属性
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class QueryAttributes {

        private LibraryEnum libraryEnum;

        private PersonAssociateAttributesEnum column;

        private String value;
    }

    /**
     * 可查询的库类型
     */
    @Getter
    @AllArgsConstructor
    public enum LibraryEnum {

        /**
         * 人员主题库
         * graphColumn
         */
        PERSON_LIBRARY(
                "R-************-********",
                new String[]{"IDNO", "VEH", "MOBILE", "IMEI", "IMSI", "MAC", "BANK", "QQ", "WECHAT", "ALIASNAME", "CHNAME", "SEX", "NATION", "USEDNAME", "EDUDEGREE", "MARR", "POLI", "RELI", "PROF", "HPLACE", "DOMPLACE"}),

        /**
         * 要素关系库
         */
        RELATION_LIBRARY("R-************-********", new String[]{}),
        ;

        private final String resourceName;

        /**
         * 要查询的字段
         */
        private final String[] queryColumn;
    }
}
