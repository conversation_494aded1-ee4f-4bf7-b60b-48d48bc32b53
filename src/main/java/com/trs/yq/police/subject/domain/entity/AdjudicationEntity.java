package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 裁决信息 T_PS_PERSON_JUDGEMENT表实体类
 *
 * <AUTHOR>
 * @since 2021/7/27
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_JUDGEMENT")
public class AdjudicationEntity extends BaseEntity {

    private static final long serialVersionUID = -4421056473255959472L;

    /**
     * 人员id
     */
    private String personId;
    /**
     * 裁决日期
     */
    private LocalDate judgementDate;
    /**
     * 截止日期
     */
    private LocalDate endDate;
    /**
     * 限制年限
     */
    private Integer limitTime;
    /**
     * 限制年限单位 YEARS/MONTHS
     */
    private String limitUnit;
    /**
     * 裁决原因
     */
    private String reason;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AdjudicationEntity that = (AdjudicationEntity) o;
        return personId.equals(that.personId) && Objects.equals(judgementDate, that.judgementDate) && Objects.equals(endDate, that.endDate) && Objects.equals(limitTime, that.limitTime) && Objects.equals(limitUnit, that.limitUnit) && Objects.equals(reason, that.reason);
    }

    @Override
    public int hashCode() {
        return Objects.hash(personId, judgementDate, endDate, limitTime, limitUnit, reason);
    }
}
