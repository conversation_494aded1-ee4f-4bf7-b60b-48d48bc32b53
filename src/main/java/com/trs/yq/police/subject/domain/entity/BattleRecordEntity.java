package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.domain.vo.BattleRecordCommandListVO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "T_BATTLE_RECORD")
public class BattleRecordEntity implements Serializable {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String detail;

    /**
     * 文件ID (多个用逗号隔开)
     */
    private String fileids;

    /**
     * 相关事件ID (多个用逗号隔开)
     */
    private String eventids;

    /**
     * 主题类别
     */
    private Short battletype;

    /**
     * 警种类别
     */
    private Short policetype;

    /**
     * 参与单位key
     */
    private String partunits;

    /**
     * 参与人key
     */
    private String partusers;

    /**
     * 主办单位key
     */
    private String hostunitkey;

    /**
     * 主办单位名称
     */
    private String hostunitname;

    /**
     * 主办人key
     */
    private String hostby;

    /**
     * 主办人姓名
     */
    private String hostname;

    /**
     * 创建单位key
     */
    private String unitkey;

    /**
     * 创建单位名称
     */
    private String unitname;

    /**
     * 创建人key
     */
    private String crtby;

    /**
     * 创建人姓名
     */
    private String crtbyname;

    /**
     * 创建时间
     */
    private LocalDateTime crttime;

    /**
     * 发布时间
     */
    private LocalDateTime publishtime;

    /**
     * 归档时间
     */
    private LocalDateTime archivetime;

    /**
     * 状态(1-待审核 2-进行中 3-已归档  4-已驳回)
     */
    private Integer state;

    /**
     * 指定审核单位
     */
    private String auditunitkey;

    /**
     * 指定审核单位名称
     */
    private String auditunitname;

    /**
     * 审核人ID
     */
    private String auditby;

    /**
     * 审核人姓名
     */
    private String auditbyname;

    /**
     * 审核结果(1-同意 2-驳回)
     */
    private Short auditresult;

    /**
     * 审核内容
     */
    private String auditcontent;

    /**
     * 审核时间
     */
    private LocalDateTime audittime;

    /**
     * 是否已删除
     */
    private Short isdelete;

    /**
     * 删除时间
     */
    private LocalDateTime deletetime;

    /**
     * 删除者ID
     */
    private String deleteby;

    /**
     * 档案ID
     */
    private String archiveid;

    /**
     * 修改后档案ID
     */
    private String editarchiveid;

    /**
     * 主题子类别
     */
    private String subtype;

    /**
     * 主案数据来源
     */
    private String srctable;

    /**
     * 主案标识字段值
     */
    private String keyval;

    /**
     * 主案分类代码
     */
    private String typecode;

    /**
     * 主案分类名称
     */
    private String typeval;

    /**
     * 涉案人ID
     */
    private String involverids;

    /**
     * 案事件扩展信息ID
     */
    private String eventexts;

    /**
     * 涉案人扩展信息ID
     */
    private String involverexts;

    /**
     * 线索ID
     */
    private String clueid;

    /**
     * 是否涉密
     */
    private Short issecret;

    /**
     * 关注人ID
     */
    private String focususerids;

    /**
     * 删除者姓名
     */
    private String deletebyname;

    /**
     * 置顶用户ID
     */
    private String stickuserids;

    /**
     * 布控信息主键
     */
    private String controlid;

    /**
     * 原合成ID（合并主题）
     */
    private String oldids;

    /**
     * 下一步审核单位代码(多个逗号隔开)
     */
    private String nextunitcode;

    /**
     * 下一步审核单位名称(多个逗号隔开)
     */
    private String nextunitname;

    /**
     * 下一步审核用户ID(多个逗号隔开)
     */
    private String nextuserid;

    /**
     * 下一步审核用户姓名(多个逗号隔开)
     */
    private String nextrealname;

    /**
     * 审核过的单位代码(多个逗号隔开)
     */
    private String auditedunitcode;

    /**
     * 审核过的用户ID(多个逗号隔开)
     */
    private String auditeduserid;

    /**
     * 原主合成ID
     */
    private String oldmainid;

    /**
     *
     * @param battleRecordEntity 合成作战信息
     * @return {@link BattleRecordCommandListVO}
     */
    public static BattleRecordCommandListVO of(BattleRecordEntity battleRecordEntity){
        BattleRecordCommandListVO battleRecordListVO = new BattleRecordCommandListVO();
        battleRecordListVO.setId(battleRecordEntity.getId());
        battleRecordListVO.setPublishTime(battleRecordEntity.getPublishtime());
        battleRecordListVO.setStatus(battleRecordEntity.getState());
        battleRecordListVO.setPublishDepartment(battleRecordEntity.getHostunitname());
        battleRecordListVO.setPublishPerson(battleRecordEntity.getHostname());
        battleRecordListVO.setTitle(battleRecordEntity.getTitle());
        return battleRecordListVO;
    }
}

