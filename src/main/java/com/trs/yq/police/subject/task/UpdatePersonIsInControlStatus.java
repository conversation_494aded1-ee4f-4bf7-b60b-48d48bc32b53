package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.domain.entity.VisitRecordEntity;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.repository.VisitRecordRepository;

import java.time.LocalDateTime;
import java.util.List;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2022/5/26 10:07
 */
@Component
@Slf4j
@ConditionalOnProperty(value = "com.trs.update.person.is_in_control_status.task.enable", havingValue = "true")
public class UpdatePersonIsInControlStatus {

    @Resource
    private PersonRepository personRepository;
    @Resource
    private VisitRecordRepository visitRecordRepository;

    /**
     * 定时任务
     */
    @Scheduled(cron = "${com.trs.update.person.is_in_control_status.task}")
    @Transactional(rollbackFor = RuntimeException.class)
    public void task() {
        log.info("开始更新人员在控状态：");
        updatePersonIsInControlStatus();
    }

    /**
     * 更新人员在控状态
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePersonIsInControlStatus() {
        final LocalDateTime now = LocalDateTime.now();
        personRepository.findAllBySubjectId("6").forEach(person -> {
            List<VisitRecordEntity> visitRecords = visitRecordRepository.findByPersonIdOrderByTimeDesc(person.getId());
            if (visitRecords.isEmpty()) {
                person.setIsInControl("0");
            } else if (visitRecords.get(0).getTime().isBefore(now.minusHours(48))) {
                person.setIsInControl("0");
            } else {
                person.setIsInControl("1");
            }
            personRepository.save(person);
        });
    }
}
