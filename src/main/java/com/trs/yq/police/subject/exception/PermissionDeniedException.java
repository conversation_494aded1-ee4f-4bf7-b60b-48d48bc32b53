package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Permission denied exception
 *
 * <AUTHOR>
 */
@ResponseStatus(value = HttpStatus.UNAUTHORIZED)
public class PermissionDeniedException extends RuntimeException {
    
    private static final long serialVersionUID = -602456516621783261L;

    /**
     * @param message 异常信息
     */
    public PermissionDeniedException(String message) {
        super(message);
    }

    /**
     * @param message 异常信息
     * @param cause   异常
     */
    public PermissionDeniedException(String message, Throwable cause) {
        super(message, cause);
    }

    protected PermissionDeniedException() {
        super();
    }
}
