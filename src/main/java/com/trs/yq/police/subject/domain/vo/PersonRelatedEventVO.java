package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.EventPersonRelationEntity;
import com.trs.yq.police.subject.repository.EventPersonRelationRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PersonRelatedEventVO extends RelatedEventVO {

    private static final long serialVersionUID = 2707927559241830949L;

    /**
     * 构造器
     *
     * @param eventEntity {@link EventEntity}
     * @param personId    人员id
     */
    public PersonRelatedEventVO(EventEntity eventEntity, String personId) {
        super(eventEntity);
        EventPersonRelationRepository eventPersonRelationRepository = BeanUtil.getBean(EventPersonRelationRepository.class);
        EventPersonRelationEntity relation = eventPersonRelationRepository.findByEventIdAndPersonId(eventEntity.getId(), personId);
        this.relationId = relation.getId();
    }

}
