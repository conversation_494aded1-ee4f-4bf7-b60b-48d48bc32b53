package com.trs.yq.police.subject.builder;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.ClueExtendEntity;
import com.trs.yq.police.subject.domain.entity.ClueLabelRelationEntity;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.trs.yq.police.subject.utils.StringUtil.getPoliceStationPrefix;

/**
 * <AUTHOR>
 * @date 2021/09/08
 */
public class ClueListPredicatesBuilder {
    /**
     * 创建模糊检索Predicates
     *
     * @param searchParams    模糊搜索参数
     * @param clueEntityRoot  群体记录
     * @param criteriaBuilder criteriaBuilder
     * @return 模糊检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildSearchPredicates(SearchParams searchParams, Root<ClueEntity> clueEntityRoot, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(searchParams.getSearchField()) && StringUtils.isNotBlank(searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            Predicate namePredicate = criteriaBuilder.like(clueEntityRoot.get("name").as(String.class), "%" + searchValue + "%");
            Predicate detailPredicate = criteriaBuilder.like(clueEntityRoot.get("detail").as(String.class), "%" + searchValue + "%");
            switch (searchField) {
                case "clueName":
                    predicates.add(namePredicate);
                    break;
                case "clueDetail":
                    predicates.add(detailPredicate);
                    break;
                case "fullText":
                    predicates.add(criteriaBuilder.or(namePredicate, detailPredicate));
                    break;
                default:
                    break;
            }
        }
        return predicates;
    }

    /**
     * 创建人员列表动态检索Predicates
     *
     * @param subjectId       专题id
     * @param filterParams    检索参数
     * @param root            线索实体
     * @param criteriaBuilder criteriaBuilder
     * @return 检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildListFilterPredicates(String subjectId, List<KeyValueVO> filterParams, Root<ClueEntity> root, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();

        // 联表查询专题和群体的关系
        predicates.add(criteriaBuilder.equal(root.get("subjectId").as(String.class), subjectId));

        //动态查询参数
        filterParams.forEach(kv -> {
            switch (kv.getKey()) {
                case "clueType":
                    predicates.add(getClueTypeRelationPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "clueSource":
                    predicates.add(getClueSourcePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "department":
                    predicates.add(getClueCreateDepartmentPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "timeParams":
                    predicates.add(getClueCreateTimePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "reportStatus":
                    predicates.add(getClueReportStatusPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "disposalStatus":
                    predicates.add(getClueDisposalStatusPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "clueActionMethod":
                    predicates.add(getClueActionMethodPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "clueActionBehaviour":
                    predicates.add(getClueActionBehaviourPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "occurrenceTime":
                    predicates.add(getClueOccurrenceTimePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                default:
                    break;
            }
        });

        return predicates;
    }

    /**
     * 创建线索维权方式查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueActionMethodPredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        Subquery<ClueExtendEntity> subQuery = criteriaBuilder.createQuery().subquery(ClueExtendEntity.class);
        Root<ClueExtendEntity> cluExtendEntityRoot = subQuery.from(ClueExtendEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(cluExtendEntityRoot.get("method"), value);
        Predicate predicate2 = criteriaBuilder.equal(cluExtendEntityRoot.get("clueId"), clueEntityRoot.get("id"));
        subQuery.select(cluExtendEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建线索维权行为查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueActionBehaviourPredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        Subquery<ClueExtendEntity> subQuery = criteriaBuilder.createQuery().subquery(ClueExtendEntity.class);
        Root<ClueExtendEntity> cluExtendEntityRoot = subQuery.from(ClueExtendEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(cluExtendEntityRoot.get("behaviour"), value);
        Predicate predicate2 = criteriaBuilder.equal(cluExtendEntityRoot.get("clueId"), clueEntityRoot.get("id"));
        subQuery.select(cluExtendEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建线索维权时间查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueOccurrenceTimePredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
        if (timeParams != null) {
            Subquery<ClueExtendEntity> subQuery = criteriaBuilder.createQuery().subquery(ClueExtendEntity.class);
            Root<ClueExtendEntity> cluExtendEntityRoot = subQuery.from(ClueExtendEntity.class);
            Predicate p1 = criteriaBuilder.greaterThanOrEqualTo(cluExtendEntityRoot.get("occurrenceTime").as(LocalDateTime.class), timeParams.getBeginTime());
            Predicate p2 = criteriaBuilder.lessThanOrEqualTo(cluExtendEntityRoot.get("occurrenceTime").as(LocalDateTime.class), timeParams.getEndTime());
            Predicate p3 = criteriaBuilder.equal(cluExtendEntityRoot.get("clueId"), clueEntityRoot.get("id"));
            subQuery.select(cluExtendEntityRoot).where(criteriaBuilder.and(p1, p2, p3));
            return criteriaBuilder.exists(subQuery);
        }
        return null;
    }

    /**
     * 创建线索处置状态查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueDisposalStatusPredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(clueEntityRoot.get("disposalStatus").as(String.class), value);
    }

    /**
     * 创建线索上报状态来源查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueReportStatusPredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(clueEntityRoot.get("reportStatus").as(String.class), value);
    }

    /**
     * 创建线索来源查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueSourcePredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(clueEntityRoot.get("source").as(String.class), value);
    }

    /**
     * 创建群体创建时间查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueCreateTimePredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
        if (timeParams != null) {
            Predicate p1 = criteriaBuilder.greaterThanOrEqualTo(clueEntityRoot.get("crTime").as(LocalDateTime.class), timeParams.getBeginTime());
            Predicate p2 = criteriaBuilder.lessThanOrEqualTo(clueEntityRoot.get("crTime").as(LocalDateTime.class), timeParams.getEndTime());
            return criteriaBuilder.and(p1, p2);
        }
        return null;
    }

    /**
     * 创建群体创建部门查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueCreateDepartmentPredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.like(clueEntityRoot.get("crDeptCode").as(String.class), getPoliceStationPrefix(value) + "%");
    }

    /**
     * 创建线索类别查询条件
     *
     * @param clueEntityRoot  线索实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getClueTypeRelationPredicates(Root<ClueEntity> clueEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        Subquery<ClueLabelRelationEntity> subQuery = criteriaBuilder.createQuery().subquery(ClueLabelRelationEntity.class);
        Root<ClueLabelRelationEntity> clueTypeRelationEntityRoot = subQuery.from(ClueLabelRelationEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(clueTypeRelationEntityRoot.get("labelId"), value);
        Predicate predicate2 = criteriaBuilder.equal(clueTypeRelationEntityRoot.get("clueId"), clueEntityRoot.get("id"));
        subQuery.select(clueTypeRelationEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }
}
