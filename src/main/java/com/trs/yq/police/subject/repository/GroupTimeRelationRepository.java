package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.GroupTimeRelationEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/28 15:14
 */
@Repository
public interface GroupTimeRelationRepository extends BaseRepository<GroupTimeRelationEntity, String> {
    /**
     * 根据敏感时间节点id查询群体关联关系
     *
     * @param sensitiveTimeId 敏感时间节点id
     * @return {@link GroupTimeRelationEntity}
     */
    List<GroupTimeRelationEntity> findAllBySensitiveTimeId(String sensitiveTimeId);

    /**
     * 根据群体id查询敏感时间节点
     *
     * @param groupId 群体id
     * @return {@link GroupTimeRelationEntity}
     */
    List<GroupTimeRelationEntity> findAllByGroupId(String groupId);

    /**
     * 根据群体id 、敏感时间节点id查询关联关系
     *
     * @param sensitiveTimeId 敏感时间节点id
     * @param groupId         群体id
     * @return {@link GroupTimeRelationEntity}
     */
    GroupTimeRelationEntity findAllBySensitiveTimeIdAndGroupId(String sensitiveTimeId, String groupId);

    /**
     * 批量删除群体关联的敏感时间节点
     *
     * @param groupId 群体id
     */
    void deleteAllByGroupId(String groupId);
}
