package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 人员轨迹
 *
 * <AUTHOR>
 * @date 2021/5/7
 */
@Data
public class PersonTrajectoryVO implements Serializable {

    private static final long serialVersionUID = -4627318953857389292L;

    /**
     * id
     */
    private String id;
    /**
     * 预警来源
     */
    private String source;
    /**
     * 活动时间
     */
    private LocalDateTime time;
    /**
     * 活动场所
     */
    private String place;
    /**
     * 活动地点
     */
    private String address;
    /**
     * 活动经度
     */
    private String lng;
    /**
     * 活动纬度
     */
    private String lat;
    /**
     * 图片url
     */
    private List<String> imageUrls;
    /**
     * 详情
     */
    private String detail;
}
