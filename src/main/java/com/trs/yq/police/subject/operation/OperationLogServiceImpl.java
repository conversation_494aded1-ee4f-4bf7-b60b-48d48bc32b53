package com.trs.yq.police.subject.operation;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.DateTimeConstants;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.CompareUtil;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;

/**
 * 操作日志业务层
 *
 * <AUTHOR>
 * @date 2021/7/27 18:55
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class OperationLogServiceImpl implements OperationLogService {

    @Resource
    private OperationLogRepository operationLogRepository;
    @Resource
    private DictService dictService;
    @Resource
    private ModuleFieldRepository moduleFieldRepository;
    @Resource
    private ClueRepository clueRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private UserRepository userRepository;
    @Resource
    private EventRepository eventRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private SensitiveTimeRepository sensitiveTimeRepository;


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void recordOperationLog(OperationLog operationLog, Map<String, Object> paramsMap, String ipAddress) {
        // 获取当前用户
        final LoginUser currentUser = AuthHelper.getCurrentUser();

        final String primaryKey = operationLog.primaryKey();
        final OperateModule module = operationLog.module();

        OperationLogEntity entity = new OperationLogEntity();
        entity.setOperator(operationLog.operator());
        entity.setOperateModule(module);
        assert currentUser != null;
        entity.setCrBy(currentUser.getId());
        entity.setCrByName(currentUser.getRealName());
        entity.setCrByUsername(currentUser.getUserName());
        entity.setCrTime(LocalDateTime.now());
        entity.setCrDept(currentUser.getUnitName());
        entity.setCrDeptCode(currentUser.getUnitCode());
        entity.setOverview(operationLog.desc());
        entity.setTargetObjectType(operationLog.targetObjectType().getCode());
        entity.setTargetObjectId(extractKey(paramsMap, primaryKey));
        // 移除无用参数
        if (operationLog.needReturnVal()) {
            paramsMap.remove(primaryKey);
        }

        final String detailKey = operationLog.detailKey();


        TargetObjectTypeEnum typeEnum = operationLog.targetObjectType();
        switch (typeEnum) {
            case WARNING:
                break;
            case GROUP:
            case CLUE:
            case PERSON:
            default:
                entity.setDetail(generateAnnotationDetail(detailKey, paramsMap));
        }
        entity.setIpAddr(ipAddress);

        // 存储
        operationLogRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void recordOperationLog(OperationLogRecord operationLog) {
        // 记录日志
        final LoginUser currentUser = operationLog.getCurrentUser();

        final String primaryKey = operationLog.getPrimaryKey();
        final Operator operator = operationLog.getOperator();
        final OperateModule module = operationLog.getModule();
        final String newObj = operationLog.getNewObj();
        final String oldObj = operationLog.getOldObj();
        final TargetObjectTypeEnum type = TargetObjectTypeEnum.codeOf(operationLog.getTargetObjectType());

        OperationLogEntity entity = new OperationLogEntity();
        entity.setOperator(operator);
        entity.setOperateModule(module);
        entity.setCrTime(LocalDateTime.now());
        entity.setTargetObjectId(primaryKey);
        entity.setTargetObjectType(operationLog.getTargetObjectType());
        entity.setOverview(operationLog.getDesc());

        //系统推送预警/系统比对无登录用户，单独处理
        if (operator.equals(Operator.PUSH) || operator.equals(Operator.SMS) || operator.equals(Operator.UPDATE)) {
            entity.setCrByName("系统");
            entity.setCrBy("system");
        } else {
            entity.setCrBy(currentUser.getId());
            entity.setCrByName(currentUser.getRealName());
            entity.setCrByUsername(currentUser.getUserName());
            entity.setCrDept(currentUser.getUnitName());
            entity.setCrDeptCode(currentUser.getUnitCode());

            // 预警操作记录单独处理
            if (Objects.requireNonNull(type).equals(TargetObjectTypeEnum.WARNING)) {
                entity.setDetail(generateWarningDetail(operator, newObj));
            } else {
                //其他操作记录详情生成
                entity.setDetail(generateOperationDetail(operator, newObj, oldObj, module.getCode(), entity));
            }
        }

        // 存储
        operationLogRepository.save(entity);
    }

    /**
     * 注解默认操作详情处理
     *
     * @param detailKey 详情键值
     * @param paramsMap 参数映射
     * @return 操作详情
     */
    private String generateAnnotationDetail(String detailKey, Map<String, Object> paramsMap) {
        String detail = null;
        final String params = JsonUtil.toJsonString(paramsMap);
        if (!paramsMap.isEmpty()) {
            if (StringUtils.isBlank(detailKey)) {
                detail = params;
            } else {
                detail = JsonUtil.toJsonString(paramsMap.get(detailKey));
            }
        }
        return detail;
    }

    /**
     * 生成预警操作记录详情
     *
     * @param operator 操作枚举
     * @return 详情
     */
    private String generateWarningDetail(Operator operator, String newObj) {
        switch (operator) {
            case JUDGE:
                return recordJudgeDetail(newObj);
            case SMS:
                return recordSMSDetail(newObj);
            default:
                return StringUtils.EMPTY;
        }
    }

    /**
     * 生成操作详情
     *
     * @param operator     操作符
     * @param newObj       新数据
     * @param oldObj       旧数据
     * @param moduleCode   模块编号
     * @param operationLog 操作日志
     * @return 操作详情
     */
    private String generateOperationDetail(Operator operator, String newObj, String oldObj, String moduleCode, OperationLogEntity operationLog) {

        switch (operator) {
            case ADD:
            case UPLOAD:
            case REPORT:
            case DISPOSE:
                // 新增
                return recordAddOrDeleteDetail(newObj, moduleCode);
            case DELETE:
            case BATCH_DELETE:
                // 删除
                return recordAddOrDeleteDetail(oldObj, moduleCode);
            case EDIT:
            case EDIT_ACTIVE:
                // 编辑
                return recordEditDetail(newObj, oldObj, moduleCode);
            case RELATE:
            case DE_RELATE:
                // 关联
                return recordRelateDetail(oldObj, newObj, moduleCode, operationLog);
            default:
                // 默认不记录
                return Strings.EMPTY;
        }
    }

    /**
     * 生成预警研判记录详情
     *
     * @param newObj 实体信息
     * @return 记录详情
     */
    private String recordJudgeDetail(String newObj) {
        final WarningJudgeVO judgeVO = JsonUtil.parseObject(newObj, WarningJudgeVO.class);
        if (Objects.isNull(judgeVO)) {
            return StringUtils.EMPTY;
        }
        final String template = "'反馈结果：'+#result+'，反馈备注：'+#remark";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();
        context.setVariable("result", dictService.getDictEntityByTypeAndCode("ps_warning_judge_result", String.valueOf(judgeVO.getJudgeResult())).getName());
        context.setVariable("remark", judgeVO.getJudgeRemark());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成预警短信记录详情
     *
     * @param newObj 实体信息
     * @return 记录详情
     */
    private String recordSMSDetail(String newObj) {
        List<String> phoneList = JsonUtil.parseArray(newObj, String.class);
        String names = userRepository.findByMobilePhoneIn(phoneList).stream().map(UserEntity::getRealName).collect(Collectors.joining("、"));
        return "发送短信到" + names;
    }

    /**
     * 记录详细信息
     *
     * @param obj        实体信息
     * @param moduleCode 模块编号
     */
    private String recordAddOrDeleteDetail(String obj, String moduleCode) {
        Map<String, Object> detailMap = new LinkedHashMap<>();
        final Map<String, Object> objectMap = JsonUtil.parseMap(obj, Object.class);
        objectMap.forEach((k, v) -> Optional.ofNullable(moduleFieldRepository.findByModuleCodeAndFieldName(moduleCode, k))
                .ifPresent(field -> {
                    final Object val = generateVal(field, v);
                    if (Objects.nonNull(val)) {
                        detailMap.put(field.getFieldCnName(), val);
                    }
                }));
        return JsonUtil.toJsonString(detailMap);
    }

    /**
     * 记录编辑详情
     *
     * @param newObj     新实体信息
     * @param oldObj     旧实体信息
     * @param moduleCode 模块编号
     * @return 详情信息
     */
    private String recordEditDetail(String newObj, String oldObj, String moduleCode) {
        Map<String, Object> detailMap = new LinkedHashMap<>();
        final Map<String, Object> newMap = JsonUtil.parseMap(newObj, Object.class);
        final Map<String, Object> oldMap = JsonUtil.parseMap(oldObj, Object.class);
        oldMap.forEach((oldKey, oldVal) -> recordEditOldObj(moduleCode, detailMap, newMap, oldKey, oldVal));
        // 补充就旧值没有的属性
        newMap.keySet()
                .stream()
                .filter(key -> !oldMap.containsKey(key))
                .forEach(key -> Optional.ofNullable(moduleFieldRepository.findByModuleCodeAndFieldName(moduleCode, key))
                        .ifPresent(field -> recordEditNewObj(detailMap, field)));
        return JsonUtil.toJsonString(detailMap);
    }

    /**
     * 记录编辑时旧的信息
     *
     * @param moduleCode 模块编号
     * @param detailMap  详情
     * @param newMap     新实体
     * @param oldKey     旧属性名
     * @param oldVal     旧属性值
     */
    private void recordEditOldObj(String moduleCode, Map<String, Object> detailMap, Map<String, Object> newMap, String oldKey, Object oldVal) {
        if (CompareUtil.nonEquals(oldVal, newMap.get(oldKey))) {
            Optional.ofNullable(moduleFieldRepository.findByModuleCodeAndFieldName(moduleCode, oldKey))
                    .ifPresent(field -> {
                        final Object val = generateVal(field, oldVal);
                        if (Objects.nonNull(val)) {
                            detailMap.put(field.getFieldCnName(), val);
                        }
                    });
        }
    }

    /**
     * 记录编辑新的实体信息
     *
     * @param detailMap 日志详情
     * @param field     模块中需要记录的属性
     */
    private void recordEditNewObj(Map<String, Object> detailMap, ModuleFieldEntity field) {
        final Object val = generateVal(field, null);
        detailMap.put(field.getFieldCnName(), Objects.isNull(val) ? OPERATION_LOG_NULL_MARK : val);
    }

    /**
     * 记录关联详情
     *
     * @param oldObj       旧关联关系
     * @param newObj       新关联关系
     * @param moduleCode   模块
     * @param operationLog 操作日志
     * @return 关联详情
     */
    private String recordRelateDetail(String oldObj, String newObj, String moduleCode, OperationLogEntity operationLog) {
        // 对于关联而言，存在修改当前操作的情况
        if (StringUtils.isNotBlank(oldObj) && StringUtils.isBlank(newObj)) {
            // 旧的数据存在，则表示
            operationLog.setOperator(Operator.DE_RELATE);
            return recordAddOrDeleteDetail(oldObj, moduleCode);
        } else if (StringUtils.isNotBlank(newObj) && StringUtils.isBlank(oldObj)) {
            // 新的数据存在，则表示
            return recordAddOrDeleteDetail(newObj, moduleCode);
        } else {
            // 两次都有说明没有改动,只是更新了时间
            return Strings.EMPTY;
        }
    }

    /**
     * 生成中文的参数值
     *
     * @param field  参数名
     * @param oldVal 参数值
     * @return 中文参数值
     */
    private Object generateVal(ModuleFieldEntity field, Object oldVal) {

        final PersonFieldEnum fieldEnum = PersonFieldEnum.fieldOf(field.getFieldName());
        final TargetObjectTypeEnum typeEnum = TargetObjectTypeEnum.codeOf(field.getType());

        if (Objects.nonNull(fieldEnum) && Objects.nonNull(typeEnum)) {
            switch (typeEnum) {
                case PERSON:
                    // 提取人员特有的属性
                    return extractPersonFieldValue(fieldEnum, field.getDictType(), oldVal);
                case GROUP:
                    // 提取群体特有的属性
                    return extractGroupFieldValue(fieldEnum, field.getDictType(), oldVal);
                case CLUE:
                    // 提取线索特有的属性
                    return extractClueFieldValue(fieldEnum, field.getDictType(), oldVal);
                case EVENT:
                    // 提取事件特有的属性
                    return extractEventFieldValue(fieldEnum, field.getDictType(), oldVal);
                default:
                    break;
            }

        }

        if (Objects.nonNull(oldVal)) {
            // 处理字典类型数据
            final String dictType = field.getDictType();
            if (StringUtils.isNotBlank(dictType)) {
                return dictService.getDictEntityByTypeAndCode(dictType, String.valueOf(oldVal)).getName();
            }
        }

        return oldVal;
    }

    private Object extractEventFieldValue(PersonFieldEnum fieldEnum, String dictType, Object oldVal) {
        switch (fieldEnum) {
            case OCCURRENCE_TIME:
                return transformDateString(oldVal, true);
            case CREATE_TIME:
                return transformDateString(oldVal, false);
            case PERSON_ID:
                return transformPersonId(oldVal);
            case GROUP_ID:
                return transformGroupId(oldVal);
            case FILE_ID:
                return transformFileId(oldVal);
            case EVENT_TYPE:
                return transformIdNameVO(oldVal);
            case BEHAVIOUR:
                return transformBehaviour(oldVal, dictType);
            case EVENT_IMAGE:
                return transformEventImages(oldVal);
            default:
                if (Objects.nonNull(oldVal) && StringUtils.isNotBlank(dictType)) {
                    // 处理字典类型数据
                    return dictService.getDictEntityByTypeAndCode(dictType, String.valueOf(oldVal)).getName();
                }
                return oldVal;
        }
    }

    /**
     * 抽取线索属性值
     *
     * @param fieldEnum 属性枚举
     * @param dictType  字段类型
     * @param oldVal    旧值
     * @return 线索属性值
     */
    private Object extractClueFieldValue(PersonFieldEnum fieldEnum, String dictType, Object oldVal) {
        switch (fieldEnum) {
            case PERSON_ID:
                return transformPersonId(oldVal);
            case GROUP_ID:
                return transformGroupId(oldVal);
            case CLUE_TYPE:
                return transformCompareList(oldVal);
            case RELATED_PERSONS:
                return transformRelatedPersons(oldVal);
            case ATTACHMENTS:
                return transformAttachments(oldVal);
            case OCCURRENCE_TIME:
                return transformDateString(oldVal, true);
            default:
                break;
        }
        if (Objects.nonNull(oldVal) && StringUtils.isNotBlank(dictType)) {
            // 处理字典类型数据
            return dictService.getDictEntityByTypeAndCode(dictType, String.valueOf(oldVal)).getName();
        }
        return oldVal;
    }

    /**
     * 提取群体属性值
     *
     * @param fieldEnum 属性枚举
     * @param dictType  字段类型
     * @param oldVal    旧值
     * @return 群体属性
     */
    private Object extractGroupFieldValue(PersonFieldEnum fieldEnum, String dictType, Object oldVal) {
        switch (fieldEnum) {
            case GROUP_TYPE:
                return transformCompareList(oldVal);
            case GROUP_ID:
                return transformGroupId(oldVal);
            case EVENT_ID:
                return transformEventId(oldVal);
            case CLUE_ID:
                return transformClueId(oldVal);
            case PERSON_ID:
                return transformPersonId(oldVal);
            case SENSITIVE_TIME:
                return transformSensitiveTimeId(oldVal);
            default:
                break;
        }
        if (Objects.nonNull(oldVal) && StringUtils.isNotBlank(dictType)) {
            // 处理字典类型数据
            return dictService.getDictEntityByTypeAndCode(dictType, String.valueOf(oldVal)).getName();
        }
        return oldVal;
    }

    /**
     * 提取人员属性值
     *
     * @param fieldEnum 属性枚举
     * @param dictType  字典类型
     * @param oldVal    旧值
     * @return 人员属性值
     */
    private Object extractPersonFieldValue(PersonFieldEnum fieldEnum, String dictType, Object oldVal) {
        switch (fieldEnum) {
            case GENDER:
                // 处理性别
                return transformGender((String) oldVal);
            case LIMIT_UNIT:
                // 年 / 月
                return transformTimeUnit(oldVal);
            case MOVE_TYPE:
                // 流动类型
                return transformMoveVal((String) oldVal);
            case GROUPS:
            case LABELS:
            case TYPES:
                // 处理列表
                return transformCompareList(oldVal);
            case IMAGES:
                return transformCompareImageList(oldVal);
            case START_TIME:
            case JUDGEMENT_DATE:
            case END_DATE:
            case WORK_END_TIME:
            case WORK_BEGIN_TIME:
            case END_TIME:
            case BEGIN_TIME:
                return transformDateString(oldVal, true);
            case TIME:
            case OUT_OF_CONTROL_TIME:
            case MOVE_TIME:
            case HAPPEN_TIME:
                return transformDateString(oldVal, false);
            case GROUP_ID:
                return transformGroupId(oldVal);
            case CLUE_ID:
                return transformClueId(oldVal);
            case EVENT_ID:
                return transformEventId(oldVal);
            case ATTACHMENTS:
            case TOPOLOGY_GRAPH:
            case CALL_FILE:
            case CALL_TIME_FEATURE:
            case CALL_POSITION_TRACK:
                return transformFileIds(oldVal);
            default:
                break;
        }
        if (Objects.nonNull(oldVal) && StringUtils.isNotBlank(dictType)) {
            // 处理字典类型数据
            return dictService.getDictEntityByTypeAndCode(dictType, String.valueOf(oldVal)).getName();
        }
        return oldVal;
    }

    /**
     * 提取对比后的图片数组
     *
     * @param oldVal 旧值
     * @return 提取后的参数值
     */
    private Object transformCompareImageList(Object oldVal) {

        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<ImageVO> vos = JsonUtil.parseArray(JsonUtil.toJsonString(oldVal), ImageVO.class);

        if (vos.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return OPERATION_LOG_UPDATE_MESSAGE;
    }

    /**
     * 提取日期字符串
     *
     * @param oldVal 旧值
     * @param isDate 是否是天
     * @return 日期字符串
     */
    private Object transformDateString(Object oldVal, boolean isDate) {

        if (isDate) {
            return Objects.isNull(oldVal) ? OPERATION_LOG_UNTIL_NOW :
                    DateUtil.utcToLocalDate((Long) oldVal).format(DateTimeConstants.DATE_FORMATTER);
        } else {
            return Objects.isNull(oldVal) ? OPERATION_LOG_UNTIL_NOW :
                    DateUtil.utcToLocalDateTime((Long) oldVal).format(DateTimeConstants.DATE_TIME_FORMATTER);
        }
    }

    /**
     * 转化流动属性值
     *
     * @param oldVal 旧值
     * @return 属性值
     */
    private String transformMoveVal(String oldVal) {

        if (StringUtils.isBlank(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final String moveInCode = "1";
        final String moveInCn = "流入";
        final String moveOutCn = "流出";
        return StringUtils.equals(oldVal, moveInCode) ? moveInCn : moveOutCn;
    }

    /**
     * 转换时间单位
     *
     * @param oldVal 旧值
     * @return 时间单位
     */
    private String transformTimeUnit(Object oldVal) {

        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        return StringUtils.equalsIgnoreCase((String) oldVal, ChronoUnit.YEARS.name())
                ? OPERATION_LOG_TIME_UNIT_YEAR
                : OPERATION_LOG_TIME_UNIT_MONTH;
    }

    /**
     * 转化比较后的列表
     *
     * @param oldVal 旧值
     * @return 转换后的字符串
     */
    private String transformCompareList(Object oldVal) {

        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<IdNameVO> vos = JsonUtil.parseArray(
                JsonUtil.toJsonString(oldVal),
                IdNameVO.class);

        if (vos.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return vos
                .stream()
                .map(IdNameVO::getName)
                .collect(Collectors.joining(","));
    }

    /**
     * idNameVO 提取出name
     *
     * @param oldVal 旧值
     * @return 转换后的字符串
     */
    private String transformIdNameVO(Object oldVal) {
        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }
        final IdNameVO vo = JsonUtil.parseObject(JsonUtil.toJsonString(oldVal), IdNameVO.class);
        if (Objects.isNull(vo)) {
            return OPERATION_LOG_NULL_MARK;
        }
        return vo.getName();
    }

    /**
     * 涉事行为转换
     *
     * @param oldVal 旧值
     * @param dictType 码表
     * @return 转换结果
     */
    private String transformBehaviour(Object oldVal, String dictType) {
        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<String> behaviours = JsonUtil.parseArray(
                JsonUtil.toJsonString(oldVal),
                String.class);

        if (behaviours.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return behaviours
                .stream()
                .map(code -> dictService.getDictEntityByTypeAndCode(dictType, code).getName())
                .collect(Collectors.joining(","));
    }

    /**
     * 涉事照片转换
     *
     * @param oldVal 旧值
     * @return 转换结果
     */
    private String transformEventImages(Object oldVal) {
        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<String> images = JsonUtil.parseArray(
                JsonUtil.toJsonString(oldVal),
                String.class);

        if (images.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return images
                .stream()
                .map(image -> fileStorageRepository.findById(image).orElse(new FileStorageEntity()).getName())
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining(","));
    }

    /**
     * 转换性别
     *
     * @param tempVal 临时属性值
     * @return 中文性别
     */
    private String transformGender(String tempVal) {

        if (StringUtils.isBlank(tempVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final String femaleCode = "1";
        final String female = "女";
        final String male = "男";
        return tempVal.equals(femaleCode) ? female : male;
    }

    /**
     * 提取键值
     *
     * @param paramsMap 参数映射
     * @param key       键值
     * @return 获取键值
     */
    private String extractKey(Map<String, Object> paramsMap, String key) {
        if (paramsMap.containsKey(key)) {
            return (String) paramsMap.get(key);
        } else {
            final String params = JsonUtil.toJsonString(paramsMap);
            final JsonNode node = JsonUtil.parseJsonNode(params);
            assert node != null;
            return node.findValue(key).asText();
        }
    }

    /**
     * 转化线索id
     *
     * @param clueId 线索id
     * @return 线索名
     */
    private String transformClueId(Object clueId) {
        return clueRepository.findById((String) clueId).orElse(new ClueEntity()).getName();
    }

    /**
     * 转化人员id
     *
     * @param personId 人员id
     * @return 人员名称
     */
    private String transformPersonId(Object personId) {
        return personRepository.findById((String) personId).orElse(new PersonEntity()).getName();
    }

    /**
     * 转化群体id
     *
     * @param groupId 群体id
     * @return 群体名称
     */
    private String transformGroupId(Object groupId) {
        return groupRepository.findById((String) groupId).orElse(new GroupEntity()).getName();
    }

    /**
     * 转化敏感时间节点id
     *
     * @param sensitiveTimeId 敏感时间节点id
     * @return 群体名称
     */
    private String transformSensitiveTimeId(Object sensitiveTimeId) {
        return sensitiveTimeRepository.findById((String) sensitiveTimeId).orElse(new SensitiveTimeEntity()).getName();
    }

    /**
     * 转化文件id
     *
     * @param fileId 文件id
     * @return 群体名称
     */
    private String transformFileId(Object fileId) {
        return fileStorageRepository.findById((String) fileId).orElse(new FileStorageEntity()).getName();
    }

    /**
     * 转换事件人员主键
     *
     * @param eventId 事件主键
     * @return 事件标题
     */
    private String transformEventId(Object eventId) {
        return eventRepository.findById((String) eventId).orElse(new EventEntity()).getTitle();
    }

    /**
     * 获得被修改的关联关系id列表
     *
     * @param oldRelations 旧关联关系id列表
     * @param newRelations 新关联关系id列表
     * @return 结果列表
     */
    public static List<String> getModifiedRelations(List<String> oldRelations, List<String> newRelations) {
        List<String> allRelations = new ArrayList<>(oldRelations);
        allRelations.removeAll(newRelations);
        allRelations.addAll(newRelations);

        List<String> repeatedRelations = new ArrayList<>(oldRelations);
        repeatedRelations.retainAll(newRelations);

        allRelations.removeAll(repeatedRelations);
        return allRelations;
    }

    private String transformRelatedPersons(Object oldVal) {
        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<IdNumberNameVO> vos = JsonUtil.parseArray(
                JsonUtil.toJsonString(oldVal),
                IdNumberNameVO.class);

        if (vos.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return vos
                .stream()
                .map(IdNumberNameVO::getName)
                .collect(Collectors.joining(","));
    }

    private String transformAttachments(Object oldVal) {
        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<AttachmentVO> vos = JsonUtil.parseArray(
                JsonUtil.toJsonString(oldVal),
                AttachmentVO.class);

        if (vos.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return vos
                .stream()
                .map(AttachmentVO::getName)
                .collect(Collectors.joining(","));
    }

    private String transformFileIds(Object oldVal) {
        if (Objects.isNull(oldVal)) {
            return OPERATION_LOG_NULL_MARK;
        }

        final List<FileStorageEntity> vos = JsonUtil.parseArray((String) oldVal, String.class).stream()
            .map(id -> fileStorageRepository.findById(id).orElse(null))
            .filter(Objects::nonNull).collect(Collectors.toList());

        if (vos.isEmpty()) {
            return OPERATION_LOG_NULL_MARK;
        }

        return vos
            .stream()
            .map(FileStorageEntity::getName)
            .collect(Collectors.joining(","));
    }

}
