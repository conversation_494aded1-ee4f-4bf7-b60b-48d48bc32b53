package com.trs.yq.police.subject.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

/**
 * 基础持久层
 *
 * @param <T> 实体类类型
 * @param <K> 主键类型
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 9:52
 */
@NoRepositoryBean
public interface BaseRepository<T, K> extends JpaRepository<T, K>, JpaSpecificationExecutor<T> {
}
