package com.trs.yq.police.subject.excel;

import com.trs.yq.police.subject.constants.enums.CrjCzryFieldEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryFieldEnum;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Excel业务层接口
 *
 * <AUTHOR>
 * @date 2021/8/9 10:17
 */
public interface ExcelService {


    /**
     * 写至临时文件
     *
     * @param groupName     分组名
     * @param extensionName 扩展名
     * @param recordRows    记录行
     * @param columnMap     表头
     * @return 路径 [0] - groupName , [1] - 路径
     * @throws IOException 文件操作异常
     */
    String[] writeToTempFile(String groupName,
                             String extensionName,
                             List<Map<Integer, String>> recordRows,
                             Map<Integer, PersonFieldEnum> columnMap) throws IOException;


    /**
     * 写至临时文件
     *
     * @param groupName     分组名
     * @param extensionName 扩展名
     * @param recordRows    记录行
     * @param columnMap     表头
     * @return 路径 [0] - groupName , [1] - 路径
     * @throws IOException 文件操作异常
     */
    String[] writeCrjToTempFile(String groupName,
        String extensionName,
        List<Map<Integer, String>> recordRows,
        Map<Integer, CrjJwryFieldEnum> columnMap) throws IOException;

    /**
     * 写至临时文件
     *
     * @param groupName     分组名
     * @param extensionName 扩展名
     * @param recordRows    记录行
     * @param columnMap     表头
     * @return 路径 [0] - groupName , [1] - 路径
     * @throws IOException 文件操作异常
     */
    String[] writeCrjCzryToTempFile(String groupName,
        String extensionName,
        List<Map<Integer, String>> recordRows,
        Map<Integer, CrjCzryFieldEnum> columnMap) throws IOException;
}
