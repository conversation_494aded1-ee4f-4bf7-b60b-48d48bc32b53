package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.FootholdListRequestVO;
import com.trs.yq.police.subject.domain.vo.FootholdVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.service.FootholdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 落脚点地址接口
 *
 * <AUTHOR>
 * @since 2021/8/3
 */
@RestController
@RequestMapping("/person")
@Slf4j
@Validated
public class FootholdController {

    @Resource
    private FootholdService footholdService;

    /**
     * 分页查询落脚点信息列表
     * http://192.168.200.192:3001/project/4897/interface/api/129735
     *
     * @param personId           人员id
     * @param footholdListRequestVO 落脚点地址列表查询参数
     * @return 分页落脚点列表
     */
    @PostMapping("/{personId}/foothold/list")
    public PageResult<FootholdVO> getStayList(@PathVariable String personId, @Valid @RequestBody FootholdListRequestVO footholdListRequestVO) {
        return footholdService.getFootholdPageable(personId, footholdListRequestVO);
    }

    /**
     * 新增落脚点信息
     * http://192.168.200.192:3001/project/4897/interface/api/129835
     *
     * @param personId   人员id
     * @param footholdVO 落脚点信息
     */
    @PostMapping("/{personId}/foothold")
    public void addStay(@PathVariable String personId, @Valid @RequestBody FootholdVO footholdVO) {
        footholdService.addStay(personId, footholdVO);
    }

    /**
     * 修改落脚点信息
     * http://192.168.200.192:3001/project/4897/interface/api/129725
     *
     * @param personId   人员id
     * @param footholdVO 落脚点信息
     */
    @PutMapping("/{personId}/foothold")
    public void updateStay(@PathVariable String personId, @Valid @RequestBody FootholdVO footholdVO) {
        footholdService.updateStay(personId, footholdVO);
    }

    /**
     * 删除落脚点信息
     * http://192.168.200.192:3001/project/4897/interface/api/129730
     *
     * @param personId 人员id
     * @param id       落脚点信息id
     */
    @DeleteMapping("/{personId}/foothold")
    public void deleteStay(@PathVariable String personId, @NotBlank(message = "id不能为空！") String id) {
        footholdService.deleteStay(personId, id);
    }

}
