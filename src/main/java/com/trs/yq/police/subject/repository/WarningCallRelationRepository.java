package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningCallRelationEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 预警与话单的关联关系
 *
 * <AUTHOR>
 * @date 2021/9/9 9:33
 */
@Repository
public interface WarningCallRelationRepository extends BaseRepository<com.trs.yq.police.subject.domain.entity.WarningCallRelationEntity, String> {


    /**
     * 根据预警id查询预警电话关联
     *
     * @param warningId 预警id
     * @return 预警电话关联列表
     *
     * <AUTHOR>
     * @date 2021/09/09 16:45
     */
    List<WarningCallRelationEntity> findAllByWarningId(String warningId);
}
