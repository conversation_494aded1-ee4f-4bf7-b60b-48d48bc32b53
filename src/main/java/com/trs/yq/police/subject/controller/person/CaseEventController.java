package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.CaseEventVO;
import com.trs.yq.police.subject.service.CaseEventService;
import com.trs.yq.police.subject.service.PersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 案事件信息接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/person")
@Slf4j
public class CaseEventController {
    @Resource
    private CaseEventService caseEventService;
    @Resource
    private PersonService personService;

    /**
     * 获取人员案事件信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link CaseEventVO}
     * <AUTHOR>
     */
    @GetMapping("/{personId}/case")
    public List<CaseEventVO> getAllCaseEvent(@PathVariable String personId) {
        return caseEventService.getAllCaseEvent(personId);
    }

    /**
     * 增加该人员案事件信息
     *
     * @param personId    人员主键
     * @param caseEventVO 案事件信息
     * <AUTHOR>
     */
    @PostMapping("/{personId}/case")
    public void addCaseEvent(@PathVariable String personId, @RequestBody CaseEventVO caseEventVO) {
        personService.checkPersonExist(personId);
        caseEventService.addCaseEvent(personId, caseEventVO);
    }

}
