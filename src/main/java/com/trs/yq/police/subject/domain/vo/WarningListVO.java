package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.constants.DateTimeConstants;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.entity.WarningTypeEntity;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.WarningTypeRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.GeoUtil;
import lombok.Data;
import java.io.Serializable;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_WARNING_LEVEL;
import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_WARNING_STATUS;

/**
 * 预警列表VO
 *
 * <AUTHOR>
 * @date 2021/09/13
 */
@Data
public class WarningListVO implements Serializable {

    private static final long serialVersionUID = -4505047120963159203L;

    /**
     * 预警id
     */
    private String warningId;
    /**
     * 预警时间
     */
    private String warningTime;
    /**
     * 预警类别
     */
    private String warningType;
    /**
     * 预警内容
     */
    private String warningContent;
    /**
     * 预警来源
     */
    private String warningSource;
    /**
     * 预警状态
     */
    private String warningStatus;
    /**
     * 预警等级
     */
    private String warningLevel;
    /**
     * 显示类别
     */
    private String displayType;
    /**
     * 是否超期未签收
     */
    private Boolean isSignOverdue;

    /**
     * 所属区县
     */
    private String areaName;
    /**
     * 预警人员姓名
     */
    private String name;
    /**
     * 预警人员身份证号
     */
    private String idNumber;
    /**
     * 是否可以操作
     */
    private Boolean canOperate;

    /**
     * 人员类别
     */
    private String personType;

    /**
     * 创建vo
     *
     * @param warningEntity {@link WarningEntity}
     * @return {@link WarningListVO}
     */
    public static WarningListVO of(WarningEntity warningEntity) {
        WarningListVO vo = new WarningListVO();
        vo.setWarningId(warningEntity.getId());
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        vo.setWarningLevel(dictRepository.findByTypeAndCode(DICT_TYPE_WARNING_LEVEL, warningEntity.getWarningLevel()).getName());
        vo.setWarningSource(String.join("、", warningEntity.getWarningSource()));
        vo.setWarningTime(warningEntity.getWarningTime().format(DateTimeConstants.DATE_TIME_FORMATTER));
        vo.setWarningContent(warningEntity.getWarningDetails());
        vo.setWarningStatus(dictRepository.findByTypeAndCode(DICT_TYPE_WARNING_STATUS, warningEntity.getWarningStatus()).getName());
        WarningTypeRepository warningTypeRepository = BeanUtil.getBean(WarningTypeRepository.class);
        WarningTypeEntity warningType = warningTypeRepository.findById(warningEntity.getWarningType()).orElse(null);
        if (Objects.nonNull(warningType)) {
            vo.setWarningType(warningType.getCnName());
            vo.setDisplayType(warningType.getDisplayType());
            long minutesSinceWarningTime = Duration.between(warningEntity.getWarningTime(), LocalDateTime.now()).toMinutes();
            vo.setIsSignOverdue(minutesSinceWarningTime >= warningType.getSignTimeLimit());
        }
        String areaName = GeoUtil.getAreaName(warningEntity.getAreaCode());
        vo.setAreaName(areaName);
        return vo;
    }
}
