package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 标签属性表
 *
 * <AUTHOR>
 * @since 2021/12/10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_LABEL_ATTRIBUTES")
public class LabelAttributesEntity extends BaseEntity {
    private static final long serialVersionUID = -8155023717128621902L;
    /**
     * 标签id
     */
    private String labelId;
    /**
     * 属性名称
     */
    private String attributeName;
}
