package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.response.HandleFeedbackResponse;
import com.trs.yq.police.subject.domain.response.OccurredEventResponse;
import com.trs.yq.police.subject.domain.response.RiskDetailPersonResponse;
import com.trs.yq.police.subject.domain.response.RiskDetailResponse;
import com.trs.yq.police.subject.domain.vo.CameraVO;
import com.trs.yq.police.subject.domain.vo.CompositeDynamicVO;
import com.trs.yq.police.subject.domain.vo.GroupListItem;
import com.trs.yq.police.subject.domain.vo.MapInfoVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.ProcessMonitoringVO;
import com.trs.yq.police.subject.domain.vo.RiskJudgeVO;
import com.trs.yq.police.subject.service.VisualControlService;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 可视化管控接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/16 14:47
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/visual-control")
public class VisualControlController {

    @Resource
    private VisualControlService controlService;

    /**
     * 查询风险研判详情
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return 风险研判分页查询结果
     */
    @GetMapping("/riskAnalysis")
    public RiskDetailResponse getRiskDetail(@NotBlank String eventId, @NotNull Pageable pageable) {
        return controlService.getRiskDetail(eventId, pageable);
    }

    /**
     * 风险研判-基本信息查询
     * <p>
     * http://192.168.200.192:3001/project/3020/interface/api/129493
     *
     * @param eventId 事件id
     * @return 风险研判-基本信息
     */
    @GetMapping("/riskJudge")
    public RiskJudgeVO getRiskDetail(@NotBlank String eventId) {
        return controlService.getRiskJudgeInfo(eventId);
    }

    /**
     * 人员列表查询
     * <p>
     * http://192.168.200.192:3001/project/3020/interface/api/132553
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return 人员列表
     */
    @GetMapping("/riskJudge/person/list/{eventId}")
    public PageResult<RiskDetailPersonResponse> getRisDetailPersonList(@PathVariable String eventId,
        @NotNull Pageable pageable) {
        return controlService.getRisDetailPersonList(eventId, pageable);
    }

    /**
     * 查询发生事件 http://192.168.200.192:3001/project/3020/interface/api/132559
     *
     * @param eventId 事件id
     * @return 发生事件详情
     */
    @GetMapping("/event-occur/basic-info")
    public OccurredEventResponse getOccurredEvent(@NotBlank String eventId) {
        return controlService.getOccurredEvent(eventId);
    }

    /**
     * 查询处警信息 http://192.168.200.192:3001/project/3020/interface/api/132583
     *
     * @param eventId 事件id
     * @return 处警信息
     */
    @GetMapping("/police-feedback/basic-info")
    public List<HandleFeedbackResponse> getHandleFeedback(@NotBlank String eventId) {
        return controlService.getHandleFeedback(eventId);
    }

    /**
     * 获取事件预案等级 http://192.168.200.192:3001/project/3020/interface/api/132805
     *
     * @param eventId 事件id
     * @return 事件预案等级
     */
    @GetMapping("/plan-level")
    public String getEventLevel(@NotBlank(message = "eventId 不能为空！") String eventId) {
        return controlService.getEventLevel(eventId);
    }

    /**
     * http://192.168.200.192:3001/project/3020/interface/api/132799
     *
     * @param eventId 事件id
     * @return 事件处置进度
     */
    @GetMapping("/disposal-step")
    public Integer getEvenDisposalStep(@NotBlank(message = "eventId 不能为空！") String eventId) {
        return controlService.getEvenDisposalStep(eventId);
    }

    /**
     * 地图点位信息查询 http://192.168.200.192:3001/project/3020/interface/api/129460
     *
     * @param eventId 事件id
     * @return 地图点位信息信息
     */
    @GetMapping("/map-info")
    public MapInfoVO getMapInfo(@NotBlank(message = "eventId 不能为空！") String eventId) {
        return controlService.getMapInfo(eventId);
    }

    /**
     * 查询摄像头详情 http://192.168.200.192:3001/project/3020/interface/api/134837
     *
     * @param cameraId 摄像头id
     * @return {@link CameraVO}
     */
    @GetMapping("/video/camera/{cameraId}")
    public CameraVO getCameraInfo(@PathVariable String cameraId) {
        return controlService.getCameraInfo(cameraId);
    }

    /**
     * 查询事件流程监测 http://192.168.200.192:3001/project/3020/interface/api/133735
     *
     * @param eventId 事件id
     * @return {@link ProcessMonitoringVO}
     */
    @GetMapping("/process-monitoring")
    public List<ProcessMonitoringVO> getProcessMonitoring(@NotBlank(message = "eventId 不能为空！") String eventId) {
        return controlService.getProcessMonitoring(eventId);
    }

    /**
     * 查询事件流程监测 http://192.168.200.192:3001/project/3020/interface/api/133735
     *
     * @param eventId 事件id
     * @return {@link ProcessMonitoringVO}
     */
    @GetMapping("/composite-dynamic")
    public List<CompositeDynamicVO> getCompositeDynamic(@NotBlank(message = "eventId 不能为空！") String eventId) {
        return controlService.getCompositeDynamic(eventId);
    }

    /**
     * 查询群体列表 http://192.168.200.192:3001/project/3020/interface/api/133657
     *
     * @param eventId 事件id
     * @return {@link GroupListItem}
     */
    @GetMapping("/riskJudge/group/list/{eventId}")
    public List<GroupListItem> getGroupList(@PathVariable String eventId) {
        return controlService.getGroupList(eventId);
    }

}
