package com.trs.yq.police.subject.service;


import com.fasterxml.jackson.databind.JsonNode;

import com.trs.yq.police.subject.domain.vo.ExportParams;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 线索批量导出服务
 *
 * <AUTHOR>
 * @date 2021/09/09
 */
public interface ClueExcelService {
    /**
     * 根据传来的属性导出excel
     *
     * @param response  响应体
     * @param exportParams   {@link ExportParams}
     * @param subjectId 主题id
     * @throws IOException IO异常
     */
    void downLoadExcel(HttpServletResponse response,  ExportParams exportParams, String subjectId) throws IOException;

    /**
     * 获得不同专题下批量导出需要的属性
     *
     * @param subjectId 专题Id
     * @return 属性json
     */
    JsonNode getExportPropertyList(String subjectId);
}
