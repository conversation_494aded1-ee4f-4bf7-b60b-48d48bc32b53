package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.BankCardEntity;
import com.trs.yq.police.subject.domain.vo.BankCardVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.BankCardRepository;
import com.trs.yq.police.subject.service.BankCardService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.AUTOMATED;

/**
 * 银行卡信息业务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class BankCardServiceImpl implements BankCardService {


    @Resource
    private BankCardRepository bankCardRepository;
    @Resource
    private OperationLogHandler operationLogHandler;


    @Override
    public List<BankCardVO> getAllByPersonId(String personId) {
        List<BankCardEntity> list = bankCardRepository.findAllByPersonId(personId);
        List<BankCardVO> bankCardVOList = new ArrayList<>();
        for (BankCardEntity bankCardEntity : list) {
            BankCardVO bankCardVO = new BankCardVO();
            bankCardVO.setId(bankCardEntity.getId());
            bankCardVO.setUseType(Integer.parseInt(bankCardEntity.getUseType()));
            bankCardVO.setBankOfDeposit(bankCardEntity.getBankOfDeposit());
            bankCardVO.setBankCardNumber(bankCardEntity.getBankCardNumber());
            bankCardVO.setIsAutomated(bankCardEntity.getIsAutomated());
            bankCardVOList.add(bankCardVO);
        }
        return bankCardVOList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteOneById(String personId, String id) {

        final BankCardEntity bankCard = bankCardRepository.findById(id).orElse(null);
        if (Objects.isNull(bankCard)) {
            throw new ParamValidationException("银行卡信息不存在，请核实!");
        }

        if(StringUtils.equals(bankCard.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可删除");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.BANK_CARD)
                .oldObj(JsonUtil.toJsonString(bankCard))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除银行卡信息")
                .build();

        bankCardRepository.deleteById(id);

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addOne(String personId, BankCardVO bankCardVO) {
        BankCardEntity bankCard = new BankCardEntity();
        BeanUtil.copyPropertiesIgnoreNull(bankCardVO, bankCard);
        bankCard.setUseType(bankCardVO.getUseType().toString());
        bankCard.setPersonId(personId);
        bankCardRepository.save(bankCard);
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.BANK_CARD)
                .newObj(JsonUtil.toJsonString(bankCard))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增银行卡信息")
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateOne(BankCardVO bankCardVO, String personId) {

        final String id = bankCardVO.getId();
        final BankCardEntity bankCard = bankCardRepository.findById(id).orElse(null);

        if (Objects.isNull(bankCard)) {
            throw new ParamValidationException("银行卡信息不存在，请核实!");
        }

        if(StringUtils.equals(bankCard.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可修改");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.BANK_CARD)
                .oldObj(JsonUtil.toJsonString(bankCard))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑银行卡信息")
                .build();

        bankCard.setBankCardNumber(bankCardVO.getBankCardNumber());
        bankCard.setBankOfDeposit(bankCardVO.getBankOfDeposit());
        bankCard.setUseType(bankCardVO.getUseType().toString());
        bankCardRepository.save(bankCard);

        logRecord.setNewObj(JsonUtil.toJsonString(bankCard));

        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }
}
