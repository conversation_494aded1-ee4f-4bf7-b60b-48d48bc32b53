package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.domain.vo.CrjJwryDetailVO;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 17:27
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_JWRY_DETAIL")
public class CrjJwryDetailEntity extends BaseEntity{

    private String cnName;

    private String enName;

    /**
     * 名
     */
    private String enFirstName;
    /**
     * 姓
     */
    private String enLastName;

    private String gender;
    /**
     * 国家代码
     */
    private String gjdm;
    /**
     * 职业
     */
    private String profession;

    private String idType;

    private String idNumber;

    private Date birthday;

    /**
     * 签证信息
     */
    private String visaType;

    private String visaNumber;
    /**
     * 在华停留时间
     */
    private Date inChinaTime;
    /**
     * 入境时间
     */
    private Date entryTime;
    /**
     * 入境口岸
     */
    private String entryPort;

    private String liveAddress;

    private String workAddress;

    private String phone;
    /**
     * 转vo，码值不变
     *
     * @return {@link CrjJwryDetailVO}
     */
    public CrjJwryDetailVO toVoWithCode() {
        CrjJwryDetailVO detailVO = new CrjJwryDetailVO();
        BeanUtils.copyProperties(this, detailVO);
        detailVO.setGender(this.getGender());
        return detailVO;
    }
}
