package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.BattleConstants;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.entity.ModuleEntity;
import com.trs.yq.police.subject.domain.vo.ContentVO;
import com.trs.yq.police.subject.repository.BattleRepository;
import com.trs.yq.police.subject.repository.ModuleRepository;
import com.trs.yq.police.subject.repository.OperationLogRepository;
import com.trs.yq.police.subject.service.ModuleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 档案目录服务层
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@Service
public class ModuleServiceImpl implements ModuleService {
    @Resource
    private ModuleRepository moduleRepository;
    @Resource
    private OperationLogRepository operationLogRepository;
    @Resource
    private BattleRepository battleRepository;

    @Override
    public List<ContentVO> getArchiveContent(String subjectId, String type) {
        List<ModuleEntity> archives = moduleRepository.findAllBySubjectIdAndTypeWithoutArchive(subjectId, type);
        return archives.stream().map(archive -> new ContentVO(archive.getCnName(), archive.getId())).collect(Collectors.toList());
    }

    @Override
    public List<ContentVO> getPersonOperationLogContent(String personId, String subjectId) {

        final List<ModuleEntity> archives = moduleRepository.findAllBySubjectIdAndType(subjectId, TargetObjectTypeEnum.PERSON.getType());
        return archives.stream().map(archive -> {
            final String code = archive.getId();
            final int count = operationLogRepository.countAllByTargetObjectIdAndOperateModuleAndTargetObjectType(personId, OperateModule.codeOf(code), TargetObjectTypeEnum.PERSON.getCode());
            return new ContentVO(archive.getCnName(), count, code);
        }).collect(Collectors.toList());
    }

    @Override
    public List<ContentVO> getOperationLogContent(String id, String subjectId, TargetObjectTypeEnum type) {
        final List<ModuleEntity> archives = moduleRepository.findAllBySubjectIdAndType(subjectId, type.getType());
        return archives.stream().map(archive -> {
            final String code = archive.getId();
            int count = countOperationLog(archive.getId(), id, type);
            return new ContentVO(archive.getCnName(), count, code);
        }).collect(Collectors.toList());
    }

    private int countOperationLog(String module, String id, TargetObjectTypeEnum type) {
        switch (module) {
                //线索合成
            case BattleConstants.MODULE_CLUE_RECORD:
                return battleRepository.getRecord(id, BattleConstants.SRC_TABLE_CLUE).size();
                //事件合成
            case BattleConstants.MODULE_EVENT_RECORD:
                return battleRepository.getRecord(id, BattleConstants.SRC_TABLE_EVENT).size();
                //线索指令
            case BattleConstants.MODULE_CLUE_COMMAND:
                return battleRepository.getCommand(id, BattleConstants.SRC_TABLE_CLUE).size();
                //事件指令
            case BattleConstants.MODULE_EVENT_COMMAND:
                return battleRepository.getCommand(id, BattleConstants.SRC_TABLE_EVENT).size();
                //其他，正常统计
            default:
                return operationLogRepository.countAllByTargetObjectIdAndOperateModuleAndTargetObjectType(id, OperateModule.codeOf(module), type.getCode());
        }
    }

}
