package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.MobilityEntity;
import com.trs.yq.police.subject.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 流动信息分页结果，增加流入信息统计字段
 *
 * <AUTHOR>
 * @since 2021/8/5
 */
@Getter
@Setter
public class MobilityPageWithTotal {
    private int pageNumber;
    private int pageSize;
    private long total;
    private int totalIn;


    private List<MobilityVO> items;


    private MobilityPageWithTotal() {

    }

    /**
     * 流动信息分页添加流入总数
     *
     * @param page 流动信息分页
     * @param totalIn 流入总数
     * @return 含流入总数的分页信息
     */
    public static MobilityPageWithTotal of(Page<MobilityEntity> page, int totalIn) {

        final MobilityPageWithTotal pageResult = new MobilityPageWithTotal();
        List<MobilityVO> items = page.getContent().stream().map(i -> {
            MobilityVO vo = new MobilityVO();
            vo.setId(i.getId());
            vo.setMoveTime(DateUtil.dateTimeToUtc(i.getMoveTime()));
            vo.setMoveType(i.getMoveType());
            vo.setLocation(i.getLocation());
            vo.setNote(i.getNote());
            return vo;
        }).collect(Collectors.toList());
        pageResult.setItems(items);
        pageResult.setPageNumber(page.getNumber() + 1);
        pageResult.setTotal(page.getTotalElements());
        pageResult.setPageSize(page.getPageable().getPageSize());
        pageResult.setTotalIn(totalIn);

        return pageResult;
    }

}
