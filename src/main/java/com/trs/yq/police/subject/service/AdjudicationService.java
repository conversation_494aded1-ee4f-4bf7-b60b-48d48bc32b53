package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.AdjudicationVO;

import java.util.List;

/**
 * 裁决信息业务层接口
 *
 * <AUTHOR>
 */
public interface AdjudicationService {

    /**
     * 根据人员id列表查询裁决信息
     *
     * @param personId 人员id
     * @return 裁决信息列表
     */
    List<AdjudicationVO> getJudgement(String personId);

    /**
     * 新增裁决信息
     *
     * @param personId       人员id
     * @param adjudicationVO 裁决信息详情
     */
    void saveJudgement(String personId, AdjudicationVO adjudicationVO);

    /**
     * 修改裁决信息
     *
     * @param personId       人员id
     * @param adjudicationVO 裁决信息详情
     */
    void updateJudgement(String personId, AdjudicationVO adjudicationVO);

    /**
     * 根据裁决信息id删除
     *
     * @param personId 人员主键
     * @param id       裁决信息id
     */
    void deleteJudgement(String personId, String id);
}
