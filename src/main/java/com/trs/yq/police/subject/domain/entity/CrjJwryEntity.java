package com.trs.yq.police.subject.domain.entity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/29 14:38
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Table(name = "T_PS_CRJ_JWRY")
@Entity
public class CrjJwryEntity extends BaseEntity{

    private String recordId;

    private String idType;

    private String idNumber;

    private String gjdm;

    private String attachment;

    private String phone;

    private String address;

    private String registrationStatus;

    private String acceptor;

    private String reason;

    private String dispatchStatus;

    private Date createTime;

    private Date planLeaveTime;
    /**
     * 数据类型 1：境外人员住宿登记 2：旅店业
     */
    private Integer sourceType;
    /**
     * 入住时间
     */
    private Date checkinTime;
    /**
     * 退房时间
     */
    private Date leaveTime;
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        CrjJwryEntity that = (CrjJwryEntity) o;
        return getId() != null && Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
