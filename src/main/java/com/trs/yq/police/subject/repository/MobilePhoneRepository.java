package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.MobilePhoneEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人员电话号信息查询接口
 *
 * <AUTHOR>
 * @date 2021/7/28 9:29
 */
@Repository
public interface MobilePhoneRepository extends BaseRepository<MobilePhoneEntity, String> {
    /**
     * 查询人员电话号码信息
     *
     * @param personId 人员Id
     * @return 电话号码信息
     */
    List<MobilePhoneEntity> findByPersonId(String personId);

    /**
     * 通过人员与联系方式查询
     *
     * @param personId    人员id
     * @param phoneNumber 手机号
     * @return 手机号实体
     */
    @Query(nativeQuery = true, value = "select * from T_PS_PERSON_MOBILE_PHONE where PHONE_NUMBER=:phoneNumber and  PERSON_ID=:personId and rownum<2")
    MobilePhoneEntity findByPersonIdAndPhoneNumber(@Param("personId") String personId, @Param("phoneNumber") String phoneNumber);

    /**
     * 获取所有手机号
     *
     * @return 手机号
     */
    @Query("select m.phoneNumber from MobilePhoneEntity m")
    List<String> findAllMobilePhone();
}
