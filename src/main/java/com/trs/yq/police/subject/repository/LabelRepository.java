package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.LabelEntity;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 标签查询接口
 *
 * <AUTHOR>
 * @date 2021/7/31 18:10
 */
@Repository
public interface LabelRepository extends BaseRepository<LabelEntity, String> {

    /**
     * 通过专题id和人员id查询所有标签
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 所有相关的标签
     */
    @Query("select l from LabelEntity l left join PersonLabelRelationEntity plr on plr.labelId = l.id left join PersonEntity p on p.id = plr.personId where l.subjectId = :subjectId and p.id = :personId and l.module = 'person'")
    List<LabelEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId,
        @Param("subjectId") String subjectId);

    /**
     * 删除标签
     *
     * @param labelId 标签id
     */
    @Modifying
    void removeAllById(@Param("id") String labelId);

    /**
     * 分页查询标签
     *
     * @param subjectId 专题id
     * @param pageable  分页查询
     * @return 分页查询结果
     */
    @Query("select l from LabelEntity l where (:subjectId is null or :subjectId = l.subjectId) order by l.crTime desc, l.subjectId asc")
    Page<LabelEntity> findPage(@Param("subjectId") String subjectId, Pageable pageable);

    /**
     * 依据名称和主题查找标签
     *
     * @param name      姓名
     * @param subjectId 主题
     * @return 标签信息
     */
    LabelEntity findByNameAndSubjectId(@Param("name") String name, @Param("subjectId") String subjectId);

    /**
     * 查找当前专题所有标签
     *
     * @param subjectId 专题主键
     * @param module    模块
     * @return 标签信息
     */
    @Query("select l from LabelEntity l where l.subjectId=:subjectId and l.createType='0' and l.status = '1' and l.module = :module order by l.showOrder")
    List<LabelEntity> findAllBySubjectId(@Param("subjectId") String subjectId, @Param("module") String module);

    /**
     * 查找当前模块和专题所有标签
     *
     * @param module    模块
     * @param subjectId 专题主键
     * @return 标签信息
     */
    @Query("select l from LabelEntity l where l.subjectId=:subjectId and l.module = :module and l.createType='0' and l.status = '1' order by l.showOrder")
    List<LabelEntity> findAllByModuleAndSubjectId(@Param("module") String module, @Param("subjectId") String subjectId);

    /**
     * 查询人员类型对应人数
     *
     * @param subjectId 专题id
     * @return 统计结果
     */
    @Query(value = "SELECT " + "l.name AS personType," + "l.id AS personTypeId,"
        + "(SELECT  COUNT(1) FROM T_PS_PERSON_LABEL_RELATION plr WHERE plr.label_id=l.id AND plr.PERSON_ID IN "
        + "   (SELECT person_id FROM T_PS_PERSON_SUBJECT_RELATION psr WHERE psr.subject_id=l.SUBJECT_ID)) AS personCount "
        + "FROM T_PS_LABEL l " + "WHERE l.SUBJECT_ID = :subjectId", nativeQuery = true)
    List<Map<String, Object>> countPersonByTag(@Param("subjectId") String subjectId);

    /**
     * 根据身份证和专题查询人员类别
     *
     * @param idNumber  身份证号码
     * @param subjectId 专题id
     * @return 人员类别
     */
    @Query("select pt " + "from LabelEntity pt " + "left join PersonLabelRelationEntity ptr on ptr.labelId = pt.id "
        + "left join PersonEntity p on p.id = ptr.personId " + "where p.idNumber = ?1 " + "and pt.subjectId = ?2")
    List<LabelEntity> findAllByIdNumberAndSubjectId(String idNumber, String subjectId);

    /**
     * 按地区统计人员类别人数
     *
     * @param subjectId 专题id
     * @param areaCode  地区编码
     * @return 统计结果
     */
    @Query(nativeQuery = true, value = "select t1.id as personTypeId,t1.NAME as personType,"
        + "       (select count(t2.ID)" + "        from T_PS_PERSON t2"
        + "                 left join T_PS_PERSON_LABEL_RELATION t3 on t2.ID = t3.PERSON_ID"
        + "        where t3.LABEL_ID = t1.ID"
        + "          and  (:areaCode is null or INSTR(t2.AREA_CODE, :areaCode) = 1)) as personCount"
        + " from T_PS_LABEL t1" + " where t1.SUBJECT_ID = :subjectId")
    List<Map<String, Object>> countByAreaCodeAndSubjectId(@Param("subjectId") String subjectId,
        @Param("areaCode") String areaCode);

    /**
     * 根据群体Id和专题Id查询群体类别
     *
     * @param groupId   群体Id
     * @param subjectId 专题Id
     * @return 群体类型
     */
    @Query("SELECT t2 FROM GroupLabelRelationEntity t1 JOIN LabelEntity t2 ON t1.labelId=t2.id WHERE t1.groupId=:groupId and t2.subjectId=:subjectId")
    List<LabelEntity> findByGroupIdAndSubjectId(@Param("groupId") String groupId, @Param("subjectId") String subjectId);

    /**
     * 根据线索Id查询线索类别
     *
     * @param clueId 线索Id
     * @return 线索类型
     */
    @Query("SELECT t2 FROM ClueLabelRelationEntity t1 JOIN LabelEntity t2 ON t1.labelId=t2.id WHERE t1.clueId=:clueId and t2.module = 'clue'")
    List<LabelEntity> findByClueId(@Param("clueId") String clueId);

    /**
     * 根据事件id查询事件的类别
     *
     * @param eventId 事件id
     * @return 事件类别
     */
    @Query("SELECT t2 FROM EventLabelRelationEntity t1 JOIN LabelEntity t2 ON t1.labelId=t2.id WHERE t1.eventId=:eventId and t2.module = 'event'")
    LabelEntity findByEventId(@Param("eventId") String eventId);

    /**
     * 查询群体所有类别名称
     *
     * @param groupId 群体id
     * @return 类别名称
     */
    @Query("SELECT t2.name FROM GroupLabelRelationEntity t1 JOIN LabelEntity t2 ON t1.labelId=t2.id WHERE t1.groupId=:groupId")
    List<String> findNamesByGroupId(@Param("groupId") String groupId);
}
