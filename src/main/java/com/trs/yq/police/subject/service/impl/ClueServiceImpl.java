package com.trs.yq.police.subject.service.impl;


import com.google.common.collect.ImmutableMap;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.ClueListPredicatesBuilder;
import com.trs.yq.police.subject.constants.enums.*;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.OperationLogServiceImpl;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.ClueService;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.service.UserRoleService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.OperationLogConstants.*;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 线索服务层实现
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class ClueServiceImpl implements ClueService {

    public static final String JPG = ".jpg";
    public static final String JPEG = ".jpeg";
    public static final String PNG = ".png";
    private static final String CLUE_SRCTABLE = "system.t_ps_clue";
    /**
     * 线索上报状态:未上报
     */
    private static final String INITIAL_REPORT = "0";
    /**
     * 线索上报状态:待处置
     */
    private static final String INITIAL_DISPOSAL = "0";
    /**
     * 线索处置状态：处置中
     */
    private static final String UNDER_DISPOSAL = "1";

    private static final String REPORT_TO_MUNICIPAL_STABILITY_CLASS = "3";
    private static final String REPORT_TO_DISTRICT_STABILITY_CLASS = "1";
    private static final String DISTRICT_CODE_PREFIX = "5105";
    /**
     * 市局维稳专班
     */
    public static final String MUNICIPAL_STABILITY_CLASS = "sjwwzb";
    /**
     * 区县维稳专班
     */
    public static final String DISTRICT_STABILITY_CLASS = "qxwwzb";
    /**
     * 派出所维稳专干
     */
    public static final String POLICE_STABILITY_CLASS = "pcswwzg";
    @Resource
    private RemoteStorageService remoteStorageService;
    @Resource
    private ClueRepository clueRepository;
    @Resource
    private CluePersonRelationRepository cluePersonRelationRepository;
    @Resource
    private ClueFileRelationRepository clueFileRelationRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private ClueLabelRelationRepository clueLabelRelationRepository;
    @Resource
    private GroupClueRelationRepository groupClueRelationRepository;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private BattleRecordRepository battleRecordRepository;
    @Resource
    private BattleCommandRepository battleCommandRepository;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private ClueExtendRepository clueExtendRepository;

    @Override
    public ClueVO getClueBasicInfo(String clueId) {
        final ClueEntity clueEntity = clueRepository.findById(clueId).orElse(null);
        if (Objects.isNull(clueEntity)) {
            throw new NoSuchElementException("没有找到该条线索！");
        }

        final ClueVO vo = new ClueVO();
        vo.setClueId(clueEntity.getId());
        vo.setClueName(clueEntity.getName());
        vo.setClueSource(clueEntity.getSource());
        vo.setClueDetail(clueEntity.getDetail());
        vo.setEmergencyLevel(clueEntity.getEmergencyLevel());
        vo.setOpennessLevel(clueEntity.getOpennessLevel());
        vo.setSubjectId(clueEntity.getSubjectId());
        List<LabelEntity> clueTypes = labelRepository.findByClueId(clueId);
        vo.setClueTypes(
            clueTypes.stream().map(type -> new IdNameVO(type.getId(), type.getName())).collect(Collectors.toList()));
        List<PersonEntity> personEntities = personRepository.findAllByClueId(clueId);
        vo.setRelatedPersons(
            personEntities.stream().map(person -> new IdNumberNameVO(person.getIdNumber(), person.getName()))
                .collect(Collectors.toList()));
        vo.setCreateTime(clueEntity.getCrTime());
        vo.setCreateDeptName(clueEntity.getCrDept());
        vo.setReportStatus(clueEntity.getReportStatus());
        vo.setDisposalStatus(clueEntity.getDisposalStatus());
        ClueExtendEntity clueExtendEntity = clueExtendRepository.findByClueId(clueId).orElse(null);
        if (Objects.nonNull(clueExtendEntity)) {
            vo.setBehaviour(clueExtendEntity.getBehaviour());
            vo.setMethod(clueExtendEntity.getMethod());
            vo.setRelatedPlace(clueExtendEntity.getRelatedPlace());
            vo.setOccurrenceTime(clueExtendEntity.getOccurrenceTime());
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String addClue(ClueVO vo) {
        ClueEntity clueEntity = new ClueEntity();
        clueEntity.setName(vo.getClueName());
        clueEntity.setDetail(vo.getClueDetail());
        clueEntity.setOpennessLevel(vo.getOpennessLevel());
        clueEntity.setEmergencyLevel(vo.getEmergencyLevel());
        clueEntity.setSource(vo.getClueSource());
        clueEntity.setSubjectId(vo.getSubjectId());
        clueEntity.setReportStatus(INITIAL_REPORT);
        clueEntity.setDisposalStatus(INITIAL_DISPOSAL);
        clueRepository.save(clueEntity);
        //维稳专题扩展字段
        if (WW_SUBJECT.equals(vo.getSubjectId())) {
            clueEntity.setSource("0");
            ClueExtendEntity clueExtendEntity = clueExtendRepository.findByClueId(clueEntity.getId())
                .orElse(new ClueExtendEntity());
            clueExtendEntity.setClueId(clueEntity.getId());
            clueExtendEntity.setBehaviour(vo.getBehaviour());
            clueExtendEntity.setMethod(vo.getMethod());
            clueExtendEntity.setRelatedPlace(vo.getRelatedPlace());
            clueExtendEntity.setOccurrenceTime(vo.getOccurrenceTime());
            clueExtendRepository.save(clueExtendEntity);
        }

        //记录类型关联
        vo.getClueTypes().forEach(type -> {
            ClueLabelRelationEntity clueLabelRelationEntity = new ClueLabelRelationEntity();
            clueLabelRelationEntity.setClueId(clueEntity.getId());
            clueLabelRelationEntity.setLabelId(type.getId());
            clueLabelRelationRepository.save(clueLabelRelationEntity);
        });

        //记录人员关联
        vo.getRelatedPersons().forEach(person -> {
            CluePersonRelationEntity cluePersonRelationEntity = new CluePersonRelationEntity();
            PersonEntity personEntity = personRepository.findByIdNumber(person.getIdNumber());
            if (Objects.isNull(personEntity)) {
                PersonEntity p = new PersonEntity();
                p.setIdNumber(person.getIdNumber());
                p.setName(person.getName());
                p.setGender(StringUtil.parseGenderFromIdNumber(person.getIdNumber(), false));
                p.setAreaCode(person.getIdNumber().substring(0, 6));
                p.setControlStatus(ControlStatusEnum.IN_CONTROL.getCode());
                personRepository.save(p);
                cluePersonRelationEntity.setPersonId(p.getId());
            } else {
                cluePersonRelationEntity.setPersonId(personEntity.getId());
            }
            cluePersonRelationEntity.setClueId(clueEntity.getId());
            cluePersonRelationRepository.save(cluePersonRelationEntity);
        });

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.ADD)
            .module(OperateModule.CLUE_ARCHIVE_MANAGE)
            .newObj(JsonUtil.toJsonString(vo))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(clueEntity.getId())
            .desc("新增线索档案")
            .targetObjectType(OPERATION_LOG_TARGET_CLUE)
            .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }

        return clueEntity.getId();
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteOne(String clueId) {
        ClueEntity clueEntity = clueRepository.findById(clueId).orElse(null);
        if (Objects.isNull(clueEntity)) {
            throw new NoSuchElementException("没有找到该线索！");
        }
        final ClueVO clueVO = getClueBasicInfo(clueId);

        //删除线索
        clueRepository.deleteById(clueId);
        //删除线索和类别关系
        clueLabelRelationRepository.deleteAllByClueId(clueId);
        //删除线索和人员的关系
        cluePersonRelationRepository.deleteAllByClueId(clueId);
        //删除附件和附件关联
        clueFileRelationRepository.deleteAllByClueId(clueId);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.DELETE)
            .module(OperateModule.CLUE_BASIC_INFO)
            .oldObj(JsonUtil.toJsonString(clueVO))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(clueEntity.getId())
            .desc("删除线索档案")
            .targetObjectType(OPERATION_LOG_TARGET_CLUE)
            .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateOne(ClueVO clueVO) {
        String clueId = clueVO.getClueId();
        ClueEntity clueEntity = clueRepository.getById(clueId);
        ClueEntity oldVal = new ClueEntity();
        BeanUtil.copyPropertiesIgnoreNull(clueEntity, oldVal);

        final ClueVO oldVO = getClueBasicInfo(clueId);

        clueEntity.setName(clueVO.getClueName());
        clueEntity.setSource(clueVO.getClueSource());
        clueEntity.setEmergencyLevel(clueVO.getEmergencyLevel());
        clueEntity.setOpennessLevel(clueVO.getOpennessLevel());
        clueEntity.setDetail(clueVO.getClueDetail());
        if (WW_SUBJECT.equals(clueEntity.getSubjectId())) {
            ClueExtendEntity extendEntity = clueExtendRepository.findByClueId(clueEntity.getId())
                .orElse(new ClueExtendEntity());
            extendEntity.setClueId(clueId);
            extendEntity.setBehaviour(clueVO.getBehaviour());
            extendEntity.setMethod(clueVO.getMethod());
            extendEntity.setRelatedPlace(clueVO.getRelatedPlace());
            extendEntity.setOccurrenceTime(clueVO.getOccurrenceTime());
            clueExtendRepository.save(extendEntity);
        }
        clueRepository.save(clueEntity);

        clueLabelRelationRepository.deleteAllByClueId(clueId);
        clueVO.getClueTypes().forEach(type -> {
            ClueLabelRelationEntity relation = new ClueLabelRelationEntity();
            relation.setClueId(clueId);
            relation.setLabelId(type.getId());
            clueLabelRelationRepository.save(relation);
        });

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.EDIT)
            .module(OperateModule.CLUE_BASIC_INFO)
            .oldObj(JsonUtil.toJsonString(oldVO))
            .newObj(JsonUtil.toJsonString(clueVO))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(clueId)
            .desc("编辑线索档案")
            .targetObjectType(OPERATION_LOG_TARGET_CLUE)
            .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    private void saveFileTypeAndModule(ClueFileRelationEntity relation, AttachmentVO attachment) {
        String url = attachment.getUrl();
        if (url.endsWith(JPG) || url.endsWith(JPEG) || url.endsWith(PNG)) {
            relation.setType(FileTypeEnum.IMAGE.getCode());
            relation.setModule(ClueModuleEnum.CLUE_MESSAGE_PHOTO.getCode());
        } else {
            relation.setType(FileTypeEnum.OFFICE.getCode());
            relation.setModule(ClueModuleEnum.CLUE_MESSAGE_WORD.getCode());
        }
    }

    @Override
    public PageResult<ClueListVO> getClueList(String subjectId, ListRequestVO request) {
        Specification<ClueEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = ClueListPredicatesBuilder.buildListFilterPredicates(subjectId,
                    request.getFilterParams(), root, criteriaBuilder).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(request.getSearchParams())) {
                predicates.addAll(
                    ClueListPredicatesBuilder.buildSearchPredicates(request.getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        //排序
        final Pageable pageable = request.getPageParams().toPageable(Sort.by(Sort.Direction.DESC, "upTime"));

        Page<ClueEntity> clueList = clueRepository.findAll(specification, pageable);
        List<ClueListVO> items = clueList.getContent().stream()
            .map(ClueListVO::of)
            .collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), clueList.getTotalElements(),
            request.getPageParams().getPageSize());
    }


    @Override
    public PageResult<ClueRelatedPersonVO> getRelatedPersonList(String clueId, PageParams pageParams) {
        Page<PersonEntity> personEntities = personRepository.findAllByClueId(clueId, pageParams.toPageable());
        ClueEntity clueEntity = clueRepository.getById(clueId);
        List<ClueRelatedPersonVO> clueRelatedPersonVOList = personEntities.stream()
            .map(personEntity -> ClueRelatedPersonVO.of(personEntity, clueEntity))
            .collect(Collectors.toList());
        return PageResult.of(clueRelatedPersonVOList, pageParams.getPageNumber(), personEntities.getTotalElements(),
            pageParams.getPageSize());
    }

    @Override
    public PageResult<ClueRelatedGroupVO> getRelatedGroupList(String clueId, PageParams pageParams) {
        Page<GroupEntity> groupEntities = groupRepository.findAllByClueId(clueId, pageParams.toPageable());
        List<ClueRelatedGroupVO> items = groupEntities.getContent().stream()
            .map(group -> ClueRelatedGroupVO.of(group, clueId))
            .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), groupEntities.getTotalElements(),
            pageParams.getPageSize());
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteClues(List<String> clueIds) {
        clueIds.forEach(clueId -> {

            final ClueVO oldVo = getClueBasicInfo(clueId);

            remoteStorageService.deleteClueImageRelation(clueId, ClueModuleEnum.CLUE_MESSAGE_PHOTO, null);
            //移除线索-人员关联关系
            cluePersonRelationRepository.removeAllByClueId(clueId);
            //移除线索-群体关联关系
            groupClueRelationRepository.removeAllByClueId(clueId);

            final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.CLUE_BASIC_INFO)
                .oldObj(JsonUtil.toJsonString(oldVo))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(clueId)
                .desc("批量删除线索")
                .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                .build();

            // 记录操作
            operationLogHandler.publishEvent(logRecord);

        });

        //执行删除
        clueRepository.deleteAllById(clueIds);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupClueRelation(ClueGroupRelationVO clueGroupRelationVO) {
        String clueId = clueGroupRelationVO.getClueId();
        List<String> newRelatedGroupIds = clueGroupRelationVO.getGroupIds();
        List<String> oldRelatedGroupIds = groupClueRelationRepository.findAllByClueId(clueId)
            .stream().map(GroupClueRelationEntity::getGroupId)
            .collect(Collectors.toList());

        //旧值和新值的并集
        List<String> allRelatedGroupIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedGroupIds,
            newRelatedGroupIds);

        //处理线索-群体关联关系变更
        groupClueRelationRepository.deleteAllByClueId(clueId);
        newRelatedGroupIds.forEach(groupId -> {
            //判断是否已经存在关联关系
            GroupClueRelationEntity groupClueRelationEntity = new GroupClueRelationEntity();
            groupClueRelationEntity.setClueId(clueId);
            groupClueRelationEntity.setGroupId(groupId);
            groupClueRelationRepository.save(groupClueRelationEntity);
        });

        //存储操作记录
        allRelatedGroupIds.forEach(groupId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.RELATE)
                .module(OperateModule.CLUE_RELATED_GROUP)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(clueId)
                .desc("修改线索-群体关联")
                .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                .build();
            if (oldRelatedGroupIds.contains(groupId)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", groupId)));
            }
            if (newRelatedGroupIds.contains(groupId)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", groupId)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateCluePersonRelations(CluePersonRelationVO cluePersonRelationVO) {
        String clueId = cluePersonRelationVO.getClueId();
        List<String> newRelatedPersonIds = cluePersonRelationVO.getPersonIds();
        List<String> oldRelatedPersonIds = cluePersonRelationRepository.findAllByClueId(clueId).stream()
            .map(CluePersonRelationEntity::getPersonId)
            .collect(Collectors.toList());

        //旧值和新值的并集
        List<String> relatedPersonIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedPersonIds,
            newRelatedPersonIds);

        //处理线索-人员关联关系变更
        cluePersonRelationRepository.deleteAllByClueId(clueId);
        List<CluePersonRelationEntity> relations = newRelatedPersonIds.stream().map(personId -> {
            CluePersonRelationEntity cluePersonRelationEntity = new CluePersonRelationEntity();
            cluePersonRelationEntity.setClueId(clueId);
            cluePersonRelationEntity.setPersonId(personId);
            return cluePersonRelationEntity;
        }).collect(Collectors.toList());
        cluePersonRelationRepository.saveAll(relations);

        //存储操作记录
        relatedPersonIds.forEach(personId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.RELATE)
                .module(OperateModule.CLUE_RELATED_PERSON)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(clueId)
                .desc("修改线索-人员关联")
                .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                .build();

            if (oldRelatedPersonIds.contains(personId)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("personId", personId)));
            }
            if (newRelatedPersonIds.contains(personId)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("personId", personId)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void removePersonFromClue(ModuleEnum module, String relationId) {
        CluePersonRelationEntity relation = cluePersonRelationRepository.findById(relationId).orElse(null);
        if (Objects.isNull(relation)) {
            throw new NoSuchElementException("未找到该关系！");
        }

        cluePersonRelationRepository.deleteById(relationId);
        //存储操作记录
        final OperationLogRecord logRecord;
        if (module.equals(ModuleEnum.PERSON)) {
            logRecord = OperationLogRecord.builder()
                .operator(Operator.DE_RELATE)
                .module(OperateModule.PERSON_RELATED_CLUE)
                .oldObj(JsonUtil.toJsonString(ImmutableMap.of("clueId", relation.getClueId())))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(relation.getPersonId())
                .desc("删除人员-线索关联")
                .targetObjectType(OPERATION_LOG_TARGET_PERSON)
                .build();
        } else {
            logRecord = OperationLogRecord.builder()
                .operator(Operator.DE_RELATE)
                .module(OperateModule.CLUE_RELATED_PERSON)
                .oldObj(JsonUtil.toJsonString(ImmutableMap.of("personId", relation.getPersonId())))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(relation.getClueId())
                .desc("删除线索-人员关联")
                .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                .build();
        }
        // 记录操作
        operationLogHandler.publishEvent(logRecord);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void removeGroupFromClue(String relationId) {
        GroupClueRelationEntity relation = groupClueRelationRepository.findById(relationId).orElse(null);
        if (Objects.isNull(relation)) {
            throw new NoSuchElementException("未找到该关系！");
        }
        groupClueRelationRepository.deleteById(relationId);
        //存储操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.DE_RELATE)
            .module(OperateModule.CLUE_RELATED_GROUP)
            .oldObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", relation.getGroupId())))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(relation.getClueId())
            .desc("删除线索-群体关联")
            .targetObjectType(OPERATION_LOG_TARGET_CLUE)
            .build();
        // 记录操作
        operationLogHandler.publishEvent(logRecord);
    }

    @Override
    public List<ClueRelatedPersonVO> getPersonsFromClue(String clueId) {
        List<PersonEntity> personEntities = personRepository.findAllByClueId(clueId);
        ClueEntity clueEntity = clueRepository.getById(clueId);
        return personEntities.stream()
            .map(personEntity -> ClueRelatedPersonVO.of(personEntity, clueEntity))
            .collect(Collectors.toList());
    }

    @Override
    public List<ClueRelatedGroupVO> getGroupsFromClue(String clueId) {
        List<GroupEntity> groups = groupRepository.findAllByClueId(clueId);
        return groups.stream()
            .map(group -> ClueRelatedGroupVO.of(group, clueId))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteClueFileRelation(String clueId, String fileId) {
        clueFileRelationRepository.deleteByClueIdAndFileStorageId(clueId, fileId);

        //存储操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.DELETE)
            .module(OperateModule.CLUE_FILE)
            .oldObj(JsonUtil.toJsonString(ImmutableMap.of("fileId", fileId)))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(clueId)
            .desc("删除线索-文件关联")
            .targetObjectType(OPERATION_LOG_TARGET_CLUE)
            .build();
        // 记录操作
        operationLogHandler.publishEvent(logRecord);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addClueFileRelation(String clueId, List<AttachmentVO> attachmentVOList) {
        attachmentVOList.forEach(attachment -> {
            ClueFileRelationEntity clueFileRelationEntity = new ClueFileRelationEntity();
            clueFileRelationEntity.setClueId(clueId);
            clueFileRelationEntity.setFileStorageId(attachment.getId());
            saveFileTypeAndModule(clueFileRelationEntity, attachment);
            clueFileRelationRepository.save(clueFileRelationEntity);

            //存储操作记录
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.UPLOAD)
                .module(OperateModule.CLUE_FILE)
                .newObj(JsonUtil.toJsonString(attachment))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(clueId)
                .desc("上传线索材料")
                .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                .build();
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    public List<FileInfoVO> getClueFileInfo(String clueId) {
        List<FileStorageEntity> files = fileStorageRepository.findAllByClueId(clueId);
        return files.stream().map(FileInfoVO::of).collect(Collectors.toList());
    }

    @Override
    public List<BattleRecordCommandListVO> getBattleCommandList(String clueId) {
        return battleCommandRepository.findCommandById(CLUE_SRCTABLE, clueId)
            .stream()
            .map(BattleRecordCommandListVO::of)
            .collect(Collectors.toList());
    }

    @Override
    public List<BattleRecordCommandListVO> getBattleRecordList(String clueId) {
        return battleRecordRepository.findRecordById(CLUE_SRCTABLE, clueId)
            .stream()
            .map(BattleRecordEntity::of)
            .collect(Collectors.toList());

    }

    @Override
    public PageResult<DialogClueListVO> getDialogClueList(DialogClueListRequestVO request) {
        String subjectId = request.getOtherParams().getSubjectId();
        String clueTypeId = request.getOtherParams().getClueTypeId();
        String createDeptId = request.getOtherParams().getCreateDeptId();
        String searchValue =
            Objects.isNull(request.getSearchParams()) ? null : request.getSearchParams().getSearchValue();
        Page<ClueEntity> clueEntities = clueRepository.findAllBySubjectId(subjectId, clueTypeId,
            StringUtil.getPoliceStationPrefix(createDeptId), searchValue, request.getPageParams().toPageable());
        List<DialogClueListVO> items = clueEntities.stream()
            .map(DialogClueListVO::of)
            .collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), clueEntities.getTotalElements(),
            request.getPageParams().getPageSize());
    }

    @Override
    public ButtonVO getButton(String clueId) {
        ButtonVO buttonVO = new ButtonVO();
        buttonVO.setBattleRecord(true);
        buttonVO.setBattleCommand(true);
        buttonVO.setDisposalCompleted(true);
        buttonVO.setReport(true);
        buttonVO.setNotDispose(true);
        return buttonVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ReportInfoVO reportToSuperior(String clueId, LoginUser currentUser) {
        ClueEntity clueRecord = clueRepository.findById(clueId)
            .orElseThrow(() -> new ParamValidationException("线索不存在"));
        String reportStatus = clueRecord.getReportStatus();
        final String userId = currentUser.getId();
        final String crDeptCode = clueRecord.getCrDeptCode();
        final String departmentIdPrefix = StringUtil.getDepartmentIdPrefix(currentUser.getUnitCode());
        ReportInfoVO reportInfoVO = new ReportInfoVO();
        if (!crDeptCode.startsWith(departmentIdPrefix)) {
            reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
            reportInfoVO.setReportInfo("无法上报非本区县线索，请核实后操作！");
            return reportInfoVO;
        }
        switch (reportStatus) {
            case INITIAL_REPORT:
                // 创建 => 创建人员可以上报
                if (!userId.equalsIgnoreCase(clueRecord.getCrBy())) {
                    reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                    reportInfoVO.setReportInfo("上报失败,当前用户非创建用户,请核实后操作！");
                } else {
                    reportInfoVO = generateReportInfo(userId, departmentIdPrefix);
                    ClueEntity clue = clueRepository.findById(clueId)
                        .orElseThrow(() -> new ParamValidationException("线索不存在!"));
                    clue.setDisposalStatus(UNDER_DISPOSAL);
                    clue.setReportStatus(reportInfoVO.getReportStatus());
                    clueRepository.save(clue);

                    //记录操作日志
                    final OperationLogRecord logRecord = OperationLogRecord.builder()
                        .operator(Operator.REPORT)
                        .module(OperateModule.CLUE_ARCHIVE_MANAGE)
                        .newObj(JsonUtil.toJsonString(ImmutableMap.of("report", reportInfoVO.getReportInfo())))
                        .currentUser(AuthHelper.getCurrentUser())
                        .primaryKey(clueId)
                        .desc("线索上报")
                        .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                        .build();
                    if (Objects.nonNull(operationLogHandler)) {
                        // 转发操作日志至队列
                        operationLogHandler.publishEvent(logRecord);
                    }
                }
                return reportInfoVO;
            case REPORT_TO_DISTRICT_STABILITY_CLASS:
                // 区县维稳专班上报至区县指挥中心
                if (userRoleService.checkUserNotContainsRole(userId, DISTRICT_STABILITY_CLASS)) {
                    reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                    reportInfoVO.setReportInfo("上报失败。已报至区县维稳专班,非区县维稳专班不可继续上报!");
                } else {
                    clueRepository.updateStatusByClueId(clueId, REPORT_TO_MUNICIPAL_STABILITY_CLASS);
                    reportInfoVO.setIsSuccess(ReportInfoVO.SUCCESS);
                    reportInfoVO.setReportInfo("上报成功。已上报至区县指挥中心！");

                    //记录操作日志
                    final OperationLogRecord logRecord = OperationLogRecord.builder()
                        .operator(Operator.REPORT)
                        .module(OperateModule.CLUE_ARCHIVE_MANAGE)
                        .newObj(JsonUtil.toJsonString(ImmutableMap.of("report", "上报至区县指挥中心")))
                        .currentUser(AuthHelper.getCurrentUser())
                        .primaryKey(clueId)
                        .desc("线索上报")
                        .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                        .build();
                    if (Objects.nonNull(operationLogHandler)) {
                        // 转发操作日志至队列
                        operationLogHandler.publishEvent(logRecord);
                    }
                }
                return reportInfoVO;
            case REPORT_TO_MUNICIPAL_STABILITY_CLASS:
                // 市局维稳专班上报至市局指挥中心
                if (userRoleService.checkUserNotContainsRole(userId, MUNICIPAL_STABILITY_CLASS)) {
                    reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                    reportInfoVO.setReportInfo("已报至市局维稳专班,非市局维稳专班不可继续上报！");
                } else {
                    reportInfoVO.setIsSuccess(ReportInfoVO.SUCCESS);
                    reportInfoVO.setReportInfo("上报成功。已上报至市局指挥中心！");

                    //记录操作日志
                    final OperationLogRecord logRecord = OperationLogRecord.builder()
                        .operator(Operator.REPORT)
                        .module(OperateModule.CLUE_ARCHIVE_MANAGE)
                        .newObj(JsonUtil.toJsonString(ImmutableMap.of("report", "上报至市局指挥中心")))
                        .currentUser(AuthHelper.getCurrentUser())
                        .primaryKey(clueId)
                        .desc("线索上报")
                        .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                        .build();
                    if (Objects.nonNull(operationLogHandler)) {
                        // 转发操作日志至队列
                        operationLogHandler.publishEvent(logRecord);
                    }
                }
                return reportInfoVO;
            default:
                // 市局指挥中心不可上报
                reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                reportInfoVO.setReportInfo("上报失败。市局指挥中心不可上报！");
        }
        return reportInfoVO;
    }

    /**
     * 生成上报内容
     *
     * @param userId             用户id
     * @param departmentIdPrefix 部门代码前缀
     * @return 上报内容
     */
    public ReportInfoVO generateReportInfo(String userId, String departmentIdPrefix) {
        ReportInfoVO reportInfoVO = new ReportInfoVO();
        String reportStatus;
        if (DISTRICT_CODE_PREFIX.equals(departmentIdPrefix)) {
            // 市局人员上报至市局维稳专班
            reportStatus =  REPORT_TO_MUNICIPAL_STABILITY_CLASS;
            reportInfoVO.setIsSuccess(ReportInfoVO.SUCCESS);
            reportInfoVO.setReportInfo("上报成功。已上报至市局维稳专班！");
        } else {
            // 区县人员上报至区县维稳专班
            if (userRoleService.checkUserContainsRole(userId, DISTRICT_STABILITY_CLASS)) {
                // 区县专班人员直接上报至市局维稳专班
                reportStatus = REPORT_TO_MUNICIPAL_STABILITY_CLASS;
                reportInfoVO.setIsSuccess(ReportInfoVO.SUCCESS);
                reportInfoVO.setReportInfo("上报成功。已上报至区县指挥中心！");
            } else {
                // 区县普通人员上报至区县维稳专班
                reportStatus = REPORT_TO_DISTRICT_STABILITY_CLASS;
                reportInfoVO.setIsSuccess(ReportInfoVO.SUCCESS);
                reportInfoVO.setReportInfo("上报成功。已上报至区县维稳专班！");
            }
        }
        reportInfoVO.setReportStatus(reportStatus);
        return reportInfoVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void changeDisposeStatus(String clueId, String status) {
        ClueEntity clue = clueRepository.findById(clueId)
            .orElseThrow(() -> new ParamValidationException("线索不存在!"));
        clue.setDisposalStatus(status);
        clueRepository.save(clue);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.DISPOSE)
            .module(OperateModule.CLUE_ARCHIVE_MANAGE)
            .newObj(JsonUtil.toJsonString(ImmutableMap.of("dispose", status)))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(clueId)
            .desc("线索处置")
            .targetObjectType(OPERATION_LOG_TARGET_CLUE)
            .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }
}

