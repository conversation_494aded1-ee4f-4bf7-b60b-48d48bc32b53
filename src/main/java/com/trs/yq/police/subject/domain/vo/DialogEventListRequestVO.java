package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
public class DialogEventListRequestVO implements Serializable {

    private static final long serialVersionUID = 2067791717297570617L;

    /**
     * 其他参数
     */
    @Data
    public static class OtherParams implements Serializable {

        private static final long serialVersionUID = 1376306861312796016L;
        /**
         * 专题id
         */
        @NotBlank(message = "专题id不能为空！")
        private String subjectId;

        /**
         * 事件类别
         */
        private String eventTypeId;

        /**
         * 录入单位
         */
        private String createDeptId;
    }

    /**
     * 其他参数
     */
    private OtherParams otherParams;

    /**
     * 模糊检索参数
     */
    private SearchParams searchParams;

    /**
     * 分页
     */
    private PageParams pageParams;
}
