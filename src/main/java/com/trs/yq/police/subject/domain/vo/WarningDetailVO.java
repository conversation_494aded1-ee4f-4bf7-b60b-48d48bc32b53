package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.utils.GeoUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警详情
 *
 * <AUTHOR>
 * @since 2021/9/1
 */
@Data
public class WarningDetailVO implements Serializable {

    private static final long serialVersionUID = 1770862633158265905L;

    /**
     * 显示类型
     */
    private String displayType;
    /**
     * 预警类型
     */
    private String warningType;
    /**
     * 时间
     */
    private LocalDateTime warningTime;
    /**
     * 预警来源
     */
    private String warningSource;
    /**
     * 预警级别
     */
    private String warningLevel;
    /**
     * 预警内容
     */
    private String warningContent;
    /**
     * 预警电话 （仅限话单）
     */
    private String warningPhoneNumber;
    /**
     * 地点 (仅限单人、多人)
     */
    private String warningAddress;
    /**
     * 预警状态
     */
    private String warningStatus;

    /**
     * 所属区县
     */
    private String areaName;
    /**
     * 是否可以操作预警
     */
    private Boolean canOperate;
    /**
     * 是否可以操作预警
     */
    private String warningCarNumber;

    /**
     * 根据entity生成vo，只填写基础字段
     *
     * @param e entity
     * @return vo
     */
    public static WarningDetailVO of(WarningEntity e) {
        WarningDetailVO vo = new WarningDetailVO();
        vo.setWarningTime(e.getWarningTime());
        vo.setWarningLevel(e.getWarningLevel());
        vo.setWarningSource(String.join(",", e.getWarningSource()));
        vo.setWarningContent(e.getWarningDetails());
        vo.setWarningStatus(e.getWarningStatus());
        String areaName = GeoUtil.getAreaName(e.getAreaCode());
        vo.setAreaName(areaName);
        return vo;
    }
}
