package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.MobilePhoneVO;
import com.trs.yq.police.subject.service.MobilePhoneService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 电话信息接口类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:23
 */
@RestController
@RequestMapping("/person")
public class MobilePhoneController {
    @Resource
    private MobilePhoneService mobilePhoneService;

    /**
     * 查询人员手机号信息
     *
     * @param personId 人员id
     * @return 手机号信息
     */
    @GetMapping("/{personId}/mobile")
    public List<MobilePhoneVO> queryPhone(@PathVariable String personId) {
        return mobilePhoneService.findByPersonId(personId);
    }

    /**
     * 添加人员手机信息
     *
     * @param personId      人员id
     * @param mobilePhoneVO 人员手机信息
     */
    @PostMapping("/{personId}/mobile")
    public void savePhone(@PathVariable String personId, @RequestBody MobilePhoneVO mobilePhoneVO) {
        mobilePhoneService.savePhone(personId, mobilePhoneVO);

    }

    /**
     * 删除手机号信息
     *
     * @param personId 人员id
     * @param mobileId 电话号id
     */
    @DeleteMapping("/{personId}/mobile/{mobileId}")
    public void deletePhone(@PathVariable String personId, @PathVariable String mobileId) {
        mobilePhoneService.deletePhone(personId, mobileId);
    }

    /**
     * 修改手机号信息
     *
     * @param personId      人员id
     * @param mobilePhoneVO 手机号信息
     */
    @PutMapping("/{personId}/mobile")
    public void updatePhone(@NotBlank(message = "人员主键不可为空！") @PathVariable String personId, @RequestBody MobilePhoneVO mobilePhoneVO) {
        mobilePhoneService.updatePhone(personId, mobilePhoneVO);
    }
}
