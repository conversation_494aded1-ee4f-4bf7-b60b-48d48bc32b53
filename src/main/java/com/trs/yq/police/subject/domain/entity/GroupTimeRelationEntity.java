package com.trs.yq.police.subject.domain.entity;

import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 群体与敏感时间节点的关联
 *
 * <AUTHOR>
 * @date 2021/3/4
 */
@Entity
@Table(name = "t_group_time_relation")
@Getter
@Setter
public class GroupTimeRelationEntity implements Serializable {

    private static final long serialVersionUID = 3956005600204814940L;
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    private String groupId;

    private String sensitiveTimeId;
}
