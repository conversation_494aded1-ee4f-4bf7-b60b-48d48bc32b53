package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileTypeEnum;
import com.trs.yq.police.subject.domain.entity.CrjAccommodationEntity;
import com.trs.yq.police.subject.domain.vo.CrjAccommodationVO;
import com.trs.yq.police.subject.domain.vo.CrjAppAccommodationListVO;
import com.trs.yq.police.subject.domain.vo.CrjAppListRequestVO;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.CrjAccommodationRepository;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.domain.entity.CrjFileRelationEntity;
import com.trs.yq.police.subject.domain.entity.CrjVisitRecordEntity;
import com.trs.yq.police.subject.domain.request.AppVisitListRequest;
import com.trs.yq.police.subject.domain.vo.AppPersonalVisitListVO;
import com.trs.yq.police.subject.domain.vo.AppVisitListVO;
import com.trs.yq.police.subject.domain.vo.CrjVisitVO;
import com.trs.yq.police.subject.repository.CrjFileRelationRepository;
import com.trs.yq.police.subject.repository.CrjVisitRecordRepository;
import com.trs.yq.police.subject.service.CrjAppService;
import com.trs.yq.police.subject.utils.BeanUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 出入境app接口实现
 *
 * <AUTHOR>
 * @since 2021/10/18
 */
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class CrjAppServiceImpl implements CrjAppService {
    @Resource
    private CrjAccommodationRepository crjAccommodationRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private CrjVisitRecordRepository visitRepository;
    @Resource
    private CrjFileRelationRepository fileRelationRepository;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void createAccommodation(CrjAccommodationVO crjAccommodationVO) {
        CrjAccommodationEntity accommodationEntity = new CrjAccommodationEntity();
        BeanUtils.copyProperties(crjAccommodationVO, accommodationEntity);
        crjAccommodationRepository.save(accommodationEntity);
        CrjFileRelationEntity relation = new CrjFileRelationEntity(
                accommodationEntity.getId(),
                crjAccommodationVO.getImage().getImageId(),
                FileTypeEnum.IMAGE.getCode(),
                FileModuleEnum.CRJ_ACCOMMODATION_PHOTO.getCode());
        fileRelationRepository.save(relation);
    }

    @Override
    public CrjAccommodationVO getAccommodationInfo(String id) {
        CrjAccommodationEntity accommodationEntity = crjAccommodationRepository.findById(id).orElse(null);
        if (Objects.isNull(accommodationEntity)) {
            throw new ParamValidationException("该住宿信息不存在！请刷新重试");
        }
        CrjAccommodationVO crjAccommodationVO = new CrjAccommodationVO();
        BeanUtil.copyPropertiesIgnoreNull(accommodationEntity, crjAccommodationVO);
        crjAccommodationVO.setName(accommodationEntity.getFirstName() + " " + accommodationEntity.getLastName());
        List<ImageVO> images = (fileStorageRepository.findAllByCrjIdAndModule(accommodationEntity.getId(),
                        FileTypeEnum.IMAGE.getCode(),
                        FileModuleEnum.CRJ_ACCOMMODATION_PHOTO.getCode()).stream()
                .filter(Objects::nonNull)
                .map(ImageVO::of)
                .collect(Collectors.toList()));
        if (!images.isEmpty()) {
            crjAccommodationVO.setImage(images.get(0));
        }
        return crjAccommodationVO;
    }

    @Override
    public List<CrjAppAccommodationListVO> getAccommodationList(CrjAppListRequestVO crjAppListRequestVO) {
        LocalDateTime beginTime = crjAppListRequestVO.getTimeParams().getBeginTime();
        LocalDateTime endTime = crjAppListRequestVO.getTimeParams().getEndTime();
        return crjAccommodationRepository.getCrjAccommodationRecordList(beginTime, endTime, crjAppListRequestVO.getSearchValue())
                .stream()
                .map(entity -> {
                    CrjAppAccommodationListVO crjAppAccommodationListVO = new CrjAppAccommodationListVO();
                    crjAppAccommodationListVO.setName(entity.getFirstName() + " " + entity.getLastName());
                    crjAppAccommodationListVO.setRecordTime(entity.getCrTime());
                    crjAppAccommodationListVO.setId(entity.getId());
                    crjAppAccommodationListVO.setLivingInfo(entity.getLivingInfo());
                    List<ImageVO> images = (fileStorageRepository.findAllByCrjIdAndModule(entity.getId(),
                                    FileTypeEnum.IMAGE.getCode(),
                                    FileModuleEnum.CRJ_ACCOMMODATION_PHOTO.getCode()).stream()
                            .filter(Objects::nonNull)
                            .map(ImageVO::of)
                            .collect(Collectors.toList()));
                    if (!images.isEmpty()) {
                        crjAppAccommodationListVO.setImage(images.get(0));
                    }
                    return crjAppAccommodationListVO;
                }).collect(Collectors.toList());
    }



    @Override
    public List<AppVisitListVO> getVisitList(AppVisitListRequest request) {
        final String searchValue = request.getSearchValue();
        final LocalDateTime beginTime = request.getTimeParams().getBeginTime();
        final LocalDateTime endTime = request.getTimeParams().getEndTime();
        List<CrjVisitRecordEntity> entities = visitRepository.getAppVisitRecordList(searchValue, beginTime, endTime);
        return entities.stream().map(this::of).collect(Collectors.toList());
    }

    private AppVisitListVO of(CrjVisitRecordEntity entity) {
        AppVisitListVO vo = new AppVisitListVO();
        vo.setId(entity.getId());
        vo.setFirstName(entity.getFirstName());
        vo.setLastName(entity.getLastName());
        vo.setAddress(entity.getLivingInfo());
        vo.setTime(entity.getVisitTime());
        vo.setNationality(entity.getNationality());
        vo.setCertificateNumber(entity.getCertificateNumber());
        vo.setCertificateType(entity.getCertificateType());
        List<ImageVO> images = fileStorageRepository.findAllByCrjIdAndModule(entity.getId(),
                        FileTypeEnum.IMAGE.getCode(),
                        FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode())
                .stream()
                .filter(Objects::nonNull)
                .map(ImageVO::of)
                .collect(Collectors.toList());
        vo.setImages(images);
        return vo;
    }

    @Override
    public List<AppPersonalVisitListVO> getPersonalVisitList(String certificateNo, AppVisitListRequest request) {
        final String searchValue = request.getSearchValue();
        final LocalDateTime beginTime = request.getTimeParams().getBeginTime();
        final LocalDateTime endTime = request.getTimeParams().getEndTime();
        return visitRepository.getAppVisitByNumber(certificateNo, searchValue, beginTime, endTime)
                .stream()
                .map(AppPersonalVisitListVO::of)
                .collect(Collectors.toList());
    }

    @Override
    public CrjVisitVO getVisitDetail(String id) {
        CrjVisitRecordEntity entity = visitRepository.findById(id).orElse(null);
        if(Objects.isNull(entity)) {
            throw new ParamValidationException("走访信息不存在！");
        }
        CrjVisitVO visitVO = new CrjVisitVO();
        BeanUtil.copyPropertiesIgnoreNull(entity, visitVO);
        List<ImageVO> images = fileStorageRepository.findAllByCrjIdAndModule(entity.getId(),
                        FileTypeEnum.IMAGE.getCode(),
                        FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode())
                .stream()
                .filter(Objects::nonNull)
                .map(ImageVO::of)
                .collect(Collectors.toList());
        visitVO.setImages(images);
        return visitVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addVisit(CrjVisitVO crjVisitVO) {
        CrjVisitRecordEntity entity = new CrjVisitRecordEntity();
        BeanUtil.copyPropertiesIgnoreNull(crjVisitVO, entity);
        visitRepository.save(entity);
        crjVisitVO.getImages().forEach(imageVO -> {
            CrjFileRelationEntity relation = new CrjFileRelationEntity(
                    entity.getId(),
                    imageVO.getImageId(),
                    FileTypeEnum.IMAGE.getCode(),
                    FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode()
            );
            fileRelationRepository.save(relation);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateVisit(String id, CrjVisitVO crjVisitVO) {
        CrjVisitRecordEntity entity = visitRepository.findById(id).orElse(null);
        if(Objects.isNull(entity)) {
            throw new ParamValidationException("走访信息不存在！");
        }
        BeanUtil.copyPropertiesIgnoreNull(crjVisitVO, entity);
        visitRepository.save(entity);
        fileRelationRepository.deleteByCrjIdAndModule(id, FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode());
        crjVisitVO.getImages().forEach(imageVO -> {
            CrjFileRelationEntity relation = new CrjFileRelationEntity(
                    entity.getId(),
                    imageVO.getImageId(),
                    FileTypeEnum.IMAGE.getCode(),
                    FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode()
            );
            fileRelationRepository.save(relation);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteVisit(String id) {
        CrjVisitRecordEntity entity = visitRepository.findById(id).orElse(null);
        if(Objects.isNull(entity)) {
            throw new ParamValidationException("走访信息不存在！");
        }
        visitRepository.deleteById(id);
        fileRelationRepository.deleteByCrjIdAndModule(id, FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode());
    }
}
