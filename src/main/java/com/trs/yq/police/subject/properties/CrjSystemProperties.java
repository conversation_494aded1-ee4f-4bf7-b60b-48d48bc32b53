package com.trs.yq.police.subject.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 短信网关配置类
 *
 * <AUTHOR>
 * @date 2023/02/09
 */
@Component
@ConfigurationProperties(prefix = "com.trs.crj.system")
@Data
public class CrjSystemProperties {

    /**
     * 获取证书签名url
     */
    private String certUrl;

    /**
     * 网关webservice接口描述地址
     */
    private String wsdl;

    /**
     * 客户端ip
     */
    private String clientIp;

    /**
     * 客户端证书编号
     */
    private String clientZsbh;

    /**
     * 客户端单位编号
     */
    private String clientDwbh;

    /**
     * 客户端单位名称
     */
    private String clientDwmc;

    /**
     * 客户端姓名
     */
    private String clientXm;

    /**
     * 客户端身份证号
     */
    private String clientSfzh;


}
