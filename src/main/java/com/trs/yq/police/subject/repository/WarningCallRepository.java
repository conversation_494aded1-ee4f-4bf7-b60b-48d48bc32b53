package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningCallEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 话单预警持久层
 *
 * <AUTHOR>
 * @date 2021/9/9 9:32
 */
@Repository
public interface WarningCallRepository extends BaseRepository<WarningCallEntity, String> {

    /**
     * 根据预警id查询话单列表
     *
     * @param warningId 预警id
     * @return 话单列表
     */
    @Query("select c from WarningCallEntity c left join WarningCallRelationEntity cr on c.id = cr.callListId where cr.warningId = :warningId")
    List<WarningCallEntity> findAllByWarningId(@Param("warningId") String warningId);

    /**
     * 根据身份证号和预警id统计该人员此次预警与该电话号码的通联次数
     *
     * @param idNumber 身份证号
     * @param warningId 预警id
     * @return 通联次数
     */
    @Query(value = "select count (*) from t_ps_warning_call c LEFT JOIN T_PS_WARNING_CALL_RELATION r ON c.ID = r.CALL_LIST_ID where id_number = :idNumber AND r.WARNING_ID = :warningId", nativeQuery = true)
    Integer countByIdNumberAndWarningId(@Param("idNumber") String idNumber, @Param("warningId") String warningId);

    /**
     * 根据预警id查找预警电话
     *
     * @param warningId 预警id
     * @return 预警电话
     */
    @Query(value = "SELECT warning_number FROM T_PS_WARNING_CALL wc join T_PS_WARNING_CALL_RELATION wcr ON WC.ID = WCR.CALL_LIST_ID WHERE WARNING_ID = :warningId GROUP BY WARNING_NUMBER ", nativeQuery = true)
    String getWarningNumberByWarningId(@Param("warningId") String warningId);
}
