package com.trs.yq.police.subject.validation;

import com.trs.yq.police.subject.utils.StringUtil;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * 18位身份证号码验证
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = Identity.IdentityValidator.class)
public @interface Identity {

    /**
     * message
     *
     * @return {identity.invalid}
     */
    String message() default "{identity.invalid}";

    /**
     * groups
     *
     * @return {}
     */
    Class<?>[] groups() default {};

    /**
     * payload
     *
     * @return {}
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * IdentityValidator
     */
    class IdentityValidator implements ConstraintValidator<Identity, String> {

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            return StringUtil.checkIdNumber(value);
        }
    }
}
