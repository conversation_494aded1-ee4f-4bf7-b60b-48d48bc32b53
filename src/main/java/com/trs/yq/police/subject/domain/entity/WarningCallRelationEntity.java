package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2021/9/9 9:28
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_CALL_RELATION")
public class WarningCallRelationEntity implements Serializable {

    private static final long serialVersionUID = 3142212164593808423L;


    /**
     * 主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 预警id
     */
    private String warningId;

    /**
     * 话单id
     */
    private String callListId;

    /**
     * 入库时间
     */
    private LocalDateTime createTime;
}
