package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.VisitRecordVO;
import com.trs.yq.police.subject.service.VisitRecordService;
import com.trs.yq.police.subject.validation.RuleGroup;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 走访记录api接口
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@RestController
@RequestMapping("/person")
@Validated
public class VisitRecordController {

    @Resource
    private VisitRecordService visitRecordService;

    /**
     * 走访记录查询接口
     *
     * @param personId 人员id
     * @return 走访记录
     */
    @GetMapping("/{personId}/visit")
    public List<VisitRecordVO> getVisitRecords(@PathVariable String personId) {
        return visitRecordService.getVisitRecords(personId);
    }

    /**
     * 走访记录新增接口
     *
     * @param personId 人员id
     * @param vo       vo
     */
    @PostMapping("/{personId}/visit")
    public void addVisitRecord(@PathVariable String personId, @Validated(RuleGroup.Create.class) @RequestBody VisitRecordVO vo) {
        visitRecordService.addVisitRecord(personId, vo);
    }

    /**
     * 走访记录编辑接口
     *
     * @param personId 人员id
     * @param vo       vo
     */
    @PutMapping("/{personId}/visit")
    public void updateVisitRecord(@PathVariable String personId, @Validated(RuleGroup.Update.class) @RequestBody VisitRecordVO vo) {
        visitRecordService.updateVisitRecord(personId, vo);
    }

    /**
     * 走访记录删除接口
     *
     * @param personId 人员id
     * @param visitId  记录id
     */
    @DeleteMapping("/{personId}/visit")
    public void deleteVisitRecord(@PathVariable String personId, @NotBlank(message = "走访记录主键缺失") String visitId) {
        visitRecordService.deleteVisitRecord(personId, visitId);
    }
}

