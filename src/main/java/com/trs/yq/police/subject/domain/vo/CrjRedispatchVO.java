package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Collections;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 14:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrjRedispatchVO {

    @NotBlank
    private String deptCode;

    @NotBlank
    private String recordId;

    /**
     * 转DispatchVO
     *
     * @return {@link CrjDispatchVO}
     */
    public CrjDispatchVO toDispatchVO(){
        return new CrjDispatchVO(deptCode, Collections.singletonList(recordId));
    }

}
