package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 导出vo
 *
 * <AUTHOR>
 */
@Data
public class CzryExportVO {

    @ExcelIgnore
    private String id;
    /**
     * 姓名
     */
    @ExcelProperty("英文名")
    private String enName;
    /**
     * 姓名
     */
    @ExcelProperty("中文名")
    private String cnName;
    /**
     * 证件号码
     */
    @ExcelProperty("证件类型")
    private String certificateType;
    /**
     * 证件号码
     */
    @ExcelProperty("证件号码")
    private String certificateNumber;
    /**
     * 证件号码
     */
    @ExcelProperty("国家地区")
    private String country;
    /**
     * 证件号码
     */
    @ExcelProperty("性别")
    private String gender;
    /**
     * 证件号码
     */
    @ExcelProperty("出生日期")
    private String birthday;
    /**
     * 居留许可签发日期
     */
    @ExcelProperty("居留许可签发日期")
    private String visaSignDate;
    /**
     * 居留许可有效期
     */
    @ExcelProperty("居留许可有效期")
    private String visaValidity;
    /**
     * 居住状态
     */
    @ExcelProperty("居留许可状态")
    private String livingStatus;
    /**
     * 管辖部门
     */
    @ExcelProperty("所属区域")
    private String dept;
    /**
     * 详细地址
     */
    @ExcelProperty("居住地详细地址")
    private String address;
    /**
     * 证件号码
     */
    @ExcelProperty("签证(注)类别")
    private String visaType;
    /**
     * 证件号码
     */
    @ExcelProperty("签证(注)号码")
    private String visaNumber;
    /**
     * 入境时间
     */
    @ExcelProperty("入境日期")
    private String entryTime;
    /**
     * 证件号码
     */
    @ExcelProperty("入境事由")
    private String entryReason;
    /**
     * 证件号码
     */
    @ExcelProperty("居留事由")
    private String stayReason;
}
