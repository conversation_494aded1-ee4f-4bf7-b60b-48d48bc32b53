package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 线索文件关系实体
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CLUE_FILE_RELATION")
public class ClueFileRelationEntity extends BaseEntity {

    private static final long serialVersionUID = 4982454119440572595L;

    /**
     * 线索id
     */
    private String clueId;
    /**
     * 文件存储id
     */
    private String fileStorageId;
    /**
     * 文件类型 0 - 照片 ， 1 - 其他现场照片
     */
    private String type;
    /**
     * 模块
     */
    private String module;
    /**
     * 关联的记录主键
     */
    private String recordId;
}
