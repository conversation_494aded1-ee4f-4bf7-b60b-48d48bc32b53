package com.trs.yq.police.subject.conf;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * <AUTHOR>
 * @date 2021/07/26
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/file/getPhoto/{idNumber}");
        web.ignoring().antMatchers("/test/**");
        web.ignoring().antMatchers("/public/**");
        web.debug(false);
    }
}
