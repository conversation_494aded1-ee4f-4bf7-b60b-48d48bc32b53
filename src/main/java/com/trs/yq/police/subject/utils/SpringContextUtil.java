package com.trs.yq.police.subject.utils;

import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/**
 * Spring容器工具类
 *
 * <AUTHOR>
 * @date 2021/07/29
 */
@Component
public class SpringContextUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    /**
     * 设置上下文
     *
     * @param applicationContext 应用程序上下文
     */
    @Override
    public synchronized void setApplicationContext(@NonNull ApplicationContext applicationContext) {
        SpringContextUtil.applicationContext = applicationContext;
    }

    /**
     * 处理上下文更新
     *
     * @param event 更新事件
     */
    @EventListener
    public synchronized void handleContextRefresh(ContextRefreshedEvent event) {
        SpringContextUtil.applicationContext = event.getApplicationContext();
    }

    /**
     * 获取上下文
     *
     * @return 应用程序上下文
     */
    public static synchronized ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 通过名字获取上下文中的bean
     *
     * @param name bean的名称
     * @return bean
     */
    public static synchronized Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    /**
     * 通过类型获取上下文中的bean
     *
     * @param requiredType bean的类型
     * @param <T>          类型参数
     * @return bean
     */
    public static synchronized <T> T getBean(Class<T> requiredType) {
        return getApplicationContext().getBean(requiredType);
    }
}
