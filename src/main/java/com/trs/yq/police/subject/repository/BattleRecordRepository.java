package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BattleRecordEntity;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 合成作战
 *
 * <AUTHOR>
 * @date 2021/12/13 10:03
 */
@Repository
public interface BattleRecordRepository extends BaseRepository<BattleRecordEntity, String> {
    /**
     * 根据使事件资源表名和id查询合成作战信息(进行中、已归档)
     *
     * @param srcTable 事件资源表名
     * @param id       id
     * @return {@link BattleRecordEntity}
     */
    @Query(nativeQuery = true, value =
            "SELECT br.* FROM T_BATTLE_RECORD br WHERE " +
            " EXISTS (" +
            "   SELECT 1 FROM T_BATTLE_EVENT be " +
            "   WHERE be.SRCTABLE= :srcTable " +
            "   AND be.KEYNAME='ID' " +
            "   AND be.KEYVAL=:id " +
            "   AND INSTR(br.eventids,be.ID)> 0)" +
            "   AND br.STATE in (2, 3) " +
            "   AND (br.ISDELETE IS NULL OR br.ISDELETE != 1) ")
    List<BattleRecordEntity> findRecordById(@Param("srcTable") String srcTable, @Param("id") String id);


    /**
     * 查询事件相关的合成记录
     *
     * @param eventId 事件id
     * @return 合成记录
     */
    @Query(nativeQuery = true, value = "SELECT br.* FROM T_BATTLE_RECORD br WHERE " +
        "br.state=2 " +
        "AND EXISTS (" +
        "   SELECT 1 FROM T_BATTLE_EVENT be " +
        "   WHERE be.SRCTABLE='system.t_event' " +
        "   AND be.KEYNAME='ID' " +
        "   AND be.KEYVAL=:eventId " +
        "   AND INSTR(br.eventids,be.ID)> 0)" +
        "   And (br.isdelete<>1 or br.isdelete is null )")
    List<BattleRecordEntity> findByEventId(@Param("eventId") String eventId);
}
