package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ImportHistoryEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * 导入记录
 *
 * <AUTHOR>
 * @date 2021/8/9 11:45
 */
public interface ImportHistoryRepository extends BaseRepository<ImportHistoryEntity, String> {

    /**
     * 根据主题和原始文件主键查找失败记录
     *
     * @param subjectId        主题主键
     * @param initialHistoryId 原始文件
     * @param type             类型 0-初始，1-成功，2-失败
     * @return 记录
     */
    @Query("select ih from ImportHistoryEntity ih where ih.subjectId = :subjectId and ih.initialHistoryId = :initialHistoryId and ih.type = :type")
    ImportHistoryEntity findBySubjectIdAndInitialHistoryIdAndType(@Param("subjectId") String subjectId, @Param("initialHistoryId") String initialHistoryId, @Param("type") String type);

}
