package com.trs.yq.police.subject.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/27 11:14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarWarningExplainDTO {

    /**
     * 日平均行驶距离此维度对结果的影响贡献
     */
    private Double dailyAvgDrivingDistance;
    /**
     * 日平均行驶时长此维度对结果的影响贡献
     */
    private Double dailyAvgDrivingDuration;
    /**
     * 日平均行驶途径地点数此维度对结果的影响贡献
     */
    private Double dailyAvgRouteLocationNum;
    /**
     * 行驶天数覆盖率此维度对结果的影响贡献
     */
    private Double drivingDayCoverage;
    /**
     * 各时间段被抓拍到的覆盖率此维度对结果的影响贡献
     */
    private Double drivingDurationCoverage;
    /**
     * 参考值，前五个维度的值对比此值，若维度值大于参考值，表示这个维度跟运营车辆的此维度更相似，若维度值小于参考值，表示这个维度跟非运营车辆的此维度更相似
     */
    private Double referenceValue;
}
