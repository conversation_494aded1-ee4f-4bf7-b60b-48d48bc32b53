package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * [专题首页-政保] 区县统计人数VO
 *
 * <AUTHOR>
 * @date 2021/9/15 20:25
 */
@Data
public class DistributePersonCountVO implements Serializable {

    private static final long serialVersionUID = -3005201115229156494L;
    /**
     * 区域code
     */
    private String areaCode;
    /**
     * 地区名称
     */
    private String areaName;
    /**
     * 人员数量
     */
    private Integer personCount;

}
