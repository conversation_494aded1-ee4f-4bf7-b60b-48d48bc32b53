package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 与前端交互的异常信息
 *
 * <AUTHOR>
 * @date 2022/1/26 10:26
 */
@ResponseStatus(value = HttpStatus.OK)
public class InteractException extends RuntimeException {
    /**
     *
     * @param message 异常信息
     */
    public InteractException(String message){super(message);}
}
