package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.request.CountRequestVO;
import com.trs.yq.police.subject.domain.response.BasicCountResponseVO;
import com.trs.yq.police.subject.domain.response.CountDistributeMobilityResponseVO;
import com.trs.yq.police.subject.domain.response.MobilityCountResponseVO;
import com.trs.yq.police.subject.domain.vo.*;

import java.util.List;

/**
 * 数据视图业务层接口
 *
 * <AUTHOR>
 * @date 2021/9/3 20:03
 */
public interface DataViewService {

    /**
     * 获取基本数量
     *
     * @param request 请求参数
     * @return 响应参数
     */
    BasicCountResponseVO getBasicCount(CountRequestVO request);

    /**
     * 获取流动信息数量
     *
     * @param request 时间参数
     * @return 流动信息
     */
    MobilityCountResponseVO getMobilityCount(CountRequestVO request);

    /**
     * 区县分布情况
     *
     * @param request 分布情况请求
     * @return 分布情况结果
     */
    List<CountDistributeMobilityResponseVO> countDistributeMobility(CountRequestVO request);


    /**
     * 区县分布
     *
     * @param request 分布请求
     * @return 分布情况
     */
    List<CountDistributeResponseVO> countDistribute(CountRequestVO request);

    /**
     * 查找异常行为
     *
     * @param subjectId 专题id
     * @param status    预警状态
     * @return 异常预警行为列表
     */
    List<FindWarningBehaviorVO> getWarningBehavior(String subjectId, String status);


    /**
     * [专题首页-反恐]预警状态查询新流入的人员
     *
     * @param status 预警状态
     * @return {@link WarningScrollListVO}
     */
    WarningScrollListVO getNewPersonInWarningList(String status);

    /**
     * [专题首页] 预警轨迹查询
     *
     * @param warningId 专题id
     * @return 异常预警行为列表
     */
    List<WarningTrajectoryVO> findWarningTrajectory(String warningId);

    /**
     * 查询群体聚集预警列表
     *
     * @param warningTypeName 预警类型英文名
     * @param status          处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    WarningScrollListVO getGatherWarningList(String warningTypeName, String status);

    /**
     * 查询关注人员风险警情预警列表
     *
     * @param warningType 预警类型
     * @param status      处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    WarningScrollListVO getFocusPersonWarningList(String warningType, String status);

    /**
     * [专题首页-禁毒] 注销驾照人员驾车
     *
     * @param status 预警状态
     * @return 注销驾照人员驾车列表
     */
    WarningScrollListVO getDrugDriver(String status);

    /**
     * [专题首页-禁毒] 模型挖掘信息
     *
     * @param status 预警状态
     * @return 挖掘信息列表
     */
    WarningScrollListVO getMiningInformation(String status);

    /**
     * [专题首页-政保] 人员预警
     *
     * @param warnType 类型
     * @param status   预警状态
     * @return {@link WarningScrollListVO}
     */
    WarningScrollListVO getWarningPersonInfoList(String warnType, String status);


    /**
     * [专题首页-禁毒] 注销驾照人员驾车
     *
     * @param status 预警状态
     * @return {@link WarningScrollListVO}
     */
    WarningScrollListVO getTrafficPoliceDriver(String status);

    /**
     * [专题首页-政保] 获取人员预警
     *
     * @param status 预警状态
     * @return {@link WarningPersonCommonVO}
     */
    WarningScrollListVO getCriminalInvestigationVOList(String status);

    /**
     * [专题首页-刑侦] 出入泸预警查询
     *
     * @param warningType 预警类别
     * @param status      预警状态
     * @return {@link WarningScrollListVO}
     */
    WarningScrollListVO getWarningPersonInOrOutArea(String warningType, String status);


    /**
     * [专题首页-反恐] 预警轨迹
     *
     * @param warningId 预警id
     * @return {@link WarningTrajectoryVO}
     */
    List<WarningTrajectoryVO> findFkWarningTrajectory(String warningId);
}
