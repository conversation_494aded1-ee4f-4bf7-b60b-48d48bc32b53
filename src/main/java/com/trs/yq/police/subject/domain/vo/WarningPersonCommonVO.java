package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * [专题首页-政保/刑侦] 人员通用VO
 *
 * <AUTHOR>
 * @date 2021/9/15 11:57
 */
@Data
public class WarningPersonCommonVO implements Serializable {
    private static final long serialVersionUID = 6871565631352913646L;
    /**
     * 预警Id
     */
    private String warningId;
    /**
     * 人员id
     */
    private String personId;
    /**
     * 人员标签
     */
    private String warningTag;
    /**
     * 姓名
     */
    private String personName;
    /**
     * 头像
     */
    private List<ImageVO> images;
    /**
     * 预警等级
     */
    private String warningLevel;
    /**
     * 预警地点
     */
    private String warningPlace;
    /**
     * 预警时间
     */
    private LocalDateTime warningTime;
}
