package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Permission denied exception
 *
 * <AUTHOR>
 */
@ResponseStatus(value = HttpStatus.FORBIDDEN)
public class PermissionForbiddenException extends RuntimeException {
    
    private static final long serialVersionUID = -1089541960678662342L;

    /**
     * @param message 异常信息
     */
    public PermissionForbiddenException(String message) {
        super(message);
    }

    /**
     * @param message 异常信息
     * @param cause   异常
     */
    public PermissionForbiddenException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     *
     */
    protected PermissionForbiddenException() {
        super();
    }
}
