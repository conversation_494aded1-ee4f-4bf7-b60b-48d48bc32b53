package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.JqxxEntity;
import com.trs.yq.police.subject.domain.vo.BigScreenStatisticVO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 警情列表查询接口
 *
 * <AUTHOR>
 * @date 2022/01/18
 */
@Repository
public interface JqRepository extends BaseRepository<JqxxEntity, String> {

    /**
     * 警情top5
     *
     * @param type      警情类别
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link JqxxEntity}
     */
    @Query(nativeQuery = true, value = "select * from (select (select ct from jwzh_dict2 where zdbh = 'BD_D_JQLBDM' and dm = jqlbdm and rownum <= 1 ) as dimension,count(jqlbdm) as count from JWZH_ASJ_JQXX where BJSJ_RQSJ>:startTime and  BJSJ_RQSJ<:endTime and jqlbdm like concat(:type,'%')   group by jqlbdm order by count desc) where rownum<6")
    List<Map<String, Object>> getJqfxJjtop(@Param("type") String type,
                                           @Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);

    /**
     * 根据type获取警情数量
     *
     * @param type      警情类型
     * @param unitCode  部门code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = "select count(0) from JWZH_ASJ_JQXX where BJSJ_RQSJ>:startTime and  BJSJ_RQSJ<:endTime and  (:unitCode is null or instr(CJDW_GAJGJGDM,:unitCode) > 0) and jqlbdm like concat(:type,'%') ")
    Long getJqslByType(@Param("type") String type, @Param("unitCode") String unitCode,
                       @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 根据type获取刑事案件立案数量
     *
     * @param type      案件类型
     * @param unitCode  部门code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = "select count(0) from vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime  AND XT_ZXBZ = 0 and  (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0) and ajlbdm like concat(:type,'%') ")
    Long getXsajslByType(@Param("type") String type, @Param("unitCode") String unitCode,
                         @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 根据type获取行政案件立案数量
     *
     * @param type      案件类型
     * @param unitCode  部门code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = "select count(0) from VW_JWZH_XZAJ_AJ where slsj>:startTime and  slsj<:endTime  AND XT_ZXBZ = 0 and  (:unitCode is null or instr(BADW_GAJGJGDM,:unitCode) > 0) and (:type is null or ASJBH like concat(:type,'%'))  ")
    Long getXzajslByType(@Param("type") String type, @Param("unitCode") String unitCode,
                         @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 获取抢劫数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT count(0) FROM vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime and (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0) and ajlbdm='05000100'")
    Long getQjSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 获取抢夺数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT count(0) FROM vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime and  (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0) and ajlbdm='05000400'")
    Long getQdSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 获取盗窃车内财物数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT count(0) FROM vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime and (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0)   and ajlbdm in('05000200') and ajxlbdm in('050002000200')")
    Long getDqcncwSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                     @Param("endTime") LocalDateTime endTime);

    /**
     * 获取盗窃机动车数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT count(0) FROM vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime  and (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0)     and ajlbdm in('05000200') and ajxlbdm in('050002000600','050002000800')")
    Long getDqjdcSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                    @Param("endTime") LocalDateTime endTime);


    /**
     * 获取扒窃数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT count(0) FROM vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime and (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0)    and ajlbdm in('05000200') and ajxlbdm in('050002000500')")
    Long getPqSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                 @Param("endTime") LocalDateTime endTime);


    /**
     * 获取接触性诈骗数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value =
            " SELECT count(0) FROM vw_jwzh_xsaj_aj where larq>:startTime and  larq<:endTime and  (:unitCode is null or instr(LADW_GAJGJGDM,:unitCode) > 0)    and ajlbdm "
                    + "                in('03050000','03050100','03050200','03050300','03050400','03050500','03050600','03050700','03050800','03080400','05000300') "
                    + "                and ajxlbdm in('050003000100')")
    Long getJcxzpSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                    @Param("endTime") LocalDateTime endTime);


    /**
     * 机制赋能-警务协作
     *
     * @param code      type
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "select count(*) from T_BATTLE_DEMAND t " +
            "where t.TYPE =:code " +
            "and t.PUBLISHTIME between :beginTime and :endTime " +
            "and t.STATE = 2 ")
    Long getJwxz(@Param("code") String code, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 机制赋能-警务合作
     *
     * @param code      type
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "select count(*) from T_BATTLE_DEMAND t " +
            "where t.SUBTYPE =:code " +
            "and t.PUBLISHTIME between :beginTime and :endTime " +
            "and t.STATE = 2 ")
    Long getJwhz(@Param("code") String code, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);


    /**
     * 机制赋能-警务合作
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      开始时间
     * @param endTime        结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "select count(*) from T_BATTLE_DEMAND t " +
            "where t.DEALUNITCODE like :unitCodePrefix||'%' " +
            "and t.PUBLISHTIME between :beginTime and :endTime " +
            "and t.STATE = 2 ")
    Long getXzdw(@Param("unitCodePrefix") String unitCodePrefix, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);


    /**
     * 获取刑事案件嫌疑人数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT COUNT(1) FROM JWZH_XSAJ_XYR xyr  LEFT JOIN VW_JWZH_XSAJ_AJ aj ON xyr.ASJBH = aj.ASJBH where  xyr.XT_ZXBZ = 0   AND aj.XT_ZXBZ = 0 AND  to_date(xyr.xt_lrsj, 'yyyy-mm-dd hh24:mi:ss')>:startTime and to_date(xyr.xt_lrsj, 'yyyy-mm-dd hh24:mi:ss')<:endTime and (:unitCode is null or instr(aj.LADW_GAJGJGDM,:unitCode) > 0) ")
    Long getXsajXyrSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime);

    /**
     * 获取行政案件嫌疑人数量
     *
     * @param unitCode  区域code
     * @param startTime 起始时间
     * @param endTime   结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = " SELECT COUNT(1) FROM JWZH_XZAJ_XYR xyr  LEFT JOIN vw_jwzh_xzaj_aj aj ON xyr.ASJBH = aj.ASJBH where  xyr.XT_ZXBZ = 0 AND aj.XT_ZXBZ = 0 AND to_date(xyr.xt_lrsj, 'yyyy-mm-dd hh24:mi:ss')>:startTime and to_date(xyr.xt_lrsj, 'yyyy-mm-dd hh24:mi:ss')<:endTime and (:unitCode is null or instr(aj.BADW_GAJGJGDM,:unitCode) > 0) ")
    Long getXzajXyrSl(@Param("unitCode") String unitCode, @Param("startTime") LocalDateTime startTime,
                      @Param("endTime") LocalDateTime endTime);

    /**
     * 获取协作总数量
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      开始时间
     * @param endTime        结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "select count(*) from T_BATTLE_DEMAND t where STATE=2 and (:unitCodePrefix is null or DEALUNITCODE like :unitCodePrefix||'%')  and (PUBLISHTIME between :beginTime and :endTime)")
    Long getXzSl(@Param("unitCodePrefix") String unitCodePrefix, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 获取协作合成数量
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      开始时间
     * @param endTime        结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "select count(*) from T_BATTLE_DEMAND t where STATE=2 and (BATTLEID is not null)  and (:unitCodePrefix is null or DEALUNITCODE like :unitCodePrefix||'%')  and (PUBLISHTIME between :beginTime and :endTime)   ")
    Long getXzhcSl(@Param("unitCodePrefix") String unitCodePrefix, @Param("beginTime") LocalDateTime beginTime,
                   @Param("endTime") LocalDateTime endTime);

    /**
     * 风险管控-治安风险
     *
     * @param district  区域code
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(*) FROM T_BATTLE_HAZARD " +
            "WHERE UNITCODE LIKE :district||'%' " +
            "AND REPORTTIME BETWEEN :beginTime AND :endTime")
    Long getZafx(@Param("district") String district, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 风险管控-风险上报
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}
     */
    @Query(nativeQuery = true, value = "SELECT " +
            "rownum as id," +
            "(SELECT tbe.DICTNAME FROM T_BATTLE_EVENTDICT tbe WHERE tbe.DICTTYPE='HAZARD_TEMPLATE' AND tbe.DICTCODE = tbh.TYPE) AS lb," +
            "tbh.TITLE AS bt," +
            "tbh.UNITNAME AS sbdw," +
            "tbh.CRTBYNAME AS sbr," +
            "tbh.REPORTTIME AS sj " +
            "FROM T_BATTLE_HAZARD tbh " +
            "where tbh.REPORTTIME between :beginTime and :endTime " +
            "and rownum <= 12")
    List<Map<String, Object>> getFxsb(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 风险管控-热点分析
     *
     * @param type      类型
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link Long}
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(*) FROM T_BATTLE_HOTWORD t " +
            "WHERE t.type = :type " +
            "and t.CRTTIME between :beginTime and :endTime")
    Long getRdfx(@Param("type") String type, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 日常工作-警务合成-合成占比
     *
     * @param type      类型
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link Long}
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(*) FROM T_BATTLE_RECORD t " +
            "WHERE t.BATTLETYPE = :type " +
            "and t.PUBLISHTIME between :beginTime and :endTime")
    Long getHczb(@Param("type") String type, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);


    /**
     * 日常工作-警务合成-协作手段
     *
     * @param type      类型
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(*) FROM T_BATTLE_DEMAND t " +
            "WHERE t.TYPE = :type " +
            "and t.PUBLISHTIME between :beginTime and :endTime")
    Long getXzsd(@Param("type") String type, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 日常工作-指令中心
     *
     * @param district  区域
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return {@link BigScreenStatisticVO}
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(1) FROM T_ALARM_YJZL t " +
            "JOIN T_ALARM_YJGJ b ON t.ID = b.YJZLID  " +
            "WHERE b.SQRDWDH like :district||'%' " +
            "and t.FBSJ between :beginTime and :endTime")
    Long getZlzx(@Param("district") String district, @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 统计日志数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 日志数量
     */
    @Query(nativeQuery = true, value = "SELECT sum(num) FROM (SELECT count(0) AS num FROM T_LOG_LOGIN where LOGINTIME between :beginTime and :endTime UNION SELECT count(0) AS num FROM T_LOG_SYSTEM where OCCURRED_TIME between :beginTime and :endTime  UNION SELECT count(0) AS num FROM T_LOG_SEARCH where SEARCHTIME between :beginTime and :endTime)")
    Long countLog(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 统计使用功能
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      起始时间
     * @param endTime        结束时间
     * @return 使用功能
     */
    @Query(nativeQuery = true, value = "select MODULE as name ,count(0) as num from T_LOG_SYSTEM where(OCCURRED_TIME between :beginTime and :endTime ) and (:unitCodePrefix is null or UNIT_CODE like concat(:unitCodePrefix,'%'))  group by MODULE")
    List<Map<String, Object>> getSygn(@Param("unitCodePrefix") String unitCodePrefix,
                                      @Param("beginTime") LocalDateTime beginTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 统计所有使用功能
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      起始时间
     * @param endTime        结束时间
     * @return 使用功能
     */
    @Query(nativeQuery = true, value = "select  count(0) as num from T_LOG_SYSTEM where (OCCURRED_TIME between :beginTime and :endTime ) and (:unitCodePrefix is null or UNIT_CODE like concat(:unitCodePrefix,'%'))")
    Long getAllSygn(@Param("unitCodePrefix") String unitCodePrefix,
                    @Param("beginTime") LocalDateTime beginTime,
                    @Param("endTime") LocalDateTime endTime);

    /**
     * app 使用
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      起始时间
     * @param endTime        结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = "SELECT count(0)  FROM T_LOG_LOGIN where (LOGINTIME between :beginTime and :endTime) and AREACODE like concat(:unitCodePrefix,'%') and LOGINTYPE='app' ")
    Long countApp(@Param("unitCodePrefix") String unitCodePrefix,
                  @Param("beginTime") LocalDateTime beginTime,
                  @Param("endTime") LocalDateTime endTime);

    /**
     * pc 使用
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      起始时间
     * @param endTime        结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = "SELECT count(0)  FROM T_LOG_LOGIN where (LOGINTIME between :beginTime and :endTime) and AREACODE like concat(:unitCodePrefix,'%') and LOGINTYPE !='app' ")
    Long countPc(@Param("unitCodePrefix") String unitCodePrefix,
                 @Param("beginTime") LocalDateTime beginTime,
                 @Param("endTime") LocalDateTime endTime);

    /**
     * 平台活跃趋势
     *
     * @param unitCodePrefix 协作单位前缀
     * @param beginTime      起始时间
     * @param endTime        结束时间
     * @return 数量
     */
    @Query(nativeQuery = true, value = "SELECT count(0)  FROM T_LOG_LOGIN where (LOGINTIME between :beginTime and :endTime) and AREACODE like concat(:unitCodePrefix,'%')")
    Long countPthyqs(@Param("unitCodePrefix") String unitCodePrefix,
                     @Param("beginTime") LocalDateTime beginTime,
                     @Param("endTime") LocalDateTime endTime);


    /**
     * 统计业务申请数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 日志数量
     */
    @Query(nativeQuery = true, value = "SELECT sum(num) FROM (SELECT count(0) AS num FROM T_BATTLE_RECORD where (PUBLISHTIME between :beginTime and :endTime) UNION SELECT count(0) AS num FROM T_BATTLE_DEMAND where (PUBLISHTIME between :beginTime and :endTime) UNION SELECT count(0) AS num FROM T_ALARM_BKXX where (SQSJ between :beginTime and :endTime))")
    Long countYwsqsl(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);


    /**
     * 统计业务申请数量
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return 日志数量
     */
    @Query(nativeQuery = true, value = "SELECT *  FROM (SELECT t.GLXM ,t.GLSFZH ,t.YJSJ ,b.BKSY ,b.BKJB,t.HDLYBCN,t.HDFSDD ,t.PICURLS " +
            "FROM T_ALARM_YJGJ t " +
            "LEFT JOIN T_ALARM_BKXX b ON t.BKXXZJ = b.ID " +
            "WHERE t.YJSJ BETWEEN :beginTime and :endTime " +
            "ORDER BY t.YJSJ ) WHERE  rownum <=4")
    List<Map<String, Object>> getYjxx(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

}


