package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwrySourceTypeEnum;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.repository.CrjJwryDetailRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import java.text.SimpleDateFormat;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/10/8 14:24
 */
@Data
public class JwryExportVO {

    @ExcelProperty(value = "英文名")
    private String enName;
    @ExcelProperty(value = "中文名")
    private String cnName;
    @ExcelProperty(value = "证件类型")
    private String idType;
    @ExcelProperty(value = "证件号码")
    private String idNumber;
    @ExcelProperty(value = "国家地区")
    private String gjmc;
    @ExcelProperty(value = "性别")
    private String gender;
    @ExcelProperty(value = "出生日期")
    private String birthday;
    @ExcelProperty(value = "居住状态")
    private String registrationStatus;
    @ExcelProperty(value = "登记类型")
    private String sourceType;
    @ExcelProperty(value = "签证(注)类别")
    private String visaType;
    @ExcelProperty(value = "签证(注)号码")
    private String visaNumber;
    @ExcelProperty(value = "拟离开时间")
    private String planLeaveTime;
    @ExcelProperty(value = "入境日期")
    private String entryTime;
    @ExcelProperty(value = "入境口岸")
    private String entryPort;
    @ExcelProperty(value = "详细地址")
    private String address;
    @ExcelProperty(value = "所属区域")
    private String controlDeptName;


    /**
     * vo转换
     *
     * @param jwryEntity entity
     * @return vo
     */
    public static JwryExportVO toExportVO(CrjJwryEntity jwryEntity) {
        JwryExportVO vo = new JwryExportVO();
        CrjJwryDetailRepository crjJwryDetailRepository = BeanUtil.getBean(CrjJwryDetailRepository.class);
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        crjJwryDetailRepository.findByIdTypeAndIdNumber(jwryEntity.getIdType(), jwryEntity.getIdNumber())
            .ifPresent(detail -> {
                vo.setEnName(detail.getEnName());
                vo.setCnName(detail.getCnName());
                vo.setGender(dictRepository.findNameByTypeAndCode(CrjConstants.MALE, detail.getGender()));
                if(Objects.nonNull(detail.getBirthday())){
                    vo.setBirthday(simpleDateFormat.format(detail.getBirthday()));
                }
                vo.setVisaType(dictRepository.findNameByTypeAndCode(CrjConstants.VISA_TYPE, detail.getVisaType()));
                vo.setVisaNumber(detail.getVisaNumber());
                if(Objects.nonNull(detail.getEntryTime())){
                    vo.setEntryTime(simpleDateFormat.format(detail.getEntryTime()));
                }
                vo.setEntryPort(
                    dictRepository.findNameByTypeAndCode(CrjConstants.ENTRY_PORT, detail.getEntryPort()));
            });
        vo.setGjmc(dictRepository.findNameByTypeAndCode("crjGjdm", jwryEntity.getGjdm()));
        vo.setIdType(dictRepository.findNameByTypeAndCode("crjZjzl", jwryEntity.getIdType()));
        vo.setIdNumber(jwryEntity.getIdNumber());
        vo.setSourceType(Objects.isNull(jwryEntity.getSourceType())?"": CrjJwrySourceTypeEnum.codeOf(jwryEntity.getSourceType()).getName());
        CrjJwryRegistrationStatusEnum registrationStatus = CrjJwryRegistrationStatusEnum.codeOf(
            jwryEntity.getRegistrationStatus());
        vo.setRegistrationStatus(
            registrationStatus == null ? CrjJwryRegistrationStatusEnum.NOT_REGISTER.getName()
                : registrationStatus.getName());
        if(Objects.nonNull(jwryEntity.getPlanLeaveTime())){
            vo.setPlanLeaveTime(simpleDateFormat.format(jwryEntity.getPlanLeaveTime()));
        }
        UnitRepository unitRepository = BeanUtil.getBean(UnitRepository.class);
        if (StringUtils.isNotBlank(jwryEntity.getAcceptor())) {
            vo.setControlDeptName(unitRepository.findByUnitCode(jwryEntity.getAcceptor()).getUnitName());
        }
        vo.setAddress(jwryEntity.getAddress());
        return vo;
    }
}
