package com.trs.yq.police.subject.task;

import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.trs.yq.police.subject.exception.SystemException;
import com.trs.yq.police.subject.repository.MobilePhoneRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.repository.VehicleRepository;
import com.trs.yq.police.subject.repository.VirtualIdentityRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.*;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 远程订阅任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/25
 **/

@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.remote.subscribe.task.enable", havingValue = "true")
public class RemoteSubscribeTask {

    @Resource
    RestTemplate restTemplate;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private VirtualIdentityRepository virtualIdentityRepository;

    @Resource
    private VehicleRepository vehicleRepository;

    @Resource
    private MobilePhoneRepository mobilePhoneRepository;

    @Resource
    private Gson gson;

    private static final String CANCEL_URL = "http://80.2.20.166:18982/rest/api/cancelSubscribe";
    private static final String SUB_URL = "http://80.2.20.166:18982/rest/api/subscribe";

    /**
     * 向xds订阅身份证号、手机号、车牌号、虚拟身份数据
     */
    @Scheduled(cron = "${com.trs.remote.subscribe.task}")
    public void remoteSubTask() {
        log.info("remote subscribe send start.");
        Map<String, String> sendMap = new HashMap<>();
        StopWatch watch = new StopWatch();
        watch.start();
        personRepository.findAllIdNumber().forEach(idNumber -> sendMap.put(idNumber, SubscribeSendType.ID_NUMBER.getType()));
        vehicleRepository.findAllVehicleNumber().forEach(vehicleNumber -> sendMap.put(vehicleNumber, SubscribeSendType.CAR_NUMBER.getType()));
        mobilePhoneRepository.findAllMobilePhone().forEach(phoneNumber -> sendMap.put(phoneNumber, SubscribeSendType.PHONE_NUMBER.getType()));
        virtualIdentityRepository.findAllPhoneVirtual().forEach(virtualMap -> translateType(virtualMap.get("virtualType")).ifPresent(type -> sendMap.put(virtualMap.get("virtualNumber"), type.getType())));
        ArrayList<List<Map.Entry<String, String>>> list = Lists.newArrayList(Iterables.partition(sendMap.entrySet(), 100));
        list.stream()
                .map(entries -> entries.stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)))
                .forEach(map -> log.info("result:{}", reSub(map)));
        watch.stop();
        log.info("remote subscribe send complete.Cost {} ms", watch.getTotalTimeMillis());
    }

    /**
     * 取消订阅&重新订阅
     *
     * @param data 数据集
     * @return true：操作成功 false：操作失败
     */
    private boolean reSub(Map<String, String> data) {
        if (data.size() > 100) {
            throw new SystemException("数据量超限");
        }
        return cancel(data) && sub(data);
    }

    /**
     * 订阅
     *
     * @param data 数据集
     * @return true：操作成功 false：操作失败
     */
    private boolean sub(Map<String, String> data) {
        Map<String, Object> body = new HashMap<>();
        body.put("app_code", "ADBF658CAF3A444891FDC53D37214D9D");
        body.put("overdue_time", "2031-01-01 00:00:00");
        body.put("status", "1");
        body.put("data", data.keySet().stream().map(k -> new HashMap<String, String>() {
            private static final long serialVersionUID = 8466658046515113034L;

            {
                put("type", data.get(k));
                put("id", k);

            }
        }).collect(Collectors.toList()));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        restTemplate.setErrorHandler(new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return false;
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                response.getRawStatusCode();
            }
        });
        ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(SUB_URL), HttpMethod.POST, entity, String.class);
        try {
            return "010000".equals(gson.fromJson(responseEntity.getBody(), JsonObject.class).get("code").getAsString());
        } catch (Exception e) {
            log.info("gson format response error.");
            return false;
        }
    }

    /**
     * 取消订阅
     *
     * @param data 数据集
     * @return true：操作成功 false：操作失败
     */
    private boolean cancel(Map<String, String> data) {
        Map<String, Object> body = new HashMap<>();
        body.put("app_code", "ADBF658CAF3A444891FDC53D37214D9D");
        body.put("data", data.keySet().stream().map(k -> new HashMap<String, String>() {
            private static final long serialVersionUID = 8466658046515113034L;

            {
                put("type", data.get(k));
                put("id", k);
            }
        }).collect(Collectors.toList()));
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        restTemplate.setErrorHandler(new ResponseErrorHandler() {
            @Override
            public boolean hasError(ClientHttpResponse response) throws IOException {
                return false;
            }

            @Override
            public void handleError(ClientHttpResponse response) throws IOException {
                response.getRawStatusCode();
            }
        });

        ResponseEntity<String> responseEntity = restTemplate.exchange(URI.create(CANCEL_URL), HttpMethod.POST, entity, String.class);
        try {
            return "010000".equals(gson.fromJson(responseEntity.getBody(), JsonObject.class).get("code").getAsString());
        } catch (Exception e) {
            log.info("gson format response error.");
            return false;
        }
    }

    /**
     * 翻译虚拟身份类型
     *
     * @param virtualType 虚拟身份类型
     * @return 发送订阅类型枚举
     */
    private Optional<SubscribeSendType> translateType(String virtualType) {
        if (Objects.isNull(virtualType)) {
            return Optional.empty();
        }
        switch (virtualType) {
            case "1":
                return Optional.of(SubscribeSendType.MAC);
            case "2":
                return Optional.of(SubscribeSendType.IMSI);
            case "3":
                return Optional.of(SubscribeSendType.IMEI);
            default:
                return Optional.empty();
        }
    }


    /**
     * 发送订阅类型枚举
     */
    @Getter
    @AllArgsConstructor
    public enum SubscribeSendType {
        ID_NUMBER("1"),
        CAR_NUMBER("2"),
        PHONE_NUMBER("3"),
        MAC("4"),
        PASSPORT("5"),
        IMSI("6"),
        FACE_ID("7"),
        IMEI("8"),

        ;
        private String type;

    }

}
