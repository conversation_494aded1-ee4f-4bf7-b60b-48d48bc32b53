package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.jpa.converter.JpaConverterListJoin;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Collections;
import java.util.List;


/**
 * 事件与群体的关联
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "t_event_preson_relation")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class EventPersonRelationEntity extends BaseEntity {

    private static final long serialVersionUID = 7558850994599085675L;

    /**
     * 事件id
     */
    private String eventId;
    /**
     * 人员id
     */
    private String personId;
    /**
     * 现场照片编号
     */
    private String photoIds;

    /**
     * 涉事行为
     * 参考码表 event_behavior
     */
    @Convert(converter = JpaConverterListJoin.class)
    private List<String> behaviors;

    /**
     * 人员来源 1：网安, 2: 视侦
     */
    private String source;

    /**
     * 构造器
     *
     * @param personId 人员id
     * @param eventId  事件id
     */
    public EventPersonRelationEntity(String eventId, String personId) {
        this.personId = personId;
        this.eventId = eventId;
        this.photoIds = "";
        this.behaviors = Collections.emptyList();
        this.source = "1";
    }

    /**
     * 构造器
     *
     * @param personId  人员id
     * @param eventId   事件id
     * @param behaviors 涉事行为
     */
    public EventPersonRelationEntity(String eventId, String personId, List<String> behaviors) {
        this.personId = personId;
        this.eventId = eventId;
        this.photoIds = "";
        this.behaviors = behaviors;
        this.source = "1";
    }
}
