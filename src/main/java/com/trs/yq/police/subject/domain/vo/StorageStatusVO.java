package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 人员存在状态VO
 *
 * <AUTHOR>
 * @date 2021/08/09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StorageStatusVO {

    private String personId;

    private List<String> subjectIds;

    /**
     * 根据人员id生成存在状态vo
     *
     * @param personId 人员id
     */
    public StorageStatusVO(String personId) {
        this.personId = personId;
    }
}
