package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

/**
 * 出入境常住人员详情
 *
 * <AUTHOR>
 */
@Data
public class CrjResidenceDetailVO {

    private BasicInfo basicInfo;

    private VisaInfo visaInfo;

    private ResidenceInfo residenceInfo;


    /**
     * 基本信息
     */
    @Data
    public static class BasicInfo {

        /**
         * 证件类型
         */
        private String certificateType;
        /**
         * 证件号码
         */
        private String certificateNumber;
        /**
         * 中文名
         */
        private String cnName;
        /**
         * 英文名
         */
        private String enName;
        /**
         * 性别
         */
        private String gender;
        /**
         * 国家地区
         */
        private String country;
        /**
         * 生日
         */
        private String birthday;
        /**
         * 居住状态
         */
        private String livingStatus;
        /**
         * 居住地派出所
         */
        private String policeStation;
        /**
         * 工作单位
         */
        private String workPlace;
        /**
         * 居住地址
         */
        private String livingAddress;
        /**
         * 签证签发日期
         */
        private String visaSignDate;
        /**
         * 签证到期日期
         */
        private String visaValidity;
    }

    /**
     * 签证信息
     */
    @Data
    public static class VisaInfo {

        /**
         * 签证类型
         */
        private String visaType;
        /**
         * 签证号码
         */
        private String visaNumber;
        /**
         * 是否r签证
         */
        private String visaLabel;
        /**
         * 入境时间
         */
        private String entryTime;

    }

    /**
     * 居住信息
     */
    @Data
    public static class ResidenceInfo {

        /**
         * 居住地核实
         */
        private String livePlaceCheck;
        /**
         * 工作地核实
         */
        private String workPlaceCheck;
        /**
         * 居留事由
         */
        private String stayReason;
        /**
         * 入境事由
         */
        private String enterReason;
        /**
         * 录入人
         */
        private String createUser;
        /**
         * 录入时间
         */
        private String createTime;
    }

}
