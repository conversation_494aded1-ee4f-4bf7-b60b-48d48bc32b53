package com.trs.yq.police.subject.domain.vo;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 导入结果
 *
 * <AUTHOR>
 * @date 2021/8/9 16:49
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ImportResultVO implements Serializable {

    private static final long serialVersionUID = -5472988068238868408L;

    /**
     * 原始文件id
     */
    private String initialId;

    /**
     * 反馈详情
     */
    private List<ImportResultListVO> details;
}
