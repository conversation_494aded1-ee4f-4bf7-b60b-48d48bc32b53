package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import lombok.Data;

import java.util.List;

/**
 * 人员列表查询请求
 *
 * <AUTHOR>
 * @date 2021/07/30
 */
@Data
public class ListRequestVO {
    /**
     * 分页参数
     */
    PageParams pageParams;

    /**
     * 动态参数
     */
    List<KeyValueVO> filterParams;

    /**
     * 文本检索参数
     */
    SearchParams searchParams;

    /**
     * 排序参数
     */
    SortParams sortParams;
}
