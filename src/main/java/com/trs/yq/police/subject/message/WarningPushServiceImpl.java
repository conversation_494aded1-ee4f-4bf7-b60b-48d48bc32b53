package com.trs.yq.police.subject.message;

import com.google.common.collect.ImmutableList;
import com.trs.dubbo.provide.MsgGateway;
import com.trs.yq.police.subject.constants.PoliceSubjectConstants;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.constants.enums.WarningTypeEnum;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FX_SUBJECT;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ZB_SUBJECT;

/**
 * 预警推送相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/08
 **/
@Service
@Slf4j
public class WarningPushServiceImpl implements WarningPushService {

    @DubboReference(check = false)
    private MsgGateway msgGateway;
    @Resource
    private WarningPushConfigRepository warningPushConfigRepository;
    @Resource
    private SubjectRepository subjectRepository;
    @Resource
    private RoleUserRepository roleUserRepository;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private WarningPushLogRepository warningPushLogRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;

    @Value("${com.trs.warning.push.switch:false}")
    private String pushSwitch;

    private static final String SMS_TEMPLATE = "您有新的预警信息待签收，请登录%s查看。";

    /**
     * 构造查询config的条件
     *
     * @param warningType  预警类型
     * @param areaCodeList 区域代码列表
     * @return 查询条件Specification
     */
    private Specification<WarningPushConfigEntity> buildConfigQueryConditions(WarningTypeEntity warningType,
        List<String> areaCodeList) {
        return (root, query, cb) -> {
            List<Predicate> p = new ArrayList<>();
            p.add(cb.equal(root.get("warning_type").as(String.class), warningType.getEnName()));
            final String subjectId = warningType.getSubjectId();
            if (!subjectId.equals(PoliceSubjectConstants.FK_SUBJECT) && !areaCodeList.isEmpty()) {
                CriteriaBuilder.In<String> in = cb.in(root.get("area_code").as(String.class));
                areaCodeList.forEach(in::value);
                p.add(in);
            }
            if (subjectId.equals(PoliceSubjectConstants.ZB_SUBJECT) || subjectId.equals(FX_SUBJECT)) {
                p.add(cb.isNull(cb.in(root.get("area_code").as(String.class))));
            }
            return query.where(cb.and(p.toArray(new Predicate[0]))).getRestriction();
        };
    }

    @Override
    public void pushJzsdMessage(WarningEntity warning, String idNumber) {
        if (!"true".equals(pushSwitch)) {
            return;
        }
        log.info("预警{}准备预警推送", warning.getId());
        if (StringUtils.isBlank(idNumber)) {
            log.error("重点人员身份证号为空！短信发送失败！");
            return;
        }
        String phoneNumber = controlRepository.getPolicePhoneNumberByIdNumber(idNumber);
        if (StringUtils.isBlank(phoneNumber)) {
            log.error("管控民警电话为空！短信发送失败！");
            return;
        }
        if (Objects.isNull(msgGateway)) {
            log.error("未检测到短信网关配置！短信发送失败！");
            return;
        }
        PersonEntity person = personRepository.findByIdNumber(idNumber);
        if (Objects.isNull(person)) {
            log.error("身份证号为{}的人员不存在！", idNumber);
            return;
        }
        String time = warning.getWarningTime().format(DateTimeFormatter.ofPattern("MM月dd日 HH:mm:ss"));
        String content = String.format("%s，人员%s触发技侦手段预警，请到系统签收处置。", time, person.getName());
        try {
            String[] phoneNumbers = Collections.singletonList(phoneNumber).toArray(new String[0]);
            msgGateway.sendMessage(content, "维稳专题", phoneNumbers);
            log.info("技侦手段预警，短信发送成功！接收电话为：{}", Arrays.toString(phoneNumbers));
        } catch (Exception e) {
            log.error("短信网关发送失败！", e);
        }
    }

    /**
     * 预警推送
     *
     * @param warningType  预警类型
     * @param warning      预警实体
     * @param areaCodeList 区域代码列表
     */
    private void push(WarningTypeEntity warningType, WarningEntity warning, List<String> areaCodeList) {
        log.info("预警{}准备预警推送", warning.getId());
        if (Objects.isNull(msgGateway)) {
            return;
        }

//        List<WarningPushConfigEntity> configList = warningType.getSubjectId().equals(PoliceSubjectConstants.FK_SUBJECT) ?
//                warningPushConfigRepository.findByWarningType(warningType.getEnName()) :
//                warningPushConfigRepository.findByWarningTypeAndAreaCode(warningType.getEnName(), areaCodeList);

        List<WarningPushConfigEntity> configList = warningPushConfigRepository.findAll(
            buildConfigQueryConditions(warningType, areaCodeList));

        log.info("预警{}命中configs：{}", warning.getId(), configList);
        if (configList.isEmpty()) {
            log.info("预警{}configList为空", warning.getId());
            return;
        }
        Optional<SubjectEntity> optionalSubject = subjectRepository.findById(warningType.getSubjectId());
        if (!optionalSubject.isPresent()) {
            log.info("预警{}Subject不存在", warning.getId());
            return;
        }
        SubjectEntity subject = optionalSubject.get();
        List<String> phoneNoList = roleUserRepository.findMobileByRoleId(
            configList.stream().filter(c -> c.getSmsFlag() == 1).map(WarningPushConfigEntity::getRoleId)
                .collect(Collectors.toList()));
        log.info("预警{}对应发送的手机号：{}", warning.getId(), phoneNoList.toString());
        try {
            if (ZB_SUBJECT.equals(subject.getId()) || FX_SUBJECT.equals(subject.getId())) {
                final List<String> phoneNumbers = warningTrajectoryRepository.findTraceListByWarningId(warning.getId())
                    .stream()
                    .map(trajectory -> {
                        PersonEntity person = warningTrajectoryRepository.findPersonByTrajectoryId(trajectory.getId());
                        return controlRepository.findByPersonIdAndSubjectId(person.getId(), subject.getId())
                            .orElse(new ControlEntity());
                    })
                    .map(ControlEntity::getResponsibleContact)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
                phoneNoList.addAll(phoneNumbers);
            }
            msgGateway.sendMessage(String.format(SMS_TEMPLATE, subject.getName()), subject.getName(),
                phoneNoList.toArray(new String[0]));

            final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.SMS)
                .module(OperateModule.WARNING)
                .primaryKey(warning.getId())
                .newObj(JsonUtil.toJsonString(phoneNoList))
                .targetObjectType(TargetObjectTypeEnum.WARNING.getCode())
                .desc("推送预警短信")
                .build();
            if (Objects.nonNull(operationLogHandler)) {
                // 记录操作
                operationLogHandler.publishEvent(logRecord);
            }
        } catch (Exception e) {
            log.error("发送预警信息异常", e);
        }
    }

    @Override
    public void push(WarningTypeEntity warningType, WarningEntity warning, String idNumber) {
        if (!"true".equals(pushSwitch)) {
            return;
        }
        if (StringUtils.isBlank(idNumber)) {
            return;
        }
        List<String> areaCodeList;
        WarningTypeEnum warningTypeEnum = WarningTypeEnum.enNameOf(warningType.getEnName());
        if (Objects.isNull(warningTypeEnum)) {
            return;
        }
        switch (warningTypeEnum) {
            case JD_SDRYJC:
                //毒驾 活动地&户籍地（身份证前六位）
                areaCodeList = ImmutableList.of(warning.getAreaCode(), idNumber.substring(0, 6));
                break;
            case ZB_GZRYFXYJ:
            case ZB_RYYJ:
            case FX_GZRYFXYJ:
            case FX_RYYJ:
                String policeStationCode = controlRepository.getPoliceStationCodeByIdNumber(idNumber);
                if (StringUtils.isBlank(policeStationCode)) {
                    log.info("预警{}policeStationCode未找到", warning.getId());
                    return;
                }
                areaCodeList = ImmutableList.of(warning.getAreaCode(), policeStationCode.substring(0, 6));
                break;
            case JJ_SJRYJC:
            case FK_GZRYFXYJ:
            case FK_QTJJ:
            case FK_XLRWARY:
                areaCodeList = ImmutableList.of(warning.getAreaCode());
                break;
            default:
                return;
        }
        push(warningType, warning, areaCodeList);
    }

    /**
     * 判断在当天内是否已经推过
     *
     * @param warningType 预警类型
     * @param idType      预警主键类型
     * @param idValue     预警主键值
     * @return true：已经推过 false：未推过
     */
    private boolean warningIsExisted(WarningTypeEntity warningType, String idType, String idValue) {
        if (WarningTypeEnum.JD_SDRYJC.equals(WarningTypeEnum.enNameOf(warningType.getEnName()))
            || ((ZB_SUBJECT.equals(warningType.getSubjectId()) || FX_SUBJECT.equals(warningType.getSubjectId())) && !idType.equals("idNumber"))) {
            //禁毒专题毒驾人员类型 && 政保专题非身份证预警，判断一天一次
            return warningPushLogRepository.findIfExistedInToday(warningType.getEnName(), warningType.getSubjectId(),
                idType, idValue) > 0;
        }
        return false;
    }
}
