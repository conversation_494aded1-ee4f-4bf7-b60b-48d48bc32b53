package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BattleHazardEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 风险表查询接口
 */
@Repository
public interface HazardRepository extends JpaRepository<BattleHazardEntity, String> {

    /**
     * 获取最近上报的风险点
     *
     * @return {@link BattleHazardEntity}
     */
    @Query(nativeQuery = true, value = "SELECT h.* FROM T_BATTLE_HAZARD h WHERE ROWNUM < 10 ORDER BY h.updtime DESC ")
    List<BattleHazardEntity> getRecentList();
}
