package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.GroupClueRelationEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.GroupClueRelationRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.PersonGroupRelationRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 线索中的群体数据
 *
 * <AUTHOR>
 * @date 2021/9/3 16:10
 */
@Data
public class ClueRelatedGroupVO implements Serializable {
    private static final long serialVersionUID = -7228872237355010521L;
    /**
     * 群体id
     */
    private String groupId;
    /**
     * 群体名称
     */
    private String groupName;
    /**
     * 群体人数
     */
    private Integer memberCount;
    /**
     * 群体类别
     */
    private String groupType;
    /**
     * 线索-群体关联id
     */
    private String relationId;

    /**
     * 创建vo
     *
     * @param groupEntity {@link GroupEntity}
     * @param clueId      线索id
     * @return {@link ClueRelatedGroupVO}
     */
    public static ClueRelatedGroupVO of(GroupEntity groupEntity, String clueId) {
        ClueRelatedGroupVO vo = new ClueRelatedGroupVO();
        vo.setGroupId(groupEntity.getId());
        PersonGroupRelationRepository personGroupRelationRepository = BeanUtil.getBean(PersonGroupRelationRepository.class);
        vo.setMemberCount(personGroupRelationRepository.countAllByGroupId(groupEntity.getId()));
        vo.setGroupName(groupEntity.getName());

        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        List<LabelEntity> groupTypes = labelRepository.findByGroupIdAndSubjectId(groupEntity.getId(), groupEntity.getSubjectId());
        vo.setGroupType(groupTypes.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));

        GroupClueRelationRepository groupClueRelationRepository = BeanUtil.getBean(GroupClueRelationRepository.class);
        GroupClueRelationEntity relation = groupClueRelationRepository.findByClueIdAndGroupId(clueId, groupEntity.getId());
        vo.setRelationId(relation.getId());
        return vo;
    }
}
