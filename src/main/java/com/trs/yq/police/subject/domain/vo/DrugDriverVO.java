package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 注销驾驶人员驾车vo
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/13 11:56
 **/
@Data
public class DrugDriverVO {

    /**
     * 预警id
     */
    private String warningId;
    /**
     * 人员名称
     */
    private String personName;
    /**
     * 预警地点
     */
    private String warningPlace;
    /**
     * 时间
     */
    private LocalDateTime warningTime;
    /**
     * 图片
     */
    private List<ImageVO> images;
    /**
     * 预警级别
     */
    private String warningLevel;
    /**
     * 人员id
     */
    private String personId;
    /**
     * 车牌号
     */
    private String carNumber;
}
