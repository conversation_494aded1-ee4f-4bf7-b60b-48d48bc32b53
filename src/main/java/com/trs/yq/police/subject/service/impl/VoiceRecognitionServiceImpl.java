package com.trs.yq.police.subject.service.impl;

import com.alibaba.nacos.common.utils.Objects;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.yq.police.subject.common.ResponseMessage;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.domain.vo.VoiceRecognitionVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.repository.VoiceRecognitionRepository;
import com.trs.yq.police.subject.service.VoiceRecognitionService;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 智能语音识别实现层
 *
 * <AUTHOR>
 * @date 2021/10/14 15:30
 */
@Service
@Slf4j
public class VoiceRecognitionServiceImpl implements VoiceRecognitionService {
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private VoiceRecognitionRepository voiceRecognitionRepository;
    @Resource
    private ObjectMapper objectMapper;
    @Resource(name = "assignRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;
    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${com.trs.voice.to.text.url}")
    private String voiceToTextUrl;
    @Value("${com.trs.text.to.voice.url}")
    private String textToVoiceUrl;

    @Value("${com.trs.redis.audio.txt.key}")
    private String voiceKeyOfRedis;

    @Value("${com.trs.redis.audio.base64.key}")
    private String voiceBase64KeyOfRedis;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public VoiceRecognitionVO recognizeVoice(MultipartFile voice) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, ByteArrayResource> form = new LinkedMultiValueMap<>(1);
        ByteArrayResource is = new ByteArrayResource(voice.getBytes()) {
            @Override
            public String getFilename() {
                return voice.getOriginalFilename();
            }
        };
        form.add("file", is);
        //远程调用
        HttpEntity<MultiValueMap<String, ByteArrayResource>> testToVoiceHttp = new HttpEntity<>(form, headers);
        ResponseEntity<ResponseMessage> responseEntity = restTemplate.postForEntity(voiceToTextUrl, testToVoiceHttp, ResponseMessage.class);
        if (HttpStatus.INTERNAL_SERVER_ERROR.value() == java.util.Objects.requireNonNull(responseEntity.getBody()).getCode()) {
            throw new ParamValidationException("语音识别失败，请传入标准wav格式文件！！");
        }
        String requestAudioTxt = String.valueOf(java.util.Objects.requireNonNull(responseEntity.getBody()).getData());
        log.info("voice_to_text:" + requestAudioTxt);
        KeyValueVO address = getAddressForTemplate(requestAudioTxt);

        headers.setContentType(MediaType.APPLICATION_JSON);
        ObjectNode textToVoiceForm = objectMapper.createObjectNode();
        VoiceRecognitionVO voiceRecognitionVO = new VoiceRecognitionVO();
        if (Objects.isNull(address)) {
            voiceRecognitionVO.setTextValue("您问的问题我还在学习中，您可以试下下面的问题：今日江阳区的警情");
            voiceRecognitionVO.setVoiceValue(requestAudioTxt);
            textToVoiceForm.put("text", "您问的问题我还在学习中，您可以试下下面的问题：今日江阳区的警情");
            HttpEntity<ObjectNode> voiceToTextHttpEntity = new HttpEntity<>(textToVoiceForm, headers);
            ResponseEntity<byte[]> textToVoice = restTemplate.postForEntity(textToVoiceUrl, voiceToTextHttpEntity, byte[].class);
            voiceRecognitionVO.setVoice(Base64.getEncoder().encodeToString(textToVoice.getBody()));
            return voiceRecognitionVO;
        }
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        voiceRecognitionVO.setVoice(hashOperations.get(voiceBase64KeyOfRedis,address.getKey()));
        voiceRecognitionVO.setTextValue(hashOperations.get(voiceKeyOfRedis,address.getKey()));
        voiceRecognitionVO.setVoiceValue(requestAudioTxt);
        return voiceRecognitionVO;
    }

    /**
     * 从语音识别出来的文本内容中提取地区信息
     *
     * @param template 语音识别返回的模板信息
     * @return 地区信息
     */
    public KeyValueVO getAddressForTemplate(String template) {
        final List<UnitEntity> units = unitRepository.findByType("1");
        //正则表达式
        String regex = "(?<parm1>[今日]*今日)(?<parm2>[^县]+县|.+区|.*市)";
        Matcher m = Pattern.compile(regex).matcher(template);
        if (m.find()) {
            String address = m.group("parm2");
            return units
                    .stream().filter(unitEntity -> unitEntity.getShortname().equals(address))
                    .findFirst()
                    .map(unitEntity -> {
                        KeyValueVO keyValueVO = new KeyValueVO();
                        keyValueVO.setKey(unitEntity.getUnitCode());
                        keyValueVO.setValue(unitEntity.getShortname());
                        return keyValueVO;
                    }).orElse(null);
        } else {

            //如果字没有匹配上 用拼音匹配一次
            final String pinyinStr = conver2Pinyin(template);

            return units.stream().
                    filter(unit -> {
                        // 将单位简称转为拼音
                        String py = unit.getShortname().length() > 2 ? conver2Pinyin(StringUtils.substring(unit.getShortname(), 0, unit.getShortname().length() - 1)) : conver2Pinyin(unit.getShortname());
                        // 筛选语音文本拼音包含单位简拼的单位
                        return StringUtils.contains(pinyinStr, py);
                    })
                    .findAny()
                    .map(unit -> {
                        KeyValueVO keyValueVO = new KeyValueVO();
                        keyValueVO.setKey(unit.getUnitCode());
                        keyValueVO.setValue(unit.getShortname());
                        return keyValueVO;
                    }).orElse(null);
        }
    }

    /**
     * 文本转拼音
     *
     * @param txt 文本
     * @return 拼音
     */
    private String conver2Pinyin(String txt) {

        return IntStream.range(0, txt.length()).mapToObj(txt::charAt)
                .map(word -> String.join("", PinyinHelper.toHanyuPinyinStringArray(word)))
                .collect(Collectors.joining(" "));
    }
}
