package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 文件类型枚举
 *
 * <AUTHOR>
 * @date 2021/8/3 11:19
 */
public enum FileTypeEnum {

    /**
     * enums
     */
    VIDEO("0", "视频"),

    OFFICE("1", "office文档"),

    IMAGE("2", "图片"),

    OTHERS("3", "其他");

    @Getter
    private final String code;

    @Getter
    private final String name;

    FileTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据后缀名返回文件类型
     *
     * @param extension 后缀名
     * @return {@link FileTypeEnum}
     */
    public static FileTypeEnum ofExtension(String extension) {
        if (StringUtils.isBlank(extension)) {
            return FileTypeEnum.OTHERS;
        }
        switch (extension.toLowerCase()) {
            case "jpeg":
            case "jpg":
            case "png":
            case "gif":
                return FileTypeEnum.IMAGE;
            case "mp4":
            case "avi":
            case "mts":
            case "rm":
            case "rmvb":
            case "3gp":
            case "mpeg":
            case "mpg":
            case "mkv":
            case "dat":
            case "asf":
            case "wmv":
            case "flv":
            case "mov":
            case "ogg":
            case "ogm":
                return FileTypeEnum.VIDEO;
            case "doc":
            case "docx":
            case "xls":
            case "xlsx":
            case "ppt":
            case "pptx":
                return FileTypeEnum.OFFICE;
            default:
                return FileTypeEnum.OTHERS;
        }
    }
}
