package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.entity.RelationEntity;
import com.trs.yq.police.subject.domain.vo.RelationInfoVO;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.service.RelationService;
import com.trs.yq.police.subject.utils.BeanUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 人员关系接口
 *
 * <AUTHOR>
 * @date 2021/7/28 14:22
 */
@RestController
@RequestMapping("/person")
public class RelationController {
    private static final String RELATE_TYPE_SOCIETY = "society";
    private static final String RELATE_TYPE_FAMILY = "family";
    @Resource
    private RelationService relationService;
    @Resource
    private OperationLogHandler operationLogHandler;

    /**
     * 查询社会关系
     *
     * @param personId 关联人id
     * @return 关联人信息
     */
    @GetMapping("{personId}/society")
    public List<RelationInfoVO> querySocietyRelationInfo(@PathVariable String personId) {

        return relationService.getRelationInfo(personId, RELATE_TYPE_SOCIETY);
    }

    /**
     * 添加社会关系
     *
     * @param personId       人员id
     * @param relationInfoVO 社会关系信息实体
     */
    @PostMapping("/{personId}/society")
    public void saveSocietyRelate(@PathVariable String personId, @RequestBody RelationInfoVO relationInfoVO) {
        RelationEntity relationEntity = new RelationEntity();
        BeanUtil.copyPropertiesIgnoreNull(relationInfoVO, relationEntity);
        relationEntity.setType(RELATE_TYPE_SOCIETY);
        relationService.saveRelation(personId, relationEntity);

    }

    /**
     * 删除社会关系信息
     *
     * @param personId 人员id
     * @param id       社会关系主键
     */
    @DeleteMapping("/{personId}/society/{id}")
    public void deleteSocietyRelate(@PathVariable String personId, @PathVariable String id) {
        relationService.deleteRelation(personId, id);
    }

    /**
     * 更改社会关联人信息
     *
     * @param personId       人员id
     * @param relationInfoVO 社会关系人信息
     */
    @PutMapping("/{personId}/society")
    public void updateSocietyRelate(@PathVariable String personId, @RequestBody RelationInfoVO relationInfoVO) {
        RelationEntity relationEntity = new RelationEntity();
        BeanUtil.copyPropertiesIgnoreNull(relationInfoVO, relationEntity);
        relationEntity.setType(RELATE_TYPE_SOCIETY);
        relationService.updateRelation(personId, relationEntity);
    }

    /**
     * 查询家庭信息
     *
     * @param personId 关联人id
     * @return 家庭关系人信息
     */
    @GetMapping("/{personId}/family")
    public List<RelationInfoVO> queryFamilyRelationInfo(@PathVariable String personId) {

        return relationService.getRelationInfo(personId, RELATE_TYPE_FAMILY);
    }

    /**
     * 添加家庭关联人信息
     *
     * @param personId       人员id
     * @param relationInfoVO 家庭关系信息
     */
    @PostMapping("/{personId}/family")
    public void saveFamilyRelate(@PathVariable String personId, @RequestBody RelationInfoVO relationInfoVO) {
        RelationEntity relationEntity = new RelationEntity();
        BeanUtil.copyPropertiesIgnoreNull(relationInfoVO, relationEntity);
        relationEntity.setType(RELATE_TYPE_FAMILY);
        relationService.saveRelation(personId, relationEntity);

    }

    /**
     * 删除家庭关系人信息
     *
     * @param personId 人员id
     * @param id       主键
     */
    @DeleteMapping("/{personId}/family/{id}")
    public void deleteFamilyRelate(@PathVariable String personId, @PathVariable String id) {
        relationService.deleteRelation(personId, id);
    }

    /**
     * 更改家庭关系人信息
     *
     * @param personId       人员id
     * @param relationInfoVO 家庭关系信息
     */
    @PutMapping("/{personId}/family")
    public void updateFamilyRelate(@PathVariable String personId, @RequestBody RelationInfoVO relationInfoVO) {
        RelationEntity relationEntity = new RelationEntity();
        BeanUtil.copyPropertiesIgnoreNull(relationInfoVO, relationEntity);
        relationEntity.setType(RELATE_TYPE_FAMILY);
        relationService.updateRelation(personId, relationEntity);
    }
}
