package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.VisitRecordEntity;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 走访记录查询接口
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@Repository
public interface VisitRecordRepository extends BaseRepository<VisitRecordEntity, String> {


    /**
     * 查询人员所有走访记录按时间倒叙排序
     *
     * @param personId 人员id
     * @return 走访记录
     */
    List<VisitRecordEntity> findByPersonIdOrderByTimeDesc(@Param("personId") String personId);
}
