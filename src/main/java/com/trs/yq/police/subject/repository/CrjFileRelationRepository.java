package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjFileRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

/**
 * 出入境文件关联接口
 *
 * <AUTHOR>
 * @date 2021/9/17 11:57
 */
@Repository
public interface CrjFileRelationRepository extends BaseRepository<CrjFileRelationEntity, String> {

    /**
     * 删除某条出入境记录下的所有文件关联信息
     *
     * @param crjId 出入境id
     * @param module 模块id
     */
    @Modifying
    void deleteByCrjIdAndModule(String crjId, String module);
}
