package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.*;

/**
 * 审批服务层
 */
public interface ApprovalService {

    /**
     * 获取审批列表
     *
     * @param request 审批查询参数
     * @return {@link ApprovalListVO}
     */
    PageResult<ApprovalListVO> getApprovalList(ApprovalListRequest request);

    /**
     * 提交人员创建审批申请
     *
     * @param idNumber 身份证号
     * @param subjectId 专题id
     * @param personBasicVO 人员基本信息
     */
    void submitCreatePersonApproval(String idNumber, String subjectId, PersonBasicVO personBasicVO);

    /**
     * 提交删除人员审批申请
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param approvalVO 审批vo
     */
    void submitDeletePersonApproval(String personId, String subjectId, ApprovalVO approvalVO);

    /**
     * 提交归档人员申请
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param personArchiveVO 归档人员vo
     */
    void submitArchivePersonApproval(String personId, String subjectId, PersonArchiveVO personArchiveVO);

    /**
     * 提交激活人员申请
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param approvalVO 申请vo
     */
    void submitActivatePersonApproval(String personId, String subjectId, ApprovalVO approvalVO);

    /**
     * 同意申请
     *
     * @param id 审批申请id
     */
    void agreeApproval(String id);

    /**
     * 驳回申请
     *
     * @param id 审批申请id
     */
    void rejectApproval(String id);
}
