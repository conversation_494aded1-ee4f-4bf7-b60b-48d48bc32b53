package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;


/**
 * 关系交互数据
 *
 * <AUTHOR>
 * @date 2021/7/27 17:34
 */
@Data
public class RelationInfoVO implements Serializable {

    private static final long serialVersionUID = -1592397897825128795L;
    /**
     * 主键
     */
    private String id;
    /**
     * 关联人id
     */
    private String personId;
    /**
     * 关系
     */
    private String relation;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String idNumber;
    /**
     * 现住址
     */
    private String currentResidence;

    /**
     * 联系方式
     */
    private String contactInformation;

    /**
     * 是否为自动更新导入
     */
    private String isAutomated;
}
