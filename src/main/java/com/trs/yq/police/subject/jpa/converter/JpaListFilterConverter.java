package com.trs.yq.police.subject.jpa.converter;

import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.utils.JsonUtil;

import javax.persistence.AttributeConverter;
import java.util.List;

/**
 * jpa list与字符串转换
 *
 * <AUTHOR>
 * @date 2021/07/27
 */
public class JpaListFilterConverter implements AttributeConverter<List<ListFilter>, String> {

    /**
     * Converts the value stored in the entity attribute into the
     * data representation to be stored in the database.
     *
     * @param filters the entity attribute value to be converted
     * @return the converted data to be stored in the database column
     */
    @Override
    public String convertToDatabaseColumn(List<ListFilter> filters) {
        return JsonUtil.toJsonString(filters);
    }

    /**
     * Converts the data stored in the database column into the
     * value to be stored in the entity attribute.
     * Note that it is the responsibility of the converter writer to
     * specify the correct <code>dbData</code> type for the corresponding
     * column for use by the JDBC driver: i.e., persistence providers are
     * not expected to do such type conversion.
     *
     * @param dbData the data from the database column to be
     *               converted
     * @return the converted value to be stored in the entity attribute
     */
    @Override
    public List<ListFilter> convertToEntityAttribute(String dbData) {
        return JsonUtil.parseArray(dbData, ListFilter.class);
    }
}
