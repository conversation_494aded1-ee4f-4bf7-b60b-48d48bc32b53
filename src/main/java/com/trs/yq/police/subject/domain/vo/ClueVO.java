package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/09/03
 */
@Data
public class ClueVO implements Serializable {

    private static final long serialVersionUID = 4690354950399771671L;

    /**
     * 线索id
     */
    private String clueId;
    /**
     * 线索名称
     */
    @NotBlank(message = "线索名称不能为空！")
    private String clueName;
    /**
     * 线索来源
     */
    private String clueSource;
    /**
     * 紧急程度
     */
    private String emergencyLevel;
    /**
     * 公开程度
     */
    private String opennessLevel;
    /**
     * 线索详情
     */
    @NotBlank(message = "线索详情不能为空！")
    private String clueDetail;
    /**
     * 专题id
     */
    @NotBlank(message = "专题id不能为空！")
    private String subjectId;
    /**
     * 线索类别
     */
    private List<IdNameVO> clueTypes;
    /**
     * 人员
     */
    private List<IdNumberNameVO> relatedPersons;
    /**
     * 附件
     */
    private List<AttachmentVO> attachments;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;
    /**
     * 录入单位
     */
    private String createDeptName;
    /**
     * 上报状态
     */
    private String reportStatus;
    /**
     * 处置状态
     */
    private String disposalStatus;
    /**
     * 维权方式
     */
    private String method;
    /**
     * 维权行为
     */
    private String behaviour;
    /**
     * 维权时间
     */
    private LocalDateTime occurrenceTime;
    /**
     * 涉及市州
     */
    private String relatedPlace;
}
