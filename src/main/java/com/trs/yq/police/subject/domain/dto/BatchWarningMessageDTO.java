package com.trs.yq.police.subject.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 批处理预警消息结构
 *
 * <AUTHOR>
 * @date 2021/9/11 15:01
 */
@Getter
@Setter
@ToString
public class BatchWarningMessageDTO implements Serializable {

    private static final long serialVersionUID = 6213466168042630657L;

    /**
     * 预警类型
     */
    private String type;

    /**
     * 场所
     */
    private String place;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 预警时间
     */
    private String warningTime;

    /**
     * 人员数量
     */
    private Integer personCount;

    /**
     * 路径列表
     */
    private List<WarningTrajectoryListDTO> trajectories;
}
