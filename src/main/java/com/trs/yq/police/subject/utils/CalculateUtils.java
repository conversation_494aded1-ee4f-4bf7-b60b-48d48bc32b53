package com.trs.yq.police.subject.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/12/23 15:32
 */
@Slf4j
public class CalculateUtils {

    private CalculateUtils() {
    }

    /**
     * 获取百分比
     *
     * @param divisor  除数
     * @param dividend 被除数
     * @return 百分比
     */
    public static String calculatePercentage(Long divisor, Long dividend) {
        if (dividend == 0) {
            if (divisor != 0) {
                return "100%";
            }
            return "0%";
        }
        BigDecimal bqBigDecimal = BigDecimal.valueOf(divisor);
        BigDecimal sqBigDecimal = BigDecimal.valueOf(dividend);
        BigDecimal result = bqBigDecimal
            .divide(sqBigDecimal, 4, RoundingMode.UP)
            .multiply(BigDecimal.valueOf(100L))
            .setScale(2, RoundingMode.UP);
        return result.doubleValue()+"%";

    }
}
