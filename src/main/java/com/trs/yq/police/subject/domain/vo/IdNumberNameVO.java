package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 身份证号码和姓名VO
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IdNumberNameVO implements Serializable {

    private static final long serialVersionUID = 5947998666043626149L;

    /**
     * 身份证号码
     */
    @NotBlank(message = "身份证号码不能为空")
    private String idNumber;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

}
