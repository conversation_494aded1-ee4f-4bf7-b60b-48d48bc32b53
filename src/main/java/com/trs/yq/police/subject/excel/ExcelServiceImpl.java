package com.trs.yq.police.subject.excel;

import com.alibaba.excel.EasyExcelFactory;
import com.trs.yq.police.subject.constants.enums.CrjCzryFieldEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryFieldEnum;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;
import com.trs.yq.police.subject.service.RemoteStorageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * excel 业务层实现
 *
 * <AUTHOR>
 * @date 2021/8/9 10:26
 */
@Service
public class ExcelServiceImpl implements ExcelService {

    @Resource
    private RemoteStorageService remoteStorageService;

    private static final Path TEMP_DIR_PATH = Paths.get(System.getProperty("user.dir"), "temp");

    @Override
    public String[] writeToTempFile(String groupName,
                                    String extensionName,
                                    List<Map<Integer, String>> recordRows,
                                    Map<Integer, PersonFieldEnum> columnMap) throws IOException {
        List<List<String>> allHead = columnMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> {
                    List<String> head = new LinkedList<>();
                    head.add("人员档案导入模版");
                    head.add(entry.getValue().getCnName());
                    return head;
                })
                .collect(Collectors.toList());
        List<List<String>> rows = recordRows.stream().map(row -> columnMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> row.get(entry.getKey()))
                .collect(Collectors.toList())).collect(Collectors.toList());

        if (Files.notExists(TEMP_DIR_PATH) || !Files.isDirectory(TEMP_DIR_PATH)) {
            // 没有临时文件存储文件夹，则创建一个
            Files.createDirectory(TEMP_DIR_PATH);
        }

        final Path tempFile = Files.createFile(Paths.get(TEMP_DIR_PATH.toString(), "temp".concat(String.valueOf(System.currentTimeMillis())).concat(".xlsx")));
        EasyExcelFactory.write(Files.newOutputStream(tempFile)).head(allHead).sheet().doWrite(rows);
        final String[] strings = remoteStorageService.uploadFile(Files.readAllBytes(tempFile), groupName, extensionName);
        // 移除临时文件
        Files.deleteIfExists(tempFile);
        return strings;
    }

    @Override
    public String[] writeCrjToTempFile(String groupName, String extensionName, List<Map<Integer, String>> recordRows,
        Map<Integer, CrjJwryFieldEnum> columnMap) throws IOException {
        List<List<String>> allHead = columnMap.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> {
                List<String> head = new LinkedList<>();
                head.add(entry.getValue().getCnName());
                return head;
            })
            .collect(Collectors.toList());
        List<List<String>> rows = recordRows.stream().map(row -> columnMap.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> row.get(entry.getKey()))
            .collect(Collectors.toList())).collect(Collectors.toList());

        if (Files.notExists(TEMP_DIR_PATH) || !Files.isDirectory(TEMP_DIR_PATH)) {
            // 没有临时文件存储文件夹，则创建一个
            Files.createDirectory(TEMP_DIR_PATH);
        }

        final Path tempFile = Files.createFile(Paths.get(TEMP_DIR_PATH.toString(), "temp".concat(String.valueOf(System.currentTimeMillis())).concat(".xlsx")));
        EasyExcelFactory.write(Files.newOutputStream(tempFile)).head(allHead).sheet().doWrite(rows);
        final String[] strings = remoteStorageService.uploadFile(Files.readAllBytes(tempFile), groupName, extensionName);
        // 移除临时文件
        Files.deleteIfExists(tempFile);
        return strings;
    }

    @Override
    public String[] writeCrjCzryToTempFile(String groupName, String extensionName, List<Map<Integer, String>> recordRows,
        Map<Integer, CrjCzryFieldEnum> columnMap) throws IOException {
        List<List<String>> allHead = columnMap.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> {
                List<String> head = new LinkedList<>();
                head.add("出入境常住人员");
                head.add(entry.getValue().getCnName());
                return head;
            })
            .collect(Collectors.toList());
        List<List<String>> rows = recordRows.stream().map(row -> columnMap.entrySet()
            .stream()
            .sorted(Map.Entry.comparingByKey())
            .map(entry -> row.get(entry.getKey()))
            .collect(Collectors.toList())).collect(Collectors.toList());

        if (Files.notExists(TEMP_DIR_PATH) || !Files.isDirectory(TEMP_DIR_PATH)) {
            // 没有临时文件存储文件夹，则创建一个
            Files.createDirectory(TEMP_DIR_PATH);
        }

        final Path tempFile = Files.createFile(Paths.get(TEMP_DIR_PATH.toString(), "temp".concat(String.valueOf(System.currentTimeMillis())).concat(".xlsx")));
        EasyExcelFactory.write(Files.newOutputStream(tempFile)).head(allHead).sheet().doWrite(rows);
        final String[] strings = remoteStorageService.uploadFile(Files.readAllBytes(tempFile), groupName, extensionName);
        // 移除临时文件
        Files.deleteIfExists(tempFile);
        return strings;
    }
}
