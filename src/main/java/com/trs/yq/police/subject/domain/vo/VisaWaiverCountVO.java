package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 成都重庆过境免签人员活动情况vo
 *
 * <AUTHOR>
 * @since 2021/9/17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class VisaWaiverCountVO implements Serializable {

    private static final long serialVersionUID = 829865084994820475L;

    /**
     * 城市名
     */
    private String cityName;
    /**
     * 人员数
     */
    private Integer totalCount;
    /**
     * 泸州活动人员
     */
    private Integer activeCount;
}
