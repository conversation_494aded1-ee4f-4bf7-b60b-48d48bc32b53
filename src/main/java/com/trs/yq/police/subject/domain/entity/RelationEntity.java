package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/7/27 15:17
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_RELATION")
public class RelationEntity extends BaseEntity {

    private static final long serialVersionUID = 2175281541808878478L;
    /**
     * 关系类型： family,society
     */
    private String type;
    /**
     * 关联人Id
     */
    private String personId;
    /**
     * 与当前人物关系
     */
    private String relation;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号码
     */
    private String idNumber;
    /**
     * 现住址
     */
    private String currentResidence;
    /**
     * 联系方式
     */
    private String contactInformation;

    /**
     * 是否为自动更新导入
     */
    private String isAutomated;
}
