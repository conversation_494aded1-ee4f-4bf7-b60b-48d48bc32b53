package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.entity.LocationEntity;
import com.trs.yq.police.subject.repository.LocationRepository;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 地点-经纬度接口
 *
 * <AUTHOR>
 * @date 2021/12/21 11:28
 */
@RestController
@RequestMapping("/map")
public class AddressController {
    @Resource
    private LocationRepository locationRepository;

    /**
     * 获取已有地图位置
     * http://***************:3001/project/4897/interface/api/134343
     *
     * @param searchValue 检索值
     * @return {@link LocationEntity}
     */
    @GetMapping("/address")
    public List<LocationEntity> getAddressInfo(String searchValue) {
        return locationRepository.findByNameLike(searchValue);
    }
}
