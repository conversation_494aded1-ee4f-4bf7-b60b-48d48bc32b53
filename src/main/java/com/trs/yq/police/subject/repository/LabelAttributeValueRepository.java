package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.LabelAttributeValueEntity;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/1/12 15:49
 */
@Repository
public interface LabelAttributeValueRepository extends BaseRepository<LabelAttributeValueEntity, String> {
    /**
     * 获取属性的属性值
     *
     * @param attributeId 属性id
     * @return {@link LabelAttributeValueEntity}
     */
    Optional<LabelAttributeValueEntity> findByAttributeId(String attributeId);
}
