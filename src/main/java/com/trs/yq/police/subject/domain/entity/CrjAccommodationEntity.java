package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 出入境住宿登记
 *
 * <AUTHOR>
 * @since 2021/9/16
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_ACCOMMODATION")
public class CrjAccommodationEntity extends BaseEntity {
    private static final long serialVersionUID = -5309613868844229958L;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 名
     */
    private String firstName;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 住宿详细地址
     */
    private String livingInfo;
    /**
     * 旅店信息
     */
    private String livingInn;
    /**
     * 联系电话
     */
    private String phoneNumber;
    /**
     * 拟离开时间
     */
    private LocalDateTime departureDate;
}
