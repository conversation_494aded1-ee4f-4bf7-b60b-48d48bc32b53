package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/30 16:42
 */
@Repository
public interface CrjJwryDetailRepository extends BaseRepository<CrjJwryDetailEntity, String> {

    /**
     * 查询
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @return {@link CrjJwryDetailEntity}
     */
    Optional<CrjJwryDetailEntity> findByIdTypeAndIdNumber(String idType, String idNumber);

    /**
     * 查询
     *
     * @param idNumber 证件号码
     * @return {@link CrjJwryDetailEntity}
     */
    List<CrjJwryDetailEntity> findByIdNumber(String idNumber);

    /**
     * 更新
     *
     * @param cnName      cnName
     * @param enName      enName
     * @param enFirstName enFirstName
     * @param enLastName  enLastName
     * @param gender      gender
     * @param gjdm        gjdm
     * @param idType      idType
     * @param idNumber    idNumber
     * @param birthday    birthday
     * @param phone       phone
     * @param liveAddress liveAddress
     * @param workAddress workAddress
     * @param inChinaTime inChinaTime
     * @param entryTime   entryTime
     * @param entryPort   entryPort
     * @param visaType    visaType
     * @param visaNumber  visaNumber
     */
    @Modifying
    @Query("update CrjJwryDetailEntity t set "
        + "t.cnName=:cnName,"
        + "t.enName=:enName,"
        + "t.enFirstName=:enFirstName,"
        + "t.enLastName=:enLastName,"
        + "t.gender=:gender,"
        + "t.gjdm=:gjdm,"
        + "t.birthday=:birthday,"
        + "t.phone=:phone,"
        + "t.liveAddress=:liveAddress,"
        + "t.workAddress=:workAddress,"
        + "t.inChinaTime=:inChinaTime, "
        + "t.entryTime=:entryTime,"
        + "t.entryPort=:entryPort," +
        "t.visaType=:visaType," +
        "t.visaNumber=:visaNumber,"
        + "t.upTime=current_timestamp "
        + "where t.idNumber=:idNumber and t.idType =:idType")
    void update(@Param("cnName") String cnName, @Param("enName") String enName,
        @Param("enFirstName") String enFirstName, @Param("enLastName") String enLastName,
        @Param("gender") String gender,
        @Param("gjdm") String gjdm, @Param("idType") String idType, @Param("idNumber") String idNumber,
        @Param("birthday") Date birthday, @Param("phone") String phone, @Param("liveAddress") String liveAddress,
        @Param("workAddress") String workAddress, @Param("inChinaTime") Date inChinaTime,
        @Param("entryTime") Date entryTime,
        @Param("entryPort") String entryPort,
        @Param("visaType") String visaType,
        @Param("visaNumber") String visaNumber);

    /**
     * 走访后更新信息
     *
     * @param idType      idType
     * @param idNumber    idNumber
     * @param phone       phone
     * @param liveAddress liveAddress
     * @param workAddress workAddress
     */
    @Modifying
    @Query("update CrjJwryDetailEntity t set "
        + "t.phone=:phone,"
        + "t.liveAddress=:liveAddress,"
        + "t.workAddress=:workAddress,"
        + "t.upTime=current_timestamp "
        + "where t.idNumber=:idNumber and t.idType =:idType")
    void updateVisit(@Param("idType") String idType, @Param("idNumber") String idNumber, @Param("phone") String phone,
        @Param("liveAddress") String liveAddress, @Param("workAddress") String workAddress);

    /**
     * 更新国家代码
     *
     * @param idType   身份证类型
     * @param idNumber 身份证号码
     * @param gjdm     国家代码
     */
    @Modifying
    @Query("update CrjJwryDetailEntity t set "
        + "t.gjdm=:gjdm,"
        + "t.upTime=current_timestamp "
        + "where t.idNumber=:idNumber and t.idType =:idType")
    void updateGjdm(@Param("idType") String idType, @Param("idNumber") String idNumber, @Param("gjdm") String gjdm);

    /**
     * 查询签证到期时间在指定日期前的
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @Query(nativeQuery = true,value = " \n"
        + "SELECT * FROM T_PS_CRJ_JWRY_DETAIL t1 join (SELECT t.ID_NUMBER,t.ID_TYPE from T_PS_CRJ_JWRY t \n"
        + "                         JOIN  (SELECT max(UP_TIME) UP_TIME,ID_TYPE,ID_NUMBER FROM T_PS_CRJ_JWRY WHERE (REGISTRATION_STATUS = '1' or REGISTRATION_STATUS = '2') GROUP BY ID_TYPE,ID_NUMBER) b \n"
        + "                        on t.UP_TIME = b.UP_TIME AND t.ID_TYPE = B.ID_TYPE AND T.ID_NUMBER = b.ID_NUMBER where  t.REGISTRATION_STATUS ='1') t2 on (t1.ID_TYPE=t2.ID_TYPE and t1.ID_NUMBER=t2.ID_NUMBER) where ((t1.IN_CHINA_TIME between :beginTime and :endTime) or (:beginTime between (t1.ENTRY_TIME + INTERVAL '7' DAY) and (t1.ENTRY_TIME + INTERVAL '15' DAY)) )")
    List<CrjJwryDetailEntity> findByInChinaTimeBetween(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * 删除
     *
     * @param idNumber 证件号码
     * @param idType   证件类型
     */
    @Modifying
    @Query(nativeQuery = true,value = "delete from T_PS_CRJ_JWRY_DETAIL c where c.id_number=:idNumber and c.id_type=:idType")
    void deleteByIdNumberAndIdType(String idNumber, String idType);
}
