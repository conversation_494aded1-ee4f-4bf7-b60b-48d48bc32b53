package com.trs.yq.police.subject.controller.group;

import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SensitiveListRequestVO;
import com.trs.yq.police.subject.domain.vo.SensitiveTimeListVO;
import com.trs.yq.police.subject.domain.vo.SensitiveTimeVO;
import com.trs.yq.police.subject.service.SensitiveTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 敏感时间节点http接口
 *
 * <AUTHOR>
 * @date 2021/12/28 14:13
 */
@RestController
@RequestMapping("/stability/sensitive")
@Slf4j
@Validated
public class SensitiveTimeController {
    @Resource
    private SensitiveTimeService sensitiveTimeService;

    /**
     * 创建敏感时间节点
     * http://192.168.200.192:3001/project/4897/interface/api/134534
     *
     * @param sensitiveTimeVO {@link SensitiveTimeVO}
     * @return 敏感时间节点id
     */
    @PostMapping("")
    public String createSensitive(@RequestBody SensitiveTimeVO sensitiveTimeVO) {
        return sensitiveTimeService.createSensitive(sensitiveTimeVO);
    }

    /**
     * 更新敏感时间节点
     * http://192.168.200.192:3001/project/4897/interface/api/134524
     *
     * @param nodeId          敏感时间节点id
     * @param sensitiveTimeVO {@link SensitiveTimeVO}
     */
    @PutMapping("/{nodeId}")
    public void updateSensitive(@PathVariable String nodeId, @RequestBody SensitiveTimeVO sensitiveTimeVO) {
        sensitiveTimeVO.setNodeId(nodeId);
        sensitiveTimeService.updateSensitive(sensitiveTimeVO);
    }

    /**
     * 删除敏感时间节点
     * http://192.168.200.192:3001/project/4897/interface/api/134529
     *
     * @param nodeId 敏感时间节点id
     */
    @DeleteMapping("/{nodeId}")
    public void deleteSensitive(@PathVariable String nodeId) {
         sensitiveTimeService.deleteSensitive(nodeId);
    }

    /**
     * 敏感时间节点列表查询
     * http://192.168.200.192:3001/project/4897/interface/api/134514
     *
     * @param sensitiveListRequestVO {@link SensitiveListRequestVO}
     * @return {@link SensitiveTimeListVO}
     */
    @PostMapping("/list")
    public PageResult<SensitiveTimeListVO> getSensitiveList(@RequestBody SensitiveListRequestVO sensitiveListRequestVO) {
        return sensitiveTimeService.getSensitiveList(sensitiveListRequestVO);
    }


}
