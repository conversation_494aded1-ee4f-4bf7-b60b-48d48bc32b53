package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.domain.entity.GroupTimeRelationEntity;
import com.trs.yq.police.subject.domain.entity.SensitiveTimeEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SensitiveListRequestVO;
import com.trs.yq.police.subject.domain.vo.SensitiveTimeListVO;
import com.trs.yq.police.subject.domain.vo.SensitiveTimeVO;
import com.trs.yq.police.subject.exception.InteractException;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.GroupRepository;
import com.trs.yq.police.subject.repository.GroupTimeRelationRepository;
import com.trs.yq.police.subject.repository.SensitiveTimeRepository;
import com.trs.yq.police.subject.service.SensitiveTimeService;
import com.trs.yq.police.subject.utils.BeanUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * <AUTHOR>
 * @date 2021/12/28 14:15
 */
@Service
@Transactional(rollbackFor = RuntimeException.class, readOnly = true)
public class SensitiveTimeServiceImpl implements SensitiveTimeService {
    @Resource
    private SensitiveTimeRepository sensitiveTimeRepository;
    @Resource
    private GroupTimeRelationRepository groupTimeRelationRepository;
    @Resource
    private GroupRepository groupRepository;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String createSensitive(SensitiveTimeVO sensitiveTimeVO) {
        SensitiveTimeEntity sensitiveTimeEntity = new SensitiveTimeEntity();
        BeanUtil.copyPropertiesIgnoreNull(sensitiveTimeVO, sensitiveTimeEntity);
        boolean needRelateGroup = SensitiveTimeEntity.SCOPE_ALL == sensitiveTimeVO.getNodeType();
        //关联群体
        String id = sensitiveTimeRepository.save(sensitiveTimeEntity).getId();
        if (needRelateGroup) {
            groupRepository.findAllBySubjectId(WW_SUBJECT).forEach(groupEntity -> {
                GroupTimeRelationEntity groupTimeRelationEntity = new GroupTimeRelationEntity();
                groupTimeRelationEntity.setGroupId(groupEntity.getId());
                groupTimeRelationEntity.setSensitiveTimeId(id);
                groupTimeRelationRepository.save(groupTimeRelationEntity);
            });
        }
        return id;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateSensitive(SensitiveTimeVO sensitiveTimeVO) {
        SensitiveTimeEntity entity = sensitiveTimeRepository.findById(sensitiveTimeVO.getNodeId())
                .orElseThrow(() -> new ParamValidationException("不存在的敏感节点"));
        BeanUtils.copyProperties(sensitiveTimeVO, entity);
        sensitiveTimeRepository.save(entity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteSensitive(String sensitiveId) {
        final SensitiveTimeEntity entity = sensitiveTimeRepository.findById(sensitiveId).orElseThrow(() -> new RuntimeException("不存在的敏感节点"));
        if (!groupTimeRelationRepository.findAllBySensitiveTimeId(sensitiveId).isEmpty()) {
            throw new InteractException("当前敏感时间节点被使用，无法删除！");
        }
        sensitiveTimeRepository.deleteById(sensitiveId);
    }

    @Override
    public PageResult<SensitiveTimeListVO> getSensitiveList(SensitiveListRequestVO sensitiveListRequestVO) {

        SearchParams searchParams = sensitiveListRequestVO.getSearchParams();
        PageParams pageParams = sensitiveListRequestVO.getPageParams();
        Specification<SensitiveTimeEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            Predicate namePredicate = criteriaBuilder.like(root.get("name").as(String.class), "%" + searchValue + "%");
            Predicate remarkPredicate = criteriaBuilder.like(root.get("remark").as(String.class), "%" + searchValue + "%");
            switch (searchField) {
                case "name":
                    predicates.add(namePredicate);
                    break;
                case "fullText":
                    predicates.add(criteriaBuilder.or(namePredicate, remarkPredicate));
                    break;
                default:
                    break;
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        Sort sort = Sort.by(Sort.Direction.DESC, "upTime");
        Page<SensitiveTimeEntity> page = sensitiveTimeRepository.findAll(specification, pageParams.toPageable(sort));
        List<SensitiveTimeListVO> result = page.stream().map(SensitiveTimeListVO::of).collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), page.getTotalElements(), pageParams.getPageSize());
    }
}
