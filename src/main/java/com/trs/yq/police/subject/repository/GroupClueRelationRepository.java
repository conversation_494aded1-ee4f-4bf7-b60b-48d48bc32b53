package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.GroupClueRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 群体-线索关联表数据层
 *
 * <AUTHOR>
 * @date 2021/9/1 15:13
 */
@Repository
public interface GroupClueRelationRepository extends BaseRepository<GroupClueRelationEntity, String> {
    /**
     * 根据群体id删除所有关联关系
     *
     * @param groupId 群体Id
     */
    @Modifying
    void removeAllByGroupId(String groupId);

    /**
     * 查询群体关联线索数量
     *
     * @param groupId 群体id
     * @return 线索数量
     */
    Integer countAllByGroupId(String groupId);

    /**
     * 批量删除线索关联关系
     *
     * @param clueId 线索id
     */
    void removeAllByClueId(String clueId);

    /**
     * 根据群体id查询群体线索关联列表
     *
     * @param groupId 群体id
     * @return 群体线索关联列表
     */
    List<GroupClueRelationEntity> findAllByGroupId(String groupId);

    /**
     * 根据线索id 和 群体id 查询关联关系
     *
     * @param clueId  线索id
     * @param groupId 群体id
     * @return 关联关系实体
     */
    GroupClueRelationEntity findByClueIdAndGroupId(String clueId, String groupId);

    /**
     * 根据线索id查询关联关系
     *
     * @param clueId 线索id
     * @return {@link GroupClueRelationEntity}
     */
    List<GroupClueRelationEntity> findAllByClueId(String clueId);

    /**
     * 根据groupId删除所有关联
     *
     * @param groupId 群体id
     */
    void deleteAllByGroupId(String groupId);

    /**
     * 根据clueId删除所有关联
     *
     * @param clueId 线索id
     */
    void deleteAllByClueId(String clueId);
}
