package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.ApprovalEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 审批列表vo
 */
@Data
public class ApprovalListVO implements Serializable {

    private static final long serialVersionUID = 6306190216257845440L;

    /**
     * id
     */
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 用户名
     */
    private String account;
    /**
     * 申请类型
     */
    private String applyType;
    /**
     * 详情
     */
    private String details;
    /**
     * 申请时间
     */
    private LocalDateTime updateTime;

    /**
     * 构造器
     *
     * @param entity {@link ApprovalEntity}
     */
    public ApprovalListVO(ApprovalEntity entity) {
        this.id = entity.getId();
        this.name = entity.getCrByName();
        this.account = entity.getCrBy();
        this.applyType = entity.getDetail();
        this.updateTime = entity.getCrTime();
    }
}
