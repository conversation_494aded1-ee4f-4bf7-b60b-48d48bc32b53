package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.CallVO;
import com.trs.yq.police.subject.domain.vo.FundVO;
import com.trs.yq.police.subject.domain.vo.PageResult;

/**
 * <AUTHOR>
 */
public interface CallService {

    /**
     * 话单列表
     *
     * @param personId 人员id
     * @param pageParams 分页参数
     * @return 列表
     */
    PageResult<CallVO> getCallListByPersonId(String personId, PageParams pageParams);

    /**
     * 添加话单
     *
     * @param personId 人员id
     * @param callVO 话单
     */
    void addCall(String personId, CallVO callVO);

    /**
     * 修改话单
     *
     * @param personId 人员id
     * @param callVO 话单
     */
    void modifyCall(String personId, CallVO callVO);

    /**
     * 删除话单
     *
     * @param id 话单id
     * @param personId 人员id
     */
    void deleteCall(String personId, String id);

    /**
     * 资金列表
     *
     * @param personId 人员id
     * @param pageParams 分页参数
     * @return 列表
     */
    PageResult<FundVO> getFundListByPersonId(String personId, PageParams pageParams);

    /**
     * 添加资金
     *
     * @param personId 人员id
     * @param fundVO 资金
     */
    void addFund(String personId, FundVO fundVO);

    /**
     * 修改资金
     *
     * @param personId 人员id
     * @param fundVO 资金
     */
    void modifyFund(String personId, FundVO fundVO);

    /**
     * 删除资金
     *
     * @param id 资金id
     * @param personId 人员id
     */
    void deleteFund(String personId, String id);
}
