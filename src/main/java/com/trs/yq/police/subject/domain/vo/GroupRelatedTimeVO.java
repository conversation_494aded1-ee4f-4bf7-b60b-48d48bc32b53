package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.SensitiveTimeEntity;
import com.trs.yq.police.subject.repository.GroupTimeRelationRepository;
import com.trs.yq.police.subject.utils.SpringContextUtil;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022/1/14 14:10
 */
@Data
public class GroupRelatedTimeVO implements Serializable {
    private static final long serialVersionUID = 2695648119160194771L;
    private String nodeId;
    /**
     * 关联id
     */
    private String relateId;
    /**
     * 敏感节点名称
     */
    @NotBlank(message = "节点名称缺失！")
    private String name;
    /**
     * 敏感节点备注
     */
    private String remark;
    /**
     * 开始时间
     */
    @NotNull(message = "起始时间缺失！！")
    private LocalDate startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间缺失！！")
    private LocalDate endTime;

    private Long days;

    /**
     * 转VO
     *
     * @param entity  实体类
     * @param groupId 群体id
     * @return VO
     */
    public static GroupRelatedTimeVO of(SensitiveTimeEntity entity, String groupId) {
        GroupRelatedTimeVO vo = new GroupRelatedTimeVO();
        BeanUtils.copyProperties(entity, vo);
        long days = entity.getEndTime().toEpochDay() - entity.getStartTime().toEpochDay() + 1;
        vo.setDays(days);
        vo.setNodeId(entity.getId());
        GroupTimeRelationRepository relationRepository = SpringContextUtil.getBean(GroupTimeRelationRepository.class);
        vo.setRelateId(relationRepository.findAllBySensitiveTimeIdAndGroupId(entity.getId(), groupId).getId());
        return vo;
    }

}
