package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonArchiveRecordEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人员归档激活记录查询接口
 *
 * <AUTHOR>
 * @date 2021/08/10
 */
@Repository
public interface PersonArchiveRecordRepository extends BaseRepository<PersonArchiveRecordEntity, String> {
    /**
     * 查询归档激活记录
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 归档激活记录
     */
    List<PersonArchiveRecordEntity> findByPersonIdAndSubjectId(String personId, String subjectId);
}
