package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BattleEventEntity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 事件信息接口
 *
 * <AUTHOR>
 * @date 2021/9/29 14:09
 */
public interface BattleEventRepository extends BaseRepository<BattleEventEntity, String> {
    /**
     * 查找所有高风险警情
     *
     * @return {@link BattleEventEntity}
     */
    @Query("select t from BattleEventEntity t where t.savetype='5' " +
            "AND t.id NOT IN (" +
            "   SELECT w.warningKey FROM WarningEntity w WHERE w.warningKey is not null" +
            ")")
    List<BattleEventEntity> findHighRiskWarning();
}
