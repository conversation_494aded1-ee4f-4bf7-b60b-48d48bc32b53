package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.MobilePhoneEntity;
import com.trs.yq.police.subject.domain.vo.MobilePhoneVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.MobilePhoneRepository;
import com.trs.yq.police.subject.service.MobilePhoneService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.AUTOMATED;

/**
 * 电话信息业务类
 *
 * <AUTHOR>
 * @date 2021/7/28 9:48
 */
@Service
public class MobilePhoneServiceImpl implements MobilePhoneService {
    @Resource
    private MobilePhoneRepository mobilePhoneRepository;
    @Resource
    private PersonService personService;
    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public List<MobilePhoneVO> findByPersonId(String personId) {
        //检查当前人员是否存在
        personService.checkPersonExist(personId);
        List<MobilePhoneEntity> mobilePhoneList = mobilePhoneRepository.findByPersonId(personId);
        if (!Objects.isNull(mobilePhoneList)) {
            List<MobilePhoneVO> mobilePhoneVOList = new ArrayList<>();
            mobilePhoneList.forEach(item -> {
                MobilePhoneVO mobilePhoneVo = new MobilePhoneVO();
                BeanUtil.copyPropertiesIgnoreNull(item, mobilePhoneVo);
                mobilePhoneVOList.add(mobilePhoneVo);
            });
            return mobilePhoneVOList;
        }
        return Collections.emptyList();
    }

    @Override
    public void deletePhone(String personId, String id) {
        //检查当前人员是否存在
        personService.checkPersonExist(personId);

        final MobilePhoneEntity phoneEntity = mobilePhoneRepository.findById(id).orElse(null);
        if (Objects.isNull(phoneEntity)) {
            throw new ParamValidationException("手机号不存在，请刷新核实");
        }

        if(StringUtils.equals(phoneEntity.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可删除");
        }
        // 操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.CONCAT_METHOD)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除手机号")
                .oldObj(JsonUtil.toJsonString(phoneEntity))
                .build();
        mobilePhoneRepository.deleteById(id);
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void savePhone(String personId, MobilePhoneVO mobilePhoneVO) {
        //检查当前人员是否存在
        personService.checkPersonExist(mobilePhoneVO.getPersonId());
        MobilePhoneEntity mobilePhoneEntity = new MobilePhoneEntity();
        BeanUtil.copyPropertiesIgnoreNull(mobilePhoneVO, mobilePhoneEntity);
        mobilePhoneRepository.save(mobilePhoneEntity);
        // 操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.CONCAT_METHOD)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增手机号")
                .newObj(JsonUtil.toJsonString(mobilePhoneEntity))
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void updatePhone(String personId, MobilePhoneVO mobilePhoneVO) {
        // 校验人员是否存在
        personService.checkPersonExist(personId);

        final MobilePhoneEntity oldPhoneEntity = mobilePhoneRepository.findById(mobilePhoneVO.getId()).orElse(null);
        // 校验手机号是否存在
        if (Objects.isNull(oldPhoneEntity)) {
            throw new ParamValidationException("手机号不存在，请刷新核实!");
        }

        if(StringUtils.equals(oldPhoneEntity.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可修改");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.CONCAT_METHOD)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑手机号")
                .oldObj(JsonUtil.toJsonString(oldPhoneEntity))
                .build();

        oldPhoneEntity.setPhoneNumber(mobilePhoneVO.getPhoneNumber());
        oldPhoneEntity.setPhoneStatus(mobilePhoneVO.getPhoneStatus());
        oldPhoneEntity.setPhoneUseStatus(mobilePhoneVO.getPhoneUseStatus());
        mobilePhoneRepository.save(oldPhoneEntity);
        // 记录操作
        logRecord.setNewObj(JsonUtil.toJsonString(oldPhoneEntity));
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

}
