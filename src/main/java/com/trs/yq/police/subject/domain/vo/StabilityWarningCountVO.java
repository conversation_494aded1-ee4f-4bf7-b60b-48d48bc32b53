package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 维稳专题进京赴省数量vo
 *
 * <AUTHOR>
 * @date 2021/10/20
 */
@Data
@AllArgsConstructor
public class StabilityWarningCountVO implements Serializable {
    @NotBlank
    private static final long serialVersionUID = -7302693227487334289L;

    /**
     * 未签收
     */
    private Integer unsigned;

    /**
     * 已签收
     */
    private Integer signed;
}
