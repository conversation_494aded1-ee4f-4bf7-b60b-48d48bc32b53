package com.trs.yq.police.subject.domain.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 11:10
 */

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_READ_RECORD")
public class CrjReadRecordEntity extends BaseEntity {

    private String recordId;

    private String userId;

    private String module;

}
