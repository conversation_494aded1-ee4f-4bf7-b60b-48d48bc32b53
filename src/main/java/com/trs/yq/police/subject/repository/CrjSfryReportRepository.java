package com.trs.yq.police.subject.repository;


import com.trs.yq.police.subject.domain.entity.CrjSfryReportEntity;
import java.util.List;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 20:22
 */
@Repository
public interface CrjSfryReportRepository extends BaseRepository<CrjSfryReportEntity, String> {

    /**
     * 获取详情
     *
     * @param reportId uuid
     * @return {@link CrjSfryReportEntity}
     */
    CrjSfryReportEntity findByReportId(String reportId);

    /**
     * 核实
     *
     * @param recordId       uuid
     * @param verifyUser     核实人
     * @param verifyResult   核实结果
     * @param description    备注
     * @param acceptor       接收者
     * @param dispatchStatus 分派状态
     */
    @Modifying
    @Query(
        "update CrjSfryReportEntity set description=:description,verifyResult=:verifyResult,isVerified=true,verifyUser=:verifyUser,"
            + "verifyTime = current_timestamp,acceptor =:acceptor,dispatchStatus=:dispatchStatus,upTime=current_timestamp"
            + " where reportId=:recordId")
    void verify(@Param("recordId") String recordId, @Param("verifyUser") String verifyUser,
        @Param("verifyResult") String verifyResult, @Param("description") String description,
        @Param("acceptor") String acceptor, @Param("dispatchStatus") String dispatchStatus
    );

    /**
     * 分派
     *
     * @param reportIds      uuids
     * @param acceptor       接受部门
     * @param dispatchStatus 分派状态
     */
    @Modifying
    @Query("update CrjSfryReportEntity t set t.acceptor=:acceptor,t.dispatchStatus=:dispatchStatus,t.upTime=current_timestamp " +
            "where t.reportId in (:reportIds) " +
            "and t.isVerified = false ")
    void setAcceptor(@Param("reportIds") List<String> reportIds, @Param("acceptor") String acceptor,
        @Param("dispatchStatus") String dispatchStatus);

    /**
     * 统计未读
     *
     * @param userId         用户id
     * @param unitCode       部门编号
     * @param unitCodePrefix 部门编号前缀
     * @param module         模块
     * @param dispatchStatus 分派状态
     * @param isVerified     是否核实
     * @return {@link Long}
     */
    @Query("select count(1) from CrjSfryReportEntity t "
        + "where not exists (select r from CrjReadRecordEntity r where r.userId =:userId and r.module=:module and r.recordId = t.reportId)"
        + "and (:unitCode is null or t.acceptor=:unitCode)"
        + "and (:unitCodePrefix is null or t.acceptor like concat(:unitCodePrefix,'%'))"
        + "and t.dispatchStatus in (:dispatchStatus) "
        + "and (:isVerified is null or t.isVerified =:isVerified)")
    Long countUnread(@Param("userId") String userId, @Param("unitCode") String unitCode,
        @Param("unitCodePrefix") String unitCodePrefix, @Param("module") String module,
        @Param("dispatchStatus") List<String> dispatchStatus,@Param("isVerified")Boolean isVerified);

    /**
     * 获取所有id
     *
     * @return {@link CrjSfryReportEntity}
     */
    @Query("select t.reportId from CrjSfryReportEntity t")
    List<String> findAllId();


    /**
     * @param reportIds reportIds
     * @return entity
     */
    @Query("SELECT crj FROM CrjSfryReportEntity crj WHERE crj.reportId in (:reportIds)")
    List<CrjSfryReportEntity> findByIds(@Param("reportIds") List<String> reportIds);
}
