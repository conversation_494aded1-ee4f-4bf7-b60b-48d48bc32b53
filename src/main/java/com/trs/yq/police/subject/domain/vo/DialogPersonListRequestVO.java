package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/09/04
 */
@Data
public class DialogPersonListRequestVO implements Serializable {

    private static final long serialVersionUID = -8714621697648053466L;

    /**
     * 其他参数
     */
    @Data
    public static class OtherParams implements Serializable {

        private static final long serialVersionUID = 7921305791514821302L;

        /**
         * 专题id
         */
        @NotBlank(message = "专题id不能为空！")
        private String subjectId;

        /**
         * 人员类别
         */
        private String personTypeId;

        /**
         * 录入单位
         */
        private String createDeptId;
    }

    /**
     * 其他参数
     */
    private OtherParams otherParams;

    /**
     * 模糊检索参数
     */
    private SearchParams searchParams;

    /**
     * 分页
     */
    private PageParams pageParams;

}
