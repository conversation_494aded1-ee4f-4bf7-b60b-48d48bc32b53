package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 档案目录实体
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_MODULE")
public class ModuleEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 中文名
     */
    private String cnName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 类型
     */
    private String type;

    /**
     * 根据id和中文名生成模块
     *
     * @param id id
     * @param cnName 中文名称
     */
    public ModuleEntity(String id, String cnName) {
        this.id = id;
        this.cnName = cnName;
    }
}

