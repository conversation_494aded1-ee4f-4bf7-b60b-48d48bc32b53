package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 态势图数据
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/12 14:04
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SituationMapVO {

    /**
     * 区域名称
     */
    private String shortName;

    /**
     * 区域中心经度
     */
    private Double centerX;

    /**
     * 区域中心纬度
     */
    private Double centerY;

    /**
     * 区域多边形
     */
    private String polygon;

    /**
     * 当前
     */
    private Long current;

    /**
     * 环比历史
     */
    private Long ringHistory;

    /**
     * 环比
     */
    private String ringCompare;

    /**
     * 同比历史
     */
    private Long yoyHistory;

    /**
     * 同比
     */
    private String yoyCompare;
}
