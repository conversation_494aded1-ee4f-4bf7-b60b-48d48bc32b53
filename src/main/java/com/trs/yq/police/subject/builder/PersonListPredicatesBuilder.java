package com.trs.yq.police.subject.builder;

import static com.trs.yq.police.subject.utils.StringUtil.getPoliceStationPrefix;

import com.trs.yq.police.subject.domain.entity.ControlEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.GroupLabelRelationEntity;
import com.trs.yq.police.subject.domain.entity.MobilePhoneEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.PersonGroupRelationEntity;
import com.trs.yq.police.subject.domain.entity.PersonLabelRelationEntity;
import com.trs.yq.police.subject.domain.entity.PersonSubjectRelationEntity;
import com.trs.yq.police.subject.domain.entity.VehicleEntity;
import com.trs.yq.police.subject.domain.entity.VirtualIdentityEntity;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import org.apache.commons.lang3.StringUtils;

/**
 * 人员列表查询条件封装类
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
public class PersonListPredicatesBuilder {

    /**
     * 创建模糊检索Predicates
     *
     * @param searchParams     模糊搜索参数
     * @param personEntityRoot 人员记录
     * @param criteriaBuilder  criteriaBuilder
     * @return 模糊检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildSearchPredicates(SearchParams searchParams, Root<PersonEntity> personEntityRoot,
        CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(searchParams.getSearchField()) && StringUtils.isNotBlank(
            searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            Predicate namePredicate = criteriaBuilder.like(personEntityRoot.get("name").as(String.class),
                "%" + searchValue + "%");
            Predicate idNumberPredicate = criteriaBuilder.like(personEntityRoot.get("idNumber").as(String.class),
                "%" + searchValue + "%");
            Predicate phoneNumberPredicate = criteriaBuilder.like(
                personEntityRoot.get("contactInformation").as(String.class), "%" + searchValue + "%");
            Predicate vehicleNumberPredicate = getCarEntityPredicates(personEntityRoot, searchValue, criteriaBuilder);
            Predicate qqPredicate = getVirtualIdentityPredicates(personEntityRoot, searchValue, "4", criteriaBuilder);
            Predicate wechatPredicate = getVirtualIdentityPredicates(personEntityRoot, searchValue, "5",
                criteriaBuilder);
            switch (searchField) {
                case "name":
                    predicates.add(namePredicate);
                    break;
                case "idNumber":
                    predicates.add(idNumberPredicate);
                    break;
                case "phoneNumber":
                    predicates.add(phoneNumberPredicate);
                    break;
                case "vehicleNumber":
                    predicates.add(vehicleNumberPredicate);
                    break;
                case "qq":
                    predicates.add(qqPredicate);
                    break;
                case "wechat":
                    predicates.add(wechatPredicate);
                    break;
                case "fullText":
                    predicates.add(criteriaBuilder.or(namePredicate, idNumberPredicate, phoneNumberPredicate,
                        vehicleNumberPredicate));
                    break;
                default:
                    break;
            }
        }
        return predicates;
    }

    /**
     * 创建人员列表动态检索Predicates
     *
     * @param subjectId        专题id
     * @param filterParams     检索参数
     * @param personEntityRoot 人员实体
     * @param criteriaBuilder  criteriaBuilder
     * @return 检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildListFilterPredicates(String subjectId, List<KeyValueVO> filterParams,
        Root<PersonEntity> personEntityRoot, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();

        // 联表查询专题和人员的关系
        predicates.add(getPersonSubjectRelationEntitySubQuery(subjectId, personEntityRoot, criteriaBuilder));

        // 动态查询参数
        filterParams.forEach(kv -> {
            switch (kv.getKey()) {
                // 分配状态
                case "distributeStatus":
                    // TODO
                    break;
                // 管控状态
                case "controlStatus":
                    predicates.add(criteriaBuilder.equal(personEntityRoot.get("controlStatus").as(String.class), kv.getValue()));
                    break;
                    // 布控状态
                case "monitorStatus":
                    predicates.add(criteriaBuilder.equal(personEntityRoot.get("monitorStatus").as(String.class), kv.getValue()));
                    break;
                // 是否纳入分组
                case "groupStatus":
                    predicates.add(
                        getPersonGroupStatusPredicate(subjectId, personEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                // 分组类型
                case "groupType":
                    predicates.add(getPersonGroupTypePredicates(personEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                // 人员类别
                case "personType":
                    predicates.add(getPersonTypePredicates(personEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                // 管控单位
                case "department":
                    predicates.add(
                        getPersonDepartmentPredicates(personEntityRoot, kv.getValue(), subjectId, criteriaBuilder));
                    break;
                // 管控级别
                case "controlLevel":
                    predicates.add(
                        getControlLevelPredicates(personEntityRoot, kv.getValue(), subjectId, criteriaBuilder));
                    break;
                // 人员标签
                case "personLabel":
                    predicates.add(getPersonLabelPredicates(personEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                // 是否在控
                case "isInControlStatus":
                    predicates.add(
                        criteriaBuilder.equal(personEntityRoot.get("isInControl").as(String.class), kv.getValue()));
                    break;
                default:
                    break;
            }
        });
        return predicates;
    }

    /**
     * 创建人员和专题关联查询条件
     *
     * @param subjectId        专题id
     * @param personEntityRoot 人员
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPersonSubjectRelationEntitySubQuery(String subjectId,
        Root<PersonEntity> personEntityRoot, CriteriaBuilder criteriaBuilder) {
        Subquery<PersonSubjectRelationEntity> subQuery = criteriaBuilder.createQuery()
            .subquery(PersonSubjectRelationEntity.class);
        Root<PersonSubjectRelationEntity> personSubjectRelationEntityRoot = subQuery.from(
            PersonSubjectRelationEntity.class);
        Predicate predicate = criteriaBuilder.equal(personSubjectRelationEntityRoot.get("subjectId").as(String.class),
            subjectId);
        subQuery = subQuery.select(personSubjectRelationEntityRoot.get("personId")).where(predicate);
        return criteriaBuilder.in(personEntityRoot.get("id")).value(subQuery);
    }

    /**
     * 创建人员是否关联群体查询条件
     *
     * @param subjectId        专题id
     * @param personEntityRoot 人员实体
     * @param value            检索值
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPersonGroupStatusPredicate(String subjectId, Root<PersonEntity> personEntityRoot,
        String value, CriteriaBuilder criteriaBuilder) {
        // 根据专题id查询专题所有的group
        Subquery<GroupEntity> groupQuery = criteriaBuilder.createQuery().subquery(GroupEntity.class);
        Root<GroupEntity> groupRoot = groupQuery.from(GroupEntity.class);
        Predicate groupPredicate = criteriaBuilder.equal(groupRoot.get("subjectId").as(String.class), subjectId);
        groupQuery = groupQuery.select(groupRoot.get("id")).where(groupPredicate);

        // 查询人员-群体关联表中属于该专题group的人员id
        Subquery<PersonGroupRelationEntity> personGroupRelationEntityQuery = criteriaBuilder.createQuery()
            .subquery(PersonGroupRelationEntity.class);
        Root<PersonGroupRelationEntity> personGroupRelationEntityRoot = personGroupRelationEntityQuery.from(
            PersonGroupRelationEntity.class);
        Predicate predicate1 = criteriaBuilder.in(personGroupRelationEntityRoot.get("groupId")).value(groupQuery);
        personGroupRelationEntityQuery = personGroupRelationEntityQuery.select(
            personGroupRelationEntityRoot.get("personId")).where(predicate1);

        // 筛选出包含该人员id的所有关联记录
        CriteriaBuilder.In<Object> in = criteriaBuilder.in(personEntityRoot.get("id"))
            .value(personGroupRelationEntityQuery);
        return "1".equals(value) ? in : criteriaBuilder.not(in);
    }

    /**
     * 创建人员群体关联查询条件
     *
     * @param personEntityRoot 人员实体
     * @param value            检索值
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPersonGroupTypePredicates(Root<PersonEntity> personEntityRoot, String value,
        CriteriaBuilder criteriaBuilder) {
        Subquery<GroupLabelRelationEntity> groupTypeQuery = criteriaBuilder.createQuery()
            .subquery(GroupLabelRelationEntity.class);
        Root<GroupLabelRelationEntity> relationRoot = groupTypeQuery.from(GroupLabelRelationEntity.class);
        Predicate relationPredicate = criteriaBuilder.equal(relationRoot.get("labelId").as(String.class), value);
        groupTypeQuery = groupTypeQuery.select(relationRoot.get("groupId")).where(relationPredicate);

        Subquery<PersonGroupRelationEntity> subQuery = criteriaBuilder.createQuery()
            .subquery(PersonGroupRelationEntity.class);
        Root<PersonGroupRelationEntity> personGroupRelationEntityRoot = subQuery.from(PersonGroupRelationEntity.class);
        Predicate predicate1 = criteriaBuilder.in(personGroupRelationEntityRoot.get("groupId")).value(groupTypeQuery);
        Predicate predicate2 = criteriaBuilder.equal(personGroupRelationEntityRoot.get("personId"),
            personEntityRoot.get("id"));
        subQuery.select(personGroupRelationEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建人员类别查询条件
     *
     * @param personEntityRoot 人员实体
     * @param value            检索值
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPersonTypePredicates(Root<PersonEntity> personEntityRoot, String value,
        CriteriaBuilder criteriaBuilder) {
        return getPersonLabelPredicates(personEntityRoot, value, criteriaBuilder);
    }

    /**
     * 创建人员管控部门查询条件
     *
     * @param personEntityRoot 人员实体
     * @param value            检索值
     * @param subjectId        专题id
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPersonDepartmentPredicates(Root<PersonEntity> personEntityRoot, String value,
        String subjectId, CriteriaBuilder criteriaBuilder) {
        Subquery<ControlEntity> subQuery = criteriaBuilder.createQuery().subquery(ControlEntity.class);
        Root<ControlEntity> controlEntityRoot = subQuery.from(ControlEntity.class);
        //截取
        Predicate predicate1 = criteriaBuilder.like(controlEntityRoot.get("policeStationCode").as(String.class),
            getPoliceStationPrefix(value) + "%");
        Predicate predicate2 = criteriaBuilder.equal(controlEntityRoot.get("subjectId").as(String.class), subjectId);
        Predicate predicate3 = criteriaBuilder.equal(controlEntityRoot.get("personId"), personEntityRoot.get("id"));
        subQuery.select(controlEntityRoot).where(predicate1, predicate2, predicate3);
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建人员标签关联查询条件
     *
     * @param personEntityRoot 人员实体
     * @param value            检索值
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPersonLabelPredicates(Root<PersonEntity> personEntityRoot, String value,
        CriteriaBuilder criteriaBuilder) {
        Subquery<PersonLabelRelationEntity> subQuery = criteriaBuilder.createQuery()
            .subquery(PersonLabelRelationEntity.class);
        Root<PersonLabelRelationEntity> personLabelRelationEntityRoot = subQuery.from(PersonLabelRelationEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(personLabelRelationEntityRoot.get("labelId").as(String.class),
            value);
        Predicate predicate2 = criteriaBuilder.equal(personLabelRelationEntityRoot.get("personId"),
            personEntityRoot.get("id"));
        subQuery.select(personLabelRelationEntityRoot).where(predicate1, predicate2);
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建车辆模糊查询条件
     *
     * @param personEntityRoot 人员实体
     * @param searchValue      模糊查询字符串
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getCarEntityPredicates(Root<PersonEntity> personEntityRoot, String searchValue,
        CriteriaBuilder criteriaBuilder) {
        Subquery<VehicleEntity> subQuery = criteriaBuilder.createQuery().subquery(VehicleEntity.class);
        Root<VehicleEntity> vehicleEntityRoot = subQuery.from(VehicleEntity.class);
        Predicate predicate1 = criteriaBuilder.like(vehicleEntityRoot.get("vehicleNumber").as(String.class),
            "%" + searchValue + "%");
        Predicate predicate2 = criteriaBuilder.equal(vehicleEntityRoot.get("personId"), personEntityRoot.get("id"));
        subQuery = subQuery.select(vehicleEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建人员虚拟身份查询条件
     *
     * @param personEntityRoot 人员实体
     * @param searchValue      模糊搜索字符串
     * @param type             类别
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getVirtualIdentityPredicates(Root<PersonEntity> personEntityRoot, String searchValue,
        String type, CriteriaBuilder criteriaBuilder) {
        Subquery<VirtualIdentityEntity> subQuery = criteriaBuilder.createQuery().subquery(VirtualIdentityEntity.class);
        Root<VirtualIdentityEntity> virtualIdentityEntityRoot = subQuery.from(VirtualIdentityEntity.class);
        Predicate predicate1 = criteriaBuilder.like(virtualIdentityEntityRoot.get("virtualNumber").as(String.class),
            "%" + searchValue + "%");
        Predicate predicate2 = criteriaBuilder.equal(virtualIdentityEntityRoot.get("personId"),
            personEntityRoot.get("id"));
        Predicate predicate3 = criteriaBuilder.equal(virtualIdentityEntityRoot.get("virtualType").as(String.class),
            type);
        subQuery = subQuery.select(virtualIdentityEntityRoot)
            .where(criteriaBuilder.and(predicate1, predicate2, predicate3));
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建人员手机号查询条件
     *
     * @param personEntityRoot 人员实体
     * @param searchValue      模糊搜索字符串
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getPhoneNumberPredicates(Root<PersonEntity> personEntityRoot, String searchValue,
        CriteriaBuilder criteriaBuilder) {
        Subquery<MobilePhoneEntity> subQuery = criteriaBuilder.createQuery().subquery(MobilePhoneEntity.class);
        Root<MobilePhoneEntity> mobilePhoneEntityRoot = subQuery.from(MobilePhoneEntity.class);
        Predicate predicate1 = criteriaBuilder.like(mobilePhoneEntityRoot.get("phoneNumber").as(String.class),
            "%" + searchValue + "%");
        Predicate predicate2 = criteriaBuilder.equal(mobilePhoneEntityRoot.get("personId"), personEntityRoot.get("id"));
        subQuery = subQuery.select(mobilePhoneEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建管控级别查询条件
     *
     * @param personEntityRoot 人员实体
     * @param value            检索值
     * @param subjectId        专题id
     * @param criteriaBuilder  criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getControlLevelPredicates(Root<PersonEntity> personEntityRoot, String value,
        String subjectId, CriteriaBuilder criteriaBuilder) {
        Subquery<ControlEntity> subQuery = criteriaBuilder.createQuery().subquery(ControlEntity.class);
        Root<ControlEntity> controlEntityRoot = subQuery.from(ControlEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(controlEntityRoot.get("controlLevel").as(String.class), value);
        Predicate predicate2 = criteriaBuilder.equal(controlEntityRoot.get("subjectId").as(String.class), subjectId);
        Predicate predicate3 = criteriaBuilder.equal(controlEntityRoot.get("personId"), personEntityRoot.get("id"));
        subQuery.select(controlEntityRoot).where(predicate1, predicate2, predicate3);
        return criteriaBuilder.exists(subQuery);
    }
}
