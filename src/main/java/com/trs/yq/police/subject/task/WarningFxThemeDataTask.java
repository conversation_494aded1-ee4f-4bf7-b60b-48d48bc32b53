package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.constants.PoliceSubjectConstants;
import com.trs.yq.police.subject.constants.enums.WarningTypeEnum;
import com.trs.yq.police.subject.domain.entity.AlarmYjgjEntity;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.entity.WarningTraceRelationEntity;
import com.trs.yq.police.subject.domain.entity.WarningTrajectoryEntity;
import com.trs.yq.police.subject.repository.AlarmYjgjRepository;
import com.trs.yq.police.subject.repository.WarningRepository;
import com.trs.yq.police.subject.repository.WarningTraceRelationRepository;
import com.trs.yq.police.subject.repository.WarningTrajectoryRepository;
import com.trs.yq.police.subject.utils.GeoUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName WarningFxThemeDataProcess
 * @Description 反邪专题数据抽取定时任务
 * <AUTHOR>
 * @Date 2024/2/28 9:53
 **/
@Slf4j
@Component
@ConditionalOnProperty(value = "com.trs.warning.fxzt.task.enable", havingValue = "true")
@AllArgsConstructor
public class WarningFxThemeDataTask {

    private final AlarmYjgjRepository yjgjRepository;

    private final WarningRepository warningRepository;

    private final WarningTraceRelationRepository warningTraceRelationRepository;

    private final WarningTrajectoryRepository warningTrajectoryRepository;

    /**
     * 生成人员预警数据的定时任务
     */
    @Scheduled(cron = "${com.trs.warning.fxzt.task.cron}")
    @Transactional(rollbackFor = RuntimeException.class)
    public void fxztWarningSyncTask() {
        log.info("开始同步反邪专题预警");
        final LocalDateTime now = LocalDateTime.now();
        final List<AlarmYjgjEntity> list = yjgjRepository.getYjgjByTime(
                "邪教人员",
                now.plusDays(-3L)
        );
        for (AlarmYjgjEntity alarmYjgjEntity : list) {
            this.yjgjToWarning(alarmYjgjEntity, WarningTypeEnum.FX_RYYJ.getCode());
        }
        log.info("反邪专题预警同步完毕！");
    }

    private void yjgjToWarning(AlarmYjgjEntity yjgj, String warningType) {
        // 如果该轨迹已经生成过预警就跳过
        if (warningRepository.selectByRawData(yjgj.getId()) > 0) {
            log.info("数据ID为: {}的轨迹，已经生成过预警", yjgj.getId());
            return;
        }
        WarningEntity warning = new WarningEntity();
        warning.setWarningTime(LocalDateTime.now());
        warning.setWarningLevel("4");
        warning.setWarningSource(new ArrayList<>(Collections.singleton(yjgj.getHdlybcn())));
        warning.setWarningStatus("1");
        warning.setWarningDetails(yjgj.getHdxq());
        warning.setSubjectId(PoliceSubjectConstants.FX_SUBJECT);
        warning.setWarningType(warningType);
        warning.setWarningKey(yjgj.getGlsfzh());
        warning.setWarningKeyType("0");
        String areaCode = GeoUtil.getDistrictCode(yjgj.getHdjd(), yjgj.getHdwd());
        warning.setAreaCode(areaCode);
        warningRepository.save(warning);

        WarningTrajectoryEntity trajectory = new WarningTrajectoryEntity();
        trajectory.setIdNumber(yjgj.getGlsfzh());
        trajectory.setIdType("idNumber");
        trajectory.setSourceId(yjgj.getId());
        trajectory.setDateTime(yjgj.getHdfssj());
        trajectory.setAreaCode(areaCode);
        trajectory.setLng(yjgj.getHdjd());
        trajectory.setLat(yjgj.getHdwd());
        trajectory.setAddress(yjgj.getHdfsdd());
        if (StringUtils.isNotBlank(yjgj.getPicurls())) {
            String[] split = yjgj.getPicurls().split(",");
            if (split.length > 0) {
                trajectory.setImageUrl(split[0]);
            }
            if (split.length > 1) {
                trajectory.setCropUrl(split[0]);
            }
        }
        trajectory.setRawData(yjgj.getId());
        warningTrajectoryRepository.save(trajectory);

        WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
        relation.setWarningId(warning.getId());
        relation.setTrajectoryId(trajectory.getId());
        relation.setCreateTime(LocalDateTime.now());
        warningTraceRelationRepository.save(relation);
    }


}
