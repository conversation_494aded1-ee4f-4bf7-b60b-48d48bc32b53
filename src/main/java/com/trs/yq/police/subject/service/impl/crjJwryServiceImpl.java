package com.trs.yq.police.subject.service.impl;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.DateTimeConstants;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwrySourceTypeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryVisitEntity;
import com.trs.yq.police.subject.domain.entity.SubjectEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.CrjDispatchVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryAddVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryBaseVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryDetailVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryRegisteredVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import com.trs.yq.police.subject.domain.vo.CrjOcrResultVO;
import com.trs.yq.police.subject.domain.vo.CrjRedispatchVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.domain.vo.ImportResultVO;
import com.trs.yq.police.subject.domain.vo.ImportVO;
import com.trs.yq.police.subject.domain.vo.JwryExportVO;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.excel.ReadCrjJwryExcelListener;
import com.trs.yq.police.subject.exception.AppLogicException;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.exception.SystemException;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.mysqlDatasource.entity.CrjJwrySourceEntity;
import com.trs.yq.police.subject.mysqlDatasource.repository.CrjJwrySourceRepository;
import com.trs.yq.police.subject.repository.CrjJwryDetailRepository;
import com.trs.yq.police.subject.repository.CrjJwryRepository;
import com.trs.yq.police.subject.repository.CrjJwryVisitRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.SubjectRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.service.CrjCommonService;
import com.trs.yq.police.subject.service.CrjJwryService;
import com.trs.yq.police.subject.service.CrjSyncService;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.service.SubjectService;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import com.trs.yq.police.subject.webService.QgCrjWebService;
import com.trs.yq.police.subject.webService.domain.QueryArgs;
import com.trs.yq.police.subject.webService.domain.StayInfoArgs;
import com.trs.yq.police.subject.webService.domain.StayInfoArgs.StayInfo;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 15:36
 */
@Slf4j
@Service
public class crjJwryServiceImpl implements CrjJwryService {

    @Resource
    private CrjJwryVisitRepository crjJwryVisitRepository;

    @Resource
    private CrjJwryRepository crjJwryRepository;

    @Resource
    private CrjJwrySourceRepository crjJwrySourceRepository;

    @Resource
    private CrjCommonService crjCommonService;

    @Resource
    private UnitRepository unitRepository;

    @Resource
    private CrjJwryDetailRepository crjJwryDetailRepository;

    @Resource
    private EntityManager entityManager;

    @Resource
    private DictRepository dictRepository;

    @Resource
    private RemoteStorageService remoteStorageService;

    @Resource
    private QgCrjWebService qgCrjWebService;

    @Resource
    private SubjectService subjectService;

    @Resource
    private SubjectRepository subjectRepository;
    @Value("${com.trs.fastdfs.group_name}")
    private String groupName;

    @Value("${com.trs.crj.palz.secretKey}")
    public String secretKey = "xiondi.cn";

    @Value("${com.trs.crj.ocrUrl}")
    public String ocrUrl;

    @Value("${com.trs.crj.passportOcrUrl}")
    public String passportOcrUrl;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private CrjSyncService crjSyncService;
    @Resource
    private PersonServiceImpl personService;

    @Override
    public synchronized PageResult<CrjJwryBaseVO> findPage(ListRequestVO requestVO, boolean isDispatchList) {
        //同步数据
        crjSyncService.syncJwry();
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;

        String sql = buildQuery(requestVO, currentUser, isDispatchList);

        Query nativeQuery = entityManager.createNativeQuery(sql, CrjJwryEntity.class);

        int maxResults = nativeQuery.getResultList().size();
        PageParams pageParams = requestVO.getPageParams();
        int first = (pageParams.getPageNumber() - 1) * pageParams.getPageSize();
        List<CrjJwryEntity> resultList = nativeQuery.setFirstResult(first).setMaxResults(pageParams.getPageSize())
            .getResultList();
        //清除缓存
        entityManager.clear();
        List<CrjJwryBaseVO> collect = resultList.stream()
            .map(e -> CrjJwryBaseVO.listVoOf(e, currentUser, isDispatchList)).collect(Collectors.toList());

        return PageResult.of(collect, requestVO.getPageParams().getPageNumber(), maxResults,
            requestVO.getPageParams().getPageSize());
    }

    private String buildQuery(ListRequestVO requestVO, LoginUser currentUser, boolean isDispatchList) {
        List<KeyValueVO> filterParams = requestVO.getFilterParams();

        StringBuilder sql = new StringBuilder("SELECT t.* FROM T_PS_CRJ_JWRY t WHERE  1=1 ");

        //构造模糊查询条件
        buildSearchParams(requestVO, sql);

        String unitCode = currentUser.getUnitCode();
        UnitEntity unit = unitRepository.findByUnitCode(unitCode);
        //判断当前用户的可看部门   市局看全部
        if (!unit.getAreaCode().equals("510500")) {
            //三区四县看自己及子部门
            if (CrjConstants.SQSXZD.contains(unitCode)) {
                sql.append("and t.ACCEPTOR like '").append(unit.getAreaCode()).append("%' ");
            } else {
                //其他看自己部门
                sql.append("and t.ACCEPTOR='").append(unitCode).append("' ");
            }
        }

        // 根据传递的对象来进行条件的构造
        for (KeyValueVO filterParam : filterParams) {
            String key = filterParam.getKey();
            String value = filterParam.getValue();
            if (StringUtils.isBlank(value) || CrjConstants.ALL.equals(value)) {
                continue;
            }
            switch (key) {
                case "leaveStatus":
                    LocalDateTime localDateTime = LocalDateTime.now().plusDays(1);
                    DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
                    String format = localDateTime.format(dateTimeFormatter);
                    if ("aboutToLeave".equals(value)) {
                        sql.append("and t.PLAN_LEAVE_TIME <= TO_DATE('").append(format)
                            .append("', 'yyyy/mm/dd HH24:mi:ss') AND t.REGISTRATION_STATUS = '1' ");
                    } else if ("normal".equals(value)) {
                        sql.append("and t.PLAN_LEAVE_TIME > TO_DATE('").append(format)
                            .append("', 'yyyy/mm/dd HH24:mi:ss') AND t.REGISTRATION_STATUS = '1' ");
                    }
                    break;
                case "isDispatched":
                    boolean isDispatched = Boolean.parseBoolean(value);
                    if (unitCode.equals(CrjConstants.SJZD)) {
                        if (isDispatched) {
                            sql.append("and t.DISPATCH_STATUS !='")
                                .append(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode()).append("' ");
                        } else {
                            //未分派
                            sql.append("and t.DISPATCH_STATUS ='")
                                .append(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode()).append("' ");
                        }
                    } else if (CrjConstants.SQSXZD.contains(unitCode)) {
                        if (isDispatched) {
                            //已分派
                            sql.append("and t.DISPATCH_STATUS ='").append(CrjDispatchStatusEnum.DISPATCHED.getCode())
                                .append("' ");
                        } else {
                            //未分派
                            sql.append("and t.DISPATCH_STATUS !='").append(CrjDispatchStatusEnum.DISPATCHED.getCode())
                                .append("' ");
                        }
                    }
                    break;
                case "normalRegistrationStatus":
                    switch (value) {
                        case "1":
                            sql.append("and t.REGISTRATION_STATUS ='")
                                .append(CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode()).append("' ");
                            break;
                        case "2":
                            sql.append("and (t.REGISTRATION_STATUS ='")
                                .append(CrjJwryRegistrationStatusEnum.LIVING.getCode())
                                .append("' or  t.REGISTRATION_STATUS ='")
                                .append(CrjJwryRegistrationStatusEnum.LEAVE.getCode()).append("') ");
                            break;
                        case "3":
                            sql.append("and t.REGISTRATION_STATUS ='")
                                .append(CrjJwryRegistrationStatusEnum.NO_REGISTER.getCode()).append("' ");
                            break;
                        default:
                            break;
                    }
                    break;
                case "acceptor":
                    sql.append("and t.ACCEPTOR ='").append(value).append("' ");
                    break;
                case "registrationStatus":
                    sql.append("and t.REGISTRATION_STATUS ='").append(value).append("' ");
                    break;
                case "jwryCreateTime":
                    TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
                    sql.append(String.format(
                        " and t.create_time >= to_date('%s','yyyy-mm-dd hh24:mi:ss') and t.create_time <= to_date('%s','yyyy-mm-dd hh24:mi:ss')",
                        DateTimeConstants.DATE_TIME_FORMATTER.format(timeParams.getBeginTime()),
                        DateTimeConstants.DATE_TIME_FORMATTER.format(timeParams.getEndTime())));
                    break;
                case "sourceType":
                    sql.append(String.format("and t.SOURCE_TYPE='%s'", value));
                    break;
                case "district":
                    String policeStationPrefix = StringUtil.getPoliceStationPrefix(value);
                    sql.append("and t.ACCEPTOR like '").append(policeStationPrefix).append("%' ");
                    break;
                default:
            }
        }
        if (!isDispatchList) {
            //多个登记指向同一个人时，只显示一个
//            List<String> latestRecordIds = crjJwryRepository.findLatestRecordIds().stream().map(e -> "'" + e + "'")
//                .collect(Collectors.toList());
//            if (latestRecordIds.isEmpty()) {
//                latestRecordIds.add("'-1'");
//            }
            sql.append("and t.RECORD_ID in (SELECT t.RECORD_ID from T_PS_CRJ_JWRY t \n"
                + "            JOIN  (SELECT max(UP_TIME) UP_TIME,ID_TYPE,ID_NUMBER FROM T_PS_CRJ_JWRY WHERE (REGISTRATION_STATUS = '1' or REGISTRATION_STATUS = '2') GROUP BY ID_TYPE,ID_NUMBER) b \n"
                + "            on t.UP_TIME = b.UP_TIME AND t.ID_TYPE = B.ID_TYPE AND T.ID_NUMBER = b.ID_NUMBER \n"
                + "            union SELECT t.RECORD_ID from T_PS_CRJ_JWRY t where t.REGISTRATION_STATUS = '0' or t.REGISTRATION_STATUS = '3')");
        }

        //构造排序条件
        buildSortParams(requestVO, currentUser, sql);
        return sql.toString();
    }

    private static void buildSortParams(ListRequestVO requestVO, LoginUser currentUser, StringBuilder sql) {
        //排序
        if (Objects.nonNull(requestVO.getSortParams()) && CrjConstants.SORT_FIELD_UNREAD.equals(
            requestVO.getSortParams().getSortField())) {
            sql.append(
                    "ORDER BY  ( CASE t.REGISTRATION_STATUS  WHEN  0 THEN  (SELECT 1 FROM T_PS_CRJ_READ_RECORD r WHERE RECORD_ID = t.RECORD_ID AND r.USER_ID = '")
                .append(currentUser.getId())
                .append("' AND r.MODULE = '").append(CrjConstants.JWRY_RECORD_MODULE)
                .append("') ELSE (SELECT 1 FROM dual) END) NULLS FIRST,t.UP_TIME desc").append(",t.CR_TIME desc");
        } else if (Objects.nonNull(requestVO.getSortParams()) && "createTime".equals(
            requestVO.getSortParams().getSortField())) {
            sql.append("ORDER BY t.CREATE_TIME ").append(requestVO.getSortParams().getSortDirection()).append(",t.CR_TIME desc");
        } else {
            sql.append("ORDER BY t.CREATE_TIME  desc").append(",t.CR_TIME desc");
        }
    }

    private static void buildSearchParams(ListRequestVO requestVO, StringBuilder sql) {
        SearchParams searchParams = requestVO.getSearchParams();
        if (Objects.nonNull(searchParams) && StringUtils.isNotBlank(searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            if (StringUtils.isNotBlank(searchParams.getSearchValue())) {
                switch (searchField) {
                    case "fullText":
                        sql.append(
                                "and (exists ( select d.ID from T_PS_CRJ_JWRY_DETAIL d where d.ID_TYPE = t.ID_TYPE and d.ID_NUMBER = t.ID_NUMBER and "
                                    + "d.EN_NAME like '%").append(searchValue).append("%' ) " + "or (t.ID_NUMBER like '%")
                            .append(searchValue).append("%' )" + "or (t.PHONE like '%").append(searchValue)
                            .append("%' ) )");
                        break;
                    case  "name" :
                    case "enName":
                        sql.append(
                                "and exists ( select d.ID from T_PS_CRJ_JWRY_DETAIL d where d.ID_TYPE = t.ID_TYPE and d.ID_NUMBER = t.ID_NUMBER and d.EN_NAME like '%")
                            .append(searchValue).append("%' ) ");
                        break;
                    case "idNumber":
                        sql.append("and t.ID_NUMBER like '%").append(searchValue).append("%' ");
                        break;
                    case "phone":
                        sql.append("and t.PHONE like '%").append(searchValue).append("%' ");
                        break;
                    default:
                        break;
                }
            }
        }
    }


    @Override
    @Synchronized
    public CrjJwryBaseVO getById(String id) {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        CrjJwryEntity byRecordId = crjJwryRepository.findByRecordId(id);
        //设置成已读
        crjCommonService.addReadRecord(id, currentUser.getId(), CrjConstants.JWRY_RECORD_MODULE);
        return CrjJwryBaseVO.of(byRecordId, currentUser);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deletedById(String id) {
        CrjJwryEntity byRecordId = crjJwryRepository.findByRecordId(id);
        if (Objects.nonNull(byRecordId)) {
            crjJwryRepository.deleteByIdNumberAndIdType(byRecordId.getIdNumber(), byRecordId.getIdType());
            crjJwryDetailRepository.deleteByIdNumberAndIdType(byRecordId.getIdNumber(), byRecordId.getIdType());
        }
    }

    @Transactional(noRollbackFor = AppLogicException.class)
    @Override
    public void dispatch(CrjDispatchVO dispatchVO) {
        if (dispatchVO.getRecordIds().isEmpty()) {
            return;
        }
        String unitCode = dispatchVO.getDeptCode();
        //设置分派状态
        CrjDispatchStatusEnum dispatchStatus =
            CrjConstants.SQSXZD.contains(unitCode) ? CrjDispatchStatusEnum.DISPATCHED_TO_QX
                : CrjDispatchStatusEnum.DISPATCHED;
        //设置接受者,分发到派出所一级才算已分派
        crjJwryRepository.setAcceptor(dispatchVO.getRecordIds(), unitCode, dispatchStatus.getCode());
        //将不能登记的id返回到前端
        List<String> cantRegister = crjJwryRepository.findCantRegister(dispatchVO.getRecordIds());
        if (!cantRegister.isEmpty()) {
            throw new AppLogicException("信息：" + cantRegister + ",不是未分派状态，不能背分派!");
        }
    }

    @Transactional
    @Override
    public void register(CrjJwryRegisteredVO registeredVO, String id) {
        CrjJwryEntity entity = crjJwryRepository.findByRecordId(id);
        CrjJwryRegistrationStatusEnum registrationStatusEnum = CrjJwryRegistrationStatusEnum.codeOf(
            entity.getRegistrationStatus());
        if (registrationStatusEnum != CrjJwryRegistrationStatusEnum.NOT_REGISTER) {
            throw new AppLogicException("当前消息不是未登记状态，不能进行登记！");
        }

        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        if (!currentUser.getUnitCode().equals(entity.getAcceptor())) {
            //如果不是当前消息的接收方，处理消息后，将接受部门设置成当前用户的部门
            crjJwryRepository.setAcceptor(id, currentUser.getUnitCode());
        }

        if (!registeredVO.getIsRegister()) {
            //更新状态
            crjJwryRepository.updateRegistrationStatus(id, CrjJwryRegistrationStatusEnum.NO_REGISTER.getCode(),
                registeredVO.getReason());
            return;
        }
        //照片
        String attachment =
            Objects.nonNull(registeredVO.getAttachment()) ? JsonUtil.toJsonString(registeredVO.getAttachment()) : null;
        //确认登记,设置成在住
        crjJwryRepository.update(id, registeredVO.getIdType(), registeredVO.getGjdm(), registeredVO.getIdNumber(),
            attachment, registeredVO.getPhone(), registeredVO.getAddress(),
            CrjJwryRegistrationStatusEnum.LIVING.getCode());

        //使用修改后的证件信息查询该人的信息
        Optional<CrjJwryDetailEntity> detailEntity = crjJwryDetailRepository.findByIdTypeAndIdNumber(
            registeredVO.getIdType(), registeredVO.getIdNumber());
        if (detailEntity.isPresent()) {
            //存在就更新（国家代码）
            crjJwryDetailRepository.updateGjdm(entity.getIdType(), entity.getIdNumber(), registeredVO.getGjdm());
        } else {
            //不存在就从平安泸州导入
            //1.使用现在的证件信息从平安泸州同步
            Optional<CrjJwrySourceEntity> sourceEntity = crjJwrySourceRepository.findFirstByZjzlAndZjhm(
                registeredVO.getIdType(), registeredVO.getIdNumber(), secretKey);
            if (!sourceEntity.isPresent()) {
                //2.使用原来的证件信息从平安泸州同步
                sourceEntity = crjJwrySourceRepository.findFirstByZjzlAndZjhm(entity.getIdType(), entity.getIdNumber(),
                    secretKey);
            }
            CrjJwryDetailEntity newDetailEntity = new CrjJwryDetailEntity();
            if (sourceEntity.isPresent()) {
                //如果存在，将查出来的数据复制到新的人员信息中
                newDetailEntity = sourceEntity.get().toJwryDetailEntity(secretKey);
            }
            //将登记时可能修改的信息更新
            newDetailEntity.setGjdm(registeredVO.getGjdm());
            newDetailEntity.setIdType(registeredVO.getIdType());
            newDetailEntity.setIdNumber(registeredVO.getIdNumber());
            crjJwryDetailRepository.save(newDetailEntity);
        }

        //新增走访记录
        CrjJwryVisitVO visitVO = new CrjJwryVisitVO();
        visitVO.setPhone(registeredVO.getPhone());
        visitVO.setLiveAddress(registeredVO.getAddress());
        visitVO.setWorkAddress(registeredVO.getWorkAddress());
        visitVO.setPlanLeaveTime(registeredVO.getPlanLeaveTime());
        visitVO.setVisitTime(new Date());
        visitVO.setInstantAddress(registeredVO.getAddress());
        visitVO.setIsLiving(true);

        this.addVisitRecord(visitVO, id, CrjConstants.REGISTER);
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public void addVisitRecord(CrjJwryVisitVO visitVO, String id, String visitType) {
        CrjJwryEntity byRecordId = crjJwryRepository.findByRecordId(id);
        if (Objects.isNull(byRecordId)) {
            throw new AppLogicException(String.format("人员：%s,不存在", id));
        }
        CrjJwryVisitEntity visitEntity = visitVO.toEntity();
        visitEntity.setIdType(byRecordId.getIdType());
        visitEntity.setIdNumber(byRecordId.getIdNumber());

        Optional<CrjJwryDetailEntity> detailEntityOptional = crjJwryDetailRepository.findByIdTypeAndIdNumber(
            visitEntity.getIdType(), visitEntity.getIdNumber());

        CrjJwryRegistrationStatusEnum registerStatus = CrjJwryRegistrationStatusEnum.LEAVE;
        Date leaveTime = new Date();
        Date planLeaveTime = null;
        //更新在住状态
        if (visitVO.getIsLiving()) {
            registerStatus = CrjJwryRegistrationStatusEnum.LIVING;
            planLeaveTime = visitVO.getPlanLeaveTime();
            leaveTime = null;
        }
        //将走访信息更新到登记信息中
        crjJwryRepository.updateVisit(id, visitEntity.getPhone(), visitVO.getLiveAddress(), registerStatus.getCode(),
            planLeaveTime, leaveTime);
        //将走访信息更新到人员档案中
        crjJwryDetailRepository.updateVisit(visitEntity.getIdType(), visitEntity.getIdNumber(), visitEntity.getPhone(),
            visitVO.getLiveAddress(), visitVO.getWorkAddress());

        if (crjJwryVisitRepository.findLatestByIdTypeAndIdNumber(byRecordId.getIdType(), byRecordId.getIdNumber())
            .isPresent()) {
            //如果不是第一条走访记录，就只存入更改过的信息
            if (detailEntityOptional.isPresent()) {
                CrjJwryDetailEntity detailEntity = detailEntityOptional.get();
                if (StringUtils.compare(visitEntity.getPhone(), detailEntity.getPhone()) == 0) {
                    visitEntity.setPhone(null);
                }
                if (StringUtils.compare(visitEntity.getLiveAddress(), detailEntity.getLiveAddress()) == 0) {
                    visitEntity.setLiveAddress(null);
                }
                if (StringUtils.compare(visitEntity.getWorkAddress(), detailEntity.getWorkAddress()) == 0) {
                    visitEntity.setWorkAddress(null);
                }
                if (DateUtil.isSame(visitEntity.getPlanLeaveTime(), byRecordId.getPlanLeaveTime())) {
                    visitEntity.setPlanLeaveTime(null);
                }
            }
        }

        //设置登记状态
        visitEntity.setRegistrationStatus(visitVO.getIsLiving() ? CrjJwryRegistrationStatusEnum.LIVING.getCode()
            : CrjJwryRegistrationStatusEnum.LEAVE.getCode());
        crjJwryVisitRepository.save(visitEntity);

        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;

        CrjJwryDetailEntity detailEntity = crjJwryDetailRepository.findByIdTypeAndIdNumber(
            byRecordId.getIdType(), byRecordId.getIdNumber()).orElseGet(
            CrjJwryDetailEntity::new);

        if (!visitType.equals(CrjConstants.VISIT) || !visitVO.getIsLiving()) {
            StayInfo stayInfo = new StayInfo();
            stayInfo.setYwx(detailEntity.getEnLastName());
            stayInfo.setYwm(detailEntity.getEnFirstName());
            stayInfo.setYwxm(detailEntity.getEnName());
            stayInfo.setZwxm(detailEntity.getCnName());
            if(Objects.nonNull(detailEntity.getGender())){
                stayInfo.setXb(Integer.parseInt(detailEntity.getGender()));
            }
            final  SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            final  SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            if(Objects.nonNull(detailEntity.getBirthday())){
                stayInfo.setCsrq(format.format(detailEntity.getBirthday()));
            }
            stayInfo.setZjzl(byRecordId.getIdType());
            stayInfo.setZjhm(byRecordId.getIdNumber());
            stayInfo.setGjdq(byRecordId.getGjdm());
            stayInfo.setRydylb("F");
            stayInfo.setSjly("5");
            stayInfo.setLzryywbh(byRecordId.getRecordId());
            stayInfo.setLrsj(format.format(new Date()));
            stayInfo.setPcsbh(currentUser.getUnitCode());
            stayInfo.setPcsmc(currentUser.getUnitName());
            stayInfo.setLsdwhzdz(visitVO.getLiveAddress());
            stayInfo.setZsrq(
                Objects.isNull(byRecordId.getCheckinTime()) ? "" : dateFormat.format(byRecordId.getCheckinTime()));
            stayInfo.setLkrq(
                Objects.isNull(byRecordId.getLeaveTime()) ? "" : dateFormat.format(byRecordId.getLeaveTime()));
            stayInfo.setQzhm(visitVO.getVisaNumber());
            stayInfo.setQzzl(visitVO.getVisaType());
            qgCrjWebService.saveStay(new StayInfoArgs(stayInfo));
        }
    }


    @Override
    public PageResult<CrjJwryVisitVO> getVisitRecord(String id, ListRequestVO requestVO) {
        PageParams pageParams = requestVO.getPageParams();
        CrjJwryEntity byRecordId = crjJwryRepository.findByRecordId(id);
        Optional<CrjJwryDetailEntity> jwryDetail = crjJwryDetailRepository.findByIdTypeAndIdNumber(
            byRecordId.getIdType(), byRecordId.getIdNumber());
        //判断是否是r签证
        boolean isRVisa = jwryDetail.filter(
            crjJwryDetailEntity -> CrjConstants.R_VISA_CODE.equals(crjJwryDetailEntity.getVisaType())).isPresent();
        Page<CrjJwryVisitEntity> page = crjJwryVisitRepository.findByIdTypeAndIdNumberOrderByCrTimeDesc(
            byRecordId.getIdType(), byRecordId.getIdNumber(), pageParams.toPageable());
        List<CrjJwryVisitVO> result = page.getContent()
            .stream()
            .map(e -> CrjJwryVisitVO.of(e, isRVisa))
            .collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), page.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public List<CrjJwryVisitVO> getCountryVisitRecord(String id) {
        CrjJwryEntity byRecordId = crjJwryRepository.findByRecordId(id);
        String gjdm = byRecordId.getGjdm();
        String idNumber = byRecordId.getIdNumber();
        String idType = byRecordId.getIdType();
        QueryArgs queryArgs = new QueryArgs(gjdm, idNumber, idType);
        return qgCrjWebService.stayInfo(queryArgs);
    }

    @Override
    public CrjJwryDetailVO getByIdTypeAndIdNumber(String idType, String idNumber) {
        Optional<CrjJwryDetailEntity> detailEntity = crjJwryDetailRepository.findByIdTypeAndIdNumber(idType, idNumber);
        if (detailEntity.isPresent()) {
            CrjJwryDetailVO crjJwryDetailVO = detailEntity.get().toVoWithCode();
            //是否是R签证
            crjJwryDetailVO.setIsRVisa(CrjConstants.R_VISA_CODE.equals(crjJwryDetailVO.getVisaType()));
            crjJwryDetailVO.setId(null);
            return crjJwryDetailVO;
        }
        Optional<CrjJwrySourceEntity> sourceEntity = crjJwrySourceRepository.findFirstByZjzlAndZjhm(idType, idNumber,
            secretKey);
        if (sourceEntity.isPresent()) {
            return sourceEntity.get().toJwryDetailEntity(secretKey).toVoWithCode();
        }
        return new CrjJwryDetailVO();
    }

    @Transactional
    @Override
    public void addJwry(CrjJwryAddVO addVO) {
        CrjJwryDetailVO detailVO = addVO.getDetail();
        CrjJwryVisitVO visitVO = addVO.getVisit();
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        //新增登记信息
        CrjJwryEntity entity = detailVO.toJwryEntity();
        entity.setPhone(visitVO.getPhone());
        entity.setAddress(visitVO.getLiveAddress());
        entity.setAcceptor(currentUser.getUnitCode());
        entity.setDispatchStatus(CrjDispatchStatusEnum.DISPATCHED.getCode());
        entity.setRegistrationStatus(CrjJwryRegistrationStatusEnum.LIVING.getCode());
        entity.setCreateTime(new Date());
        entity.setCheckinTime(new Date());
        entity.setSourceType(CrjJwrySourceTypeEnum.MANUAL_ENROLLMENT.getCode());
        CrjJwryEntity save = crjJwryRepository.save(entity);
        //将recordId设置成id
        crjJwryRepository.update(save.getId());
        //新增档案信息
        Optional<CrjJwryDetailEntity> detailEntityOptional = crjJwryDetailRepository.findByIdTypeAndIdNumber(
            detailVO.getIdType(), detailVO.getIdNumber());
        if (detailEntityOptional.isPresent()) {
            //存在就更新
            String enName = detailVO.getEnFirstName() + " " + detailVO.getEnLastName();
            crjJwryDetailRepository.update(detailVO.getCnName(), enName, detailVO.getEnFirstName(),
                detailVO.getEnLastName(), detailVO.getGender(),
                detailVO.getGjdm(), detailVO.getIdType(), detailVO.getIdNumber(),
                detailVO.getBirthday(), detailVO.getPhone(), visitVO.getLiveAddress(),
                visitVO.getWorkAddress(), detailVO.getInChinaTime(), detailVO.getEntryTime(),
                detailVO.getEntryPort(), detailVO.getVisaType(), detailVO.getVisaNumber());
        } else {
            crjJwryDetailRepository.save(detailVO.toJwryDetailEntity());
        }
        //新增走访记录
        visitVO.setIsLiving(true);
        visitVO.setVisitTime(new Date());
        this.addVisitRecord(visitVO, save.getId(), CrjConstants.ADD_JWRY);
    }

    @Override
    public CrjOcrResultVO ocrIdCard(MultipartFile image) {
        try {
            ImageVO imageVO = remoteStorageService.uploadImage(image);
            String ocrResult = this.requestOcr(image.getBytes(), image.getOriginalFilename(), ocrUrl);

            String idType;
            String idNumber = "";
            String gjdm = "";
            if (ocrResult.contains("台湾")) {
                idType = "16";
            } else if (ocrResult.contains("港澳")) {
                idType = "24";
            } else {
                //默认当作护照处理
                CrjOcrResultVO crjOcrResultVO = ocrPassport(image);
                crjOcrResultVO.setAttachment(imageVO.getUrl());
                return crjOcrResultVO;
            }
            Matcher idNumberMatcher = Pattern.compile("[HM][0-9]{8}").matcher(ocrResult);
            if (idNumberMatcher.find()) {
                idNumber = idNumberMatcher.group();
                if (idNumber.startsWith("H")) {
                    gjdm = "HKG";
                } else if (idNumber.startsWith("M")) {
                    gjdm = "MAC";
                }
            } else {
                Matcher matcher = Pattern.compile("(?<=\n)[0-9]{8}").matcher(ocrResult);
                if (matcher.find()) {
                    idNumber = matcher.group();
                    gjdm = "TWN";
                }
            }
            return new CrjOcrResultVO(imageVO.getUrl(), gjdm, idNumber, idType);
        } catch (NullPointerException e) {
            log.info("", e);
            throw new AppLogicException("ocr识别失败！");
        } catch (IOException e) {
            log.error("图片上传失败!", e);
            throw new AppLogicException("图片上传失败!");
        }
    }


    private CrjOcrResultVO ocrPassport(MultipartFile image) throws IOException {
        String ocrOriginalResult = this.requestOcr(image.getBytes(), image.getOriginalFilename(), passportOcrUrl);
        log.info("护照识别结果：" + ocrOriginalResult);
        //护照类型设置成'普通护照' --- 14
        CrjOcrResultVO crjOcrResultVO = new CrjOcrResultVO("", "", "14");
        //识别结果全部转为大写、去掉空格
        String ocrResult = ocrOriginalResult.toUpperCase().replace(" ", "");
        //获取国家代码
        Matcher gjdm = Pattern.compile("(?<=^.{2})\\w{3}").matcher(ocrResult);
        if (gjdm.find()) {
            String group = gjdm.group();
            if (dictRepository.findByTypeAndCode("crjGjdm", group) != null) {
                crjOcrResultVO.setGjdm(group);
            }
        }
        //获取证件号码和验证码
        Matcher passportNumberMatcher = Pattern.compile("(?<=\\n).{9}").matcher(ocrResult);
        Matcher verifyCodeMather = Pattern.compile("(?<=\\n.{9}).").matcher(ocrResult);
        if (passportNumberMatcher.find() && verifyCodeMather.find()) {
            String verifyCodeString = verifyCodeMather.group().replace("O", "0");
            String passportNumber = passportNumberMatcher.group().replace("O", "0");
            int verifyCode = NumberUtils.toInt(verifyCodeString, -1);
            log.info(String.format("护照号码:%s,校验码:%s", passportNumber, verifyCodeString));
            //校验证件号码
            if (verifyPassportNumber(passportNumber) == verifyCode) {
                crjOcrResultVO.setIdNumber(passportNumber);
            }
        }
        return crjOcrResultVO;
    }


    private static int verifyPassportNumber(String number) {
        String s = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        int[] w = new int[]{7, 3, 1};
        int c = 0;
        char[] chars = number.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            if (chars[i] == '<') {
                continue;
            }
            c += s.indexOf(chars[i]) * w[i % 3];
        }
        c %= 10;
        return c;
    }

    private String requestOcr(byte[] bytes, String fileName, String url) {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        ByteArrayResource resource = new ByteArrayResource(bytes) {
            @Override
            public String getFilename() {
                return fileName;
            }
        };
        params.add("file", resource);
        try {
            String ocrOriginalResult = restTemplate.postForObject(url, params, String.class);
            log.info("ocr识别结果：" + ocrOriginalResult);
            return JsonUtil.parseJsonNode(ocrOriginalResult).get("data").get("txt").get(0).asText();
        } catch (Exception exception) {
            log.error(String.format("failed to ocr! fileName:[%s] url:[%s]", fileName, url), exception);
            throw new AppLogicException("文字识别失败！");
        }
    }

    @Transactional
    @Override
    public void redispatch(CrjRedispatchVO dispatchVO) {
        String recordId = dispatchVO.getRecordId();
        CrjJwryEntity byRecordId = crjJwryRepository.findByRecordId(recordId);
        CrjJwryRegistrationStatusEnum registrationStatus = CrjJwryRegistrationStatusEnum.codeOf(
            byRecordId.getRegistrationStatus());
        if (registrationStatus != CrjJwryRegistrationStatusEnum.NOT_REGISTER) {
            throw new AppLogicException("该信息已经被登记,不可重新分派！");
        }
        this.dispatch(dispatchVO.toDispatchVO());
    }

    @Override
    @Transactional
    public ImportResultVO importJwry(ImportVO importVO) {
        final MultipartFile excel = importVO.getExcel();
        final String repeatStrategy = importVO.getRepeatStrategy();
        final String subjectId = importVO.getSubjectId();
        LoginUser currentUser = AuthHelper.getCurrentUser();
        // 批量导入人员基本信息
        final String extension = FilenameUtils.getExtension(excel.getOriginalFilename());
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        final String filename = "批量导入临住人员-" + date + "." + extension;
        final String xlsx = "xlsx";
        if (StringUtils.isBlank(filename) || StringUtils.isBlank(extension) || !(xlsx.equals(extension) || "xls".equals(
            extension))) {
            throw new ParamValidationException("暂只支持xlsx文件，请重新上传，谢谢！");
        }
        ReadCrjJwryExcelListener listener = ReadCrjJwryExcelListener
            .builder()
            .currentUser(currentUser)
            .qgCrjWebService(qgCrjWebService)
            .unitRepository(unitRepository)
            .dictRepository(dictRepository)
            .crjJwryDetailRepository(crjJwryDetailRepository)
            .crjJwryRepository(crjJwryRepository)
            .importDateFormat(new SimpleDateFormat("yyyy-MM-dd"))
            .importDateFormat2(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
            .format(new SimpleDateFormat("yyyyMMddHHmmss"))
            .dateFormat(new SimpleDateFormat("yyyyMMdd"))
            .repeatStrategy(repeatStrategy).build();
        try (InputStream inputStream = excel.getInputStream()) {
            EasyExcelFactory.read(inputStream, listener).sheet().headRowNumber(1).doRead();
            // 记录历史信息
            String initialId = personService.recordInitial(subjectId, filename, excel);
            // 记录失败
            personService.recordCrjResult(initialId, listener.getFailRows(), listener.getColumnMap());
            return new ImportResultVO(initialId, listener.getFailResult());
        } catch (IOException e) {
            throw new SystemException(
                String.format("import person fail! subjectId = %s, fileName = %s", subjectId, filename), e);
        }
    }

    @Override
    public void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request,
        String subjectId) throws IOException {
        String fileName = String.format("临住人员-%s.xlsx",
            LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<JwryExportVO> vos = crjJwryRepository.findByIds(request.getIds()).stream()
            .map(JwryExportVO::toExportVO).collect(Collectors.toList());
        EasyExcelFactory.write(response.getOutputStream(), JwryExportVO.class)
            .registerWriteHandler(new CustomCellWriteHandler())
            .includeColumnFiledNames(fieldNames)
            .sheet()
            .doWrite(vos);
    }

    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getCrjJwryListProperty());
    }
}
