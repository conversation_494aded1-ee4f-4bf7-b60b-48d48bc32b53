package com.trs.yq.police.subject.controller.event;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.operation.OperationLog;
import com.trs.yq.police.subject.repository.EventRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

/**
 * 事件档案
 *
 * <AUTHOR>
 * @date 2021/12/13 16:31
 */
@Validated
@RestController
@RequestMapping("/event")
@Slf4j
public class EventController {
    @Resource
    private EventService eventService;
    @Resource
    private PersonService personService;
    @Resource
    private GroupService groupService;
    @Resource
    private SubjectService subjectService;
    @Resource
    private ModuleService moduleService;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private ExportExcelService excelService;
    @Resource
    private EventRepository eventRepository;

    /**
     * 事件详情
     *
     * @param eventId 事件
     * @return {@link EventVO}
     */
    @GetMapping("/{eventId}")
    public EventVO getEvent(@PathVariable String eventId) {
        return eventService.getEventBasicInfo(eventId);
    }

    /**
     * 添加事件
     *
     * @param eventVO 事件
     * @return id
     */
    @PostMapping("")
    public String addEvent(@RequestBody @Valid EventVO eventVO) {
        return eventService.addEvent(eventVO);
    }

    /**
     * 修改
     *
     * @param eventVO 事件
     * @param eventId 事件id
     */
    @PutMapping("/{eventId}")
    public void updateEvent(@PathVariable String eventId, @RequestBody EventVO eventVO) {
        eventVO.setId(eventId);
        eventService.updateEvent(eventVO);
    }

    /**
     * 批量删除事件
     *
     * @param eventIds 事件id
     */
    @DeleteMapping("/delete/batch")
    public void deleteClues(@RequestBody @NotEmpty(message = "事件id缺失") List<String> eventIds) {
        eventService.deleteEvents(eventIds);
    }

    /**
     * 查询事件已经关联的人员列表
     *
     * @param eventId       事件id
     * @param requestParams {@link RequestParams}
     * @return {@link ClueRelatedPersonVO}
     */
    @PostMapping("/{eventId}/person/list")
    public PageResult<EventRelatedPersonVO> getEventAssociatedPersonVO(@PathVariable String eventId,
                                                                       @RequestBody @Valid RequestParams requestParams) {
        return eventService.getRelatedPersonList(eventId, requestParams.getPageParams());
    }

    /**
     * 查询事件已经关联的群体列表
     *
     * @param eventId       事件id
     * @param requestParams {@link RequestParams}
     * @return {@link EventRelateGroupVO}
     */
    @PostMapping("/{eventId}/group/list")
    public PageResult<EventRelateGroupVO> getEventAssociatedGroupVO(@PathVariable String eventId,
                                                                    @RequestBody @Valid RequestParams requestParams) {
        return eventService.getRelatedGroupList(eventId, requestParams.getPageParams());
    }

    /**
     * 获取事件关联的材料信息
     *
     * @param eventId 事件id
     * @return {@link FileInfoVO}
     */
    @GetMapping("/{eventId}/material")
    public List<FileInfoVO> getClueFileInfo(@PathVariable String eventId) {
        return eventService.getEventFileInfo(eventId);
    }

    /**
     * 根据事件id查询合成作战信息
     *
     * @param eventId 事件id
     * @return {@link BattleRecordCommandListVO}
     */
    @GetMapping("/{eventId}/composite/list")
    public List<BattleRecordCommandListVO> getBattleRecordList(@PathVariable String eventId) {
        return eventService.getBattleRecordList(eventId);
    }

    /**
     * 根据事件id查询指令信息
     *
     * @param eventId 事件id
     * @return {@link BattleRecordCommandListVO}
     */
    @GetMapping("/{eventId}/instruct/list")
    public List<BattleRecordCommandListVO> getBattleCommandList(@PathVariable String eventId) {
        return eventService.getBattleCommandList(eventId);
    }

    /**
     * 不分页查询事件已经关联的人员列表
     *
     * @param eventId 事件id
     * @return {@link EventRelatedPersonVO}
     */
    @GetMapping("/{eventId}/person/related")
    public List<EventRelatedPersonVO> getGroupRelatedPerson(@PathVariable String eventId) {
        return eventService.getPersonsFromEvent(eventId);
    }

    /**
     * 事件关联人员对话框中查询人员列表
     *
     * @param eventId 事件id
     * @param request {@link DialogPersonListRequestVO}
     * @return {@link DialogPersonListVO}
     */
    @PostMapping("/{eventId}/person/list/condition")
    public PageResult<DialogPersonListVO> getDialogPersonListRequestVOList(@PathVariable String eventId,
                                                                           @Valid @RequestBody DialogPersonListRequestVO request) {
        return personService.getDialogPersonListVOPageResult(request);
    }

    /**
     * 不分页查询事件已经关联的群体列表
     *
     * @param eventId 事件id
     * @return {@link EventRelateGroupVO}
     */
    @GetMapping("/{eventId}/group/related")
    public List<EventRelateGroupVO> getClueRelatedGroup(@PathVariable String eventId) {
        return eventService.getGroupsFromEvent(eventId);
    }

    /**
     * 事件关联群体对话框分页查询
     *
     * @param eventId 事件id
     * @param request {@link DialogGroupListRequestVO}
     * @return {@link DialogGroupListVO}
     */
    @PostMapping("/{eventId}/group/list/condition")
    public PageResult<DialogGroupListVO> getDialogGroupListVO(@PathVariable String eventId,
                                                              @RequestBody @Validated DialogGroupListRequestVO request) {
        return groupService.getDialogGroupListVO(request);
    }

    /**
     * 事件关联多个群体
     *
     * @param eventId 事件id
     * @param vo      {@link EventGroupRelationRequestVO}
     */
    @PutMapping("/{eventId}/group/relations")
    public void updateGroupEventRelation(@PathVariable String eventId, @RequestBody @Validated EventGroupRelationRequestVO vo) {
        vo.setEventId(eventId);
        eventService.updateGroupEventRelation(vo);
    }

    /**
     * 事件关联多个人员
     *
     * @param eventId 事件id
     * @param vo      {@link EventPersonRelationRequestVO }
     */
    @PutMapping("/{eventId}/person/relations")
    public void updateEventPersonRelations(@PathVariable String eventId, @RequestBody @Validated EventPersonRelationRequestVO vo) {
        vo.setEventId(eventId);
        eventService.updateEventPersonRelations(vo);
    }

    /**
     * 移除事件关联人员
     *
     * @param relationId 关系id
     */
    @DeleteMapping("/person/relation/{relationId}")
    public void removePersonFromEvent(@PathVariable String relationId) {
        eventService.removePersonFromEvent(ModuleEnum.EVENT, relationId);
    }

    /**
     * 移除事件关联群体
     *
     * @param relationId 关系id
     */
    @DeleteMapping("/group/relation/{relationId}")
    public void removeGroupFromEvent(@PathVariable String relationId) {
        eventService.removeGroupFromEvent(relationId);
    }

    /**
     * 添加事件材料关联
     *
     * @param eventId          事件id
     * @param attachmentVOList 材料信息
     */
    @PostMapping("/{eventId}/file/relation")
    public void addEventFileRelation(@PathVariable String eventId, @RequestBody List<AttachmentVO> attachmentVOList) {
        eventService.addEventFileRelation(eventId, attachmentVOList);
    }

    /**
     * 删除事件材料关联
     *
     * @param eventId 事件id
     * @param fileId  材料id
     */
    @DeleteMapping("/{eventId}/file/{fileId}")
    public void deleteEventFileRelation(@PathVariable String eventId, @PathVariable String fileId) {
        eventService.deleteClueFileRelation(eventId, fileId);
    }

    /**
     * 查询事件列表筛选条件
     *
     * @param subjectId 专题id
     * @return 线索列表筛选条件
     */
    @GetMapping("/list/filters")
    public List<ListFilter> getListFilters(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return subjectService.getEventListQueryFilters(subjectId);
    }

    /**
     * 事件列表查询
     *
     * @param subjectId 专题主键
     * @param request   查询参数
     * @return 分页查询结果
     */
    @PostMapping("/list")
    public PageResult<EventListVO> getEventList(@NotBlank(message = "专题编号缺失") String subjectId,
                                                @RequestBody @Valid ListRequestVO request) {
        return eventService.getEventList(subjectId, request);
    }

    /**
     * 查询档案目录
     *
     * @param subjectId 专题id
     * @return 模块列表 {@link ContentVO}
     */
    @GetMapping("/content")
    public List<ContentVO> getArchiveContent(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return moduleService.getArchiveContent(subjectId, "event");
    }

    /**
     * 查询专题下所有事件类别
     *
     * @param subjectId 专题id
     * @return 事件类别列表
     */
    @GetMapping("/types")
    public List<IdNameVO> getEventTypes(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        List<LabelEntity> types = labelRepository.findAllBySubjectId(subjectId, "event");
        return types.stream().map(type -> new IdNameVO(type.getId(), type.getName())).collect(Collectors.toList());
    }

    /**
     * 批量导出列表
     *
     * @param response  {@link HttpServletResponse}
     * @param exportParams   {@link ExportParams}
     * @throws IOException io异常
     */
    @PostMapping("/list/export")
    public void listExport(HttpServletResponse response,
                           @Validated @RequestBody ExportParams exportParams) throws IOException {
        eventService.downLoadExcel(response, exportParams, exportParams.getSubjectId());
    }

    /**
     * 根据专题id出查询可导出的字段信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getPersonProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return eventService.getExportPropertyList(subjectId);
    }

    /**
     * 获取线索展示的按钮
     *
     * @param eventId 线索id
     * @return {@link ButtonVO}
     */
    @GetMapping("/{eventId}/button")
    public ButtonVO getButton(@PathVariable String eventId) {
        return eventService.getButton(eventId);
    }

    /**
     * 上报
     *
     * @param eventId 线索id
     * @return {@link ReportInfoVO}
     */
    @PostMapping("/{eventId}/report")
    public ReportInfoVO reportToSuperior(@PathVariable @NotBlank String eventId) {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        return eventService.reportToSuperior(eventId, currentUser);
    }

    /**
     * 改变线索的处置状态
     *
     * @param eventId 线索id
     * @param status  状态
     */
    @PostMapping("/{eventId}/dispose")
    public void changeDisposeStatus(@PathVariable @NotBlank String eventId, @NotNull String status) {

        eventService.changeDisposeStatus(eventId, status);
    }

    /**
     * 修改涉事行为
     *
     * @param eventId                事件id
     * @param personEventBehaviourVO {@link PersonEventBehaviourVO}
     */
    @PutMapping("/{eventId}/person/behaviour")
    public void updateBehaviour(@PathVariable String eventId, @RequestBody PersonEventBehaviourVO personEventBehaviourVO) {
        eventService.updateBehaviour(eventId, personEventBehaviourVO);
    }

    /**
     * [事件详情]修改事件关联人员涉事照片
     * http://192.168.200.192:3001/project/4897/interface/api/134439
     *
     * @param eventId        事件id
     * @param personImagesVO 人员涉事图片vo
     */
    @PutMapping("/{eventId}/person/images")
    public void updatePersonImages(@PathVariable String eventId, @Validated @RequestBody EventPersonImagesVO personImagesVO) {
        eventService.updatePersonImages(eventId, personImagesVO);
    }

    /**
     * [事件详情]修改事件关联人员来源
     * http://192.168.200.192:3001/project/4897/interface/api/134434
     *
     * @param eventId        事件id
     * @param personSourceVO 人员来源vo
     */
    @PutMapping("/{eventId}/person/source")
    public void updatePersonSource(@PathVariable String eventId, @Validated @RequestBody EventPersonSourceVO personSourceVO) {
        eventService.updatePersonSource(eventId, personSourceVO);
    }

    /**
     * 导出excel
     *
     * @param response  响应体
     * @param eventId   人员id
     * @param subjectId 专题id
     */
    @GetMapping("/{eventId}/export/{subjectId}")
    @OperationLog(operator = Operator.EXPORT, module = OperateModule.EVENT_ARCHIVE_MANAGE, desc = "下载事件档案", primaryKey = "eventId", targetObjectType = TargetObjectTypeEnum.EVENT)
    public void exportPerson(@PathVariable String eventId, @PathVariable String subjectId, HttpServletResponse response) {
        EventEntity eventEntity = eventRepository.findById(eventId).orElse(new EventEntity());
        excelService.export(response, "事件档案-"+eventEntity.getTitle(), excelService.getEventExcel(eventId, subjectId));
    }
}
