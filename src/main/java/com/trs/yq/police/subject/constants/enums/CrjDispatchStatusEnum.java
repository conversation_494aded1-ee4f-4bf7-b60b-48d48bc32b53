package com.trs.yq.police.subject.constants.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/22 17:55
 */
public enum CrjDispatchStatusEnum {
    NOT_DISPATCHED("0", "未分派"),
    DISPATCHED_TO_QX("1", "已分派到区县"),
    DISPATCHED("2", "已分派");

    CrjDispatchStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * 通过模块码获取枚举
     *
     * @param code 操作符码
     * @return 操作符 {@link Operator}
     * <AUTHOR>
     * @since 2021/7/27 17:55
     */
    public static CrjDispatchStatusEnum codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (CrjDispatchStatusEnum syncStatus : CrjDispatchStatusEnum.values()) {
                if (StringUtils.equals(code, syncStatus.code)) {
                    return syncStatus;
                }
            }
        }
        return null;
    }
}
