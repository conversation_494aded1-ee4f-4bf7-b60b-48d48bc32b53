package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 角色-用户映射实体类
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_ADMIN_ROLE_USER")
public class RoleUserEntity extends BaseEntity {

    private static final long serialVersionUID = -6757201121295594727L;

    /**
     * 角色id
     */
    private String roleId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 角色名称
     */
    private String roleName;
}
