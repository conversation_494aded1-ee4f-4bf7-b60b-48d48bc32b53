package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.UnitPcsEntity;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/06
 **/
public interface UnitPcsRepository extends BaseRepository<UnitPcsEntity, String> {

    /**
     * 根据type查询派出所
     *
     * @param type 类别
     * @return 派出所
     */
    List<UnitPcsEntity> findAllByType(Integer type);
}
