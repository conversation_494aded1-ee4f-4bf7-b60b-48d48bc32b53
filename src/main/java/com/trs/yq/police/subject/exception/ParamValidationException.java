package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Parameter validation failure exception
 *
 * <AUTHOR>
 */
@ResponseStatus(value = HttpStatus.BAD_REQUEST)
public class ParamValidationException extends SystemException {

    private static final long serialVersionUID = 8032239958825529356L;

    /**
     * @param message 异常信息
     */
    public ParamValidationException(String message) {
        super(message);
    }

    /**
     *
     * @param message 异常消息
     * @param cause 引发异常的异常
     */
    public ParamValidationException(String message, Throwable cause) {
        super(message, cause);
    }
}
