package com.trs.yq.police.subject.domain.dto;

import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 风险研判查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/16 16:34
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class RiskDetailDto {

    private String personId;

    private String name;

    private String idNumber;

    private String photo;

    private String controlStatus;

    private List<String> behaviors;

    private LocalDateTime addTime;
}
