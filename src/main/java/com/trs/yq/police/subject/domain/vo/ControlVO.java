package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.validation.ControlGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 管控信息视图层
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
public class ControlVO implements Serializable {

    private static final long serialVersionUID = 5778477805440348898L;

    /**
     * 派出所code
     */
    @NotBlank(message = "派出所编号不能为空", groups = ControlGroup.PoliceStation.class)
    private List<String> policeStationCode;
    /**
     * 派出所名称
     */
    @NotBlank(message = "派出所名称不能为空", groups = ControlGroup.PoliceStation.class)
    private String policeStationName;
    /**
     * 派出所责任领导
     */
    private String leaderName;
    /**
     * 责任领导职务
     */
    private String leaderJob;
    /**
     * 责任领导联系方式
     */
    private String leaderContact;
    /**
     * 责任人id
     */
    private String responsibleId;
    /**
     * 责任人姓名
     */
    private String responsibleName;
    /**
     * 责任人职务
     */
    private String responsibleJob;
    /**
     * 责任人联系方式
     */
    private String responsibleContact;

}
