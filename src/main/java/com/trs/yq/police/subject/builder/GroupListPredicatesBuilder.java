package com.trs.yq.police.subject.builder;

import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.GroupLabelRelationEntity;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.trs.yq.police.subject.utils.StringUtil.getPoliceStationPrefix;

/**
 * <AUTHOR>
 * @date 2021/09/07
 */
public class GroupListPredicatesBuilder {
    /**
     * 创建模糊检索Predicates
     *
     * @param searchParams    模糊搜索参数
     * @param groupEntityRoot 群体记录
     * @param criteriaBuilder criteriaBuilder
     * @return 模糊检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildSearchPredicates(SearchParams searchParams, Root<GroupEntity> groupEntityRoot, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(searchParams.getSearchField()) && StringUtils.isNotBlank(searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            Predicate namePredicate = criteriaBuilder.like(groupEntityRoot.get("name").as(String.class), "%" + searchValue + "%");
            switch (searchField) {
                case "groupName":
                case "fullText":
                    predicates.add(namePredicate);
                    break;
                default:
                    break;
            }
        }
        return predicates;
    }

    /**
     * 创建人员列表动态检索Predicates
     *
     * @param subjectId       专题id
     * @param filterParams    检索参数
     * @param groupEntityRoot 群体实体
     * @param criteriaBuilder criteriaBuilder
     * @return 检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildListFilterPredicates(String subjectId, List<KeyValueVO> filterParams, Root<GroupEntity> groupEntityRoot, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();

        // 联表查询专题和群体的关系
        predicates.add(criteriaBuilder.equal(groupEntityRoot.get("subjectId").as(String.class), subjectId));

        //动态查询参数
        filterParams.forEach(kv -> {
            switch (kv.getKey()) {
                case "groupType":
                    predicates.add(getGroupTypeRelationPredicates(groupEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                case "department":
                    predicates.add(getGroupCreateDepartmentPredicates(groupEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                case "timeParams":
                    predicates.add(getGroupCreateTimePredicates(groupEntityRoot, kv.getValue(), criteriaBuilder));
                    break;
                default:
                    break;
            }
        });

        return predicates;
    }

    /**
     * 创建群体创建时间查询条件
     *
     * @param groupEntityRoot 人员实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getGroupCreateTimePredicates(Root<GroupEntity> groupEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
        if (timeParams != null) {
            Predicate p1 = criteriaBuilder.greaterThanOrEqualTo(groupEntityRoot.get("crTime").as(LocalDateTime.class), timeParams.getBeginTime());
            Predicate p2 = criteriaBuilder.lessThanOrEqualTo(groupEntityRoot.get("crTime").as(LocalDateTime.class), timeParams.getEndTime());
            return criteriaBuilder.and(p1, p2);
        }
        return null;
    }

    /**
     * 创建群体创建部门查询条件
     *
     * @param groupEntityRoot 人员实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getGroupCreateDepartmentPredicates(Root<GroupEntity> groupEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.like(groupEntityRoot.get("crDeptCode").as(String.class), getPoliceStationPrefix(value) + "%");
    }

    /**
     * 创建群体类别查询条件
     *
     * @param groupEntityRoot 人员实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getGroupTypeRelationPredicates(Root<GroupEntity> groupEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        Subquery<GroupLabelRelationEntity> subQuery = criteriaBuilder.createQuery().subquery(GroupLabelRelationEntity.class);
        Root<GroupLabelRelationEntity> groupTypeRelationEntityRoot = subQuery.from(GroupLabelRelationEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(groupTypeRelationEntityRoot.get("labelId"), value);
        Predicate predicate2 = criteriaBuilder.equal(groupTypeRelationEntityRoot.get("groupId"), groupEntityRoot.get("id"));
        subQuery.select(groupTypeRelationEntityRoot).where(criteriaBuilder.and(predicate1, predicate2));
        return criteriaBuilder.exists(subQuery);
    }
}
