package com.trs.yq.police.subject.operation.handler;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.WorkHandler;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.trs.yq.police.subject.operation.event.BaseEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 抽象事件处理器
 *
 * @param <D> 数据
 * @param <E> 事件
 * <AUTHOR>
 * @date 2021/8/18 15:46
 */
@Slf4j
public abstract class AbstractEventHandler<D, E extends BaseEvent<D>> {

    private final ConcurrentLinkedQueue<AbstractEventHandler<D, E>> eventHandlers = new ConcurrentLinkedQueue<>();

    private final ReentrantReadWriteLock readWriteLock = new ReentrantReadWriteLock();

    private Disruptor<E> disruptor;

    private RingBuffer<E> ringBuffer;

    private final ConcurrentLinkedQueue<D> initialQueue = new ConcurrentLinkedQueue<>();

    private final AtomicBoolean started = new AtomicBoolean(false);

    /**
     * 初始化
     */
    public synchronized void start() {
        // 判断是否存在环形队列
        if (disruptor != null) {
            // 存在则用现有的
            if (log.isDebugEnabled()) {
                log.debug("OperationLogHandler not start new disruptor, using existing object");
            }
            return;
        }
        // 不存在，则新建
        disruptor = new Disruptor<>(getEventFactory(), getBufferSize(), new EventThreadFactory("OperationLog"), ProducerType.MULTI, getWaitStrategy());
        disruptor.setDefaultExceptionHandler(new DefaultExceptionHandler());
        disruptor.handleEventsWithWorkerPool(getWorkers());
        ringBuffer = disruptor.start();

        // 初始化数据发布
        initialQueue.forEach(data -> this.ringBuffer.publishEvent((event, sequence, arg0) -> event.setValue(arg0), data));

        // 增加清理资源的钩子
        readWriteLock.writeLock().lock();
        if (!eventHandlers.isEmpty()) {
            Runtime.getRuntime().addShutdownHook(new Thread(() -> eventHandlers.forEach(AbstractEventHandler::shutdown)));
        }
        eventHandlers.add(this);
        readWriteLock.writeLock().unlock();

        started.compareAndSet(false, true);
    }

    private final ReentrantLock publishLock = new ReentrantLock();

    /**
     * 推送event
     *
     * @param data payload
     */
    public void publishEvent(D data) {
        publishLock.lock();
        try {
            if (ringBuffer == null) {
                initialQueue.add(data);
                return;
            }
            if (!started.get()) {
                start();
            }
            ringBuffer.publishEvent((event, sequence, arg0) -> event.setValue(arg0), data);
        } catch (Exception ex) {
            log.error("publish event error ! data ==>> {}", data, ex);
        } finally {
            publishLock.unlock();
        }
    }

    private void shutdown() {
        if (Objects.nonNull(disruptor)) {
            disruptor.shutdown();
        }
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小，默认为2^n
     */
    protected abstract int getBufferSize();

    /**
     * 获取等待策略
     * <p> {@code BlockingWaitStrategy} 默认，CPU消耗最小，最低效 </p>
     * <p> {@code SleepingWaitStrategy} CPU消耗类似于 {@code BlockingWaitStrategy}, 适合用于异步日志 </p>
     * <p> {@code TimeoutBlockingWaitStrategy} 超时等待策略,log4j2默认使用 10ms </p>
     * <p> {@code YieldingWaitStrategy} 性能优,EventHandler 线程数需要低于CPU逻辑核心数，对部署环境要求高 </p>
     * <p> {@code BusySpinWaitStrategy} 性能最优，EventHandler线程数需要低于物理机的物理核心数，禁用超线程 </p>
     *
     * @return 默认返回 {@code BlockingWaitStrategy}
     */
    protected abstract WaitStrategy getWaitStrategy();

    /**
     * 消费者组
     *
     * @return 消费者组
     */
    protected abstract WorkHandler<E>[] getWorkers();

    /**
     * 事件工厂
     *
     * @return 事件工厂
     */
    protected abstract EventFactory<E> getEventFactory();
}
