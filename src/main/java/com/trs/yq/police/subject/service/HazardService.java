package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.HazardBarNode;
import com.trs.yq.police.subject.domain.vo.HazardResult;

import java.util.List;

/**
 * 风险洞察服务
 */
public interface HazardService {

    /**
     * 获取风险洞察列表
     *
     * @return {@link HazardResult}
     */
    List<HazardResult> getHazardList();

    /**
     * 查询柱状图
     *
     * @return {@link HazardBarNode}
     */
    List<HazardBarNode> getHazardBarList();
}
