package com.trs.yq.police.subject.webService.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/4/24 14:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StayInfoArgs {

    private StayInfo djxx;


    /**
     * 住宿信息
     */
    @Data
    public static class StayInfo {

        /**
         * 英文姓
         */
        private String ywx;
        /**
         * 英文名
         */
        private String ywm;
        /**
         * 英文姓名
         */
        private String ywxm;
        /**
         * 中文姓名
         */
        private String zwxm;
        /**
         * 性别
         */
        private Integer xb;
        /**
         * 出生日期
         */
        private String csrq;
        /**
         * 证件种类(字典 903)
         */
        private String zjzl;

        /**
         * 证件号码
         */
        private String zjhm;

        /**
         * 国家地区(字典 904)
         */
        private String gjdq;

        /**
         * 人员地域类别(字典 920)
         */
        private String rydylb;

        /**
         * 数据来源(字典 988)
         */
        private String sjly;

        /**
         * 来源数据编号(用来进行人员信息更新操作)
         */
        private String lzryywbh;

        /**
         * 录入时间(采集时间). 应传递 yyyyMMddHHmmss 格式的日期字符
         */
        private String lrsj;

        /**
         * 派出所编号(字典 700)
         */
        private String pcsbh;
        /**
         * 派出所名称
         */
        private String pcsmc;
        /**
         * 住宿日期
         */
        private String zsrq;
        /**
         * 离开日期
         */
        private String lkrq;
        /**
         * 留宿单位
         */
        private String lsdwhz;
        /**
         * 留宿单位名称
         */
        private String lsdwmc;
        /**
         * 留宿单位地址
         */
        private String lsdwhzdz;

        private String qzzl;

        private String qzhm;
    }


}
