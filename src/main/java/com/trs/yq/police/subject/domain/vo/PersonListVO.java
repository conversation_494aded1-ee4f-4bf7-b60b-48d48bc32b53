package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.repository.ControlRepository;
import com.trs.yq.police.subject.repository.GroupRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.SpringContextUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.DictTypeConstants.*;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 人员列表VO
 *
 * <AUTHOR>
 * @date 2021/07/27
 */
@Data
public class PersonListVO implements Serializable {

    private static final long serialVersionUID = 3084389814406599021L;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 证件类型
     */
    private String idType="1";

    /**
     * 群体类型
     */
    private String groupType;

    /**
     * 人员类型
     */
    private String personType;

    /**
     * 管控级别
     */
    private String controlLevel;

    /**
     * 管控状态
     */
    private String controlStatus;

    /**
     * 布控状态
     */
    private String monitorStatus;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 管辖区县
     */
    private String controlArea;

    /**
     * 是否允许删除
     */
    private Boolean canDelete = false;

    /**
     * 是否在控
     */
    private String isInControl;

    /**
     * 人员DO->人员列表VO
     *
     * @param personEntity 人员DO
     * @param subjectId    专题id
     * @return 人员列表VO
     */
    public static PersonListVO of(PersonEntity personEntity, String subjectId) {
        PersonListVO vo = new PersonListVO();
        //id
        vo.setPersonId(personEntity.getId());

        //姓名
        vo.setPersonName(personEntity.getName());

        //身份证号
        vo.setIdNumber(personEntity.getIdNumber());

        vo.setIdType(StringUtils.isBlank(personEntity.getIdType()) ?"1" :personEntity.getIdType());
        //群体
        LabelRepository labelRepository = SpringContextUtil.getBean(LabelRepository.class);
        GroupRepository groupRepository = SpringContextUtil.getBean(GroupRepository.class);
        List<GroupEntity> groups = groupRepository.findByPersonIdAndSubjectId(personEntity.getId(), subjectId);
        if (!groups.isEmpty()) {
            String types = groups.stream()
                    .map(group -> labelRepository.findByGroupIdAndSubjectId(group.getId(), subjectId)).flatMap(List::stream)
                    .map(LabelEntity::getName).distinct().collect(Collectors.joining("、"));
            vo.setGroupType(types);
        }

        //类别
        List<LabelEntity> types = labelRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), subjectId);
        if (!labelRepository.findAllBySubjectId(subjectId, "person").isEmpty()) {
            vo.setPersonType(types.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));
        }

        //管控状态
        DictService dictService = SpringContextUtil.getBean(DictService.class);
        vo.setControlStatus(
                dictService.getDictEntityByTypeAndCode(DICT_TYPE_CONTROL_STATUS, personEntity.getControlStatus()).getName());
        //布控状态
        vo.setMonitorStatus(
            dictService.getDictEntityByTypeAndCode(DICT_TYPE_MONITOR_STATUS, personEntity.getMonitorStatus()).getName());

        //管控级别
        ControlRepository controlRepository = SpringContextUtil.getBean(ControlRepository.class);
        ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(personEntity.getId(), subjectId)
                .orElse(null);
        if (Objects.nonNull(controlEntity) && StringUtils.isNotBlank(controlEntity.getControlLevel())) {
            DictEntity dictEntityByTypeAndCode = dictService.getDictEntityByTypeAndCode(subjectId.equals(WW_SUBJECT) ? "ps_ww_control_level" : DICT_TYPE_CONTROL_LEVEL, controlEntity.getControlLevel());
            vo.setControlLevel(dictEntityByTypeAndCode == null ? "" : dictEntityByTypeAndCode.getName());
        }
        UnitRepository unitRepository = SpringContextUtil.getBean(UnitRepository.class);
        if (Objects.nonNull(controlEntity) && StringUtils.isNotBlank(controlEntity.getPoliceStationCode())) {
            vo.setControlArea(
                    unitRepository.getAreaNameByUnitCode(controlEntity.getPoliceStationCode().substring(0, 6)));
        }
        //更新时间
        vo.setUpdateTime(personEntity.getUpTime().format(DATE_TIME_FORMATTER));
        //创建时间
        vo.setCreateTime(personEntity.getCrTime().format(DATE_TIME_FORMATTER));

        //是否允许删除
        Optional.ofNullable(AuthHelper.getCurrentUser())
                .ifPresent(user -> vo.setCanDelete(user.getId().equals(personEntity.getCrBy())));

        //是否在控
        DictEntity isInControl = dictService.getDictEntityByTypeAndCode(DICT_TYPE_PERSON_IN_CONTROL,
                personEntity.getIsInControl());
        Optional.ofNullable(isInControl).ifPresent(dict -> vo.setIsInControl(dict.getName()));
        return vo;
    }
}
