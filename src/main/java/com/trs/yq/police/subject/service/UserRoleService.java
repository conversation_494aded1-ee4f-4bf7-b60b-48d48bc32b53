package com.trs.yq.police.subject.service;

/**
 * <AUTHOR>
 */
public interface UserRoleService {
    /**
     * 判断用户是否不包含某种角色
     *
     * @param userId   用户id
     * @param roleName 角色名
     * @return 是否不包含
     */
    boolean checkUserNotContainsRole(String userId, String roleName);

    /**
     * 判断用户是否包含某种角色
     *
     * @param userId   用户id
     * @param roleName 角色名
     * @return 是否包含
     */
    boolean checkUserContainsRole(String userId, String roleName);
}
