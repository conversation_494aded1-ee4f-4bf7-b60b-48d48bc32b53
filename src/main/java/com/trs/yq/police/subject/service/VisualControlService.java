package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.response.HandleFeedbackResponse;
import com.trs.yq.police.subject.domain.response.OccurredEventResponse;
import com.trs.yq.police.subject.domain.response.RiskDetailPersonResponse;
import com.trs.yq.police.subject.domain.response.RiskDetailResponse;
import com.trs.yq.police.subject.domain.vo.CameraVO;
import com.trs.yq.police.subject.domain.vo.CompositeDynamicVO;
import com.trs.yq.police.subject.domain.vo.GroupListItem;
import com.trs.yq.police.subject.domain.vo.MapInfoVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.ProcessMonitoringVO;
import com.trs.yq.police.subject.domain.vo.RiskJudgeVO;
import java.util.List;
import org.springframework.data.domain.Pageable;

/**
 * 可视化管控业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/16 15:08
 */
public interface VisualControlService {

    /**
     * 获取风险研判详情
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return 分页查询结果
     */
    RiskDetailResponse getRiskDetail(String eventId, Pageable pageable);

    /**
     * 获取事件发生详情
     *
     * @param eventId 事件id
     * @return 事件详情
     */
    OccurredEventResponse getOccurredEvent(String eventId);

    /**
     * 获取处警反馈信息
     *
     * @param eventId 事件id
     * @return 处警反馈
     */
    List<HandleFeedbackResponse> getHandleFeedback(String eventId);

    /**
     * 风险研判-基本信息查询
     *
     * @param eventId 事件id
     * @return 风险研判-基本信息
     */
    RiskJudgeVO getRiskJudgeInfo(String eventId);

    /**
     * 人员列表查询
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return 人员列表
     */
    PageResult<RiskDetailPersonResponse> getRisDetailPersonList(String eventId, Pageable pageable);

    /**
     * 获取事件预案登记
     *
     * @param eventId 事件id
     * @return 预案等级
     */
    String getEventLevel(String eventId);

    /**
     * 地图点位信息查询
     *
     * @param eventId 事件id
     * @return {@link MapInfoVO}
     */
    MapInfoVO getMapInfo(String eventId);

    /**
     * 查询事件流程监测
     *
     * @param eventId 事件id
     * @return {@link ProcessMonitoringVO}
     */
    List<ProcessMonitoringVO> getProcessMonitoring(String eventId);

    /**
     * 查询事件
     *
     * @param eventId 事件id
     * @return {@link CompositeDynamicVO}
     */
    List<CompositeDynamicVO> getCompositeDynamic(String eventId);


    /**
     * 查询群体列表
     *
     * @param eventId 事件id
     * @return {@link GroupListItem}
     */
    List<GroupListItem> getGroupList(String eventId);

    /**
     * 查询事件处置步骤
     *
     * @param eventId 事件id
     * @return 步骤编号
     */
    Integer getEvenDisposalStep(String eventId);

    /**
     * 查询摄像头详情
     *
     * @param cameraId 摄像头id
     * @return {@link CameraVO}
     */
    CameraVO getCameraInfo(String cameraId);
}
