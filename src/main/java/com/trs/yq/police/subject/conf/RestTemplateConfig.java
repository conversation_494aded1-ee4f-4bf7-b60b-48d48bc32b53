package com.trs.yq.police.subject.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * http客户端配置
 *
 * <AUTHOR>
 * @date 2021/09/15
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 配置全局的RestTemplate
     *
     * @return {@link RestTemplate}
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(10 * 1000);
        requestFactory.setReadTimeout(30 * 60 * 1000);
        return setRestTemplateStringEncode(new RestTemplate(requestFactory));
    }

    /**
     * 设置RestTemplate string默认编码
     *
     * @param restTemplate RestTemplate实例
     * @return RestTemplate实例
     */
    public static RestTemplate setRestTemplateStringEncode(RestTemplate restTemplate) {

        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        for (HttpMessageConverter<?> messageConverter : messageConverters) {
            if (messageConverter instanceof StringHttpMessageConverter) {
                ((StringHttpMessageConverter) messageConverter)
                        .setDefaultCharset(StandardCharsets.UTF_8);
                break;
            }
        }
        return restTemplate;
    }
}
