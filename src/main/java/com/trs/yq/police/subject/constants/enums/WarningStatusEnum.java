package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 预警状态枚举
 *
 * <AUTHOR>
 * @date 2021/9/7 10:32
 */
public enum WarningStatusEnum {

    /**
     * enums
     */
    WAIT_SIGN("1", "待签收"),

    SIGN_TO_JUDGE("2", "已签收待研判"),

    JUDGED("3", "已研判");

    @Getter
    private final String code;

    @Getter
    private final String name;

    WarningStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 编码转换为预警状态
     *
     * @param code 编码
     * @return 预警状态
     */
    public static WarningStatusEnum codeOf(String code) {
        if (StringUtils.isBlank(code)) {
            throw new UnsupportedOperationException("不支持的编码");
        }
        for (WarningStatusEnum warningStatus : WarningStatusEnum.values()) {
            if (StringUtils.equals(code, warningStatus.getCode())) {
                return warningStatus;
            }
        }
        return null;
    }
}
