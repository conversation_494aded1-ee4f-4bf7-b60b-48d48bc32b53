package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ModuleFieldEntity;
import org.springframework.data.repository.query.Param;

/**
 * 模块属性持久层接口
 *
 * <AUTHOR>
 * @date 2021/8/19 18:10
 */
public interface ModuleFieldRepository extends BaseRepository<ModuleFieldEntity, String> {

    /**
     * 根据模块编号和属性名查询
     *
     * @param moduleCode 模块编号
     * @param fieldName  属性名
     * @return 模块属性
     */
    ModuleFieldEntity findByModuleCodeAndFieldName(@Param("moduleCode") String moduleCode, @Param("fieldName") String fieldName);
}
