package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 活动详情
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/9 21:30
 **/
@Data
public class ActivityDetailVO {
    /**
     * 活动地址（仅限单人和多人）
     */
    private String activityAddress;
    /**
     * 活动时间（仅限单人和多人）
     */
    private LocalDateTime activityTime;
    /**
     * 活动类型（仅限单人和多人）
     */
    private String activityType;
    /**
     * 通联次数（仅话单类型预警有该字段）
     */
    private Integer contactCount;
}
