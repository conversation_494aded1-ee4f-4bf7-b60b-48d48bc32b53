package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.FootholdEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 落脚点信息数据层
 *
 * <AUTHOR>
 */
@Repository
public interface FootholdRepository extends BaseRepository<FootholdEntity, String>{

    /**
     * 按人员id查询所有落脚点，按开始时间倒序排列
     *
     * @param personId 人员id
     * @return 落脚点列表 {@link FootholdEntity}
     */
    List<FootholdEntity> findAllByPersonIdOrderByStartTimeDesc(String personId);
}
