package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 线索、事件 能展示的按钮
 *
 * <AUTHOR>
 * @date 2021/12/20 16:08
 */
@Data
public class ButtonVO implements Serializable {
    private static final long serialVersionUID = -8776291069115400199L;
    /**
     * 上报
     */
    private Boolean report;
    /**
     * 不处置
     */
    private Boolean notDispose;
    /**
     * 处置完毕
     */
    private Boolean disposalCompleted;
    /**
     * 合成
     */
    private Boolean battleCommand;
    /**
     * 合成
     */
    private Boolean battleRecord;
}
