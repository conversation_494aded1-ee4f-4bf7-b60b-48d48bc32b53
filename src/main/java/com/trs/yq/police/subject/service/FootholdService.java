package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.FootholdListRequestVO;
import com.trs.yq.police.subject.domain.vo.FootholdVO;
import com.trs.yq.police.subject.domain.vo.PageResult;

/**
 * 落脚点信息业务层接口
 *
 * <AUTHOR>
 */
public interface FootholdService {
    /**
     * 根据参数分页查询落脚点信息
     *
     * @param personId           人员id
     * @param footholdListRequestVO 落脚点分页查询参数
     * @return 落脚点分页列表
     */
    PageResult<FootholdVO> getFootholdPageable(String personId, FootholdListRequestVO footholdListRequestVO);

    /**
     * 新增落脚点
     *
     * @param personId   人员id
     * @param footholdVO 落脚点信息
     */
    void addStay(String personId, FootholdVO footholdVO);

    /**
     * 修改落脚点
     *
     * @param personId   人员id
     * @param footholdVO 落脚点信息
     */
    void updateStay(String personId, FootholdVO footholdVO);

    /**
     * 删除落脚点
     *
     * @param personId 人员id
     * @param id       落脚点id
     */
    void deleteStay(String personId, String id);
}
