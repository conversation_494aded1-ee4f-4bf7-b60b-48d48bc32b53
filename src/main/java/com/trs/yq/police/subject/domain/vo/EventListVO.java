package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.BattleCommandRepository;
import com.trs.yq.police.subject.repository.BattleRecordRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.SpringContextUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/12/14 16:06
 */
@Data
public class EventListVO implements Serializable {

    private static final long serialVersionUID = 3267152026864025068L;

    private String id;
    /**
     * 事件标题
     */
    private String title;

    /**
     * 参见码表ps_event_type
     */
    private String level;

    /**
     * 参考码表ps_event_data_source
     */
    private String source;

    /**
     * 事件发生时间
     */
    private LocalDateTime occurrenceTime;
    /**
     * 事件发生地
     */
    private String address;

    /**
     * 管控单位名称
     */
    private String controlDeptName;

    /**
     * 处置状态 参见码表(ps_event_disposal_status)
     */
    private String disposalStatus;
    /**
     * 上报状态
     * 码表 ps_report_status
     */
    private String reportStatus;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;

    /**
     * 发起合成数
     */
    private Integer composeCount;

    /**
     * 发起指令数
     */
    private Integer commandCount;

    /**
     * 是否允许删除
     */
    private Boolean canDelete = false;

    /**
     * 创建事件列表vo
     *
     * @param eventEntity {@link EventEntity}
     * @return {@link EventListVO}
     */
    public static EventListVO of(EventEntity eventEntity) {
        EventListVO vo = new EventListVO();
        vo.setId(eventEntity.getId());
        vo.setTitle(eventEntity.getTitle());
        vo.setCreateTime(eventEntity.getCrTime());
        //设置事件级别
        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        LabelEntity eventType = labelRepository.findByEventId(eventEntity.getId());
        if (Objects.nonNull(eventType)) {
            vo.setLevel(eventType.getName());
        }

        //设置事件来源
        DictService dictService = SpringContextUtil.getBean(DictService.class);
        vo.setSource(dictService.getDictEntityByTypeAndCode("ps_event_data_source", eventEntity.getSource()).getName());
        vo.setOccurrenceTime(eventEntity.getOccurrenceTime());
        vo.setAddress(eventEntity.getAddress());
        vo.setControlDeptName(eventEntity.getControlDeptName());
        //设置事件状态
        vo.setDisposalStatus(dictService.getDictEntityByTypeAndCode("ps_event_disposal_status", eventEntity.getDisposalStatus()).getName());
        vo.setReportStatus(dictService.getDictEntityByTypeAndCode("ps_report_status", eventEntity.getReportStatus()).getName());
        vo.setCreateTime(eventEntity.getCrTime());
        BattleRecordRepository battleRecordRepository = SpringContextUtil.getBean(BattleRecordRepository.class);
        vo.setComposeCount(battleRecordRepository.findRecordById("system.t_event", eventEntity.getId()).size());
        BattleCommandRepository battleCommandRepository = SpringContextUtil.getBean(BattleCommandRepository.class);
        vo.setCommandCount(battleCommandRepository.findCommandById("system.t_event", eventEntity.getId()).size());

        //是否允许删除
        Optional.ofNullable(AuthHelper.getCurrentUser()).ifPresent(user -> vo.setCanDelete(user.getId().equals(eventEntity.getCrBy())));
        return vo;
    }
}
