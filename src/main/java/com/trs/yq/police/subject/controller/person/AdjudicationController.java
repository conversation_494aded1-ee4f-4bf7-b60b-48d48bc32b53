package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.AdjudicationVO;
import com.trs.yq.police.subject.service.AdjudicationService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 裁决接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/person")
@Validated
public class AdjudicationController {

    @Resource
    private AdjudicationService adjudicationService;

    /**
     * 查询裁决信息
     * http://192.168.200.192:3001/project/4897/interface/api/129995
     *
     * @param personId 人员id
     * @return 裁决信息列表
     */
    @GetMapping("{personId}/adjudication")
    public List<AdjudicationVO> getJudgement(@PathVariable String personId) {
        return adjudicationService.getJudgement(personId);
    }


    /**
     * 新增裁决信息
     * http://192.168.200.192:3001/project/4897/interface/api/129880
     *
     * @param personId       人员id
     * @param adjudicationVO 裁决信息
     */
    @PostMapping("{personId}/adjudication")
    public void addJudgment(@PathVariable String personId, @Valid @RequestBody AdjudicationVO adjudicationVO) {
        adjudicationService.saveJudgement(personId, adjudicationVO);
    }

    /**
     * 修改裁决信息
     * http://192.168.200.192:3001/project/4897/interface/api/129990
     *
     * @param personId       人员id
     * @param adjudicationVO 裁决信息
     */
    @PutMapping("{personId}/adjudication")
    public void updateJudgment(@PathVariable String personId, @Valid @RequestBody AdjudicationVO adjudicationVO) {
        adjudicationService.updateJudgement(personId, adjudicationVO);
    }

    /**
     * 删除裁决信息
     * http://192.168.200.192:3001/project/4897/interface/api/129985
     *
     * @param personId 人员id
     * @param id       裁决信息id
     */
    @DeleteMapping("{personId}/adjudication")
    public void deleteJudgement(@PathVariable String personId, @NotBlank(message = "裁决信息id不能为空！") String id) {
        adjudicationService.deleteJudgement(personId, id);
    }


}
