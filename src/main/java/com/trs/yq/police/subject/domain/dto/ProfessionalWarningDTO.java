package com.trs.yq.police.subject.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/7/20 16:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfessionalWarningDTO {

    @JsonProperty(value = "JOBTYPE")
    private String JOBTYPE;
    @JsonProperty(value = "JOBID")
    private String JOBID;
    @JsonProperty(value = "JOBNAME")
    private String JOBNAME;
    @JsonProperty(value = "RESID")
    private String RESID;
    @JsonProperty(value = "DISTRICT")
    private String DISTRICT;
    @JsonProperty(value = "POLICE")
    private String POLICE;
    @JsonProperty(value = "DATA")
    private String DATA;

    /**
     * 专业手段预警详情
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class JzsdData {

        @JsonProperty(value = "TASKID")
        private String TASKID;
        @JsonProperty(value = "WARNING_TIME")
        private String WARNING_TIME;
        @JsonProperty(value = "TARGET")
        private String TARGET;
        @JsonProperty(value = "HOMEAREA")
        private String HOMEAREA;
        @JsonProperty(value = "MSISDN")
        private String MSISDN;
        @JsonProperty(value = "CURAREA")
        private String CURAREA;
        @JsonProperty(value = "RELATENUM")
        private String RELATENUM;
        @JsonProperty(value = "RELATEHOMEAC")
        private String RELATEHOMEAC;
        @JsonProperty(value = "LONGITUDE")
        private String LONGITUDE;
        @JsonProperty(value = "LATITUDE")
        private String LATITUDE;
        @JsonProperty(value = "UUID")
        private String UUID;
        @JsonProperty(value = "SENDTIME")
        private String SENDTIME;
        @JsonProperty(value = "RESULTDESC")
        private String RESULTDESC;
        @JsonProperty(value = "SUBALARMTYPE")
        private String SUBALARMTYPE;
    }

    /**
     * 专业手段预警反馈
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class JzsdFeedBack extends JzsdData {
        @JsonProperty(value = "info_true")
        private String info_true;
        @JsonProperty(value = "remark")
        private String remark;
    }
}
