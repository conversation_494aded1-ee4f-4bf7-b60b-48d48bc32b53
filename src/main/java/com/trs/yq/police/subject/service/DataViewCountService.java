package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.request.CountRequestVO;
import com.trs.yq.police.subject.domain.vo.*;

import java.util.List;

/**
 * 专题首页数据统计接口
 *
 * <AUTHOR>
 * @since 2021/9/14
 */
public interface DataViewCountService {

    /**
     * 获取区域失驾人员数量TOP10
     *
     * @param timeParams 时间参数
     * @return {@link AreaCountVO}
     */
    List<AreaCountVO> getCancelDriveCount(TimeParams timeParams);

    /**
     * [专题首页-交警/刑侦] 区县分布情况
     *
     * @param request {@link CountRequestVO}
     * @return 区县分布情况
     */
    List<AreaDistributeVO> getAreaDistribute(CountRequestVO request);

    /**
     * [专题首页]获取泸州市区域信息
     *
     * @return {@link AreaVO}
     */
    List<AreaVO> getAreaInfo();

    /**
     * [专题首页-反恐]获取人员变化率
     *
     * @param subjectId 专题id
     * @return {@link PersonChangeRateVO}
     */
    List<PersonChangeRateVO> getPersonChangeRateVOList(String subjectId);

    /**
     * [专题首页-反恐]获取热力图数据
     *
     * @param subjectId  专题id
     * @param areaCode   地区编码
     * @param personType 人员类别
     * @param timeParams 时间参数
     * @return {@link HeatMapVO}
     */
    List<HeatMapVO> getHeatMap(String subjectId, String areaCode, String personType, TimeParams timeParams);

    /**
     * [专题首页-禁毒] 警综涉毒案件统计(柱状图)
     *
     * @param caseType   案件类型：0（刑事案件），1（刑侦案件）
     * @param timeParams {@link TimeParams}
     * @return 区县分布情况
     */
    CountDistributeCaseVO getCaseDistrictCounty(String caseType, TimeParams timeParams);

    /**
     * [专题首页-政保] 人员区县分布查询
     *
     * @param timeParams 时间参数
     * @param subjectId  类型ID
     * @return {@link DistributePersonCountVO}
     */
    List<DistributePersonCountVO> getDistributePersonCountList(TimeParams timeParams, String subjectId);

    /**
     * [专题首页-禁毒] 警综涉毒案件统计(总数)
     *
     * @param timeParams {@link TimeParams}
     * @return 总数
     */
    List<CountTypeResponseVO> getDrugCaseTotal(TimeParams timeParams);

    /**
     * [专题首页] 涉毒前科人员
     *
     * @return 前科人员地区统计
     */
    List<CountTypeResponseVO> getDoneDrugWarningPerson();

    /**
     * 类型分布
     *
     * @param subjectId 专题id
     * @return 分布情况
     */
    PersonTypeCountVO countType(String subjectId);

    /**
     * 标签分布
     *
     * @param subjectId 专题id
     * @return 分布情况
     */
    PersonTypeCountVO countTag(String subjectId);

    /**
     * [专题首页-政保] 群体信息
     *
     * @param subjectId 专题id
     * @return 群体信息
     */
    List<GroupPersonStatisticsVO> statisticsPoliticalGroup(String subjectId);
}
