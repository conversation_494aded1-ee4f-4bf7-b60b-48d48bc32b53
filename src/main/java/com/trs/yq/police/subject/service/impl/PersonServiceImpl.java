package com.trs.yq.police.subject.service.impl;

import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_PERSON;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FK_SUBJECT;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.IMPORT_TYPE_FAILURE;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.IMPORT_TYPE_INITIAL;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.MODULE_PERSON;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.PersonListPredicatesBuilder;
import com.trs.yq.police.subject.constants.enums.ControlStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjCzryFieldEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryFieldEnum;
import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileTypeEnum;
import com.trs.yq.police.subject.constants.enums.MonitorStatusEnum;
import com.trs.yq.police.subject.constants.enums.MoveTypeEnum;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.PersonArchiveStatusEnum;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;
import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.CluePersonRelationEntity;
import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import com.trs.yq.police.subject.domain.entity.ControlEntity;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.EventPersonRelationEntity;
import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.ImportHistoryEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.MobilePhoneEntity;
import com.trs.yq.police.subject.domain.entity.MobilityEntity;
import com.trs.yq.police.subject.domain.entity.PersonArchiveRecordEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.PersonFileRelationEntity;
import com.trs.yq.police.subject.domain.entity.PersonGroupRelationEntity;
import com.trs.yq.police.subject.domain.entity.PersonImportTemplateEntity;
import com.trs.yq.police.subject.domain.entity.PersonLabelRelationEntity;
import com.trs.yq.police.subject.domain.entity.PersonSubjectRelationEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.DialogPersonListRequestVO;
import com.trs.yq.police.subject.domain.vo.DialogPersonListVO;
import com.trs.yq.police.subject.domain.vo.IdNameVO;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.domain.vo.ImportResultVO;
import com.trs.yq.police.subject.domain.vo.ImportVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.PersonArchiveVO;
import com.trs.yq.police.subject.domain.vo.PersonBasicVO;
import com.trs.yq.police.subject.domain.vo.PersonCluesRelationVO;
import com.trs.yq.police.subject.domain.vo.PersonEventsRelationVO;
import com.trs.yq.police.subject.domain.vo.PersonGroupRelationVO;
import com.trs.yq.police.subject.domain.vo.PersonLabelVO;
import com.trs.yq.police.subject.domain.vo.PersonRelatedClueVO;
import com.trs.yq.police.subject.domain.vo.PersonRelatedEventVO;
import com.trs.yq.police.subject.domain.vo.PersonRelatedGroupVO;
import com.trs.yq.police.subject.domain.vo.StorageStatusVO;
import com.trs.yq.police.subject.excel.ExcelService;
import com.trs.yq.police.subject.excel.ReadExcelListener;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.exception.SystemException;
import com.trs.yq.police.subject.operation.OperationLogServiceImpl;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.AdjudicationRepository;
import com.trs.yq.police.subject.repository.CluePersonRelationRepository;
import com.trs.yq.police.subject.repository.ClueRepository;
import com.trs.yq.police.subject.repository.CommonExtendRepository;
import com.trs.yq.police.subject.repository.ControlRepository;
import com.trs.yq.police.subject.repository.EventPersonRelationRepository;
import com.trs.yq.police.subject.repository.EventRepository;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.repository.GroupRepository;
import com.trs.yq.police.subject.repository.ImportHistoryRepository;
import com.trs.yq.police.subject.repository.ImportTemplateRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.MobilePhoneRepository;
import com.trs.yq.police.subject.repository.MobilityRepository;
import com.trs.yq.police.subject.repository.OperationLogRepository;
import com.trs.yq.police.subject.repository.PersonArchiveRecordRepository;
import com.trs.yq.police.subject.repository.PersonFileRelationRepository;
import com.trs.yq.police.subject.repository.PersonGroupRelationRepository;
import com.trs.yq.police.subject.repository.PersonLabelRelationRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.repository.PersonSubjectRelationRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.repository.VirtualIdentityRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

/**
 * 人员档案业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 14:24
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class PersonServiceImpl implements PersonService {

    @Resource
    private PersonRepository personRepository;
    @Resource
    private PersonSubjectRelationRepository personSubjectRelationRepository;
    @Resource
    private PersonGroupRelationRepository personGroupRelationRepository;
    @Resource
    private EventPersonRelationRepository eventPersonRelationRepository;
    @Resource
    private PersonLabelRelationRepository personLabelRelationRepository;
    @Resource
    private PersonFileRelationRepository personFileRelationRepository;
    @Resource
    private MobilityRepository mobilityRepository;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private ClueRepository clueRepository;
    @Resource
    private RemoteStorageService remoteStorageService;
    @Resource
    private MobilePhoneRepository mobilePhoneRepository;
    @Resource
    private PersonArchiveRecordRepository personArchiveRecordRepository;
    @Resource
    private ImportHistoryRepository importHistoryRepository;
    @Resource
    private ImportTemplateRepository importTemplateRepository;
    @Resource
    private ExcelService excelService;
    @Resource
    private AdjudicationRepository adjudicationRepository;
    @Resource
    private DictService dictService;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private OperationLogRepository operationLogRepository;
    @Resource
    private CluePersonRelationRepository cluePersonRelationRepository;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private CommonExtendRepository commonExtendRepository;
    @Resource
    private EventRepository eventRepository;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private VirtualIdentityRepository virtualIdentityRepository;
    @Resource
    private UnitRepository unitRepository;


    @Value("${com.trs.fastdfs.group_name}")
    private String groupName;
    @Resource(name = "removeExecutor")
    private Executor removeExecutor;

    private static final String GENDER_MALE = "1";
    private static final String GENDER_FEMALE = "2";

    private static final Map<String, String> BELIEFS_MAP = new HashMap<>();

    static {
        BELIEFS_MAP.put("00", "无宗教信仰");
        BELIEFS_MAP.put("10", "佛教");
        BELIEFS_MAP.put("20", "喇嘛教");
        BELIEFS_MAP.put("30", "道教");
        BELIEFS_MAP.put("40", "天主教");
        BELIEFS_MAP.put("50", "基督教");
        BELIEFS_MAP.put("60", "东正教");
        BELIEFS_MAP.put("70", "伊斯兰教");
        BELIEFS_MAP.put("99", "其他");
    }

    @Value("${com.trs.download.person.info.url}")
    private String url;

    @Value("${com.trs.download.person.info.apikey}")
    private String apikey;

    @Value("${com.trs.police.subject.district.code:5105}")
    private String districtCodePrefix;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String createPerson(PersonBasicVO vo) {
        // 排重身份证
        final PersonEntity person = Optional.ofNullable(personRepository.findByIdNumber(vo.getIdNumber()))
            .orElse(new PersonEntity());
        final String subjectId = vo.getSubjectId();
        if (StringUtils.isNotBlank(person.getIdNumber())) {
            final PersonSubjectRelationEntity relation = personSubjectRelationRepository.findByPersonIdAndSubjectId(
                person.getId(), subjectId);
            if (Objects.nonNull(relation)) {
                throw new ParamValidationException("人员信息已存在，请核实！");
            } else {
                // copy属性
                BeanUtil.copyPropertiesIgnoreNull(vo, person, PersonFieldEnum.ID.getField(),
                    PersonFieldEnum.CR_BY.getField(), PersonFieldEnum.CR_TIME.getField(),
                    PersonFieldEnum.CR_DEPT.getField(), PersonFieldEnum.CR_BY_NAME.getField(),
                    PersonFieldEnum.CR_DEPT_CODE.getField(), PersonFieldEnum.ID_NUMBER.getField(),
                    PersonFieldEnum.CONTROL_STATUS.getField());
            }
        } else {
            // copy属性
            BeanUtil.copyPropertiesIgnoreNull(vo, person);
        }
        // check control status
        person.setControlStatus(
            checkPersonControlStatus(person.getIdNumber())
                ? ControlStatusEnum.IN_CONTROL.getCode()
                : ControlStatusEnum.NOT_CONTROL.getCode());
        //新建人员默认未布控
        person.setMonitorStatus("0");
        // 存储
        personRepository.save(person);

        // 处理关联关系
        final String personId = person.getId();
        processPersonSubjectRelation(personId, subjectId);
        processPersonLabelRelation(personId, vo.getLabels());
        processPersonGroupRelation(personId, vo.getGroups());
        processPersonImageRelation(personId, vo.getImages());
        processPersonPhoneRelation(personId, vo.getContactInformation());
        processPersonMobilityRelation(personId, vo.getInTime(), vo.getInDirection());
        processControlLevel(personId, vo.getIdNumber(), subjectId, vo.getControlLevel());

        /*
          维稳专题特殊处理
         */
        if (subjectId.equals(WW_SUBJECT)) {
            CommonExtentEntity extent = commonExtendRepository.findByRecordIdAndModule(person.getId(), "person")
                .orElse(new CommonExtentEntity());
            extent.setModule("person");
            extent.setWorkUnit(vo.getWorkUnit());
            extent.setNativePlace(vo.getNativePlace());
            extent.setRecordId(person.getId());
            extent.setMainDemand(vo.getMainDemand());
            extent.setTreatment(vo.getTreatment());
            commonExtendRepository.save(extent);
        }

        final OperationLogRecord logRecord = OperationLogRecord.builder().operator(Operator.ADD)
            .module(OperateModule.ARCHIVES_MANAGE).newObj(JsonUtil.toJsonString(person))
            .currentUser(AuthHelper.getCurrentUser()).primaryKey(personId).desc("新增人员档案").build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
        return personId;
    }

    /**
     * 移除人员
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deletePerson(String personId, String subjectId) {

        checkPersonExist(personId, subjectId);

        // 移除关联
        final List<String> groups = groupRepository.findByPersonIdAndSubjectId(personId, subjectId).stream()
            .filter(Objects::nonNull).map(GroupEntity::getId).collect(Collectors.toList());
        personGroupRelationRepository.removeAllByPersonIdAndGroupIdIn(personId, groups);

        final List<String> labels = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId).stream()
            .filter(Objects::nonNull).map(LabelEntity::getId).collect(Collectors.toList());
        personLabelRelationRepository.removeAllByPersonIdAndLabelIdIn(personId, labels);

        // 查询人员是否与专题关联
        final List<PersonSubjectRelationEntity> relations = personSubjectRelationRepository.findAllByPersonId(personId)
            .stream().filter(relation -> !relation.getSubjectId().equals(subjectId)).collect(Collectors.toList());

        if (relations.isEmpty()) {
            // 关系为空 -> 移除所有关联
            personFileRelationRepository.removeByPersonId(personId);
            // 移除操作日志
            CompletableFuture.completedFuture(personId)
                .thenRunAsync((() -> operationLogRepository.removeAllByTargetObjectId(personId)), removeExecutor);
        }

        // 移除人与专题之间的关联即为删除
        personSubjectRelationRepository.removeByPersonIdAndSubjectId(personId, subjectId);
    }

    /**
     * 更新人员基本信息
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param vo        人员信息
     * @return 人员id
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String updatePersonBasicInfo(String personId, String subjectId, PersonBasicVO vo) {

        final PersonEntity person = checkPersonExist(personId, subjectId);

        final PersonBasicVO basicInfo = getBasicInfo(personId, subjectId);
        transferToRequired(basicInfo);

        BeanUtils.copyProperties(vo, person, PersonFieldEnum.ID.getField(), PersonFieldEnum.CR_BY.getField(),
            PersonFieldEnum.CR_TIME.getField(), PersonFieldEnum.CR_DEPT.getField(),
            PersonFieldEnum.CR_BY_NAME.getField(), PersonFieldEnum.CR_DEPT_CODE.getField(),
            PersonFieldEnum.ID_NUMBER.getField(), PersonFieldEnum.CONTROL_STATUS.getField());

        personRepository.save(person);

        // 处理关联关系
        updatePersonLabelRelation(personId, subjectId, vo.getLabels());
        updatePersonGroupRelation(personId, subjectId, vo.getGroups());
        processPersonPhoneRelation(personId, vo.getContactInformation());
        updateControlLevel(personId, subjectId, vo.getControlLevel());

        /*
          维稳专题特殊处理
         */
        if (subjectId.equals(WW_SUBJECT)) {
            CommonExtentEntity extent = commonExtendRepository.findByRecordIdAndModule(person.getId(), "person")
                .orElse(new CommonExtentEntity());
            extent.setModule("person");
            extent.setRecordId(person.getId());
            extent.setMainDemand(vo.getMainDemand());
            extent.setTreatment(vo.getTreatment());
            extent.setWorkUnit(vo.getWorkUnit());
            extent.setNativePlace(vo.getNativePlace());
            commonExtendRepository.save(extent);
        }

        // 发送至操作日志处理队列
        final OperationLogRecord logRecord = OperationLogRecord.builder().operator(Operator.EDIT)
            .module(OperateModule.BASIC_INFO).primaryKey(personId).oldObj(JsonUtil.toJsonString(basicInfo))
            .newObj(JsonUtil.toJsonString(vo)).desc("编辑人员基本信息").currentUser(AuthHelper.getCurrentUser())
            .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }

        return personId;
    }

    private void transferToRequired(PersonBasicVO basicInfo) {
        final List<PersonLabelVO> newLabelVOList = basicInfo.getLabels().stream().map(label -> {
            final String pushType = "0";
            if (StringUtils.equals(label.getCreateType(), pushType)) {
                PersonLabelVO labelVO = new PersonLabelVO();
                labelVO.setId(label.getId());
                labelVO.setName(label.getName());
                return labelVO;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        basicInfo.setLabels(newLabelVOList);
    }

    /**
     * 查询人员基本信息
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 人员基本信息 {@link PersonBasicVO}
     */
    @Override
    public PersonBasicVO getBasicInfo(String personId, String subjectId) {

        // 核实人员是否存在
        final PersonEntity person = checkPersonExist(personId, subjectId);

        PersonBasicVO vo = new PersonBasicVO();
        BeanUtil.copyPropertiesIgnoreNull(person, vo);
        vo.setIdType(StringUtils.isBlank(person.getIdType()) ? "1" : person.getIdType());
        // 处理关联关系 - 人员标签
        final List<LabelEntity> labels = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        vo.setLabels(labels.stream().filter(Objects::nonNull).map(label -> {
            PersonLabelVO result = new PersonLabelVO();
            BeanUtils.copyProperties(label, result);
            return result;
        }).collect(Collectors.toList()));
        // 处理关联关系 - 人员群组
        final List<LabelEntity> groupTypes = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        vo.setGroups(groupTypes.stream().map(type -> new IdNameVO(type.getId(), type.getName())).distinct()
            .collect(Collectors.toList()));
        //管控级别
        controlRepository.findByPersonIdAndSubjectId(personId, subjectId)
            .ifPresent(controlEntity -> vo.setControlLevel(controlEntity.getControlLevel()));

        // 图片
        final List<ImageVO> images = fileStorageRepository.findAllByPersonIdAndModule(personId,
                FileTypeEnum.IMAGE.getCode(), FileModuleEnum.BASIC_INFO_PHOTO.getCode(), null).stream()
            .filter(Objects::nonNull).map(ImageVO::of).collect(Collectors.toList());

        if (images.isEmpty()) {
            ImageVO imageVO = new ImageVO();
            imageVO.setImageId(UUID.randomUUID().toString().replace("-", ""));
            imageVO.setUrl("/dispatchWarning/getPhoto/" + person.getIdNumber());
            images.add(imageVO);
        }

        vo.setImages(images);

        /*
          维稳专题特殊处理
         */
        if (WW_SUBJECT.equals(subjectId)) {
            vo.setBirthday(StringUtil.parseBirthdayFromIdNumber(person.getIdNumber()));
            Optional<CommonExtentEntity> extentEntity = commonExtendRepository.findByRecordIdAndModule(personId,
                CommonExtentEntity.PERSON);
            extentEntity.ifPresent(e -> {
                vo.setNativePlace(e.getNativePlace());
                vo.setTreatment(e.getTreatment());
                vo.setMainDemand(e.getMainDemand());
                vo.setWorkUnit(e.getWorkUnit());
            });
        }

        return vo;
    }

    /**
     * 根据身份证号码查询人员基本信息
     *
     * @param idNumber 身份证号码
     * @return 人员基本信息 {@link PersonBasicVO}
     */
    @Override
    public PersonBasicVO getBasicInfoByIdNumber(String idNumber) {
        final PersonEntity person = personRepository.findByIdNumber(idNumber);

        if (Objects.isNull(person)) {
            throw new ParamValidationException("人员信息库暂无对应人员，请核实！");
        }

        PersonBasicVO vo = new PersonBasicVO();
        BeanUtil.copyPropertiesIgnoreNull(person, vo);
        vo.setPersonId(person.getId());
        // 图片
        final List<ImageVO> images = fileStorageRepository.findAllByPersonIdAndModule(person.getId(),
                FileTypeEnum.IMAGE.getCode(), FileModuleEnum.BASIC_INFO_PHOTO.getCode(), null).stream()
            .filter(Objects::nonNull).map(ImageVO::of).collect(Collectors.toList());

        vo.setImages(images);

        return vo;
    }

    /**
     * 批量导入人员信息
     *
     * @param importVO 导入参数
     * @return 导入结果 {@link ImportResultVO}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ImportResultVO importPerson(ImportVO importVO) {

        final MultipartFile excel = importVO.getExcel();
        final String repeatStrategy = importVO.getRepeatStrategy();
        final String subjectId = importVO.getSubjectId();
        final String personType = importVO.getPersonType();

        // 批量导入人员基本信息
        final String filename = excel.getOriginalFilename();
        final String extension = FilenameUtils.getExtension(filename);
        final String xlsx = "xlsx";
        if (StringUtils.isBlank(filename) || StringUtils.isBlank(extension) || !xlsx.equals(extension)) {
            throw new ParamValidationException("暂只支持xlsx文件，请重新上传，谢谢！");
        }
        ReadExcelListener listener = ReadExcelListener.builder().personRepository(personRepository)
            .personSubjectRelationRepository(personSubjectRelationRepository)
            .adjudicationRepository(adjudicationRepository).personLabelRelationRepository(personLabelRelationRepository)
            .labelRepository(labelRepository).dictService(dictService).mobilePhoneRepository(mobilePhoneRepository)
            .commonExtendRepository(commonExtendRepository).virtualIdentityRepository(virtualIdentityRepository)
            .controlRepository(controlRepository).unitRepository(unitRepository)
            .currentUser(AuthHelper.getCurrentUser()).operationLogHandler(operationLogHandler).personLabel(personType)
            .subjectId(subjectId).repeatStrategy(repeatStrategy).build();
        try (InputStream inputStream = excel.getInputStream()) {
            EasyExcelFactory.read(inputStream, listener).sheet().headRowNumber(2).doRead();
            // 记录历史信息
            String initialId = recordInitial(subjectId, filename, excel);
            // 记录失败
            recordResult(initialId, listener.getFailRows(), listener.getColumnMap());
            return new ImportResultVO(initialId, listener.getFailResult());
        } catch (IOException e) {
            throw new SystemException(
                String.format("import person fail! subjectId = %s, fileName = %s", subjectId, filename), e);
        }
    }

    /**
     * 记录失败
     *
     * @param initialId 文件id
     * @param rows      row
     * @param columnMap columnMap
     * @throws IOException ioException
     */
    public void recordResult(String initialId, List<Map<Integer, String>> rows,
        Map<Integer, PersonFieldEnum> columnMap) throws IOException {

        final ImportHistoryEntity initial = importHistoryRepository.findById(initialId).orElse(null);

        if (Objects.nonNull(initial) && !rows.isEmpty()) {
            final String extensionName = initial.getExtensionName();
            final String fileName = initial.getFileName();
            final String[] paths = excelService.writeToTempFile(groupName, extensionName, rows, columnMap);
            ImportHistoryEntity entity = new ImportHistoryEntity();
            entity.setPath(paths[1]);
            entity.setFileName(fileName.replace("." + extensionName, "-fail." + extensionName));
            entity.setExtensionName(extensionName);
            entity.setType(IMPORT_TYPE_FAILURE);
            entity.setSubjectId(initial.getSubjectId());
            entity.setInitialHistoryId(initialId);
            importHistoryRepository.save(entity);
        }
    }


    /**
     * 记录失败
     *
     * @param initialId 文件id
     * @param rows      row
     * @param columnMap columnMap
     * @throws IOException ioException
     */
    public void recordCrjResult(String initialId, List<Map<Integer, String>> rows,
        Map<Integer, CrjJwryFieldEnum> columnMap) throws IOException {

        final ImportHistoryEntity initial = importHistoryRepository.findById(initialId).orElse(null);

        if (Objects.nonNull(initial) && !rows.isEmpty()) {
            final String extensionName = initial.getExtensionName();
            final String fileName = initial.getFileName();
            final String[] paths = excelService.writeCrjToTempFile(groupName, extensionName, rows, columnMap);
            ImportHistoryEntity entity = new ImportHistoryEntity();
            entity.setPath(paths[1]);
            entity.setFileName(fileName.replace("." + extensionName, "-fail." + extensionName));
            entity.setExtensionName(extensionName);
            entity.setType(IMPORT_TYPE_FAILURE);
            entity.setSubjectId(initial.getSubjectId());
            entity.setInitialHistoryId(initialId);
            importHistoryRepository.save(entity);
        }
    }

    /**
     * 记录失败
     *
     * @param initialId 文件id
     * @param rows      row
     * @param columnMap columnMap
     * @throws IOException ioException
     */
    public void recordCrjCzryResult(String initialId, List<Map<Integer, String>> rows,
        Map<Integer, CrjCzryFieldEnum> columnMap) throws IOException {

        final ImportHistoryEntity initial = importHistoryRepository.findById(initialId).orElse(null);

        if (Objects.nonNull(initial) && !rows.isEmpty()) {
            final String extensionName = initial.getExtensionName();
            final String fileName = initial.getFileName();
            final String[] paths = excelService.writeCrjCzryToTempFile(groupName, extensionName, rows, columnMap);
            ImportHistoryEntity entity = new ImportHistoryEntity();
            entity.setPath(paths[1]);
            entity.setFileName(fileName.replace("." + extensionName, "-fail." + extensionName));
            entity.setExtensionName(extensionName);
            entity.setType(IMPORT_TYPE_FAILURE);
            entity.setSubjectId(initial.getSubjectId());
            entity.setInitialHistoryId(initialId);
            importHistoryRepository.save(entity);
        }
    }

    /**
     * @param subjectId subjectId
     * @param filename  filename
     * @param excel     excel
     * @return String
     * @throws IOException IOException
     */
    public String recordInitial(String subjectId, String filename, MultipartFile excel) throws IOException {

        final String extension = FilenameUtils.getExtension(filename);
        final String[] paths = remoteStorageService.uploadFile(excel.getBytes(), groupName, extension);
        ImportHistoryEntity history = new ImportHistoryEntity();
        history.setPath(paths[1]);
        history.setFileName(filename);
        history.setExtensionName(extension);
        history.setType(IMPORT_TYPE_INITIAL);
        history.setSubjectId(subjectId);
        importHistoryRepository.save(history);
        return history.getId();
    }

    /**
     * 下载批量导入模板
     *
     * @param subjectId  专题编号
     * @param personType 人员类别
     * @return 模板文件二进制流
     */
    @Override
    public ResponseEntity<ByteArrayResource> downloadImportTemplate(String subjectId, String personType)
        throws UnsupportedEncodingException {

        final PersonImportTemplateEntity template = importTemplateRepository.findTemplateBySubjectIdAndPersonType(
            subjectId, personType);

        if (Objects.isNull(template)) {
            throw new ParamValidationException("主题不存在，请核实！");
        }

        final ByteArrayResource resource = new ByteArrayResource(
            remoteStorageService.downloadFile(groupName, template.getPath()));
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION,
            "attachment;filename=" + URLEncoder.encode(template.getFileName(), StandardCharsets.UTF_8.name()));
        headers.add(HttpHeaders.PRAGMA, "no-cache");
        headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");

        return ResponseEntity.ok().headers(headers).contentLength(resource.contentLength())
            .contentType(MediaType.APPLICATION_OCTET_STREAM).body(resource);
    }

    /**
     * 下载批量导入失败文件
     *
     * @param subjectId 专题id
     * @param initialId 原始文件id
     * @return 返回
     */
    @Override
    public ResponseEntity<ByteArrayResource> downloadImportFailure(String subjectId, String initialId)
        throws UnsupportedEncodingException {

        final String fail = "2";
        final ImportHistoryEntity history = importHistoryRepository.findBySubjectIdAndInitialHistoryIdAndType(subjectId,
            initialId, fail);

        if (Objects.isNull(history)) {
            throw new ParamValidationException("暂无此失败记录，请核实！");
        }

        final ByteArrayResource resource = new ByteArrayResource(
            remoteStorageService.downloadFile(groupName, history.getPath()));
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION,
            "attachment;filename=" + URLEncoder.encode(history.getFileName(), StandardCharsets.UTF_8.name()));
        headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
        headers.add(HttpHeaders.PRAGMA, "no-cache");

        return ResponseEntity.ok().headers(headers).contentLength(resource.contentLength())
            .contentType(MediaType.APPLICATION_OCTET_STREAM).body(resource);
    }

    /**
     * 查询人员类别
     *
     * @param subjectId 专题id
     * @return 人员类型
     */
    @Override
    public List<IdNameVO> getTypes(String subjectId) {
        return labelRepository.findAllByModuleAndSubjectId(MODULE_PERSON, subjectId).stream()
            .map(type -> new IdNameVO(type.getId(), type.getName())).collect(Collectors.toList());
    }

    /**
     * 查询人员列表
     *
     * @param subjectId 专题id
     * @param params    查询条件
     * @return 分页查询结果 {@link PersonEntity}
     */
    @Override
    public PageResult<PersonEntity> getGroupPersonList(@NotBlank(message = "专题主键不可为空！") String subjectId,
        ListRequestVO params) {
        // 组装查询条件
        Specification<PersonEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = PersonListPredicatesBuilder.buildListFilterPredicates(subjectId,
                params.getFilterParams(), root, criteriaBuilder);
            if (Objects.nonNull(params.getSearchParams())) {
                predicates.addAll(
                    PersonListPredicatesBuilder.buildSearchPredicates(params.getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        //排序
        Sort sort = Objects.isNull(params.getSortParams()) ? Sort.by(Sort.Direction.DESC, "upTime")
            : params.getSortParams().toSort();
        Pageable pageable = params.getPageParams().toPageable(sort);

        Page<PersonEntity> results = personRepository.findAll(specification, pageable);
        return PageResult.of(results);
    }

    /**
     * 更新人员基本信息图片
     *
     * @param personId 人员id
     * @param images   图片 {@link ImageVO}
     */
    @Override
    public void updateBaseImages(String personId, List<ImageVO> images) {
        remoteStorageService.updatePersonImageRelations(personId, images, FileModuleEnum.BASIC_INFO_PHOTO, null);
    }

    /**
     * 查询人员存在状态
     *
     * @param idNumber 身份证号
     * @return 人员存在状态 {@link StorageStatusVO}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public StorageStatusVO getPersonStorageStatus(String idNumber) {
        final PersonEntity person = personRepository.findByIdNumber(idNumber);
        String personId;
        if (Objects.isNull(person)) {
            //从外部接口同步人员信息
            personId = updateSinglePersonInfo(idNumber);
            if (StringUtils.isBlank(personId)) {
                return new StorageStatusVO();
            }
        } else {
            personId = person.getId();
        }

        List<PersonSubjectRelationEntity> all = personSubjectRelationRepository.findAllByPersonId(personId);

        if (all.isEmpty()) {
            return new StorageStatusVO(personId);
        }

        List<String> subjectIds = all.stream().map(PersonSubjectRelationEntity::getSubjectId)
            .collect(Collectors.toList());
        return new StorageStatusVO(person.getId(), subjectIds);
    }

    /**
     * 同步一条人员信息
     *
     * @param idNumber 身份证号
     * @return 同步结果
     */
    private String updateSinglePersonInfo(String idNumber) {
        log.info("begin downloading person info : " + idNumber);
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url).queryParam("gmsfhm", idNumber)
            .queryParam("apikey", apikey).build();
        String jsonText = null;
        try {
            jsonText = restTemplate.getForObject(uriComponents.toUriString(), String.class);
        } catch (Exception e) {
            log.error("请求外部接口失败！", e);
        }

        if (StringUtils.isBlank(jsonText)) {
            log.error("response empty!");
            return null;
        }
        log.info("finish downloading person info : \n");
        JsonNode jsonNode = JsonUtil.parseJsonNode(jsonText);
        if (Objects.isNull(jsonNode)) {
            log.error("parse json failed!\n" + jsonText);
            return null;
        }

        if (!jsonNode.get("data").elements().hasNext()) {
            log.error("data is empty!");
            return null;
        }

        JsonNode vo = jsonNode.get("data").elements().next();
        //更新基本信息
        PersonEntity personEntity = new PersonEntity();
        personEntity.setIdNumber(vo.get("GMSFHM").asText());
        personEntity.setName(vo.get("XM").asText());
        personEntity.setNation(vo.get("MZ").asText());
        personEntity.setFormerName(vo.get("CYM").asText().replace("null", ""));
        String xb = vo.get("XB").asText();
        if (GENDER_FEMALE.equals(xb)) {
            personEntity.setGender("1");
        } else if (GENDER_MALE.equals(xb)) {
            personEntity.setGender("0");
        }
        personEntity.setReligiousBelief(BELIEFS_MAP.get(vo.get("ZJXY").asText()));
        String ssxq = vo.get("SSXQ").asText();
        if (ssxq.matches("\\d{6}")) {
            personEntity.setAreaCode(ssxq);
        }
        personEntity.setRegisteredResidence(vo.get("ZZ").asText());
        personEntity.setControlStatus(ControlStatusEnum.IN_CONTROL.getCode());
        personRepository.save(personEntity);
        String personId = personEntity.getId();

        //存储照片
        String xp = vo.get("XP").asText();
        if (!StringUtils.isNotBlank(xp)) {
            log.info("no photo data to update");
            return personId;
        }

        List<PersonFileRelationEntity> relations = personFileRelationRepository.findAllByPersonId(personEntity.getId());
        if (relations.stream()
            .anyMatch(relation -> relation.getModule().equals(FileModuleEnum.BASIC_INFO_PHOTO.getCode()))) {
            log.info("photo existed, no need to update");
            return personId;
        }

        try {
            log.info("begin to update photo");
            byte[] imageData = Base64.getDecoder().decode(xp);
            ImageVO imageVO = remoteStorageService.uploadImage(imageData, idNumber + ".jpg");
            remoteStorageService.savePersonImageRelations(personEntity.getId(), Collections.singletonList(imageVO),
                FileModuleEnum.BASIC_INFO_PHOTO, null);
            log.info("photo update finished");
        } catch (IOException exception) {
            log.error("upload image failed! ", exception);
        }
        return personId;
    }

    /**
     * 查询人员的归档状态
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 人员归档状态 {@link PersonArchiveStatusEnum}
     */
    @Override
    public PersonArchiveStatusEnum getPersonArchiveStatus(String personId, String subjectId) {
        List<PersonArchiveRecordEntity> entities = personArchiveRecordRepository.findByPersonIdAndSubjectId(personId,
            subjectId);
        PersonArchiveRecordEntity latest = entities.stream()
            .max(Comparator.comparing(PersonArchiveRecordEntity::getUpTime, LocalDateTime::compareTo)).orElse(null);
        if (Objects.isNull(latest)) {
            return PersonArchiveStatusEnum.ACTIVE;
        }
        return PersonArchiveStatusEnum.codeOf(latest.getArchiveStatus());
    }

    /**
     * 人员归档
     *
     * @param personId        人员id
     * @param subjectId       专题id
     * @param personArchiveVO vo
     */
    @Override
    public void archivePerson(String personId, String subjectId, PersonArchiveVO personArchiveVO) {
        PersonArchiveRecordEntity entity = new PersonArchiveRecordEntity();
        entity.setPersonId(personId);
        entity.setSubjectId(subjectId);
        entity.setArchiveStatus(PersonArchiveStatusEnum.ARCHIVE.getCode());
        entity.setOutTime(personArchiveVO.getOutTime());
        entity.setOutDestination(personArchiveVO.getOutDestination());
        personArchiveRecordRepository.save(entity);

        PersonEntity person = personRepository.getById(personId);
        person.setControlStatus(ControlStatusEnum.IN_ARCHIVE.getCode());
        personRepository.save(person);

        //如果是反恐专题，需要顺便更新流动信息
        if (subjectId.equals(FK_SUBJECT)) {
            MobilityEntity mobilityEntity = new MobilityEntity();
            mobilityEntity.setPersonId(personId);
            mobilityEntity.setLocation(personArchiveVO.getOutDestination());
            mobilityEntity.setMoveType(MoveTypeEnum.OUT.getCode());
            mobilityEntity.setMoveTime(personArchiveVO.getOutTime());
            mobilityEntity.setNote("归档操作自动创建");
            mobilityRepository.save(mobilityEntity);
        }
    }

    /**
     * 人员激活
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    @Override
    public void activePerson(String personId, String subjectId) {
        PersonArchiveRecordEntity entity = new PersonArchiveRecordEntity();
        entity.setPersonId(personId);
        entity.setSubjectId(subjectId);
        entity.setArchiveStatus(PersonArchiveStatusEnum.ACTIVE.getCode());
        personArchiveRecordRepository.save(entity);

        PersonEntity person = personRepository.getById(personId);
        //如果已布控
        if (checkPersonControlStatus(person.getIdNumber())) {
            person.setMonitorStatus(MonitorStatusEnum.IN_MONITOR.getCode());
        }
        //如果未布控就列控
        else {
            person.setControlStatus(ControlStatusEnum.IN_CONTROL.getCode());
        }
        personRepository.save(person);

        //如果是反恐专题，需要顺便更新流动信息
        if (subjectId.equals(FK_SUBJECT)) {
            MobilityEntity mobilityEntity = new MobilityEntity();
            mobilityEntity.setPersonId(personId);
            mobilityEntity.setMoveType(MoveTypeEnum.IN.getCode());
            mobilityEntity.setMoveTime(LocalDateTime.now());
            mobilityEntity.setNote("激活操作自动创建");
            mobilityRepository.save(mobilityEntity);
        }
    }

    @Override
    public PageResult<PersonRelatedGroupVO> getPersonRelatedGroupList(String personId, String subjectId,
        PageParams pageParams) {
        Page<PersonGroupRelationEntity> relations = personGroupRelationRepository.findAllByPersonIdAndSubjectId(
            personId, subjectId, pageParams.toPageable());
        List<PersonRelatedGroupVO> items = relations.getContent().stream().map(relation -> {
            GroupEntity group = groupRepository.getById(relation.getGroupId());
            PersonRelatedGroupVO vo = new PersonRelatedGroupVO();
            vo.setGroupId(group.getId());
            vo.setGroupName(group.getName());
            vo.setRelationId(relation.getId());
            vo.setActivityLevel(relation.getActivityLevel());
            return vo;
        }).collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), relations.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public List<PersonRelatedGroupVO> getPersonRelatedGroupList(String personId, String subjectId) {
        List<GroupEntity> groups = groupRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        return groups.stream().map(group -> PersonRelatedGroupVO.of(personId, group)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePersonGroupsRelation(PersonGroupRelationVO vo) {
        final String personId = vo.getPersonId();
        final String subjectId = vo.getSubjectId();
        final List<String> newRelatedGroupIds = vo.getGroupIds();
        final List<String> oldRelatedGroupIds =
            personGroupRelationRepository.findAllByPersonIdAndSubjectId(personId, subjectId).stream()
                .map(PersonGroupRelationEntity::getGroupId)
                .collect(Collectors.toList());

        //旧值和新值的并集
        final List<String> allRelatedGroupIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedGroupIds,
            newRelatedGroupIds);

        //存储操作记录
        allRelatedGroupIds.forEach(groupId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.RELATE)
                .module(OperateModule.RELATE_TO_GROUP)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId).desc("修改人员-群体关联")
                .targetObjectType(OPERATION_LOG_TARGET_PERSON)
                .build();
            if (oldRelatedGroupIds.contains(groupId)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", groupId)));
            }
            if (newRelatedGroupIds.contains(groupId)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", groupId)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });

        //处理人员-群体关联关系变更
        newRelatedGroupIds.removeAll(oldRelatedGroupIds);
        List<PersonGroupRelationEntity> relations = newRelatedGroupIds.stream()
            .map(groupId -> new PersonGroupRelationEntity(personId, groupId))
            .collect(Collectors.toList());
        personGroupRelationRepository.saveAll(relations);
        oldRelatedGroupIds.removeAll(vo.getGroupIds());
        personGroupRelationRepository.deleteByIdIn(oldRelatedGroupIds);
    }

    @Override
    public PageResult<DialogPersonListVO> getDialogPersonListVOPageResult(DialogPersonListRequestVO request) {
        String subjectId = request.getOtherParams().getSubjectId();
        String personTypeId = request.getOtherParams().getPersonTypeId();
        String createDeptId = request.getOtherParams().getCreateDeptId();
        String searchValue =
            Objects.isNull(request.getSearchParams()) ? null : request.getSearchParams().getSearchValue();
        Pageable pageable = request.getPageParams().toPageable();
        Page<PersonEntity> personList = personRepository.findAllBySubjectId(subjectId, personTypeId,
            StringUtil.getPoliceStationPrefix(createDeptId), searchValue, pageable);
        List<DialogPersonListVO> items = personList.stream().map(person -> DialogPersonListVO.of(person, subjectId))
            .collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), personList.getTotalElements(),
            request.getPageParams().getPageSize());
    }

    /**
     * 校验人员是否存在
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 如果存在则返回 人物实体，如果不存在，则抛出异常 {@see ParamValidationException}
     */
    @Override
    public PersonEntity checkPersonExist(String personId, String subjectId) {

        final PersonEntity person = personRepository.findById(personId).orElse(null);

        final PersonSubjectRelationEntity relation = personSubjectRelationRepository.findByPersonIdAndSubjectId(
            personId, subjectId);

        if (Objects.isNull(person) || Objects.isNull(relation)) {
            throw new ParamValidationException("人员不存在，请核实!");
        }
        return person;
    }

    /**
     * 校验人员是否存在
     *
     * @param personId 人员id
     * @return 如果存在则返回 人物实体，如果不存在，则抛出异常 {@see ParamValidationException}
     */
    @Override
    public PersonEntity checkPersonExist(String personId) {

        final PersonEntity person = personRepository.findById(personId).orElse(null);

        if (Objects.isNull(person)) {
            throw new ParamValidationException("人员不存在，请核实!");
        }
        return person;
    }

    /**
     * 关联人员与群体
     *
     * @param personId 人员id
     * @param groups   群体id列表
     */
    private void processPersonGroupRelation(String personId, List<IdNameVO> groups) {

        if (Objects.isNull(groups) || groups.isEmpty()) {
            return;
        }

        final List<PersonGroupRelationEntity> relations = groups.stream().filter(Objects::nonNull).map(group -> {
            PersonGroupRelationEntity relation = new PersonGroupRelationEntity();
            relation.setPersonId(personId);
            relation.setGroupId(group.getId());
            return relation;
        }).collect(Collectors.toList());
        personGroupRelationRepository.saveAll(relations);
    }

    /**
     * 更新人员群体关系
     *
     * @param personId  人员id
     * @param subjectId 主题id
     * @param groups    群体
     */
    private void updatePersonGroupRelation(String personId, String subjectId, List<IdNameVO> groups) {

        if (Objects.isNull(groups)) {
            return;
        }

        // 移除相关
        final List<String> groupIds = groupRepository.findByPersonIdAndSubjectId(personId, subjectId).stream()
            .filter(Objects::nonNull).map(GroupEntity::getId).collect(Collectors.toList());
        personGroupRelationRepository.removeAllByPersonIdAndGroupIdIn(personId, groupIds);
        // 新增更新数据
        processPersonGroupRelation(personId, groups);
    }

    /**
     * 关联人员与标签
     *
     * @param personId 人员id
     * @param labels   标签id列表
     */
    private void processPersonLabelRelation(String personId, List<PersonLabelVO> labels) {

        if (Objects.isNull(labels) || labels.isEmpty()) {
            return;
        }

        final List<PersonLabelRelationEntity> relations = labels.stream().filter(Objects::nonNull).map(label -> {
            PersonLabelRelationEntity relation = new PersonLabelRelationEntity();
            relation.setPersonId(personId);
            relation.setLabelId(label.getId());
            return relation;
        }).collect(Collectors.toList());
        personLabelRelationRepository.saveAll(relations);
    }


    /**
     * 更新人员与标签的绑定关系
     *
     * @param personId  人员id
     * @param subjectId 主题id
     * @param labels    标签id列表
     */
    private void updatePersonLabelRelation(String personId, String subjectId, List<PersonLabelVO> labels) {

        if (Objects.isNull(labels)) {
            return;
        }

        final List<String> labelIds = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId).stream()
            .filter(label -> {
                final String personCreate = "0";
                return Objects.nonNull(label) && StringUtils.equals(personCreate, label.getCreateType());
            }).map(LabelEntity::getId).collect(Collectors.toList());
        personLabelRelationRepository.removeAllByPersonIdAndLabelIdIn(personId, labelIds);
        processPersonLabelRelation(personId, labels);
    }

    /**
     * 关联人员与专题
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    private void processPersonSubjectRelation(String personId, String subjectId) {
        PersonSubjectRelationEntity relation = new PersonSubjectRelationEntity();
        relation.setPersonId(personId);
        relation.setSubjectId(subjectId);
        personSubjectRelationRepository.save(relation);
    }

    /**
     * 处理人员联系方式
     *
     * @param personId           人员id
     * @param contactInformation 联系方式
     */
    private void processPersonPhoneRelation(String personId, String contactInformation) {
        // 有数据才去联动更新和新增
        if (StringUtils.isNotBlank(contactInformation)) {
            MobilePhoneEntity mobilePhone = mobilePhoneRepository.findByPersonIdAndPhoneNumber(personId,
                contactInformation);

            if (Objects.nonNull(mobilePhone)) {
                // 如果存在，则更新状态
                mobilePhone.setPhoneUseStatus("1");
                mobilePhone.setPhoneStatus("1");
            } else {
                // 没有就新增
                MobilePhoneEntity phone = new MobilePhoneEntity();
                phone.setPersonId(personId);
                phone.setPhoneUseStatus("1");
                phone.setPhoneStatus("1");
                phone.setPhoneNumber(contactInformation);
                mobilePhoneRepository.save(phone);
            }
        }
    }

    /**
     * 关联人员与照片
     *
     * @param personId 人员id
     * @param images   图片id列表
     */
    private void processPersonImageRelation(String personId, List<ImageVO> images) {
        if (Objects.isNull(images) || images.isEmpty()) {
            return;
        }
        remoteStorageService.savePersonImageRelations(personId, images, FileModuleEnum.BASIC_INFO_PHOTO, null);
    }

    /**
     * 关联人员流动信息
     *
     * @param personId    人员id
     * @param inTime      流入时间
     * @param inDirection 来自省市
     */
    private void processPersonMobilityRelation(String personId, LocalDateTime inTime, String inDirection) {
        if (Objects.nonNull(inTime)) {
            MobilityEntity movement = new MobilityEntity();
            movement.setPersonId(personId);
            movement.setMoveTime(inTime);
            movement.setLocation(inDirection);
            movement.setMoveType("1");
            mobilityRepository.save(movement);
        }
    }

    /**
     * 处理管控级别
     *
     * @param personId     人员id
     * @param subjectId    专题id
     * @param controlLevel 管控级别
     */
    private void processControlLevel(String personId, String idNumber, String subjectId, String controlLevel) {

        ControlEntity control = controlRepository.findByPersonIdAndSubjectId(personId, subjectId)
            .orElse(new ControlEntity());
        control.setPersonId(personId);
        control.setSubjectId(subjectId);
        control.setControlLevel(StringUtils.isNotBlank(controlLevel) ? controlLevel : "1");
        if (idNumber.startsWith(districtCodePrefix)) {
            UnitEntity unit = unitRepository.findByUnitCode(idNumber.substring(0, 6) + "000000");
            if (Objects.nonNull(unit)) {
                control.setPoliceStationCode(unit.getUnitCode());
                control.setPoliceStationName(unit.getShortname());
            }
        }
        controlRepository.save(control);
    }

    /**
     * 更新管控级别
     *
     * @param personId     人员id
     * @param subjectId    专题id
     * @param controlLevel 管控级别
     */
    private void updateControlLevel(String personId, String subjectId, String controlLevel) {
        ControlEntity control = controlRepository.findByPersonIdAndSubjectId(personId, subjectId).orElse(null);
        if (Objects.isNull(control)) {
            control = new ControlEntity();
            control.setPersonId(personId);
            control.setSubjectId(subjectId);
        }
        control.setControlLevel(controlLevel);
        controlRepository.save(control);
    }

    /**
     * check person control status
     *
     * @param idNumber identity number
     * @return true - person under control / false - person haven't control
     */
    private boolean checkPersonControlStatus(String idNumber) {
        return (personRepository.checkPersonIsEscapedCriminal(idNumber) > 0) || (
            personRepository.checkPersonIsUnderNormalizeControl(idNumber) > 0) || (
            personRepository.checkPersonIsUnderTempControl(idNumber) > 0);
    }

    @Override
    public ImageVO getPhotoFromPerson(PersonEntity person) {
        List<FileStorageEntity> files = fileStorageRepository.findAllByPersonIdAndModule(person.getId(),
            FileTypeEnum.IMAGE.getCode(), FileModuleEnum.BASIC_INFO_PHOTO.getCode(), null);
        List<ImageVO> i = files.stream().filter(Objects::nonNull).map(ImageVO::of).collect(Collectors.toList());
        if (!i.isEmpty()) {
            return (i.get(0));
        }
        return new ImageVO();
    }

    @Override
    public PageResult<PersonRelatedClueVO> getPersonRelatedClueList(String personId, String subjectId,
        PageParams pageParams) {
        Page<ClueEntity> clueEntities = clueRepository.findAllByPersonIdAndSubjectId(personId, subjectId,
            pageParams.toPageable());
        List<PersonRelatedClueVO> items = clueEntities.stream()
            .map(clueEntity -> PersonRelatedClueVO.of(clueEntity, personId, subjectId)).collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), clueEntities.getTotalElements(),
            pageParams.getPageSize());
    }

    @Override
    public List<PersonRelatedClueVO> getPersonRelatedClueList(String personId, String subjectId) {
        List<ClueEntity> clues = clueRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        return clues.stream().map(clue -> PersonRelatedClueVO.of(clue, personId, subjectId))
            .collect(Collectors.toList());
    }

    @Override
    public PageResult<PersonRelatedEventVO> getPersonRelatedEventList(String personId, String subjectId,
        PageParams pageParams) {
        Page<EventEntity> eventEntities = eventRepository.findAllByPersonIdAndSubjectId(personId, subjectId,
            pageParams.toPageable());
        List<PersonRelatedEventVO> items = eventEntities.stream()
            .map(event -> new PersonRelatedEventVO(event, personId)).collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), eventEntities.getTotalElements(),
            pageParams.getPageSize());
    }

    @Override
    public List<PersonRelatedEventVO> getPersonRelatedEventList(String personId, String subjectId) {
        List<EventEntity> events = eventRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        return events.stream().map(event -> new PersonRelatedEventVO(event, personId)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePersonEventsRelation(PersonEventsRelationVO vo) {
        final String personId = vo.getPersonId();
        final List<String> newRelatedEventIds = vo.getEventIds();
        final List<String> oldRelatedEventIds = eventPersonRelationRepository.findAllByPersonId(personId).stream()
            .map(EventPersonRelationEntity::getEventId).collect(Collectors.toList());

        //旧值和新值的并集
        List<String> allRelations = OperationLogServiceImpl.getModifiedRelations(oldRelatedEventIds,
            newRelatedEventIds);

        //处理群体-人员关联关系变更
        eventPersonRelationRepository.deleteAllByPersonId(personId);
        final List<EventPersonRelationEntity> eventPersonRelationEntities = newRelatedEventIds.stream()
            .map(eventId -> new EventPersonRelationEntity(eventId, personId)).collect(Collectors.toList());
        eventPersonRelationRepository.saveAll(eventPersonRelationEntities);

        //存储操作记录
        allRelations.forEach(id -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder().operator(Operator.RELATE)
                .module(OperateModule.PERSON_RELATED_EVENT).currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId).desc("修改人员-事件关联").targetObjectType(OPERATION_LOG_TARGET_PERSON).build();

            if (oldRelatedEventIds.contains(id)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("eventId", id)));
            }
            if (newRelatedEventIds.contains(id)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("eventId", id)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePersonCluesRelation(PersonCluesRelationVO vo) {
        final String personId = vo.getPersonId();
        final List<String> newRelatedClueIds = vo.getClueIds();
        final List<String> oldRelatedClueIds = cluePersonRelationRepository.findAllByPersonId(personId).stream()
            .map(CluePersonRelationEntity::getClueId).collect(Collectors.toList());

        //旧值和新值的并集
        List<String> allRelations = OperationLogServiceImpl.getModifiedRelations(oldRelatedClueIds, newRelatedClueIds);

        //处理群体-人员关联关系变更
        cluePersonRelationRepository.deleteAllByPersonId(personId);
        final List<CluePersonRelationEntity> cluePersonRelationEntities = newRelatedClueIds.stream()
            .map(clueId -> new CluePersonRelationEntity(clueId, personId)).collect(Collectors.toList());
        cluePersonRelationRepository.saveAll(cluePersonRelationEntities);

        //存储操作记录
        allRelations.forEach(id -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder().operator(Operator.RELATE)
                .module(OperateModule.PERSON_RELATED_CLUE).currentUser(AuthHelper.getCurrentUser()).primaryKey(personId)
                .desc("修改人员-线索关联").targetObjectType(OPERATION_LOG_TARGET_PERSON).build();

            if (oldRelatedClueIds.contains(id)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("clueId", id)));
            }
            if (newRelatedClueIds.contains(id)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("clueId", id)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void batchDeletePersons(String subjectId, List<String> personIds) {
        personIds.forEach(id -> deletePerson(id, subjectId));
    }

    @Override
    public void controlPerson(String personId, String subjectId) {
        PersonEntity person = personRepository.getById(personId);
        person.setControlStatus(ControlStatusEnum.IN_CONTROL.getCode());
        personRepository.save(person);
    }

    @Override
    public void cancelControlPerson(String personId, String subjectId) {
        PersonEntity person = personRepository.getById(personId);
        person.setControlStatus(ControlStatusEnum.NOT_CONTROL.getCode());
        personRepository.save(person);
    }
}
