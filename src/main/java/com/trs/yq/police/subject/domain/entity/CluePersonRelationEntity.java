package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 线索人员关系表
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CLUE_PERSON_RELATION")
public class CluePersonRelationEntity extends BaseEntity {

    private static final long serialVersionUID = -6461871583855375053L;
    /**
     * 线索ID
     */
    private String clueId;
    /**
     * 人员ID
     */
    private String personId;
}
