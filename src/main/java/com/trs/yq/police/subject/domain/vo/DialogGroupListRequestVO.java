package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/9/6 10:30
 */
@Data
public class DialogGroupListRequestVO implements Serializable {

    private static final long serialVersionUID = 3187037082214968870L;

    /**
     * 其他参数
     */
    @Data
    public static class OtherParams implements Serializable {

        private static final long serialVersionUID = 181039281879264635L;

        /**
         * 专题id
         */
        @NotBlank(message = "专题id不能为空！")
        private String subjectId;

        /**
         * 群体类别
         */
        private String groupTypeId;

        /**
         * 录入单位
         */
        private String createDeptId;
    }

    /**
     * 其他参数
     */
    private DialogGroupListRequestVO.OtherParams otherParams;

    /**
     * 模糊检索参数
     */
    private SearchParams searchParams;

    /**
     * 分页
     */
    private PageParams pageParams;
}
