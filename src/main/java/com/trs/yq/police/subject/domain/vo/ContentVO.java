package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 档案目录VO
 *
 * <AUTHOR>
 * @date 2021/08/02
 */
@Data
@AllArgsConstructor
public class ContentVO implements Serializable {

    private static final long serialVersionUID = -4687311076910196633L;
    /**
     * 名称
     */
    private String name;

    /**
     * 计数
     */
    private Integer count;

    /**
     * 模块编号
     */
    private String code;

    /**
     * 构造方法
     *
     * @param name 模块名
     * @param code 编号
     */
    public ContentVO(String name, String code) {
        this.name = name;
        this.code = code;
    }
}
