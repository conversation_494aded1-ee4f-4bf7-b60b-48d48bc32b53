package com.trs.yq.police.subject.properties;

import java.util.Base64;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 * @date 2023/1/31 10:49
 */
@Validated
@Component
@ConfigurationProperties(prefix = "com.trs.warning")
@Data
public class WarningProperties {
    /**
     * 用户名
     */
    @NotNull
    private String username;

    /**
     * 密码
     */
    @NotNull
    private String password;

    /**
     * 通用查询（人员全量轨迹查询/感知源查询）
     */
    @NotNull
    private String generalSearchUrl;
    @NotNull
    private String endUserName;
    @NotNull
    private String endUserIdentifier;
    @NotNull
    private String endUserDept;
    @NotNull
    private String endUserDeviceId;

    /**
     * 轨迹查询页大小
     */
    @NotNull
    private Integer trackPageSize;

    /**
     * 轨迹查询表名
     */
    @NotNull
    private String trackTableName;

    /**
     * 生成moye请求headers
     *
     * @return headers map
     */
    public MultiValueMap<String, String> getHeadersMap() {
        MultiValueMap<String, String> headersMap = new LinkedMultiValueMap<>();
        headersMap.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        headersMap.add("endUserName", endUserName);
        headersMap.add("endUserIdentifier", endUserIdentifier);
        headersMap.add("endUserDept", endUserDept);
        headersMap.add("endUserDeviceId", endUserDeviceId);
        String authentication = username + ":" + password;
        headersMap.add(HttpHeaders.AUTHORIZATION, "Basic " + Base64.getEncoder().encodeToString(authentication.getBytes()));
        return headersMap;
    }


}
