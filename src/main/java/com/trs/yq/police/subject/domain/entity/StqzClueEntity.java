package com.trs.yq.police.subject.domain.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 省厅情指-线索
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Data
@TableName("zhzh_qzx_xsjcxx")
public class StqzClueEntity {

    /**
     * 线索名称
     */
    private String id;

    /**
     * 线索名称
     */
    private String xsbt;

    /**
     * 线索详情
     */
    private String xsnr;

    /**
     * 线索编号
     */
    private String xsbh;

    public ClueEntity of(){
        ClueEntity clueEntity = new ClueEntity();
        clueEntity.setName(this.xsbt);
        clueEntity.setDetail(this.xsnr);
        clueEntity.setCode(this.xsbh);
        clueEntity.setXsid(this.id);
        return clueEntity;
    }
}
