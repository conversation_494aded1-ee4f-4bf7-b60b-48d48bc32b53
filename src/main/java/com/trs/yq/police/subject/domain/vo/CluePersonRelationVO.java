package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/4 16:59
 */
@Data
public class CluePersonRelationVO implements Serializable {

    private static final long serialVersionUID = -6618816469177914150L;

    /**
     * 线索id
     */
    private String clueId;

    /**
     * 人员Id
     */
    @NotNull(message = "人员id不能为空！")
    private List<String> personIds;
}
