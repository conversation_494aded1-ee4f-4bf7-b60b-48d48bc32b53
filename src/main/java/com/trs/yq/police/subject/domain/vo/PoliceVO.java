package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.UserEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 民警信息
 *
 * <AUTHOR>
 * @since 2021/8/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoliceVO implements Serializable {

    private static final long serialVersionUID = 6067283818568795592L;

    /**
     * id
     */
    private String responsibleId;
    /**
     * 姓名
     */
    private String responsibleName;
    /**
     * 职务
     */
    private String responsibleJob;
    /**
     * 联系方式
     */
    private String responsibleContact;

    /**
     * 根据用户生成PoliceVO
     *
     * @param user 用户信息
     * @return PoliceVO
     */
    public static PoliceVO userEntityToPoliceVO(UserEntity user) {
        return new PoliceVO(
                user.getId(),
                user.getRealName(),
                user.getDuty(),
                user.getMobilePhone()
        );
    }
}
