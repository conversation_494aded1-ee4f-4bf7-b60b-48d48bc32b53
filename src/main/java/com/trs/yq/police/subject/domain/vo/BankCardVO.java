package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 银行卡信息vo
 *
 * <AUTHOR>
 */
@Data
public class BankCardVO implements Serializable {

    private static final long serialVersionUID = 8001930697899834283L;

    /**
     * 银行卡id
     */
    private String id;

    /**
     * 银行卡号
     */
    @NotBlank(message = "银行卡号缺失")
    private String bankCardNumber;

    /**
     * 开户行
     */
    @NotBlank(message = "开户银行缺失")
    private String bankOfDeposit;

    /**
     * 使用状态
     */
    private Integer useType;

    /**
     * 是否为自动更新导入
     */
    private String isAutomated;
}
