package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.DictEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Validated
public interface DictRepository extends BaseRepository<DictEntity, String> {

    /**
     * 获取指定类型码表
     *
     * @param type 码表类型字段
     * @return 码表
     */
    @Query("SELECT d FROM DictEntity d WHERE d.type=:type ORDER BY d.code ASC")
    List<DictEntity> findAllByType(@Param("type") String type);

    /**
     * 根据type和code查询制定码表项
     *
     * @param type type字段
     * @param code code字段
     * @return 码表项
     */
    DictEntity findByTypeAndCode(String type, String code);

    /**
     * 查询 T_EVENT_DICT表
     *
     * @param type type
     * @param code code
     * @return 中文
     */
    @Query(nativeQuery = true, value = "SELECT DICTNAME FROM T_BATTLE_EVENTDICT WHERE DICTTYPE=:type AND DICTCODE=:code")
    String getEventType(@Param("type") String type, @Param("code") String code);

    /**
     * 根据type和码值前缀查询
     *
     * @param type 类型
     * @param codePrefix 码值前缀
     * @return {@link DictEntity}
     */
    List<DictEntity> findAllByTypeAndCodeStartingWith(String type,String codePrefix);


    /**
     * 根据type和code查询name
     *
     * @param type type字段
     * @param code code字段
     * @return name
     */
    @Query("select t.name from DictEntity t where t.type=:type and t.code=:code")
    String findNameByTypeAndCode(String type,String code);
}
