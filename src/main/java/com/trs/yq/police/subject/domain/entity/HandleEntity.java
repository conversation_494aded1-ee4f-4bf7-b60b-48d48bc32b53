package com.trs.yq.police.subject.domain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "v_cjdb")
public class HandleEntity implements Serializable {

    private static final long serialVersionUID = -6438505946987651302L;

    @Id
    @Column(name = "HANDLENUM")
    private String handleNum;

    @Column(name = "RECEIVENUM")
    private String receiveNum;

    private String contents;

    private String results;

    @Column(name = "HANDLETIME")
    private LocalDateTime handleTime;

    @Column(name = "ARRIVETIME")
    private LocalDateTime arriveTime;

    @Column(name = "DONETIME")
    private LocalDateTime doneTime;

    @Column(name = "HANDLEUNIT")
    private String handleUnit;

    private String policeman;

    @Column(name = "CARNUM")
    private String carNum;

    @Column(name = "POLICENUM")
    private String policeNum;

    @Column(name = "HELPNUM")
    private String helpNum;

    @Column(name = "HELPWOMAN")
    private String helpWoman;

    @Column(name = "UPUNIT")
    private String upUnit;

    @Column(name = "ISDELETE")
    private boolean isDelete;

    @Column(name = "FLAGTIME")
    private LocalDateTime flatTime;
}
