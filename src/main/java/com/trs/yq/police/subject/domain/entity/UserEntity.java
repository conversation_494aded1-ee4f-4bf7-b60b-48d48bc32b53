package com.trs.yq.police.subject.domain.entity;


import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户信息实体类
 *
 * <AUTHOR>
 * @since 2021/8/9
 */
@Entity
@Table(name = "T_USER")
@Data
public class UserEntity implements Serializable {

    private static final long serialVersionUID = -4365401669481519880L;

    @Id
    @GeneratedValue
    private String id;

    private String username;
    @Column(name = "IDCARD")
    private String idCard;

    private String password;

    private String gender;

    private LocalDate birthday;

    private String telephone;
    @Column(name = "MOBILEPHONE")
    private String mobilePhone;

    private String email;
    @Column(name = "POLICECODE")
    private String policeCode;
    @Column(name = "policekind")
    private String policeKind;
    @Column(name = "areacode")
    private String areaCode;
    @Column(name = "validdate")
    private LocalDate validDate;

    private Integer status;
    @Column(name = "crtime")
    private LocalDateTime crTime;
    @Column(name = "cruser")
    private String crUser;
    @Column(name = "lastlogintime")
    private LocalDateTime lastLoginTime;
    @Column(name = "lastloginip")
    private String lastLoginIP;
    @Column(name = "idsuserid")
    private String idsUserId;
    @Column(name = "idsunitid")
    private String idsUnitId;
    @Column(name = "unitcode")
    private String unitCode;
    @Column(name = "unitname")
    private String unitName;

    private String signet;
    @Column(name = "isadmin")
    private Integer isAdmin;
    @Column(name = "showorder")
    private Integer showOrder;
    @Column(name = "realname")
    private String realName;

    private String photo;

    private String duty;
    @Column(name = "dutydm")
    private String dutyDM;

    private String hasdsj;

    private String hasgabys;


}
