package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 线索类型关联表
 *
 * <AUTHOR>
 * @date 2021/09/06
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CLUE_LABEL_RELATION")
public class ClueLabelRelationEntity extends BaseEntity {

    private static final long serialVersionUID = -129801343177303717L;
    /**
     * 专题id
     */
    private String clueId;

    /**
     * 线索名称
     */
    private String labelId;
}
