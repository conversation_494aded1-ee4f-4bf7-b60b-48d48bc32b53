package com.trs.yq.police.subject.domain.response;

import com.trs.yq.police.subject.domain.dto.RiskDetailDto;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 风险研判人员信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/16 17:14
 */
@Getter
@Setter
@ToString
public class RiskDetailPersonResponse {

    /**
     * 人员id
     */
    private String personId;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 人员身份证号
     */
    private String idNumber;

    /**
     * 人员相片
     */
    private String photo;

    /**
     * 管控状态
     */
    private String controlStatus;

    /**
     * 添加时间
     */
    private LocalDateTime addTime;

    /**
     * 构造器
     *
     * @param detailDto dto
     */
    public RiskDetailPersonResponse(RiskDetailDto detailDto) {
        this.personId = detailDto.getPersonId();
        this.name = detailDto.getName();
        this.idNumber = detailDto.getIdNumber();
        this.photo = detailDto.getPhoto();
        this.controlStatus = detailDto.getControlStatus();
        this.addTime = detailDto.getAddTime();
    }
}
