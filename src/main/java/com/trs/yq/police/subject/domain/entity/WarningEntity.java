package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.jpa.converter.JpaStringArrayConverter;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 预警实体类
 *
 * <AUTHOR>
 * @since 2021/9/1
 */

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING")
public class WarningEntity implements Serializable {

    private static final long serialVersionUID = -906418814484107589L;

    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 预警时间
     */
    private LocalDateTime warningTime;

    /**
     * 地址
     */
    private String address;

    /**
     * 预警等级
     * ps_warning_level
     */
    private String warningLevel;

    /**
     * 预警来源
     */
    @Convert(converter = JpaStringArrayConverter.class)
    private List<String> warningSource;

    /**
     * 预警状态
     * ps_warning_status
     */
    private String warningStatus;

    /**
     * 预警详情
     */
    private String warningDetails;

    /**
     * 专题id
     */
    private String subjectId;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 预警主键
     */
    private String warningKey;

    /**
     * 预警主键类型
     * 0-idNumber,1-phoneNumber,2-imei,3-imsi
     */
    private String warningKeyType;

    /**
     * 地点
     */
    private String place;

    /**
     * 研判结果
     * ps_warning_judge_result
     */
    private String judgeResult;

    /**
     * 研判备注
     */
    private String judgeRemark;

    /**
     * 研判附件ids ,分隔
     */
    private String judgeFileIds;

    /**
     * 研判时间
     */
    private LocalDateTime judgeTime;

    /**
     * 签收时间
     */
    private LocalDateTime signTime;

    /**
     * 预警区县
     */
    private String areaCode;

    /**
     * 高风险警情事件id
     */
    private String eventUrl;

    /**
     * 数据信息
     */
    private String rawData;
}
