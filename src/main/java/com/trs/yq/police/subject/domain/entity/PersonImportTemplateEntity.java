package com.trs.yq.police.subject.domain.entity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/08/24
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Table(name = "T_PS_PERSON_IMPORT_TEMPLATE")
@Entity
public class PersonImportTemplateEntity implements Serializable {

    private static final long serialVersionUID = -980192738428294882L;

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 主题主键
     */
    private String subjectId;

    /**
     * 人员类型
     */
    private String personType;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        PersonImportTemplateEntity that = (PersonImportTemplateEntity) o;

        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return 1879236780;
    }
}

