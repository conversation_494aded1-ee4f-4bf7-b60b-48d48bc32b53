package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonGroupRelationEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人员和类型的关联关系
 *
 * <AUTHOR>
 * @date 2021/7/30 15:34
 */
@Repository
public interface PersonGroupRelationRepository extends BaseRepository<PersonGroupRelationEntity, String> {

    /**
     * 依据人员id删除所有关联关系
     *
     * @param personId 人员id
     * @param groups   群体id列表
     */
    @Modifying
    void removeAllByPersonIdAndGroupIdIn(String personId, List<String> groups);

    /**
     * 根据群体id删除所有关联关系
     *
     * @param groupId 群体Id
     */
    @Modifying
    void removeAllByGroupId(String groupId);

    /**
     * 根据专题Id对人员计数
     *
     * @param groupId 专题Id
     * @return 人员数量
     */
    int countAllByGroupId(String groupId);

    /**
     * 根据 groupId 查询所有关联的人
     *
     * @param groupId groupId
     * @return {@link PersonGroupRelationEntity}
     */
    List<PersonGroupRelationEntity> findAllByGroupId(String groupId);

    /**
     * 根据 groupId 删除所有关联人员
     *
     * @param groupId 群体id
     */
    void deleteAllByGroupId(String groupId);

    /**
     * 根据 personId 删除所有关联群体
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    @Modifying
    @Query("DELETE FROM PersonGroupRelationEntity t1 WHERE t1.groupId in (SELECT g.id FROM GroupEntity g WHERE g.subjectId=:subjectId) AND t1.personId=:personId")
    void deleteAllByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId);

    /**
     * 根据id删除关联群体
     *
     * @param ids  关联id
     */
    void deleteByIdIn(List<String> ids);

    /**
     * 分页查询人员涉事群体
     *
     * @param personId  personId
     * @param subjectId subjectId
     * @param pageable  分页
     * @return {@link PersonGroupRelationEntity}
     */
    @Query("SELECT t1 " +
            "FROM PersonGroupRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id " +
            "WHERE t2.subjectId=:subjectId " +
            "AND t1.personId=:personId " +
            "order by t1.upTime desc ")
    Page<PersonGroupRelationEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId,
                                                                  @Param("subjectId") String subjectId,
                                                                  Pageable pageable);

    /**
     * 不分页查询人员涉事群体
     *
     * @param personId  personId
     * @param subjectId subjectId
     * @return {@link PersonGroupRelationEntity}
     */
    @Query("SELECT t1 " +
            "FROM PersonGroupRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id " +
            "WHERE t2.subjectId=:subjectId " +
            "AND t1.personId=:personId")
    List<PersonGroupRelationEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId,
                                                                  @Param("subjectId") String subjectId);

    /**
     * 根据群体id和人员id查询关系
     *
     * @param groupId  群体id
     * @param personId 人员id
     * @return {@link PersonGroupRelationEntity}
     */
    PersonGroupRelationEntity findByGroupIdAndPersonId(String groupId, String personId);
}
