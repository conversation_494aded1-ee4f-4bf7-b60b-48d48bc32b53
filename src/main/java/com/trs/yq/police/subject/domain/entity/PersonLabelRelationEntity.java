package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 人员与标签关系实体
 *
 * <AUTHOR>
 * @date 2021/7/30 15:47
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_LABEL_RELATION")
public class PersonLabelRelationEntity extends BaseEntity {

    private static final long serialVersionUID = 1457829635436482503L;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 标签id
     */
    private String labelId;

    /**
     * 构造方法
     *
     * @param personId 人员id
     * @param labelId  标签id
     */
    public void personLabelRelationEntity(String personId, String labelId) {
        this.personId = personId;
        this.labelId = labelId;
    }
}
