package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/9/25 15:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrjJwryImportDataVO {

    /**
     * 证件号码
     */
    private String idNumber;
    /**
     * 证件类型
     */
    private String idType;
    /**
     * 国家/地区
     */
    private String gjdm;
    private String enFirstName;
    private String enLastName;
    /**
     * cnName
     */
    private String cnName;
    /**
     * enName
     */
    private String enName;
    /**
     * 性别
     */
    private String gender;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 在华时间
     */
    private String inChinaTime;
    /**
     * 入境日期
     */
    private String entryTime;
    /**
     * 入境口岸
     */
    private String entryPort;
    /**
     * 住宿详细地址
     */
    private String liveAddress;
    /**
     * 签证类型
     */
    private String visaType;
    /**
     * 签证号码
     */
    private String visaNumber;
    /**
     * 派出所名称
     */
    private String policeStation;
    /**
     * 住宿时间
     */
    private String zsrq;
    /**
     * 拟离开时间
     */
    private String planLeaveTime;
    /**
     * 录入时间
     */
    private String createTime;
    /**
     * 居住状态
     */
    private String liveStatus;
    /**
     * 留宿单位
     */
    private String lsdw;

    /**
     * 数据来源
     */
    private String dataSource;
}
