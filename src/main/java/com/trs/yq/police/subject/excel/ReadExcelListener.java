package com.trs.yq.police.subject.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.google.common.collect.Lists;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.ImportResultListVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;
import static com.trs.yq.police.subject.constants.enums.PersonFieldEnum.*;


/**
 * Excel 读取类 配合 {@link com.alibaba.excel.EasyExcelFactory} 的 read() 使用
 *
 * <AUTHOR>
 * @date 2021/3/26 10:58
 */
@Slf4j
@Builder
public class ReadExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    /**
     * 人员信息持久层接口
     */
    private final PersonRepository personRepository;

    /**
     * 人员与专题关联接口
     */
    private final PersonSubjectRelationRepository personSubjectRelationRepository;

    /**
     * 裁决持久层
     */
    private final AdjudicationRepository adjudicationRepository;

    /**
     * 人员标签关系
     */
    private final PersonLabelRelationRepository personLabelRelationRepository;

    /**
     * 标签持久层
     */
    private final LabelRepository labelRepository;

    /**
     * 手机号
     */
    private final MobilePhoneRepository mobilePhoneRepository;

    /**
     * 政府部门管控信息 + 维稳人员扩展字段
     */
    private final CommonExtendRepository commonExtendRepository;

    /**
     * 虚拟身份
     */
    private final VirtualIdentityRepository virtualIdentityRepository;

    /**
     * 公安管控信息
     */
    private final ControlRepository controlRepository;
    /**
     * 字典持久层
     */
    private final DictService dictService;
    /**
     * 派出所
     */
    private final UnitRepository unitRepository;
    /**
     * 操作日志
     */
    private final OperationLogHandler operationLogHandler;

    /**
     * 当前用户
     */
    private final LoginUser currentUser;

    /**
     * 专题主键
     */
    private final String subjectId;

    /**
     * 重复策略 1-不导入, 2-覆盖导入，3-继续导入
     */
    private final String repeatStrategy;

    /**
     * 人员标签
     */
    private final String personLabel;

    /**
     * 字段映射
     */
    @Getter
    private final Map<Integer, PersonFieldEnum> columnMap = new ConcurrentHashMap<>();

    /**
     * 处理总量
     */
    @Getter
    private final AtomicInteger analysisCount = new AtomicInteger();

    /**
     * 表头行数
     */
    @Getter
    private final AtomicInteger headCount = new AtomicInteger(0);

    /**
     * 失败记录
     */
    @Getter
    private final List<ImportResultListVO> failResult = new LinkedList<>();

    /**
     * 成功记录
     * key - 行数
     * value - key : 属性， value : 值
     */
    @Getter
    private final Map<Integer, Map<Integer, String>> success = new HashMap<>();

    /**
     * 失败记录
     */
    @Getter
    private final List<Map<Integer, String>> failRows = new LinkedList<>();

    private List<DictEntity> nationDict;
    private List<DictEntity> maritalDict;
    private List<DictEntity> politicalDict;
    private List<DictEntity> identityDict;
    private List<UnitEntity> policeStation;

    private final Pattern phoneNumberPattern = Pattern.compile("^1[3-9][0-9]{9}$");
    private final Map<Pattern, DateTimeFormatter> datePatterns = Stream.of(
                    new AbstractMap.SimpleEntry<>(Pattern.compile("(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9][1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|" +
                            "((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
                            "((0[48]|[2468][048]|[3579][26])00))0229)$"),
                            DateTimeFormatter.ofPattern("yyyyMMdd")),
                    new AbstractMap.SimpleEntry<>(Pattern.compile("(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9][1-9][0-9]{2}|[1-9][0-9]{3})/(((0[13578]|1[02])/(0[1-9]|[12][0-9]|3[01]))|" +
                            "((0[469]|11)/(0[1-9]|[12][0-9]|30))|(02/(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
                            "((0[48]|[2468][048]|[3579][26])00))/02/29)$"),
                            DateTimeFormatter.ofPattern("yyyy/MM/dd")),
                    new AbstractMap.SimpleEntry<>(Pattern.compile("(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]|[0-9][1-9][0-9]{2}|[1-9][0-9]{3})-(((0[13578]|1[02])-(0[1-9]|[12][0-9]|3[01]))|" +
                            "((0[469]|11)-(0[1-9]|[12][0-9]|30))|(02-(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
                            "((0[48]|[2468][048]|[3579][26])00))-02-29)$"),
                            DateTimeFormatter.ofPattern("yyyy-MM-dd")))
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

    private final Pattern limitTimePattern = Pattern.compile("^\\d{1,3}((个月)|年)");

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 检验是否为正常的header
        if (checkHeader(headMap)) {
            // 通过header 转换字段结构
            headMap.entrySet()
                    .stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> columnMap.put(entry.getKey(), PersonFieldEnum.cnNameOf(entry.getValue())));

            if (!columnMap.containsValue(PersonFieldEnum.ID_NUMBER)) {
                throw new ParamValidationException("导入的表格格式不正确，请核实！");
            }

            // 缓存字典
            nationDict = dictService.getDictEntitiesByType("nation");
            maritalDict = dictService.getDictEntitiesByType("ps_marital_status");
            politicalDict = dictService.getDictEntitiesByType("ps_political_status");
            identityDict = dictService.getDictEntitiesByType("ps_virtual_identity_type");
            policeStation = unitRepository.findByType("4");
        }
        headCount.incrementAndGet();
    }

    private boolean checkHeader(Map<Integer, String> headMap) {
        return headMap.values().stream().anyMatch(value -> Objects.nonNull(PersonFieldEnum.cnNameOf(value)));
    }

    @Override
    public void invoke(Map<Integer, String> rows, AnalysisContext analysisContext) {
        // 1.先找到当前行的身份证信息
        if (checkAssignColumn(rows)) {
            success.put(analysisCount.get() + headCount.get(), rows);
        } else {
            failRows.add(rows);
        }
        // 计数
        analysisCount.incrementAndGet();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 存储
        success.forEach((key, value) -> {
            Map<String, String> personMap = new LinkedHashMap<>();
            value.forEach((k, v) -> personMap.put(columnMap.get(k).getField(), v));

            // 生成人员
            final PersonEntity personEntity = generatePerson(personMap);

            // 处理警种
            processPersonSubject(personEntity);

            // 处理标签
            processPersonLabels(personMap, personEntity);

            // 处理手机号
            processPersonPhone(personEntity);

            //处理政府管控信息
            processGovernment(personMap, personEntity);
            //处理公安管控信息
            processPoliceStation(personMap, personEntity);
            //处理虚拟身份
            processVirtualIdentity(personMap, personEntity);
            if (StringUtils.isNotBlank(personLabel)) {
                //处理人员标签
                processPersonType(personLabel, personEntity);
                labelRepository.findById(personLabel).ifPresent(type -> {
                    String lossLicense = "失驾人员";
                    if (lossLicense.equals(type.getName())) {
                        // 处理交警裁决
                        processTrafficAdjudication(personEntity, personMap);
                    }
                });
            }
        });
    }

    private void processPersonType(String personType, PersonEntity personEntity) {
        labelRepository.findById(personType).ifPresent(labelEntity -> {
            PersonLabelRelationEntity relation = personLabelRelationRepository.findByPersonIdAndLabelId(personEntity.getId(), labelEntity.getId()).orElse(new PersonLabelRelationEntity(personEntity.getId(), labelEntity.getId()));
            personLabelRelationRepository.save(relation);
        });
    }

    private void processVirtualIdentity(Map<String, String> personMap, PersonEntity personEntity) {
        //如果选择为覆盖导入，先删除已有的虚拟身份
        if (repeatStrategy.equals(REPEAT_STRATEGY_REPLACE)) {
            virtualIdentityRepository.deleteAllByPersonId(personEntity.getId());
        }
        for (Map.Entry<String, String> entry : personMap.entrySet()) {
            if (StringUtils.isBlank(entry.getValue())) {
                continue;
            }
            identityDict.stream().filter(item -> item.getName().contains(entry.getKey())).findAny().ifPresent(item -> {
                VirtualIdentityEntity virtualIdentityEntity = new VirtualIdentityEntity();
                virtualIdentityEntity.setVirtualType(item.getCode());
                virtualIdentityEntity.setVirtualNumber(entry.getValue());
                virtualIdentityEntity.setPersonId(personEntity.getId());
                virtualIdentityRepository.save(virtualIdentityEntity);
            });
        }
    }

    private void processPoliceStation(Map<String, String> personMap, PersonEntity personEntity) {
        ControlEntity controlEntity = JsonUtil.parseObject(JsonUtil.toJsonString(personMap), ControlEntity.class);
        ControlEntity entity = controlRepository.findByPersonIdAndSubjectId(personEntity.getId(), subjectId).orElse(null);
        //数据库不存在老数据并且peronMap中有数据则存入数据库
        if (Objects.isNull(entity) && Objects.nonNull(controlEntity)) {
            controlEntity.setSubjectId(subjectId);
            controlEntity.setPersonId(personEntity.getId());
            policeStation.stream()
                    .filter(unitEntity -> StringUtils.isNotBlank(controlEntity.getPoliceStationName()) && unitEntity.getUnitName().contains(controlEntity.getPoliceStationName()))
                    .findAny()
                    .ifPresent(item -> {
                        controlEntity.setPoliceStationCode(item.getUnitCode());
                    });
            controlRepository.save(controlEntity);
        } else if (Objects.nonNull(entity) && Objects.nonNull(controlEntity)) {
            //数据库存在老数据并且perMap中有数据则更新老数据
            BeanUtil.copyPropertiesIgnoreNull(controlEntity, entity);
            policeStation.stream()
                    .filter(unitEntity -> StringUtils.isNotBlank(controlEntity.getPoliceStationName()) && unitEntity.getUnitName().contains(controlEntity.getPoliceStationName()))
                    .findAny()
                    .ifPresent(item -> {
                        entity.setPoliceStationCode(item.getUnitCode());
                    });
            controlRepository.save(entity);
        }
    }

    private void processGovernment(Map<String, String> personMap, PersonEntity personEntity) {
        // 读取的excel信息转换为人员实体
        CommonExtentEntity commonExtentEntity = JsonUtil.parseObject(JsonUtil.toJsonString(personMap), CommonExtentEntity.class);
        CommonExtentEntity entity = commonExtendRepository.findByRecordIdAndModule(personEntity.getId(), "person").orElse(null);
        //数据库不存在老数据并且peronMap中有数据则存入数据库
        if (Objects.isNull(entity) && Objects.nonNull(commonExtentEntity)) {
            commonExtentEntity.setRecordId(personEntity.getId());
            commonExtentEntity.setModule("person");
            commonExtendRepository.save(commonExtentEntity);
        } else if (Objects.nonNull(entity) && Objects.nonNull(commonExtentEntity)) {
            //数据库存在老数据并且perMap中有数据则更新老数据
            BeanUtil.copyPropertiesIgnoreNull(commonExtentEntity, entity);
            commonExtendRepository.save(entity);
        }

    }

    private void processPersonSubject(PersonEntity person) {
        final String personId = person.getId();
        final PersonSubjectRelationEntity relation =
                personSubjectRelationRepository.findByPersonIdAndSubjectId(personId, subjectId);

        if (Objects.isNull(relation)) {
            PersonSubjectRelationEntity entity = new PersonSubjectRelationEntity();
            BeanUtils.copyProperties(person, entity, PersonFieldEnum.ID.getField());
            entity.setSubjectId(subjectId);
            entity.setPersonId(personId);
            personSubjectRelationRepository.save(entity);
        }
    }

    private PersonEntity generatePerson(Map<String, String> personMap) {
        // 操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.IMPORT)
                .currentUser(currentUser)
                .module(OperateModule.ARCHIVES_MANAGE)
                .desc("导入人员档案")
                .build();
        // 读取的excel信息转换为人员实体
        PersonEntity person = JsonUtil.parseObject(JsonUtil.toJsonString(personMap), PersonEntity.class);
        assert person != null;
        // 验证人员是否存在
        final String idNumber = person.getIdNumber().toUpperCase();
        // 回填身份证大写
        person.setIdNumber(idNumber);
        final PersonEntity entity = personRepository.findByIdNumber(idNumber);
        if (Objects.isNull(entity)) {
            // 不存在则补充创建人信息
            person.setCrBy(currentUser.getId());
            person.setCrByName(currentUser.getRealName());
            person.setCrTime(LocalDateTime.now());
            person.setCrDept(currentUser.getUnitName());
            person.setCrDeptCode(currentUser.getUnitCode());
        } else {
            // 存在则不更新部分使用老数据
            BeanUtil.copyPropertiesIgnoreNull(person, entity);
            person = entity;
        }
        person.setUpBy(currentUser.getId());
        person.setUpByName(currentUser.getRealName());
        person.setUpTime(LocalDateTime.now());
        person.setGender(StringUtil.parseGenderFromIdNumber(idNumber, false));
        person.setControlStatus("1");
        // 民族
        person.setNation(parseNation(person.getNation()));
        // 婚姻状况
        person.setMaritalStatus(parseMaritalStatus(person.getMaritalStatus()));
        // 政治面貌
        person.setPoliticalStatus(parsePoliticalStatus(person.getPoliticalStatus()));

        personRepository.save(person);

        // 记录日志
        logRecord.setPrimaryKey(person.getId());
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
        return person;
    }

    private void processPersonLabels(Map<String, String> personMap, PersonEntity personEntity) {

        final String personId = personEntity.getId();

        //查出该人员现有关联的labelIds
        final List<String> existingLabelIds = labelRepository.findAllByPersonIdAndSubjectId(personId, subjectId)
                .stream()
                .filter(Objects::nonNull)
                .filter(label -> {
                    final String personCreate = "0";
                    return StringUtils.equals(personCreate, label.getCreateType());
                })
                .map(LabelEntity::getId)
                .collect(Collectors.toList());

        //取出导入数据中的labelIds
        final String label = personMap.get(PersonFieldEnum.LABELS.getField());
        if (StringUtils.isNotBlank(label)) {
            final String enDelimiter = ",";
            final String cnDelimiter = "，";
            List<String> labels;
            if (label.contains(enDelimiter)) {
                labels = Lists.newArrayList(StringUtils.split(StringUtils.trimToEmpty(label), enDelimiter));
            } else if (label.contains(cnDelimiter)) {
                labels = Lists.newArrayList(StringUtils.split(StringUtils.trimToEmpty(label), cnDelimiter));
            } else {
                labels = Collections.singletonList(label);
            }

            final List<PersonLabelRelationEntity> labelRelations = labels.stream().map(l -> {
                LabelEntity entity = labelRepository.findByNameAndSubjectId(l, subjectId);
                if (Objects.nonNull(entity)) {
                    PersonLabelRelationEntity labelRelation = new PersonLabelRelationEntity();
                    BeanUtils.copyProperties(personEntity, labelRelation, PersonFieldEnum.ID.getField());
                    labelRelation.setLabelId(entity.getId());
                    labelRelation.setPersonId(personId);
                    return labelRelation;
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            personLabelRelationRepository.saveAll(labelRelations);
        }
    }

    private void processTrafficAdjudication(PersonEntity person, Map<String, String> personMap) {

        boolean checkField = personMap.containsKey(ADJUDICATION_REASON.getField())
                || personMap.containsKey(JUDGEMENT_DATE.getField())
                || personMap.containsKey(LIMIT_TIME.getField());

        if (checkField) {
            AdjudicationEntity adjudication = new AdjudicationEntity();
            adjudication.setPersonId(person.getId());
            int limitTime = 0;
            ChronoUnit limitUnit = ChronoUnit.YEARS;
            // 裁决原因
            final String reason = personMap.get(ADJUDICATION_REASON.getField());
            adjudication.setReason(reason);
            // 裁决时间
            String judgementDateStr = personMap.get(JUDGEMENT_DATE.getField());
            if (judgementDateStr.length() < STANDARD_DATE_PATTERN.length()) {
                judgementDateStr = judgementDateStr + "01";
            }
            for (Map.Entry<Pattern, DateTimeFormatter> entry : datePatterns.entrySet()) {
                if (entry.getKey().matcher(judgementDateStr).matches()) {
                    adjudication.setJudgementDate(LocalDate.parse(judgementDateStr, entry.getValue()));
                    break;
                }
            }
            // 限制时间
            final String limitTimeStr = personMap.get(LIMIT_TIME.getField());
            if (limitTimeStr.endsWith(TIME_UNIT_YEAR)) {
                limitTime = Integer.parseInt(limitTimeStr.replace(TIME_UNIT_YEAR, ""));
            } else if (limitTimeStr.endsWith(TIME_UNIT_MONTH_ALIAS) || limitTimeStr.endsWith(TIME_UNIT_MONTH)) {
                if (limitTimeStr.contains(TIME_UNIT_YEAR)) {
                    final int yearIndex = limitTimeStr.indexOf(TIME_UNIT_YEAR);
                    final int year = Integer.parseInt(
                            limitTimeStr
                                    .substring(0, yearIndex)
                                    .replace(TIME_UNIT_YEAR, ""));
                    final int month = Integer.parseInt(
                            limitTimeStr
                                    .substring(yearIndex + 1)
                                    .replace(TIME_UNIT_MONTH_ALIAS, "")
                                    .replace(TIME_UNIT_MONTH, ""));
                    limitTime = month + year * 12;
                } else {
                    limitTime = Integer.parseInt(
                            limitTimeStr
                                    .replace(TIME_UNIT_MONTH_ALIAS, "")
                                    .replace(TIME_UNIT_MONTH, ""));
                }
                limitUnit = ChronoUnit.MONTHS;
            } else if (StringUtils.equals(TIME_UNIT_FOREVER, limitTimeStr)) {
                limitTime = 99;
            }

            adjudication.setLimitTime(limitTime);
            adjudication.setLimitUnit(limitUnit.name());
            LocalDate endDate = adjudication.getJudgementDate().plus(limitTime, limitUnit);
            adjudication.setEndDate(endDate);
            BeanUtils.copyProperties(person, adjudication, PersonFieldEnum.ID.getField());
            adjudicationRepository.save(adjudication);
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {

        // 记录异常
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException convertException = (ExcelDataConvertException) exception;
            final Integer rowIndex = convertException.getRowIndex();
            final Integer columnIndex = convertException.getColumnIndex();
            final String cellData = convertException.getCellData().toString();
            log.error("read excel fail! line : [ "
                    .concat(String.valueOf(rowIndex))
                    .concat(" ] row, [ ")
                    .concat(String.valueOf(columnIndex))
                    .concat(" ] cell, data : [ ")
                    .concat(cellData).concat(" ]"), exception);
        } else {
            log.error("analysis excel error !", exception);
        }

    }

    private boolean checkAssignColumn(Map<Integer, String> rows) {

        AtomicBoolean validate = new AtomicBoolean(false);

        // 校验姓名
        checkName(rows, validate);

        // 校验身份证
        if (validate.get()) {
            // 姓名校验通过才校验身份证
            checkIdNumber(rows, validate);
        }
        //检验手机号
//        if (validate.get()) {
//            //身份校验通过才校验联系方式
//            checkPhoneNumber(rows, validate);
//        }

        //检验裁决信息
        if (validate.get()) {
            checkJudgement(rows, validate);
        }

        return validate.get();
    }

    private void checkJudgement(Map<Integer, String> rows, AtomicBoolean validate) {
        //校验裁决日期
        columnMap.entrySet().stream()
                .filter(entry -> JUDGEMENT_DATE.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkJudgementDate(rows.get(entry.getKey()), validate));

        //校验限制期限
        if (validate.get()) {
            columnMap.entrySet().stream()
                    .filter(entry -> LIMIT_TIME.equals(entry.getValue()))
                    .findFirst()
                    .ifPresent(entry -> checkLimitTime(rows.get(entry.getKey()), validate));
        }
    }

    private void checkLimitTime(String text, AtomicBoolean validate) {
        if (StringUtils.isBlank(text)) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("限制期限为空"))
                    .build();
            failResult.add(validFailResult);
            validate.compareAndSet(true, false);
            return;
        }

        if (!limitTimePattern.matcher(text).matches()) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("限制年限格式符合！应为X年 或 X个月"))
                    .build();
            failResult.add(validFailResult);
            validate.compareAndSet(true, false);
        }
    }

    private void checkJudgementDate(String text, AtomicBoolean validate) {
        if (StringUtils.isBlank(text)) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("裁决日期为空"))
                    .build();
            failResult.add(validFailResult);
            validate.compareAndSet(true, false);
            return;
        }

        if (datePatterns.entrySet().stream().noneMatch(entry -> entry.getKey().matcher(text).matches())) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("裁决日期格式不符合要求"))
                    .build();
            failResult.add(validFailResult);
            validate.compareAndSet(true, false);
        }
    }

    private void checkPhoneNumber(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
                .stream()
                .filter(entry -> CONTACT_INFORMATION.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkPhoneNumberAvailable(rows.get(entry.getKey()), validate));
    }

    private void checkPhoneNumberAvailable(String phoneNumber, AtomicBoolean validate) {
        if (StringUtils.isNotBlank(phoneNumber) && !phoneNumberPattern.matcher(phoneNumber).matches()) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("联系方式不符合要求"))
                    .build();
            failResult.add(validFailResult);
            validate.compareAndSet(true, false);
        }

    }

    private void checkName(Map<Integer, String> rows, AtomicBoolean validate) {
        // 找到姓名
        columnMap.entrySet()
                .stream()
                .filter(entry -> NAME.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkNameIsBlank(rows.get(entry.getKey()), validate));
    }

    private void checkIdNumber(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
                .stream()
                .filter(entry -> PersonFieldEnum.ID_NUMBER.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkIdNumberCompliance(rows.get(entry.getKey()), entry.getKey(), validate));
    }

    private void checkNameIsBlank(String name, AtomicBoolean validate) {
        if (StringUtils.isNotBlank(name)) {
            // 如果名字不为空，则将标识符改为true
            validate.compareAndSet(false, true);
        } else {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("姓名不符合要求"))
                    .build();
            failResult.add(validFailResult);
        }
    }

    private void checkIdNumberCompliance(String idNumber, Integer rowIndex, AtomicBoolean validate) {

        // 校验是否规范
        checkIdNumberIsCanonical(idNumber, validate);

        // 上面成功才继续
        if (validate.get()) {
            // 校验是否与分析完成的文件重复
            success.entrySet()
                    .stream()
                    .filter(successEntry -> StringUtils.equals(successEntry.getValue().get(rowIndex), idNumber))
                    .findFirst()
                    .ifPresent(successEntry -> checkIdNumberIsRepeat(validate, successEntry));
        }

        if (validate.get()) {
            // 校验是否与数据库重复
            checkIdNumberIsRepeat(idNumber, validate);
        }
    }

    private void checkIdNumberIsCanonical(String idNumber, AtomicBoolean validate) {
        if (!StringUtil.checkIdNumber(idNumber)) {
            // 身份证校验未通过
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("身份证不符合要求"))
                    .build();
            failResult.add(validFailResult);
            validate.compareAndSet(true, false);
        }
    }

    private void checkIdNumberIsRepeat(String idNumber, AtomicBoolean validate) {
        if (repeatStrategy.equals(REPEAT_STRATEGY_STOP)
                && personRepository.checkExistByIdNumberAndSubjectId(idNumber, subjectId)) {
            // 如果选择为不导入，则当前用户为不能导入，则为失败
            final ImportResultListVO repeatFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("身份证与人员库重复"))
                    .build();
            failResult.add(repeatFailResult);
            validate.compareAndSet(true, false);
        }
    }

    private void checkIdNumberIsRepeat(AtomicBoolean validate, Map.Entry<Integer, Map<Integer, String>> successEntry) {
        final Integer repeatIndex = successEntry.getKey();
        final int currentIndex = headCount.get() + analysisCount.get() + 1;
        // 身份证重复
        final ImportResultListVO initialRepeatFail = ImportResultListVO.builder()
                .status(1)
                .desc("行" + repeatIndex + "与行" + currentIndex + "数据重复")
                .build();
        failResult.add(initialRepeatFail);
        final ImportResultListVO repeatFail = ImportResultListVO.builder()
                .status(1)
                .desc("行" + currentIndex + "与行" + repeatIndex + "数据重复")
                .build();
        failResult.add(repeatFail);
        // 去除成功的数据,增加至错误信息
        final Map<Integer, String> remove = success.remove(repeatIndex);
        failRows.add(remove);
        validate.compareAndSet(true, false);
    }

    private String generateFailMsg(String errorMsg) {
        return "第".concat(String.valueOf(analysisCount.get() + headCount.get() + 1))
                .concat("行 ").concat(errorMsg);
    }

    private void processPersonPhone(PersonEntity person) {
        final String personId = person.getId();
        final String contactInformation = person.getContactInformation();
        // 有数据才去联动更新和新增
        if (StringUtils.isNotBlank(contactInformation)) {
            MobilePhoneEntity mobilePhone = mobilePhoneRepository.findByPersonIdAndPhoneNumber(personId, contactInformation);

            if (Objects.nonNull(mobilePhone)) {
                // 如果存在，则更新状态
                mobilePhone.setPhoneUseStatus("1");
                mobilePhone.setPhoneStatus("1");
            } else {
                // 没有就新增
                MobilePhoneEntity phone = new MobilePhoneEntity();
                phone.setPersonId(personId);
                phone.setPhoneUseStatus("1");
                phone.setPhoneStatus("1");
                phone.setPhoneNumber(contactInformation);
                BeanUtils.copyProperties(person, person, PersonFieldEnum.ID.getField());
                mobilePhoneRepository.save(phone);
            }
        }
    }


    private String parseNation(String nation) {
        if (StringUtils.isNotBlank(nation)) {
            return nationDict
                    .stream()
                    .filter(nationEnum -> StringUtils.startsWith(nationEnum.getName(), nation) || nationEnum.getCode().equals(nation))
                    .findFirst()
                    .orElse(new DictEntity())
                    .getCode();
        }
        return nation;
    }

    private String parsePoliticalStatus(String politicalStatus) {
        if (StringUtils.isNotBlank(politicalStatus)) {
            return politicalDict
                    .stream()
                    .filter(political -> StringUtils.startsWith(political.getName(), politicalStatus) || political.getCode().equals(politicalStatus))
                    .findFirst()
                    .orElse(new DictEntity())
                    .getCode();
        }
        return politicalStatus;
    }

    private String parseMaritalStatus(String maritalStatus) {
        if (StringUtils.isNotBlank(maritalStatus)) {
            return maritalDict
                    .stream()
                    .filter(marital -> StringUtils.startsWith(marital.getName(), maritalStatus) || marital.getCode().equals(maritalStatus))
                    .findFirst()
                    .orElse(new DictEntity())
                    .getCode();
        }
        return maritalStatus;
    }
}
