package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 标签实体类
 *
 * <AUTHOR>
 * @since 2021/7/27 14:11
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_LABEL")
public class LabelEntity extends BaseEntity {

    private static final long serialVersionUID = -5752123125490863925L;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 主题编号
     */
    private String subjectId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建方式 0-手动创建，1-大数据平台推送
     */
    private String createType;

    /**
     * 模块 person/group/clue/event
     */
    private String module;

    /**
     * 上级标签id
     */
    private String pid;

    /**
     * 状态 1=启用 0=停用
     */
    private String status;

    /**
     * 显示顺序
     */
    private String showOrder;
}

