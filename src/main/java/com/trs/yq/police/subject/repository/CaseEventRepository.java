package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CaseEventEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 案事件信息持久层
 *
 * <AUTHOR>
 */
@Repository
public interface CaseEventRepository extends BaseRepository<CaseEventEntity, String> {

    /**
     * 查询人员案事件信息按发生时间倒叙排序
     *
     * @param personId 人员id
     * @return 人员基本信息 {@link CaseEventEntity}
     * <AUTHOR>
     */
    List<CaseEventEntity> findAllByPersonIdOrderByHappenTimeDesc(String personId);

    /**
     * 根据地区id，开始时间，结束时间查询刑事案件数量
     *
     * @param areaCode  地区编码
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 数量
     * <AUTHOR>
     */
    @Query(nativeQuery = true,
            value = "SELECT count(a.ASJBH) FROM JWZH_XSAJ_AJ  a " +
                    "WHERE to_date(a.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') >= :beginTime AND to_date(a.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') < :endTime " +
                    "AND a.AJLBDM IN " +
                    "('06070000','06070100','06070101','06070102','06070103','06070104','06070200'," +
                    "'06070300','06070400','06070401','06070402','06070403','06070404','06070405'," +
                    "'06070406','06070500','06070600','06070700','06070800','06070801','06070802'," +
                    "'06070803','06070804','06070805','06070806','06070807','06070808','06070900'," +
                    "'06070901','06070902','06070903','06071000','06071100','06071200') " +
                    "AND instr(GXFXJDM,:areaCode) >0 ")
    Long getCriminal(@Param("areaCode") String areaCode, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据地区id，开始时间，结束时间查询打处人数
     *
     * @param areaCode  地区编码
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 数量
     * <AUTHOR>
     */
    @Query(nativeQuery = true,
            value = "SELECT COUNT(b.ASJBH) FROM JWZH_XSAJ_AJ a,JWZH_XSAJ_XYR b " +
                    "WHERE a.ASJBH = b.ASJBH AND to_date(b.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') >= :beginTime AND to_date(b.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') < :endTime " +
                    "AND  a.AJLBDM IN ('06070000','06070100','06070101','06070102','06070103','06070104','06070200'," +
                    "'06070300','06070400','06070401','06070402','06070403','06070404','06070405','06070406'," +
                    "'06070500','06070600','06070700','06070800','06070801','06070802','06070803','06070804'," +
                    "'06070805','06070806','06070807','06070808','06070900','06070901','06070902','06070903'," +
                    "'06071000','06071100','06071200') AND " +
                    "  instr(b.GXFXJDM,:areaCode) >0 ")
    Long getCriminalPerson(@Param("areaCode") String areaCode, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据地区id，开始时间，结束时间查询行政案件数量
     *
     * @param areaCode  地区编码
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 数量
     * <AUTHOR>
     */
    @Query(nativeQuery = true,
            value = "SELECT count(a.ASJBH) FROM JWZH_XZAJ_AJ  a " +
                    "WHERE to_date(a.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') >= :beginTime AND to_date(a.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') < :endTime " +
                    "AND a.AJLBDM IN " +
                    "('03040058','03040059','03040061','03040062','03040063','03040065','03040066'," +
                    "'24000001','24000002','65000001','65000002') " +
                    "AND  instr(GXFXJDM,:areaCode) >0 ")
    Long getAdministration(@Param("areaCode") String areaCode, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据地区id，开始时间，结束时间查询打处人数
     *
     * @param areaCode  地区编码
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 数量
     * <AUTHOR>
     */
    @Query(nativeQuery = true,
            value = "SELECT COUNT(b.ASJBH) FROM JWZH_XZAJ_AJ a,JWZH_XZAJ_XYR b " +
                    "WHERE a.ASJBH = b.ASJBH AND to_date(b.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') >= :beginTime AND to_date(b.XT_LRSJ,'yyyy-mm-dd hh24:mi:ss') < :endTime " +
                    "AND  a.AJLBDM IN ('03040058','03040059','03040061','03040062','03040063'," +
                    "'03040065','03040066','24000001','24000002','65000001','65000002') AND " +
                    " instr(b.GXFXJDM,:areaCode) >0")
    Long getAdministrationPerson(@Param("areaCode") String areaCode, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

}
