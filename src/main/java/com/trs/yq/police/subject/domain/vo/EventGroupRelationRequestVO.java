package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/09/07
 */
@Data
public class EventGroupRelationRequestVO implements Serializable {


    private static final long serialVersionUID = 6421879124919556299L;
    /**
     * 群体Id
     */
    private String eventId;

    /**
     * 线索id
     */
    @NotNull(message = "群体id缺失")
    private List<String> groupIds;
}
