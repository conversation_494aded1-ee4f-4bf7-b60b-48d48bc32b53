package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonImportTemplateEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 批量导出模板查询接口
 *
 * <AUTHOR>
 * @date 2021/08/24
 */
@Repository
public interface ImportTemplateRepository extends BaseRepository<PersonImportTemplateEntity, String> {

    /**
     * 通过专题id和人员类别查询批量导出模板
     *
     * @param subjectId  专题id
     * @param personType 人员类别
     * @return 批量导出模板
     */
    @Query("SELECT t FROM PersonImportTemplateEntity t where t.subjectId=:subjectId and (:personType is null or :personType = t.personType)")
    PersonImportTemplateEntity findTemplateBySubjectIdAndPersonType(@Param("subjectId") String subjectId, @Param("personType") String personType);
}
