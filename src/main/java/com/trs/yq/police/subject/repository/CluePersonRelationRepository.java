package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CluePersonRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 线索和人员关系表查询接口
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Repository
public interface CluePersonRelationRepository extends BaseRepository<CluePersonRelationEntity, String> {

    /**
     * 根据线索id批量删除管线关系
     *
     * @param clueId 线索Id
     */
    @Modifying
    void removeAllByClueId(String clueId);

    /**
     * 根据线索id和人员id 查询关联关系
     *
     * @param personId 人员id
     * @param clueId   线索id
     * @return {@link CluePersonRelationEntity}
     */
    CluePersonRelationEntity findByClueIdAndPersonId(String personId, String clueId);

    /**
     * 根据clueId 删除所有关系
     *
     * @param clueId 群体id
     */
    void deleteAllByClueId(String clueId);

    /**
     * 根据线索id查询所有关系
     *
     * @param clueId 线索id
     * @return {@link CluePersonRelationEntity}
     */
    List<CluePersonRelationEntity> findAllByClueId(String clueId);

    /**
     * 根据人员id查询所有关系
     *
     * @param personId 人员id
     * @return {@link CluePersonRelationEntity}
     */
    List<CluePersonRelationEntity> findAllByPersonId(String personId);

    /**
     * 根据人员id删除关系
     *
     * @param personId 人员id
     */
    void deleteAllByPersonId(String personId);
}
