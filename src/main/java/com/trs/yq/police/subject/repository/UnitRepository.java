package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.UnitEntity;
import java.util.Map;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * T_UNIT表查询接口
 *
 * <AUTHOR>
 * @date 2021/07/28
 */
@Repository
public interface UnitRepository extends BaseRepository<UnitEntity, String> {

    /**
     * 根据code和type字段查询
     *
     * @param code 单位代码
     * @param type 类型
     * @return 结果
     */
    @Query("select t from UnitEntity t where t.type=:type and t.unitCode like concat(:code,'%') order by t.unitCode ASC ")
    List<UnitEntity> findByTypeAndCodeLike(@Param("type") String type, @Param("code") String code);

    /**
     * 根据code查询
     *
     * @param code 单位代码
     * @return 结果
     */
    UnitEntity findByUnitCode(String code);

    /**
     * 根据地区编码和类型查询单位
     *
     * @param areaCode 地区编码
     * @param type     类型
     * @return 单位列表
     */
    List<UnitEntity> findByAreaCodeAndType(String areaCode, String type);

    /**
     * 根据类型查询单位
     *
     * @param type 类型
     * @return 单位列表
     */
    List<UnitEntity> findByType(String type);

    /**
     * 根据code和type字段查询
     *
     * @param code  单位代码
     * @param types 类型
     * @return 结果
     */
    @Query(
            "select t from UnitEntity t where t.type in (:types) and t.unitCode like concat(:code,'%')")
    List<UnitEntity> findByTypeInAndCodeLike(
            @Param("types") List<String> types, @Param("code") String code);

    /**
     * 根据code和type字段查询
     *
     * @param cityCode 城市代码
     * @return 结果
     */
//    @Query(
//            "select t from UnitEntity t where t.type = '1' and t.unitCode like concat(:cityCode,'%') and t.unitCode not like concat(:cityCode,'00%')")
    @Query("select t2 from UnitEntity t2 where t2.type in ('1', '4') and t2.parentId = (select t1.id from UnitEntity t1 where t1.unitCode = :cityCode)")
    List<UnitEntity> findCountiesByCityCode(@Param("cityCode") String cityCode);

    /**
     * 根据code和type字段查询
     *
     * @param areaCode 区域代码
     * @param types    类型
     * @return 结果
     */
    @Query("select t from UnitEntity t where t.type in :types and t.areaCode = :areaCode")
    List<UnitEntity> findUnitsByAreaCodeAndTypes(
            @Param("areaCode") String areaCode, @Param("types") List<String> types);

    /**
     * 查询部门名称
     *
     * @param unitCode 部门code
     * @return 结果
     */
    @Query("select t.shortname from UnitEntity t where  t.unitCode = :unitCode")
    String getAreaNameByUnitCode(@Param("unitCode")String unitCode);
    /**
     * 查询子节点
     *
     * @param pid 父节点id
     * @return {@link UnitEntity}
     */
    List<UnitEntity> findAllByParentId(String pid);

    /**
     * 跟据unitCode查询派出所信息
     *
     * @param unitCode 部门code
     * @return 派出所名称
     */
    @Query(nativeQuery = true, value = "select unit_name  from T_UNIT_PCS where unit_code =:unitCode ")
    Map<String, String> findByAreaCode(@Param("unitCode") String unitCode);


    /**
     * 通过派出所部门code查询区县级出入境部门code
     *
     * @param pcsUnitCode   派出所code
     * @param sqsxUnitCodes 三区四县的出入境管理部门code
     * @return {@link String}
     */
    @Query("select t.unitCode from UnitEntity t "
        + "where t.areaCode = (select u.areaCode from UnitEntity u where u.unitCode=:pcsUnitCode) "
        + "and t.unitCode in (:sqsxUnitCodes)")
    String findCrjQxUnitCodeByPcsUnitCode(@Param("pcsUnitCode") String pcsUnitCode,
        @Param("sqsxUnitCodes") List<String> sqsxUnitCodes);

    /**
     * 根据类型查询单位
     *
     * @param unitName 单位名称
     * @return 单位
     */
    UnitEntity findByUnitName(String unitName);
}



