package com.trs.yq.police.subject.repository.impl;

import com.trs.hybase.client.TRSConnection;
import com.trs.hybase.client.TRSException;
import com.trs.hybase.client.TRSRecord;
import com.trs.hybase.client.TRSResultSet;
import com.trs.hybase.client.params.ConnectParams;
import com.trs.hybase.client.params.GeoDistanceFilterBuilder;
import com.trs.hybase.client.params.SearchParams;
import com.trs.yq.police.subject.domain.entity.CameraEntity;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.FaceHitEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.WarningCarTrajectoryVO;
import com.trs.yq.police.subject.repository.EventPersonRelationRepository;
import com.trs.yq.police.subject.repository.GroupRepository;
import com.trs.yq.police.subject.repository.HybaseDao;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.utils.DateUtil;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/11/23
 */
@Slf4j
@Component
public class HybaseDaoImpl implements HybaseDao {

    @Value("${com.trs.hybase.url}")
    private String url;
    @Value("${com.trs.hybase.user}")
    private String user;
    @Value("${com.trs.hybase.pwd}")
    private String pwd;
    private TRSConnection trsConnection;

    @Resource
    private EventPersonRelationRepository eventPersonRelationDao;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private PersonRepository personRepository;

    /**
     * 创建hybase数据库链接
     */
    @PostConstruct
    public void initSearchItem() {
        trsConnection = new TRSConnection(url, user, pwd, new ConnectParams());
    }


    /**
     * 查询海康视频探头数据
     *
     * @param lng      经度
     * @param lat      纬度
     * @param distance 距离
     */
    @Override
    public List<CameraEntity> getVideoCameras(String lng, String lat, Double distance) {
        List<CameraEntity> results = new ArrayList<>();
        try {
            SearchParams params = new SearchParams();
            params.setFilterBuilder(new GeoDistanceFilterBuilder("point").point(String.format("POINT(%s %s)", lng, lat))
                .distance(distance));
            TRSResultSet rs = trsConnection.executeSelectNoSort("hk.camera", "*:* -lng:0.0 -lat:0.0", 0, 20000, params);
            while (rs.moveNext()) {
                TRSRecord record = rs.get();
                CameraEntity camera = getCameraEntity(record);
                results.add(camera);
            }
        } catch (Exception exception) {
            log.error("query hybase failed!", exception);
            return Collections.emptyList();
        }
        return results;
    }

    @Override
    public CameraEntity getVideoCamera(String cameraId) {
        SearchParams params = new SearchParams();
        try {
            TRSResultSet rs = trsConnection.executeSelectNoSort("hk.camera", "service_id:" + cameraId, 0, 20000,
                params);
            if (rs.moveNext()) {
                TRSRecord record = rs.get();
                return getCameraEntity(record);
            }
        } catch (Exception exception) {
            log.error("query hybase failed!", exception);
        }
        return null;
    }

    @NotNull
    private CameraEntity getCameraEntity(TRSRecord record) throws TRSException {
        String name = record.getString("name");
        Double longitude = record.getDouble("lng");
        Double latitude = record.getDouble("lat");
        String serviceId = record.getString("service_id");
        String areaCode = record.getString("area_code");
        CameraEntity camera = new CameraEntity();
        camera.setLng(longitude);
        camera.setLat(latitude);
        camera.setDeviceId(serviceId);
        camera.setDeviceName(name);
        camera.setAreaCode(areaCode);
        return camera;
    }

    @Override
    public List<CameraEntity> getImageCameras() {
        return Collections.emptyList();
    }

    @Override
    public List<FaceHitEntity> getFaceHits(EventEntity event) {
        List<FaceHitEntity> results = new ArrayList<>();
        List<PersonEntity> personByEvent =
            Stream.concat(
                eventPersonRelationDao.findPersonByEventId(event.getId()).stream(),
                groupRepository.findAllByEventId(event.getId())
                    .stream()
                    .map(item -> personRepository.findAllByGroupId(item.getId()))
                    .flatMap(Collection::stream)).collect(Collectors.toList()
            );
        try {
            String condition = String.format("%s:[%s TO %s] AND ", "datetime",
                DateTimeFormatter.ofPattern("yyyyMMdd").format(event.getOccurrenceTime()),
                DateTimeFormatter.ofPattern("yyyyMMdd").format(event.getOccurrenceTime()));
            condition += String.format("%s#LIST:%s", "hit_person_id",
                personByEvent.stream().map(PersonEntity::getIdNumber).collect(Collectors.joining(",")));
            TRSResultSet rs = trsConnection.executeSelectNoSort("yt.face_hit1", condition, 0, 20000,
                new SearchParams());
            if (Objects.nonNull(rs)) {
                while (rs.moveNext()) {
                    TRSRecord record = rs.get();
                    String idNumber = record.getString("hit_person_id");
                    PersonEntity person = personByEvent.stream().filter(p -> p.getIdNumber().equals(idNumber))
                        .findFirst()
                        .orElse(null);
                    if (Objects.isNull(person)) {
                        continue;
                    }
                    LocalDateTime dateTime = DateUtil.utcToLocalDateTime(record.getDate("datetime").getTime());
                    Double lng = record.getDouble("camera_lng");
                    Double lat = record.getDouble("camera_lat");
                    FaceHitEntity faceHitEntity = new FaceHitEntity();
                    faceHitEntity.setIdNumber(idNumber);
                    faceHitEntity.setPersonId(person.getId());
                    faceHitEntity.setPersonName(person.getName());
                    faceHitEntity.setLat(lat);
                    faceHitEntity.setLng(lng);
                    faceHitEntity.setDateTime(dateTime);
                    results.add(faceHitEntity);
                }
            }
        } catch (Exception exception) {
            log.error("query hybase failed!", exception);
            return Collections.emptyList();
        }
        return results;
    }

    @Override
    public List<WarningCarTrajectoryVO> getWarningCarTrajectory(String carNumber, String warningLevel, TimeParams timeParams,Integer maxRecord) {
        List<WarningCarTrajectoryVO> results = new ArrayList<>();
        SearchParams params = new SearchParams();
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            String startTime = timeParams.getBeginTime().format(formatter);
            String endTime = timeParams.getEndTime().format(formatter);
            String where = String.format("PlateNo:%s AND PassTime:[%s TO %s] -LONGITUDE:0.0 -LATITUDE:0.0", carNumber, startTime, endTime);
            params.setSortMethod("-PassTime");
            TRSResultSet rs = trsConnection.executeSelect("hk.TrafficInfo", where, 0, maxRecord, params);
            if (rs.moveNext()) {
                while (rs.moveNext()) {
                    TRSRecord record = rs.get();
                    final WarningCarTrajectoryVO vo = new WarningCarTrajectoryVO();
                    LocalDateTime dateTime = DateUtil.utcToLocalDateTime(record.getDate("PassTime").getTime());
                    Double lng = record.getDouble("LONGITUDE");
                    Double lat = record.getDouble("LATITUDE");
                    String name = record.getString("DZ");
                    vo.setName(name);
                    vo.setMonitorLevel(warningLevel);
                    vo.setLat(lat);
                    vo.setLng(lng);
                    vo.setDate(dateTime);
                    vo.setImgs(Collections.singletonList(record.getString("StorageUrl1")));
                    results.add(vo);
                }
            }
        } catch (Exception exception) {
            log.error("query hybase failed!", exception);
        }
        return results;
    }
}
