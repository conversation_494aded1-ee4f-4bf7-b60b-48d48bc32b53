package com.trs.yq.police.subject.domain.vo;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * 分页结果 因为部分接口已经使用jpa的pageable 为了统一性 不建议使用
 *
 * @param <T> 封装的分页查询结果类型
 * <AUTHOR>
 */
@Getter
@Setter
public class PageResult<T> {

    private int pageNumber;
    private int pageSize;
    private long total;


    private List<T> items;


    private PageResult() {

    }

    /**
     * 将Page对象转换为PageResult
     *
     * @param page {@link Page}
     * @param <T>  泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> of(Page<T> page) {

        final PageResult<T> pageResult = new PageResult<>();

        pageResult.setItems(page.getContent());
        pageResult.setPageNumber(page.getNumber() + 1);
        pageResult.setTotal(page.getTotalElements());
        pageResult.setPageSize(page.getPageable().getPageSize());

        return pageResult;
    }

    /**
     * 由List构造PageResult
     *
     * @param items      {@link List}
     * @param pageNumber 页号
     * @param total      总数
     * @param pageSize   每页大小
     * @param <T>        泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> of(List<T> items,
                                       int pageNumber,
                                       long total,
                                       int pageSize) {

        final PageResult<T> pageResult = new PageResult<>();

        pageResult.setItems(items);
        pageResult.setPageNumber(pageNumber);
        pageResult.setTotal(total);
        pageResult.setPageSize(pageSize);

        return pageResult;
    }

    /**
     * 创建空的PageResult
     *
     * @param pageNumber 页码
     * @param pageSize   每页大小
     * @param <T>        泛型参数
     * @return {@link PageResult}
     */
    public static <T> PageResult<T> empty(int pageNumber, int pageSize) {
        return PageResult.of(new ArrayList<>(),
                pageNumber,
                0,
                pageSize);
    }
}
