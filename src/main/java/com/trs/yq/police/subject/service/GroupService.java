package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 群体业务层接口
 *
 * <AUTHOR>
 * @date 2021/8/3 9:31
 */
@Validated
public interface GroupService {

    /**
     * 群体列表查询
     *
     * @param subjectId 专题id
     * @param request   请求参数
     * @return 列表请求结果
     */
    PageResult<GroupListVO> listGroups(@NotBlank(message = "专题编号缺失") String subjectId,
                                       @NotNull(message = "列表查询参数缺失") ListRequestVO request);


    /**
     * 新增群体
     *
     * @param groupVO 群体信息
     * @return 群体主键
     */
    String createGroup(@NotNull(message = "群体基础信息缺失") GroupVO groupVO);

    /**
     * 批量删除
     *
     * @param groupIds 群体Id
     */
    void deleteGroups(@NotEmpty(message = "群体主键缺失") List<String> groupIds);

    /**
     * 详情页群体信息
     *
     * @param id 群体Id
     * @return 群体信息
     */
    GroupVO getGroup(@NotBlank(message = "群体主键缺失") String id);

    /**
     * 修改群体信息
     *
     * @param id      群体主键
     * @param groupVO 群体信息
     */
    void updateGroup(String id, @NotNull(message = "群体基础信息缺失") GroupVO groupVO);


    /**
     * 在群体添加线索
     *
     * @param groupClueRelationVO {@link GroupClueRelationVO}
     */
    void updateGroupClueRelation(GroupClueRelationVO groupClueRelationVO);

    /**
     * 查询该群体对应的线索
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return 线索列表
     */
    PageResult<GroupRelatedClueVO> getGroupRelatedClue(String groupId, PageParams pageParams);

    /**
     * 不分页查询群体下关联的线索列表
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedClueVO}
     */
    List<GroupRelatedClueVO> getGroupRelatedClue(String groupId);

    /**
     * 取消群体和线索关联
     *
     * @param groupClueRelationId 群体id
     */
    void deleteGroupClueRelation(String groupClueRelationId);

    /**
     * 在群体添加人员
     *
     * @param groupPersonRelationVO {@link GroupPersonRelationVO}
     */
    void updateGroupPersonRelation(GroupPersonRelationVO groupPersonRelationVO);

    /**
     * 查询该群体对应的人员
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return 群体人员关联列表
     */
    PageResult<GroupRelatedPersonVO> getGroupRelatedPerson(String groupId, RequestParams pageParams);

    /**
     * 不分页查询群体下关联的人员列表
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedPersonVO}
     */
    List<GroupRelatedPersonVO> getGroupRelatedPerson(String groupId);

    /**
     * 取消群体和人员关联
     *
     * @param groupPersonRelationId 群体id
     */
    void cancelGroupPersonRelation(String groupPersonRelationId);

    /**
     * 更新群体人员活跃程度
     *
     * @param module 编辑模块
     * @param vo     {@link GroupPersonActivityLevelVO}
     */
    void updateGroupPersonActivityLevel(ModuleEnum module, GroupPersonActivityLevelVO vo);

    /**
     * 获取该专题下所有的群体类别
     *
     * @param subjectId 专题id
     * @return 群体类别信息
     */
    List<IdNameVO> getGroupTypes(@NotBlank(message = "专题id缺失") String subjectId);

    /**
     * 关联群体对话框分页查询
     *
     * @param request {@link DialogGroupListRequestVO}
     * @return {@link DialogGroupListVO}
     */
    PageResult<DialogGroupListVO> getDialogGroupListVO(DialogGroupListRequestVO request);

    /**
     * [群体档案] 根据群体id查询上级群体列表
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return 线索列表
     */
    PageResult<GroupParentVO> getParentGroupList(String groupId, PageParams pageParams);

    /**
     * 更新群体和上级群体关联
     *
     * @param groupId   群体id
     * @param parentIds 上级群体id列表
     */
    void updateGroupParentRelation(String groupId, List<String> parentIds);

    /**
     * 取消群体和上级群体关联
     *
     * @param relationId 关联id
     */
    void deleteGroupParentRelation(String relationId);

    /**
     * 不分页查询群体已经关联的上级群体列表
     *
     * @param groupId 群体id
     * @return {@link GroupParentVO}
     */
    List<GroupParentVO> getParentGroups(String groupId);

    /**
     * 不分页查询群体关联的事件
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedEventVO}
     */
    List<GroupRelatedEventVO> getGroupRelatedEvent(String groupId);

    /**
     * 分页查询群体关联的事件
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return {@link GroupRelatedEventVO}
     */
    PageResult<GroupRelatedEventVO> getGroupRelatedEvent(String groupId, PageParams pageParams);

    /**
     * 批量更新群体-事件关联
     *
     * @param vo {@link GroupEventsRelationVO}
     */
    void updateGroupEventRelations(GroupEventsRelationVO vo);

    /**
     * 删除群体和事件的关联
     *
     * @param relationId 关联id
     */
    void deleteGroupEventRelation(String relationId);

    /**
     * 群体已关联敏感时间节点
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return {@link SensitiveTimeListVO}
     */
    PageResult<GroupRelatedTimeVO> getSensitiveList(String groupId, PageParams pageParams);

    /**
     * 更新群体-敏感时间节点关联关系
     *
     * @param vo {@link GroupTimeRelationRequestVO}
     */
    void updateGroupTimeRelations(GroupTimeRelationRequestVO vo);

    /**
     * 删除单个关联关系
     *
     * @param relationId 关联id
     */
    void deleteGroupTimeRelation(String relationId);

    /**
     * 群体关联敏感时间节点（不分页）
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedTimeVO}
     */
    List<GroupRelatedTimeVO> getGroupRelatedTime(String groupId);
}
