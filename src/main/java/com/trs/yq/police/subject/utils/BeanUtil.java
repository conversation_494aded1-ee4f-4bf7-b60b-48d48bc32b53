package com.trs.yq.police.subject.utils;

import org.apache.commons.lang3.ArrayUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * Bean 工具类
 *
 * <AUTHOR>
 * @date 2021/3/5
 */
@Component
public class BeanUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    private BeanUtil() {
    }

    /**
     * 获取applicationContext
     *
     * @return 上下文对象
     */
    public static synchronized ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    @Override
    public synchronized void setApplicationContext(@NotNull ApplicationContext applicationContext) {
        BeanUtil.applicationContext = applicationContext;
    }

    /**
     * 上下文刷新事件回调函数，将新的上下文内容覆盖掉原来的应用程序上下文
     *
     * @param event 上下文刷新事件
     */
    @EventListener
    public synchronized void handleContextRefresh(ContextRefreshedEvent event) {
        BeanUtil.applicationContext = event.getApplicationContext();
    }

    /**
     * 通过name获取 Bean
     *
     * @param name bean的名称
     * @return bean对象
     */
    public static synchronized Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    /**
     * 通过name获取 Bean
     *
     * @param <T>   返回值泛型
     * @param clazz 参数类型
     * @return 泛型对象
     */
    public static synchronized <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    /**
     * 拷贝属性，忽略null属性
     *
     * @param src    源对象
     * @param target 目标对象
     */
    public static void copyPropertiesIgnoreNull(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    /**
     * 拷贝属性，忽略null属性
     *
     * @param src          源对象
     * @param target       目标对象
     * @param ignoreFields 舍弃的属性
     */
    public static void copyPropertiesIgnoreNull(Object src, Object target, String... ignoreFields) {
        BeanUtils.copyProperties(src, target,
                ArrayUtils.addAll(
                        ArrayUtils.nullToEmpty(ignoreFields),
                        ArrayUtils.nullToEmpty(getNullPropertyNames(src))));
    }

    private static String[] getNullPropertyNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) {
                emptyNames.add(pd.getName());
            }
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
}
