package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 操作权限实体类
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_ADMIN_OPERATION")
public class OperationEntity implements Serializable {

    private static final long serialVersionUID = -8525273232706717376L;

    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;
    /**
     * 操作名称
     */
    private String name;
    /**
     * 请求方法
     */
    private String requestMethod;
    /**
     * 请求url
     */
    private String url;
    /**
     * 其他参数 以json字符串格式存储
     */
    private String params;
    /**
     * 微服务模块名称
     */
    private String service;
}
