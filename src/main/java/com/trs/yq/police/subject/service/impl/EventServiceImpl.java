package com.trs.yq.police.subject.service.impl;

import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.EventListPredicatesBuilder;
import com.trs.yq.police.subject.constants.enums.*;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.operation.OperationLogServiceImpl;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.EventService;
import com.trs.yq.police.subject.service.UserRoleService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_EVENT_SOURCE;
import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_EVENT;
import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_PERSON;
import static com.trs.yq.police.subject.service.impl.ClueServiceImpl.*;


/**
 * <AUTHOR>
 * @date 2021/12/13 16:47
 */
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class EventServiceImpl implements EventService {
    @Resource
    private EventRepository eventRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private BattleRecordRepository battleRecordRepository;
    @Resource
    private BattleCommandRepository battleCommandRepository;
    @Resource
    private EventFileRelationRepository eventFileRelationRepository;
    @Resource
    private EventPersonRelationRepository eventPersonRelationRepository;
    @Resource
    private EventGroupRelationRepository eventGroupRelationRepository;
    @Resource
    private SubjectRepository subjectRepository;
    @Resource
    private DictRepository dictRepository;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private UserRoleService userRoleService;
    @Resource
    private ClueServiceImpl clueService;
    @Resource
    private EventLabelRelationRepository eventLabelRelationRepository;
    @Resource
    private LabelRepository labelRepository;

    private static final String EVENT_SRCTABLE = "system.t_event";
    /**
     * 事件上报状态:未上报
     */
    private static final String INITIAL_REPORT = "0";
    /**
     * 事件处置状态：处置中
     */
    private static final String UNDER_DISPOSAL = "1";

    private static final String REPORT_TO_MUNICIPAL_STABILITY_CLASS = "3";
    private static final String REPORT_TO_DISTRICT_STABILITY_CLASS = "1";

    public static final String JPG = ".jpg";
    public static final String JPEG = ".jpeg";
    public static final String PNG = ".png";

    @Override
    public EventVO getEventBasicInfo(String eventId) {
        final EventEntity eventEntity = eventRepository.findById(eventId).orElse(null);
        if (Objects.isNull(eventEntity)) {
            throw new NoSuchElementException("没有找到该条事件！");
        }
        EventVO eventVO = new EventVO();
        BeanUtil.copyPropertiesIgnoreNull(eventEntity, eventVO);
        eventVO.setCreateTime(eventEntity.getCrTime());
        List<String> codeList = new ArrayList<>();
        if (StringUtils.isNotBlank(eventEntity.getControlDept())) {
            UnitEntity unit = unitRepository.findByUnitCode(eventEntity.getControlDept());
            UnitEntity parentUnit = unitRepository.findByUnitCode(unit.getAreaCode() + "000000");
            if (Objects.nonNull(parentUnit)) {
                codeList.add(parentUnit.getUnitCode());
            }
            codeList.add(eventEntity.getControlDept());
            eventVO.setControlDeptName(unit.getUnitName());
        }
        eventVO.setControlDeptCode(codeList);
        LabelEntity label = labelRepository.findByEventId(eventId);
        if (Objects.nonNull(label)) {
            eventVO.setType(new IdNameVO(label.getId(), label.getName()));
        }
        eventVO.setRelatedPersonCount(eventPersonRelationRepository.findAllByEventId(eventId).size());
        return eventVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String addEvent(EventVO eventVO) {
        EventEntity eventEntity = new EventEntity();
        BeanUtil.copyPropertiesIgnoreNull(eventVO, eventEntity);
        if (Objects.nonNull(eventVO.getControlDeptCode()) && !eventVO.getControlDeptCode().isEmpty()) {
            //管控单位传的数组，第一个是父单位code
            eventEntity.setControlDept(eventVO.getControlDeptCode().get(1));
        }
        eventEntity.setReportStatus(INITIAL_REPORT);
        eventEntity.setDisposalStatus(UNDER_DISPOSAL);
        eventEntity.setSource("0");
        eventEntity.setSthyType("手动创建");
        String id = eventRepository.save(eventEntity).getId();

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.EVENT_ARCHIVE_MANAGE)
                .newObj(JsonUtil.toJsonString(eventVO))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(id)
                .desc("新增事件档案")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }

        //记录类型关联
        EventLabelRelationEntity eventLabelRelationEntity = new EventLabelRelationEntity();
        eventLabelRelationEntity.setEventId(eventEntity.getId());
        eventLabelRelationEntity.setLabelId(eventVO.getType().getId());
        eventLabelRelationRepository.save(eventLabelRelationEntity);

        return id;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateEvent(EventVO eventVO) {
        EventEntity eventEntity = eventRepository.findById(eventVO.getId()).orElse(null);
        if (Objects.isNull(eventEntity)) {
            throw new ParamValidationException("该事件不存在，请刷新重试！！");
        }
        final EventVO oldVO = getEventBasicInfo(eventVO.getId());

        BeanUtil.copyPropertiesIgnoreNull(eventVO, eventEntity);
        eventEntity.setLat(eventVO.getLat());
        eventEntity.setLng(eventVO.getLng());
        eventEntity.setAddress(eventVO.getAddress());
        eventEntity.setControlDeptName(eventVO.getControlDeptName());
        if (Objects.nonNull(eventVO.getControlDeptCode()) && !eventVO.getControlDeptCode().isEmpty()) {
            //管控单位传的数组，第一个是父单位code
            eventEntity.setControlDept(eventVO.getControlDeptCode().get(1));
        }else {
            eventEntity.setControlDept(null);
        }
        eventEntity.setClaim(eventVO.getClaim());
        eventRepository.save(eventEntity);

        //更新事件类别关联关系
        eventLabelRelationRepository.deleteAllByEventId(eventEntity.getId());
        EventLabelRelationEntity eventLabelRelationEntity = new EventLabelRelationEntity();
        eventLabelRelationEntity.setEventId(eventEntity.getId());
        eventLabelRelationEntity.setLabelId(eventVO.getType().getId());
        eventLabelRelationRepository.save(eventLabelRelationEntity);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.EVENT_BASIC_INFO)
                .oldObj(JsonUtil.toJsonString(oldVO))
                .newObj(JsonUtil.toJsonString(eventVO))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(eventVO.getId())
                .desc("修改基本信息")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteEvents(List<String> eventIds) {
        eventIds.forEach(eventId -> {
            //移除线索-文件关联
            eventFileRelationRepository.removeAllByEventId(eventId);
            //移除线索-人员关联关系
            eventPersonRelationRepository.removeAllByEventId(eventId);
            //移除线索-群体关联关系
            eventGroupRelationRepository.removeAllByEventId(eventId);
            //移除事件-标签关联
            eventLabelRelationRepository.deleteAllByEventId(eventId);
        });
        //执行删除
        eventRepository.deleteAllById(eventIds);
    }

    @Override
    public PageResult<EventRelatedPersonVO> getRelatedPersonList(String eventId, PageParams pageParams) {
        Page<PersonEntity> personEntities = personRepository.findAllByEventId(eventId, pageParams.toPageable());
        EventEntity eventEntity = eventRepository.getById(eventId);
        List<EventRelatedPersonVO> eventRelatedPersonVO = personEntities.stream()
                .map(personEntity -> EventRelatedPersonVO.of(personEntity, eventEntity))
                .collect(Collectors.toList());
        return PageResult.of(eventRelatedPersonVO, pageParams.getPageNumber(), personEntities.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public PageResult<EventRelateGroupVO> getRelatedGroupList(String eventId, PageParams pageParams) {
        Page<GroupEntity> groupEntities = groupRepository.findAllByEventId(eventId, pageParams.toPageable());
        List<EventRelateGroupVO> items = groupEntities.getContent().stream()
                .map(group -> EventRelateGroupVO.of(group, eventId))
                .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), groupEntities.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public List<FileInfoVO> getEventFileInfo(String eventId) {
        List<FileStorageEntity> files = fileStorageRepository.findAllByEventId(eventId);
        return files.stream().map(FileInfoVO::of).collect(Collectors.toList());
    }

    @Override
    public List<BattleRecordCommandListVO> getBattleRecordList(String eventId) {
        return battleRecordRepository.findRecordById(EVENT_SRCTABLE, eventId)
                .stream()
                .map(BattleRecordEntity::of)
                .collect(Collectors.toList());
    }

    @Override
    public List<BattleRecordCommandListVO> getBattleCommandList(String eventId) {
        return battleCommandRepository.findCommandById(EVENT_SRCTABLE, eventId)
                .stream()
                .map(BattleRecordCommandListVO::of)
                .collect(Collectors.toList());
    }

    @Override
    public List<EventRelatedPersonVO> getPersonsFromEvent(String eventId) {
        List<PersonEntity> personEntities = personRepository.findAllByEventId(eventId);
        EventEntity eventEntity = eventRepository.getById(eventId);
        return personEntities.stream()
                .map(personEntity -> EventRelatedPersonVO.of(personEntity, eventEntity))
                .collect(Collectors.toList());
    }

    @Override
    public List<EventRelateGroupVO> getGroupsFromEvent(String eventId) {
        List<GroupEntity> groups = groupRepository.findAllByEventId(eventId);
        return groups.stream()
                .map(group -> EventRelateGroupVO.of(group, eventId))
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupEventRelation(EventGroupRelationRequestVO vo) {
        String eventId = vo.getEventId();
        List<String> newRelatedIds = vo.getGroupIds();
        List<String> oldRelatedIds = eventGroupRelationRepository.findAllByEventId(eventId).stream()
                .map(EventGroupRelationEntity::getGroupId)
                .collect(Collectors.toList());
        //旧值和新值的并集
        List<String> relatedPersonIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedIds, newRelatedIds);

        //处理事件-群体关联关系变更
        eventGroupRelationRepository.deleteAllByEventId(vo.getEventId());
        List<EventGroupRelationEntity> eventGroupRelationEntities = vo.getGroupIds().stream().map(groupId -> {
            EventGroupRelationEntity eventGroupRelationEntity = new EventGroupRelationEntity();
            eventGroupRelationEntity.setEventId(vo.getEventId());
            eventGroupRelationEntity.setGroupId(groupId);
            return eventGroupRelationEntity;
        }).collect(Collectors.toList());
        eventGroupRelationRepository.saveAll(eventGroupRelationEntities);

        //存储操作记录
        relatedPersonIds.forEach(groupId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.EVENT_RELATED_GROUP)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(eventId)
                    .desc("修改事件-群体关联")
                    .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                    .build();

            if (oldRelatedIds.contains(groupId)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", groupId)));
            }
            if (newRelatedIds.contains(groupId)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", groupId)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateEventPersonRelations(EventPersonRelationRequestVO vo) {
        String eventId = vo.getEventId();
        List<String> newRelatedIds = vo.getPersonIds();
        List<String> oldRelatedIds = eventPersonRelationRepository.findAllByEventId(eventId).stream()
                .map(EventPersonRelationEntity::getPersonId)
                .collect(Collectors.toList());
        //旧值和新值的并集
        List<String> relatedPersonIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedIds, newRelatedIds);

        //处理事件-人员关联关系变更
        oldRelatedIds.stream().filter(personId -> !newRelatedIds.contains(personId))
                .forEach(personId -> eventPersonRelationRepository.deleteByPersonIdAndEventId(personId, eventId));
        List<EventPersonRelationEntity> eventPersonRelationEntities = newRelatedIds.stream().filter(personId -> !oldRelatedIds.contains(personId))
                .map(personId -> new EventPersonRelationEntity(vo.getEventId(), personId, vo.getBehaviours()))
                .collect(Collectors.toList());
        eventPersonRelationRepository.saveAll(eventPersonRelationEntities);

        //存储操作记录
        relatedPersonIds.forEach(personId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.EVENT_RELATED_PERSON)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(eventId)
                    .desc("修改事件-人员关联")
                    .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                    .build();

            if (oldRelatedIds.contains(personId)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("personId", personId)));
            }
            if (newRelatedIds.contains(personId)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("personId", personId)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void removePersonFromEvent(ModuleEnum module, String relationId) {
        EventPersonRelationEntity relation = eventPersonRelationRepository.findById(relationId).orElse(null);
        if (Objects.isNull(relation)) {
            throw new NoSuchElementException("未找到该关系！");
        }
        eventPersonRelationRepository.deleteById(relationId);

        final OperationLogRecord logRecord;
        if (module.equals(ModuleEnum.PERSON)) {
            //存储操作记录
            logRecord = OperationLogRecord.builder()
                    .operator(Operator.DE_RELATE)
                    .module(OperateModule.PERSON_RELATED_EVENT)
                    .oldObj(JsonUtil.toJsonString(ImmutableMap.of("eventId", relation.getEventId())))
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(relation.getPersonId())
                    .desc("删除人员-事件关联")
                    .targetObjectType(OPERATION_LOG_TARGET_PERSON)
                    .build();
        } else {
            logRecord = OperationLogRecord.builder()
                    .operator(Operator.DE_RELATE)
                    .module(OperateModule.EVENT_RELATED_PERSON)
                    .oldObj(JsonUtil.toJsonString(ImmutableMap.of("personId", relation.getPersonId())))
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(relation.getEventId())
                    .desc("删除事件-人员关联")
                    .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                    .build();
        }
        // 记录操作
        operationLogHandler.publishEvent(logRecord);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void removeGroupFromEvent(String relationId) {
        EventGroupRelationEntity relation = eventGroupRelationRepository.findById(relationId).orElse(null);
        if (Objects.isNull(relation)) {
            throw new NoSuchElementException("未找到该关系！");
        }
        eventGroupRelationRepository.deleteById(relationId);

        //存储操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DE_RELATE)
                .module(OperateModule.EVENT_RELATED_GROUP)
                .oldObj(JsonUtil.toJsonString(ImmutableMap.of("groupId", relation.getGroupId())))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(relation.getEventId())
                .desc("删除事件-群体关联")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        // 记录操作
        operationLogHandler.publishEvent(logRecord);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addEventFileRelation(String eventId, List<AttachmentVO> attachmentVOList) {
        attachmentVOList.forEach(attachment -> {
            EventFileRelationEntity eventFileRelationEntity = new EventFileRelationEntity();
            eventFileRelationEntity.setEventId(eventId);
            eventFileRelationEntity.setFileStorageId(attachment.getId());
            saveFileTypeAndModule(eventFileRelationEntity, attachment);
            eventFileRelationRepository.save(eventFileRelationEntity);

            //存储操作记录
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.UPLOAD)
                    .module(OperateModule.EVENT_FILE)
                    .newObj(JsonUtil.toJsonString(attachment))
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(eventId)
                    .desc("上传事件材料")
                    .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                    .build();
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteClueFileRelation(String eventId, String fileId) {
        eventFileRelationRepository.deleteByEventIdAndFileStorageId(eventId, fileId);

        //存储操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.EVENT_FILE)
                .oldObj(JsonUtil.toJsonString(ImmutableMap.of("fileId", fileId)))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(eventId)
                .desc("删除事件-文件关联")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        // 记录操作
        operationLogHandler.publishEvent(logRecord);
    }

    @Override
    public PageResult<EventListVO> getEventList(String subjectId, ListRequestVO request) {
        Specification<EventEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = EventListPredicatesBuilder.buildListFilterPredicates(subjectId, request.getFilterParams(), root, criteriaBuilder).stream()
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(request.getSearchParams())) {
                predicates.addAll(EventListPredicatesBuilder.buildSearchPredicates(request.getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        //排序
        final Pageable pageable = request.getPageParams().toPageable(Sort.by(Sort.Direction.DESC, "upTime"));

        Page<EventEntity> eventList = eventRepository.findAll(specification, pageable);
        List<EventListVO> items = eventList.getContent().stream()
                .map(EventListVO::of)
                .collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), eventList.getTotalElements(), request.getPageParams().getPageSize());
    }

    private void saveFileTypeAndModule(EventFileRelationEntity relation, AttachmentVO attachment) {
        String url = attachment.getUrl();
        if (url.endsWith(JPG) || url.endsWith(JPEG) || url.endsWith(PNG)) {
            relation.setType(FileTypeEnum.IMAGE.getCode());
            relation.setModule(EventModuleEnum.Event_MESSAGE_PHOTO.getCode());
        } else {
            relation.setType(FileTypeEnum.OFFICE.getCode());
            relation.setModule(EventModuleEnum.Event_MESSAGE_WORD.getCode());
        }
    }

    @Override
    public void downLoadExcel(HttpServletResponse response, ExportParams request, String subjectId) throws IOException {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElseThrow(IOException::new);
        String fileName = String.format("%s-事件档案-%s.xlsx", subjectEntity.getName(), LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<EventEntity> eventList =request.getIsAll()
            ?eventRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = EventListPredicatesBuilder.buildListFilterPredicates(subjectId, request.getListParams().getFilterParams(), root, criteriaBuilder).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(request.getListParams().getSearchParams())) {
                predicates.addAll(EventListPredicatesBuilder.buildSearchPredicates(request.getListParams().getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));})
            :eventRepository.findAllByIdIn(request.getIds());
        List<EventExportListVO> vos = new ArrayList<>();
        eventList.forEach(event -> {
            EventExportListVO vo = new EventExportListVO();
            vo.setTitle(event.getTitle());
            vo.setSource(dictRepository.findByTypeAndCode(DICT_TYPE_EVENT_SOURCE, event.getSource()).getName());
            LabelEntity eventType = labelRepository.findByEventId(event.getId());
            if (Objects.nonNull(eventType)) {
                vo.setEmergencyLevel(eventType.getName());
            }
            vo.setAddress(event.getAddress());
            vo.setOccurrenceTime(event.getOccurrenceTime().format(DATE_TIME_FORMATTER));
            vo.setAppealPlace(event.getAppealPlace());
            List<GroupEntity> groupEntities = groupRepository.findAllByEventId(event.getId());
            vo.setGroupNames(groupEntities.stream().map(GroupEntity::getName).collect(Collectors.joining(",")));
            vos.add(vo);
        });
        EasyExcel.write(response.getOutputStream(), EventExportListVO.class)
                .registerWriteHandler(new CustomCellWriteHandler())
                .includeColumnFiledNames(request.getFieldNames())
                .sheet()
                .doWrite(vos);
    }

    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getEventListProperty());
    }

    @Override
    public ButtonVO getButton(String eventId) {
        ButtonVO buttonVO = new ButtonVO();
        buttonVO.setBattleRecord(true);
        buttonVO.setBattleCommand(true);
        buttonVO.setDisposalCompleted(true);
        buttonVO.setReport(true);
        buttonVO.setNotDispose(true);
        return buttonVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public ReportInfoVO reportToSuperior(String eventId, LoginUser currentUser) {
        EventEntity eventEntity = eventRepository.findById(eventId).orElseThrow(() -> new ParamValidationException("线索不存在"));
        String reportStatus = eventEntity.getReportStatus();
        final String userId = currentUser.getId();
        final String crDeptCode = eventEntity.getCrDeptCode();
        final String departmentIdPrefix = StringUtil.getDepartmentIdPrefix(currentUser.getUnitCode());
        ReportInfoVO reportInfoVO = new ReportInfoVO();
        if (!crDeptCode.startsWith(departmentIdPrefix)) {
            reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
            reportInfoVO.setReportInfo("无法上报非本区县线索，请核实后操作！");
            return reportInfoVO;
        }
        switch (reportStatus) {
            case INITIAL_REPORT:
                // 创建 => 创建人员可以上报
                if (!userId.equalsIgnoreCase(eventEntity.getCrBy())) {
                    reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                    reportInfoVO.setReportInfo("上报失败，当前用户非创建用户，请核实后操作！");
                } else {
                    reportInfoVO = clueService.generateReportInfo(userId, departmentIdPrefix);
                    EventEntity event = eventRepository.findById(eventId)
                            .orElseThrow(() -> new ParamValidationException("事件不存在!"));
                    event.setDisposalStatus(UNDER_DISPOSAL);
                    event.setReportStatus(reportInfoVO.getReportStatus());
                    eventRepository.save(event);

                    //记录操作日志
                    final OperationLogRecord logRecord = OperationLogRecord.builder()
                            .operator(Operator.REPORT)
                            .module(OperateModule.EVENT_ARCHIVE_MANAGE)
                            .newObj(JsonUtil.toJsonString(ImmutableMap.of("report", reportInfoVO.getReportInfo())))
                            .currentUser(AuthHelper.getCurrentUser())
                            .primaryKey(eventId)
                            .desc("事件上报")
                            .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                            .build();
                    if (Objects.nonNull(operationLogHandler)) {
                        // 转发操作日志至队列
                        operationLogHandler.publishEvent(logRecord);
                    }
                }
                return reportInfoVO;
            case REPORT_TO_DISTRICT_STABILITY_CLASS:
                // 区县维稳专班上报至市局维稳专班
                if (userRoleService.checkUserNotContainsRole(userId, DISTRICT_STABILITY_CLASS)) {
                    reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                    reportInfoVO.setReportInfo("上报失败。已报至区县维稳专班，非区县维稳专班不可继续上报!");
                } else {
                    eventRepository.updateReportStatusByEventId(eventId, REPORT_TO_MUNICIPAL_STABILITY_CLASS);
                    reportInfoVO.setIsSuccess(ReportInfoVO.SUCCESS);
                    reportInfoVO.setReportInfo("上报成功。已上报至市局维稳专班！");

                    //记录操作日志
                    final OperationLogRecord logRecord = OperationLogRecord.builder()
                            .operator(Operator.REPORT)
                            .module(OperateModule.EVENT_ARCHIVE_MANAGE)
                            .newObj(JsonUtil.toJsonString(ImmutableMap.of("report", "上报至区县指挥中心")))
                            .currentUser(AuthHelper.getCurrentUser())
                            .primaryKey(eventId)
                            .desc("事件上报")
                            .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                            .build();
                    if (Objects.nonNull(operationLogHandler)) {
                        // 转发操作日志至队列
                        operationLogHandler.publishEvent(logRecord);
                    }
                }
                return reportInfoVO;
            default:
                // 市局维稳专班不可上报
                reportInfoVO.setIsSuccess(ReportInfoVO.FAILURE);
                reportInfoVO.setReportInfo("上报失败。市局维稳专班不可上报！");
        }
        return reportInfoVO;
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void changeDisposeStatus(String eventId, String status) {
        EventEntity event = eventRepository.findById(eventId)
                .orElseThrow(() -> new ParamValidationException("事件不存在!"));
        event.setDisposalStatus(status);
        eventRepository.save(event);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DISPOSE)
                .module(OperateModule.EVENT_ARCHIVE_MANAGE)
                .newObj(JsonUtil.toJsonString(ImmutableMap.of("dispose", status)))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(eventId)
                .desc("事件处置")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public PageResult<DialogEventListVO> getDialogEventList(DialogEventListRequestVO request) {
        String subjectId = request.getOtherParams().getSubjectId();
        String eventTypeId = request.getOtherParams().getEventTypeId();
        String createDeptId = request.getOtherParams().getCreateDeptId();
        String searchValue = Objects.isNull(request.getSearchParams()) ? null : request.getSearchParams().getSearchValue();
        Pageable pageable = request.getPageParams().toPageable();
        Page<EventEntity> eventList = eventRepository.findAllBySubjectId(subjectId, eventTypeId, StringUtil.getPoliceStationPrefix(createDeptId), searchValue, pageable);
        List<DialogEventListVO> items = eventList.stream().map(DialogEventListVO::of).collect(Collectors.toList());
        return PageResult.of(items,
                request.getPageParams().getPageNumber(),
                eventList.getTotalElements(),
                request.getPageParams().getPageSize());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateBehaviour(String eventId, PersonEventBehaviourVO personEventBehaviourVO) {
        EventPersonRelationEntity eventPersonRelationEntity = eventPersonRelationRepository.findById(personEventBehaviourVO.getRelationId())
                .orElseThrow(() -> new ParamValidationException("关联关系不存在!"));
        String oldObjString = JsonUtil.toJsonString(
                ImmutableMap.of(
                        "personId", eventPersonRelationEntity.getPersonId(),
                        "behaviour", eventPersonRelationEntity.getBehaviors()));
        eventPersonRelationEntity.setBehaviors(personEventBehaviourVO.getBehaviours());
        eventPersonRelationRepository.save(eventPersonRelationEntity);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.EVENT_RELATED_PERSON)
                .oldObj(oldObjString)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(eventId)
                .desc("更新涉及人员涉事行为")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePersonImages(String eventId, EventPersonImagesVO personImagesVO) {
        List<String> fileIdList = eventFileRelationRepository.findAllByEventIdAndModuleAndRecordId(eventId, EventModuleEnum.Event_PERSON_PHOTO.getCode(), personImagesVO.getRelationId())
                .stream().map(EventFileRelationEntity::getFileStorageId).collect(Collectors.toList());
        String oldObjString = JsonUtil.toJsonString(
                ImmutableMap.of(
                        "personId", eventPersonRelationRepository.findById(personImagesVO.getRelationId()).orElse(new EventPersonRelationEntity()).getPersonId(),
                        "image", fileIdList));

        eventFileRelationRepository.deleteByEventIdAndModuleAndRecordId(eventId, EventModuleEnum.Event_PERSON_PHOTO.getCode(), personImagesVO.getRelationId());
        List<EventFileRelationEntity> entities = personImagesVO.getImages().stream().map(image -> {
            EventFileRelationEntity eventFileRelationEntity = new EventFileRelationEntity();
            eventFileRelationEntity.setEventId(eventId);
            eventFileRelationEntity.setModule(EventModuleEnum.Event_PERSON_PHOTO.getCode());
            eventFileRelationEntity.setType(FileTypeEnum.IMAGE.getCode());
            eventFileRelationEntity.setRecordId(personImagesVO.getRelationId());
            eventFileRelationEntity.setFileStorageId(image.getImageId());
            return eventFileRelationEntity;
        }).collect(Collectors.toList());
        eventFileRelationRepository.saveAll(entities);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.EVENT_RELATED_PERSON)
                .oldObj(oldObjString)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(eventId)
                .desc("更新涉及人员涉事照片")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updatePersonSource(String eventId, EventPersonSourceVO personSourceVO) {
        EventPersonRelationEntity relation = eventPersonRelationRepository.getById(personSourceVO.getRelationId());
        String oldObjString = JsonUtil.toJsonString(
                ImmutableMap.of(
                        "personId", relation.getPersonId(),
                        "source", relation.getSource()));

        relation.setSource(personSourceVO.getSource());
        eventPersonRelationRepository.save(relation);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.EVENT_RELATED_PERSON)
                .oldObj(oldObjString)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(eventId)
                .desc("更新涉及人员来源")
                .targetObjectType(OPERATION_LOG_TARGET_EVENT)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public PageResult<ActivityRelatedPersonVO> getEventRelatedPerson(String personId, PageParams pageParams) {
        final Integer pageNumber = pageParams.getPageNumber();
        final Integer pageSize = pageParams.getPageSize();

        List<EventEntity> eventEntities = eventPersonRelationRepository.findEventsByPerson(personId);
        List<ActivityRelatedPersonVO> vos = new ArrayList<>();
        eventEntities.forEach(event -> {
            List<PersonEntity> personEntities = eventPersonRelationRepository.findPersonByEventId(event.getId());
            List<ActivityRelatedPersonVO> voList = personEntities.stream()
                    .filter(personEntity -> !personEntity.getId().equals(personId))
                    .map(person -> {
                        ActivityRelatedPersonVO vo = new ActivityRelatedPersonVO();
                        vo.setPersonId(person.getId());
                        vo.setPersonName(person.getName());
                        vo.setIdNumber(person.getIdNumber());
                        vo.setRelevantEvent(event.getTitle());
                        vo.setEventId(event.getId());
                        return vo;
                    }).collect(Collectors.toList());
            vos.addAll(voList);
        });
        List<ActivityRelatedPersonVO> result = vos.stream().skip(pageParams.getOffset())
                .limit(pageParams.getPageSize())
                .collect(Collectors.toList());
        return PageResult.of(result, pageNumber, vos.size(), pageSize);
    }
}
