package com.trs.yq.police.subject;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2021/07/09
 */
@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
@RefreshScope
@EnableConfigurationProperties
@EnableCaching
@MapperScan(value = "com.trs.yq.police.subject.mapper")
public class PoliceSubjectApplication {

    /**
     * main method
     *
     * @param args arguments
     */
    public static void main(final String[] args) {
        SpringApplication.run(PoliceSubjectApplication.class, args);
    }

}
