package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 处理状态
 */
public enum CrjJwryRegistrationStatusEnum {
    /**
     * enums
     */
    NOT_REGISTER("0", "未登记"),
    LIVING("1", "在住"),
    LEAVE("2", "离开"),
    NO_REGISTER("3", "不登记");


    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    CrjJwryRegistrationStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 显示类别code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static CrjJwryRegistrationStatusEnum codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (CrjJwryRegistrationStatusEnum display : CrjJwryRegistrationStatusEnum.values()) {
                if (StringUtils.equals(code, display.code)) {
                    return display;
                }
            }
        }
        return null;
    }

    /**
     * 判断该码值对应的枚举值是否已登记
     *
     * @param code 编码
     * @return 布尔
     */
    public static boolean isRegistered(String code) {
        CrjJwryRegistrationStatusEnum crjJwryRegistrationStatusEnum = codeOf(code);
        if (crjJwryRegistrationStatusEnum == null) {
            return false;
        }
        return crjJwryRegistrationStatusEnum == CrjJwryRegistrationStatusEnum.LIVING || crjJwryRegistrationStatusEnum == CrjJwryRegistrationStatusEnum.LEAVE;
    }
}
