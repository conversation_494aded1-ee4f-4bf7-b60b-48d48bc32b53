package com.trs.yq.police.subject.constants.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 10:18
 */
public enum UnitTypeEnum {
    XZQH("1", "行政区划"),
    QJ("2","区级"),
    XJ("3","县级"),
    PCS("4","派出所");


    @JsonValue
    @Getter
    private final String code;

    @Getter
    private final String name;

    UnitTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过模块码获取枚举
     *
     * @param code 操作符码
     * @return 操作符 {@link Operator}
     * <AUTHOR>
     * @since 2021/7/27 17:55
     */
    public static UnitTypeEnum codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            return Arrays.stream(UnitTypeEnum.values())
                .filter(module -> module.getCode().equals(code))
                .findFirst()
                .orElse(null);
        }
        return null;
    }
}
