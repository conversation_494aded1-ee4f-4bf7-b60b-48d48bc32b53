package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/12/28 15:28
 */
@Data
public class SensitiveListVO implements Serializable {
    private static final long serialVersionUID = -561890187964747443L;
    private String id;
    /**
     * 敏感节点名称
     */
    private String name;
    /**
     * 敏感节点备注
     */
    private String remark;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 有效范围 0 默认有效 1 选择有效
     */
    private Integer scope;
    /**
     * 0 停用 1 启用 -1 删除
     */
    private Integer status;
    /**
     * 0 每年 1 固定时间
     */
    private Integer timeType;
}
