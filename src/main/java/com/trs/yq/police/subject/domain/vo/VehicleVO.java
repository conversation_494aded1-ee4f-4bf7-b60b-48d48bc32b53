package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 人员车辆信息Vo
 *
 * <AUTHOR>
 * @date 2021/7/28 9:03
 */
@Data
public class VehicleVO implements Serializable {

    private static final long serialVersionUID = -8638885011894583447L;

    /**
     * 车辆Id
     */
    private String id;
    /**
     * 类型
     */
    private String type;
    /**
     * 车牌号
     */
    private String vehicleNumber;
    /**
     * 所属人
     */
    private String owner;
    /**
     * 关联人Id
     */
    private String personId;

    /**
     * 是否为自动更新的
     */
    private String isAutomated;
}
