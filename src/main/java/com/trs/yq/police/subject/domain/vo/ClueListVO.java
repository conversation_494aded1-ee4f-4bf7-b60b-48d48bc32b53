package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.ClueExtendEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.BattleCommandRepository;
import com.trs.yq.police.subject.repository.ClueExtendRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.SpringContextUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class ClueListVO implements Serializable {

    private static final long serialVersionUID = -40236142824369544L;
    /**
     * 线索id
     */
    private String clueId;

    /**
     * 线索名称
     */
    private String clueName;
    /**
     * 线索来源
     */
    private String clueSource;
    /**
     * 紧急程度
     */
    private String emergencyLevel;
    /**
     * 录入单位
     */
    private String createDeptName;
    /**
     * 录入时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 线索类别
     */
    private List<IdNameVO> clueTypes;
    /**
     * 上报状态
     */
    private String reportStatus;
    /**
     * 处置状态
     */
    private String disposalStatus;
    /**
     * 维权方式
     */
    private String method;
    /**
     * 维权行为
     */
    private String behaviour;
    /**
     * 维权时间
     */
    private String occurrenceTime;
    /**
     * 涉及市州
     */
    private String relatedPlace;

    /**
     * 已发指令数
     */
    private Long commandCount;

    /**
     * 是否允许删除
     */
    private Boolean canDelete = false;

    /**
     * 创建线索列表vo
     *
     * @param clueEntity {@link ClueEntity}
     * @return {@link ClueListVO}
     */
    public static ClueListVO of(ClueEntity clueEntity) {
        ClueListVO vo = new ClueListVO();

        //id
        vo.setClueId(clueEntity.getId());

        //名称
        vo.setClueName(clueEntity.getName());

        //来源
        DictService dictService = SpringContextUtil.getBean(DictService.class);
        vo.setClueSource(clueEntity.getSource());

        //紧急程度
        String clueEmergencyLevel = StringUtils.isNotBlank(clueEntity.getEmergencyLevel()) ? dictService.getDictEntityByTypeAndCode("ps_clue_emergency_level", clueEntity.getEmergencyLevel()).getName() : "";
        vo.setEmergencyLevel(clueEmergencyLevel);

        //录入单位
        vo.setCreateDeptName(clueEntity.getCrDept());

        //录入时间
        vo.setCreateTime(clueEntity.getCrTime().format(DATE_TIME_FORMATTER));

        //更新时间
        vo.setUpdateTime(clueEntity.getUpTime().format(DATE_TIME_FORMATTER));

        //上报状态
        vo.setReportStatus(clueEntity.getReportStatus());
        //处置状态
        vo.setDisposalStatus(clueEntity.getDisposalStatus());
        //线索类别
        LabelRepository labelRepository = SpringContextUtil.getBean(LabelRepository.class);
        List<LabelEntity> clueTypes = labelRepository.findByClueId(clueEntity.getId());
        vo.setClueTypes(clueTypes.stream().map(type -> new IdNameVO(type.getId(), type.getName())).collect(Collectors.toList()));
        if (WW_SUBJECT.equals(clueEntity.getSubjectId())) {
            ClueExtendRepository extendRepository = SpringContextUtil.getBean(ClueExtendRepository.class);
            ClueExtendEntity extendEntity = extendRepository.findByClueId(clueEntity.getId()).orElse(new ClueExtendEntity());
            if (StringUtils.isNotBlank(extendEntity.getBehaviour())) {
                vo.setBehaviour(dictService.getDictEntityByTypeAndCode("clue_action_behaviour", extendEntity.getBehaviour()).getName());
            }
            if (StringUtils.isNotBlank(extendEntity.getMethod())) {
                vo.setMethod(dictService.getDictEntityByTypeAndCode("clue_action_method", extendEntity.getMethod()).getName());

            }
            vo.setRelatedPlace(extendEntity.getRelatedPlace());
            if (Objects.nonNull(extendEntity.getOccurrenceTime())) {
                vo.setOccurrenceTime(extendEntity.getOccurrenceTime().format(DATE_TIME_FORMATTER));
            }
        }

        //已发指令数
        BattleCommandRepository battleCommandRepository = BeanUtil.getBean(BattleCommandRepository.class);
        int commands = battleCommandRepository.findCommandById("system.t_ps_clue", clueEntity.getId()).size();
        vo.setCommandCount((long) commands);

        //是否允许删除
        Optional.ofNullable(AuthHelper.getCurrentUser()).ifPresent(user -> vo.setCanDelete(user.getId().equals(clueEntity.getCrBy())));
        return vo;
    }
}
