package com.trs.yq.police.subject.domain.vo;

import java.util.List;
import lombok.Data;

/**
 * 话单分析vo
 *
 * <AUTHOR>
 */
@Data
public class CallVO {

    private String id;

    private List<AttachmentVO> topologyGraph;

    private List<AttachmentVO> callFile;

    private List<AttachmentVO> callTimeFeature;

    private List<AttachmentVO> callPositionTrack;

    private List<AttachmentVO> attachments;

    private String remark;

    private String uploadUser;

    private String uploadTime;
}
