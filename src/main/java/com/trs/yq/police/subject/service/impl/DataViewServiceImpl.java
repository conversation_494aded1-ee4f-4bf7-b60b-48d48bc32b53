package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.*;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.request.CountRequestVO;
import com.trs.yq.police.subject.domain.response.BasicCountResponseVO;
import com.trs.yq.police.subject.domain.response.CountDistributeMobilityResponseVO;
import com.trs.yq.police.subject.domain.response.MobilityCountResponseVO;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DataViewService;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.WarningService;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FX_SUBJECT;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ZB_SUBJECT;

/**
 * 数据视图业务层实现类
 *
 * <AUTHOR>
 * @date 2021/9/3 20:13
 */
@Slf4j
@Service
public class DataViewServiceImpl implements DataViewService {
    @Resource
    private PersonRepository personRepository;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private PersonSubjectRelationRepository personSubjectRelationRepository;
    @Resource
    private MobilityRepository mobilityRepository;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private DictService dictService;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private PersonService personService;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private WarningCallRepository warningCallRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningService warningService;
    @Resource
    private WarningCallRelationRepository warningCallRelationRepository;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private BattleEventRepository battleEventRepository;
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private CrjJwryDetailRepository crjJwryDetailRepository;

    private static final String DICT_TYPE_DISTRICT = "district";

    @Value("${com.trs.police.subject.district.code}")
    private String districtCodePrefix;

    @Override
    public BasicCountResponseVO getBasicCount(CountRequestVO request) {

        final String subjectId = request.getSubjectId();
        BasicCountResponseVO response = new BasicCountResponseVO();

        // 获取人员总数
        final List<String> personIds = personSubjectRelationRepository.findPersonIdBySubjectId(subjectId).stream().distinct().collect(Collectors.toList());
        response.setPersonCount(personIds.size());
        // 获取群体数量
        final int groupCount = groupRepository.countDistinctBySubjectId(subjectId);
        response.setGroupCount(groupCount);
        personRepository.findAllByIdAndControlStatus(subjectId).forEach(controlCount -> {
            final ControlStatusEnum controlStatusEnum = ControlStatusEnum.codeOf(controlCount.getControlStatus());
            switch (Objects.requireNonNull(controlStatusEnum)) {
                case IN_CONTROL:
                    // 获取已列管
                    response.setInList(controlCount.getCount());
                    break;
                case IN_ARCHIVE:
                    // 获取已归档
                    response.setInArchive(controlCount.getCount());
                    break;
                default:
                    log.error("error status = {}, count = {}, subjectId = {}!", controlCount.getControlStatus(), controlCount.getCount(), subjectId);
                    break;
            }
        });
        response.setInControl(personRepository.countBySubjectIdInMonitor(subjectId));
        return response;
    }

    @Override
    public MobilityCountResponseVO getMobilityCount(CountRequestVO request) {

        MobilityCountResponseVO response = new MobilityCountResponseVO();
        final String subjectId = request.getSubjectId();
        final TimeParams timeParams = request.getTimeParams();
        final LocalDateTime beginTime = timeParams.getBeginTime();
        final LocalDateTime endTime = timeParams.getEndTime();
        final List<String> personIds = personSubjectRelationRepository.findPersonIdBySubjectIdAndCrTimeIsBetween(
                        subjectId,
                        beginTime,
                        endTime)
                .stream()
                .distinct()
                .collect(Collectors.toList());
        // 统计人员新增数量
        response.setIncrement(personIds.size());

        // 当前月流入
        //TODO refactor: 使用sql实现
        AtomicInteger inCount = new AtomicInteger(0);
        AtomicInteger outCount = new AtomicInteger(0);
        mobilityRepository.findAllByMoveTimeBetween(beginTime, endTime).stream()
                .collect(Collectors.groupingBy(MobilityEntity::getPersonId))
                .values().stream()
                .map(mobilityEntities -> {
                    MobilityEntity result = new MobilityEntity();
                    mobilityEntities.stream()
                            .max(Comparator.comparing(MobilityEntity::getMoveTime))
                            .ifPresent(mobility -> BeanUtil.copyPropertiesIgnoreNull(mobility, result));
                    return result;
                })
                .forEach(mobility -> {
                    final String inType = "1";
                    if (StringUtils.equals(inType, mobility.getMoveType())) {
                        inCount.incrementAndGet();
                    } else {
                        outCount.incrementAndGet();
                    }
                });
        response.setIn(inCount.get());
        // 当前月流出
        response.setOut(outCount.get());
        return response;
    }

    @Override
    public List<CountDistributeMobilityResponseVO> countDistributeMobility(CountRequestVO request) {
        long startTime = System.currentTimeMillis();
        final String subjectId = request.getSubjectId();
        final TimeParams timeParams = request.getTimeParams();
        final LocalDateTime beginTime = timeParams.getBeginTime();
        final LocalDateTime endTime = timeParams.getEndTime();

        // 查询人员分布
        final List<ControlEntity> allControl = controlRepository.findAllBySubjectId(subjectId);
        final long allPerson = personSubjectRelationRepository.getPersonCountBySubjectId(subjectId);
        final long incrementPerson = personRepository.getPersonIncrement(subjectId, beginTime, endTime);
        final List<ControlEntity> controlEntities = controlRepository.findAllBySubjectIdAndCrTime(subjectId, beginTime, endTime);
        List<UnitEntity> unitEntities = unitRepository.findByTypeAndCodeLike("1", districtCodePrefix);
        final List<GroupEntity> groups = groupRepository.findAllBySubjectId(subjectId);
        List<CountDistributeMobilityResponseVO> collect = unitEntities.stream().map(unitEntity -> {
            CountDistributeMobilityResponseVO response = new CountDistributeMobilityResponseVO();
            final String areaCode = unitEntity.getAreaCode();
            response.setAreaCode(areaCode);
            response.setAreaName(unitEntity.getShortname());
            if (districtCodePrefix.equals(areaCode)) {
                response.setPersonCount(allPerson);
                response.setPersonIncrement(incrementPerson);
            } else {
                response.setPersonCount(allControl.stream().filter(control -> Objects.nonNull(control.getPoliceStationCode())).filter(control -> control.getPoliceStationCode().startsWith(areaCode)).count());
                response.setPersonIncrement(controlEntities.stream().filter(control -> Objects.nonNull(control.getPoliceStationCode())).filter(control -> control.getPoliceStationCode().startsWith(areaCode)).count());
            }
            response.setGroupCount(groups.stream().filter(group -> group.getCrDeptCode().startsWith(areaCode)).count());
            return response;
        }).collect(Collectors.toList());
        log.info("专题首页区县统计运行时间： " + (System.currentTimeMillis() - startTime) + "ms");
        return collect;
    }


    @Override
    public WarningScrollListVO getNewPersonInWarningList(String status) {
        List<NewPersonInWarningVO> newPersonInWarningVoList = new ArrayList<>();
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(WarningTypeEnum.FK_XLRWARY.getCode(), status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        warningPage.getContent().forEach(warningEntity -> {
            List<PersonEntity> personEntityList = warningTraceRelationRepository.findAllPersonByWarning(warningEntity.getId());
            List<NewPersonInWarningVO> result = personEntityList.stream().map(personEntity -> {
                NewPersonInWarningVO newPersonInWarningVo = new NewPersonInWarningVO();
                newPersonInWarningVo.setWarningId(warningEntity.getId());
                newPersonInWarningVo.setPersonName(personEntity.getName());
                newPersonInWarningVo.setWarningPlace(warningEntity.getPlace());
                newPersonInWarningVo.setPersonId(personEntity.getId());
                newPersonInWarningVo.setWarningTime(warningEntity.getWarningTime());
                newPersonInWarningVo.setWarningLevel(warningEntity.getWarningLevel());
                newPersonInWarningVo.setImages(fileStorageRepository.findAllByPersonIdAndModule(personEntity.getId(),
                                FileTypeEnum.IMAGE.getCode(),
                                FileModuleEnum.BASIC_INFO_PHOTO.getCode(),
                                null)
                        .stream()
                        .filter(Objects::nonNull)
                        .map(ImageVO::of)
                        .collect(Collectors.toList()));
                return newPersonInWarningVo;
            }).collect(Collectors.toList());
            newPersonInWarningVoList.addAll(result);
        });
        warningScrollListVO.setItem(newPersonInWarningVoList);
        return warningScrollListVO;
    }


    @Override
    public List<WarningTrajectoryVO> findWarningTrajectory(String warningId) {
        //查询预警
        WarningEntity warningEntity = warningRepository.findById(warningId).orElse(null);
        if (Objects.isNull(warningEntity)) {
            throw new ParamValidationException("预警不存在！");
        }

        //查询轨迹
        List<WarningTrajectoryEntity> all = warningTrajectoryRepository.findAllByWarningId(warningId);

        //将轨迹按照身份证号分组
        Map<String, List<WarningTrajectoryEntity>> map = all.stream()
                .filter(t->Objects.nonNull(t.getIdNumber()))
                .collect(Collectors.groupingBy(WarningTrajectoryEntity::getIdNumber));

        //生成轨迹结果
        return map.entrySet().stream().map(entry -> {
            WarningTrajectoryVO vo = new WarningTrajectoryVO();
            List<PointVO> points = entry.getValue().stream().map(PointVO::of).collect(Collectors.toList());
            vo.setPoints(points);
            PersonEntity person = personRepository.findByIdNumber(entry.getKey());
            if(Objects.nonNull(person)){
                vo.setPersonId(person.getId());
                vo.setPersonName(person.getName());
            }else {
                List<CrjJwryDetailEntity> jwryList = crjJwryDetailRepository.findByIdNumber(warningEntity.getWarningKey());
                if(!jwryList.isEmpty()){
                    vo.setPersonName(jwryList.get(0).getEnName());
                }
            }
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<WarningTrajectoryVO> findFkWarningTrajectory(String warningId) {
        //查询预警
        WarningEntity warningEntity = warningRepository.findById(warningId).orElse(null);
        if (Objects.isNull(warningEntity)) {
            throw new ParamValidationException("预警不存在！");
        }
        return warningTrajectoryRepository.findAllByWarningId(warningId).stream().map(track->{
            PointVO pointVO = new PointVO();
            pointVO.setLng(track.getLng());
            pointVO.setLat(track.getLat());
            pointVO.setDateTime(track.getDateTime());
            WarningTrajectoryVO vo = new WarningTrajectoryVO();
            vo.setPoints(Collections.singletonList(pointVO));
            return vo;
        }).collect(Collectors.toList());

    }

    @Override
    public List<CountDistributeResponseVO> countDistribute(CountRequestVO request) {

        final String subjectId = request.getSubjectId();
        final TimeParams timeParams = request.getTimeParams();
        final LocalDateTime beginTime = timeParams.getBeginTime();
        final LocalDateTime endTime = timeParams.getEndTime();

        if (StringUtils.equals(subjectId, ZB_SUBJECT) || StringUtils.equals(subjectId, FX_SUBJECT)) {
            // 政保
            final List<ControlEntity> controlEntities = controlRepository.findAllBySubjectIdAndCrTime(subjectId, beginTime, endTime);

            final Map<String, String> sortedDistrict = extractDistrictMap();
            return sortedDistrict.entrySet().stream().map(district -> {
                CountDistributeResponseVO response = new CountDistributeResponseVO();
                final String areaCode = district.getKey();
                response.setAreaCode(areaCode);
                response.setAreaName(district.getValue());
                response.setPersonCount(controlEntities.stream().filter(control -> control.getPoliceStationCode().startsWith(areaCode)).count());
                return response;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    @Override
    public List<FindWarningBehaviorVO> getWarningBehavior(String subjectId, String status) {
        List<WarningEntity> warningEntities = warningRepository.findByWarningTypeAndWarningStatus(WarningTypeEnum.FK_GZRYFXYJ.getCode(), status);
        return warningEntities.stream()
                .map(warningEntity -> {
                    FindWarningBehaviorVO findWarningBehaviorVO = new FindWarningBehaviorVO();
                    String warningId = warningEntity.getId();
                    //根据warningId找轨迹id
                    String trajectoryId = warningTraceRelationRepository.findTrajectoryIdIdByWarningId(warningId).get(0);
                    String idNumber = warningTraceRelationRepository.findIdNumberByTrajectoryId(trajectoryId);
                    PersonBasicVO personBasicVO = personService.getBasicInfoByIdNumber(idNumber);

                    findWarningBehaviorVO.setWarningId(warningEntity.getId());
                    findWarningBehaviorVO.setWarningPlace(warningEntity.getPlace());
                    findWarningBehaviorVO.setWarningTime(warningEntity.getWarningTime());
                    findWarningBehaviorVO.setImages(personBasicVO.getImages());
                    findWarningBehaviorVO.setPersonName(personBasicVO.getName());
                    findWarningBehaviorVO.setPersonId(personBasicVO.getPersonId());
                    findWarningBehaviorVO.setWarningLabel("异常行为");
                    findWarningBehaviorVO.setWarningLevel(warningEntity.getWarningLevel());
                    return findWarningBehaviorVO;
                }).collect(Collectors.toList());
    }

    /**
     * 抽取地区分布
     *
     * @return 区县
     */
    private Map<String, String> extractDistrictMap() {
        final Map<String, String> districtMap = dictService.getDictEntitiesByType(DICT_TYPE_DISTRICT).stream()
                .filter(d -> StringUtils.startsWith(d.getCode(), districtCodePrefix))
                .collect(Collectors.toMap(DictEntity::getCode, DictEntity::getName));
        final List<String> sortedDistrictCode =
                districtMap.keySet().stream()
                        .sorted()
                        .collect(Collectors.toList());
        Map<String, String> sortedDistrict = new LinkedHashMap<>();
        for (String s : sortedDistrictCode) {
            sortedDistrict.put(s, districtMap.get(s));
        }
        return sortedDistrict;
    }

    @Override
    public WarningScrollListVO getGatherWarningList(String warningType, String status) {
        if (!warningService.getDisplayTypeByWarningType(warningType).equals(DisplayTypeEnum.MULTI_PERSON)) {
            throw new ParamValidationException("预警显示类型不为多人类型，请核实！");
        }
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(warningType, status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        warningScrollListVO.setItem(warningPage.getContent().stream().map(this::warningToGather).collect(Collectors.toList()));
        return warningScrollListVO;
    }

    /**
     * 根据预警entity生成聚集预警vo
     *
     * @param e entity {@link WarningEntity}
     * @return vo {@link GatherWarningVO}
     */
    private GatherWarningVO warningToGather(WarningEntity e) {
        GatherWarningVO warningVO = new GatherWarningVO();
        warningVO.setWarningId(e.getId());
        warningVO.setWarningLevel(e.getWarningLevel());
        warningVO.setWarningTime(e.getWarningTime());
        warningVO.setWarningPlace(e.getAddress());
        List<PersonEntity> persons = warningTraceRelationRepository.findAllPersonByWarning(e.getId());
        warningVO.setWarningPersonCount(persons.size());

        List<ImageVO> images = new ArrayList<>();
        for (PersonEntity person : persons) {
            images.add(personService.getPhotoFromPerson(person));
        }
        warningVO.setImages(images);

        return warningVO;
    }

    @Override
    public WarningScrollListVO getFocusPersonWarningList(String warningType, String status) {
        if (!warningService.getDisplayTypeByWarningType(warningType).equals(DisplayTypeEnum.SINGLE_PERSON)) {
            throw new ParamValidationException("预警显示类型不为单人类型，请核实！");
        }
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(warningType, status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        warningScrollListVO.setItem(warningPage.getContent().stream().map(this::warningToFocusPerson).collect(Collectors.toList()));
        return warningScrollListVO;
    }

    /**
     * 根据预警entity生成关注人员风险警情vo
     *
     * @param warning entity {@link WarningEntity}
     * @return vo {@link FocusPersonWarningVO}
     */
    private FocusPersonWarningVO warningToFocusPerson(WarningEntity warning) {
        FocusPersonWarningVO focus = new FocusPersonWarningVO();
        focus.setWarningId(warning.getId());
        focus.setWarningLevel(warning.getWarningLevel());
        focus.setWarningTime(warning.getWarningTime());
        focus.setWarningPlace(warning.getAddress());
        focus.setWarningTag(Objects.requireNonNull(battleEventRepository.findById(warning.getWarningKey()).orElse(null)).getTypeval());
        try {
            PersonEntity person = warningTraceRelationRepository.findPersonByWarning(warning.getId());
            focus.setPersonName(person.getName());
            focus.setImages(Collections.singletonList(personService.getPhotoFromPerson(person)));
        } catch (Exception e) {
            throw new ParamValidationException("单人预警对应人员不唯一，请核实！warning id：" + warning.getId());
        }

        return focus;
    }

    @Override
    public WarningScrollListVO getDrugDriver(String status) {
        //获取所有失驾人员的的预警信息
        return getWarningEntityList(status, WarningTypeEnum.JD_SDRYJC.getCode());
    }

    @Override
    public WarningScrollListVO getMiningInformation(String status) {
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(WarningTypeEnum.JD_YXSDWD.getCode(), status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        List<MiningInformationVO> miningInformationList = warningPage.stream()
                .map(warningEntity -> {
                    //获得所有的轨迹id，然后对轨迹id中的idNumber进行去重
                    List<String> trajectoryIds = warningTraceRelationRepository.findTrajectoryIdIdByWarningId(warningEntity.getId());
                    HashSet<String> hashSet = warningTrajectoryRepository.findAllById(trajectoryIds)
                            .stream()
                            .map(WarningTrajectoryEntity::getIdNumber)
                            .collect(Collectors.toCollection(HashSet::new));
                    MiningInformationVO miningInformationVO = new MiningInformationVO();
                    miningInformationVO.setWarningLevel(warningEntity.getWarningLevel());
                    miningInformationVO.setWarningName(warningEntity.getPlace());
                    miningInformationVO.setInformationCasually("场所");
                    miningInformationVO.setWarningId(warningEntity.getId());
                    miningInformationVO.setPersonCount(hashSet.size() + "");
                    return miningInformationVO;
                }).collect(Collectors.toList());
        //查询所有的电话预警
        List<WarningEntity> warningEntityListNumber = warningRepository.findByWarningTypeAndWarningStatus(WarningTypeEnum.JD_YXSDRY.getCode(), status);
        for (WarningEntity warningEntity : warningEntityListNumber) {
            //获得预警所对应的话单
            List<String> callList = warningCallRelationRepository.findAllByWarningId(warningEntity.getId())
                    .stream().map(WarningCallRelationEntity::getCallListId).collect(Collectors.toList());
            //根据idNUmber去重
            HashSet<String> hashSet = warningCallRepository.findAllById(callList)
                    .stream()
                    .map(WarningCallEntity::getIdNumber)
                    .collect(Collectors.toCollection(HashSet::new));
            WarningCallEntity warningCallEntity = warningCallRepository.findAllById(callList).get(0);
            MiningInformationVO miningInformationVO = new MiningInformationVO();
            miningInformationVO.setWarningLevel(warningEntity.getWarningLevel());
            miningInformationVO.setWarningName(warningCallEntity.getWarningNumber());
            miningInformationVO.setInformationCasually("通联号码");
            miningInformationVO.setWarningId(warningEntity.getId());
            miningInformationVO.setPersonCount(hashSet.size() + "");
            miningInformationList.add(miningInformationVO);
        }
        warningScrollListVO.setItem(miningInformationList);
        return warningScrollListVO;
    }

    @Override
    public WarningScrollListVO getWarningPersonInfoList(String warnType, String status) {
        List<WarningPersonCommonVO> result = new ArrayList<>();
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(warnType, status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        warningPage.forEach(warningEntity -> result.addAll(getWarningCriminalInvestigationVOList(warningEntity)));
        warningScrollListVO.setItem(result);
        return warningScrollListVO;
    }

    @Override
    public WarningScrollListVO getTrafficPoliceDriver(String status) {
        //获取所有失驾人员的的预警信息
        return getWarningEntityList(status, WarningTypeEnum.JJ_SJRYJC.getCode());
    }

    @NotNull
    private WarningScrollListVO getWarningEntityList(String status, String drivingByLostDrivers) {
        PageRequest pageParams = getDefaultPage();
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(drivingByLostDrivers, status, pageParams);
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        List<DrugDriverVO> result = warningPage.stream()
                .map(warningEntity -> {
                    String warningId = warningEntity.getId();
                    String trajectoryId;
                    //根据warningId找轨迹id
                    try {
                        trajectoryId = warningTraceRelationRepository.findTrajectoryIdIdByWarningId(warningId).get(0);
                    } catch (Exception e) {
                        return null;
                    }
                    //通过轨迹id查找人员基本信息
                    String idNumber = warningTraceRelationRepository.findIdNumberByTrajectoryId(trajectoryId);
                    PersonBasicVO personBasicVO = personService.getBasicInfoByIdNumber(idNumber);
                    WarningTrajectoryEntity warningTrajectoryEntity = warningTrajectoryRepository.getById(trajectoryId);
                    DrugDriverVO drugDriverVO = new DrugDriverVO();
                    drugDriverVO.setWarningId(warningEntity.getId());
                    drugDriverVO.setPersonName(personBasicVO.getName());
                    drugDriverVO.setWarningPlace(warningEntity.getPlace());
                    drugDriverVO.setWarningTime(warningEntity.getWarningTime());
                    drugDriverVO.setImages(personBasicVO.getImages());
                    drugDriverVO.setWarningLevel(warningEntity.getWarningLevel());
                    drugDriverVO.setPersonId(personBasicVO.getPersonId());
                    drugDriverVO.setCarNumber(warningTrajectoryEntity.getIdValue());
                    return drugDriverVO;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        warningScrollListVO.setItem(result);
        return warningScrollListVO;
    }

    @Override
    public WarningScrollListVO getCriminalInvestigationVOList(String status) {
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(WarningTypeEnum.XZ_RYYJ.getCode(), status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        List<WarningPersonCommonVO> result = new ArrayList<>();
        warningPage.forEach(warningEntity -> result.addAll(getWarningCriminalInvestigationVOList(warningEntity)));
        warningScrollListVO.setItem(result);
        return warningScrollListVO;
    }

    @Override
    public WarningScrollListVO getWarningPersonInOrOutArea(String warningType, String status) {
        Page<WarningEntity> warningPage = warningRepository.findWarningPage(warningType, status, getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningPage.getTotalElements());
        List<WarningPersonCommonVO> result = new ArrayList<>();
        warningPage.forEach(warningEntity -> result.addAll(getWarningCriminalInvestigationVOList(warningEntity)));
        warningScrollListVO.setItem(result);
        return warningScrollListVO;
    }


    /**
     * [刑侦/政保]通过预警实体获取对应专题人员信息
     *
     * @param warningEntity {@link WarningEntity}
     * @return {@link WarningPersonCommonVO}
     */
    private List<WarningPersonCommonVO> getWarningCriminalInvestigationVOList(WarningEntity warningEntity) {
        List<WarningPersonCommonVO> result = new ArrayList<>();
        List<PersonEntity> personEntityList = warningTraceRelationRepository.findAllPersonByWarning(warningEntity.getId());
        personEntityList.forEach(personEntity -> {
            WarningPersonCommonVO warningPersonCommonVo = new WarningPersonCommonVO();
            warningPersonCommonVo.setWarningId(warningEntity.getId());
            warningPersonCommonVo.setPersonName(personEntity.getName());
            warningPersonCommonVo.setWarningPlace(warningEntity.getPlace());
            warningPersonCommonVo.setWarningLevel(warningEntity.getWarningLevel());
            warningPersonCommonVo.setWarningTime(warningEntity.getWarningTime());
            warningPersonCommonVo.setPersonId(personEntity.getId());
            //warningTag字段在刑侦专题是personType，政保专题是personLabel -> 统一为label
            List<LabelEntity> labels = labelRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), warningEntity.getSubjectId());
            String warningTag = labels.isEmpty() ? Strings.EMPTY : labels.get(0).getName();
            warningPersonCommonVo.setWarningTag(warningTag);

            warningPersonCommonVo.setImages(fileStorageRepository.findAllByPersonIdAndModule(personEntity.getId(),
                            FileTypeEnum.IMAGE.getCode(),
                            FileModuleEnum.BASIC_INFO_PHOTO.getCode(),
                            null)
                    .stream()
                    .filter(Objects::nonNull)
                    .map(ImageVO::of)
                    .collect(Collectors.toList()));
            result.add(warningPersonCommonVo);
        });
        return result;
    }

    /**
     * 专题首页-预警滚动列表设置默认分页参数
     *
     * @return 分页参数
     */
    public static PageRequest getDefaultPage() {
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(20);
        return pageParams.toPageable();
    }
}
