package com.trs.yq.police.subject.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.annotation.ExcelProperty;
import com.trs.yq.police.subject.common.SkipResponseBodyAdvice;
import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.VisitRecordEntity;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.service.WarningService;
import com.trs.yq.police.subject.task.SyncClueTask;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_FORMATTER;
import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CONTROL_STATUS;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 测试controller
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/16
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {

    @Resource
    private PersonRepository personRepository;

    @Resource
    private PersonLabelRelationRepository personLabelRelationRepository;

    @Resource
    private PersonSubjectRelationRepository personSubjectRelationRepository;

    @Resource
    private ControlRepository controlRepository;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private DictService dictService;

    @Resource
    private CommonExtendRepository commonExtendRepository;

    @Resource
    private VisitRecordRepository visitRecordRepository;

    @Resource
    private ClueRepository clueRepository;

    @Resource
    private WarningService warningService;

    @Resource
    private SyncClueTask syncClueTask;

    /**
     * testDriveNoLicenseAnalysisSql<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 10:17
     */
    @GetMapping("testDriveNoLicenseAnalysisSql")
    public String testDriveNoLicenseAnalysisSql() {
        return warningService.testDriveNoLicenseAnalysisSql();
    }

    /**
     * 下载人员列表
     *
     * @param response 返回体
     */
    @GetMapping("/export/person")
    @SkipResponseBodyAdvice
    public void getFile(HttpServletResponse response) throws IOException {
        String fileName = String.format("%s-人员档案-%s.xlsx", "维稳专题", LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<PersonEntity> personList = personRepository.findAllBySubjectId("6");
        List<PersonExportDto> vos = new ArrayList<>();
        personList.forEach(personEntity -> {
            PersonExportDto vo = new PersonExportDto();
            BeanUtil.copyPropertiesIgnoreNull(personEntity, vo);

            //管控状态
            vo.setControlStatus(dictService.getDictEntityByTypeAndCode(DICT_TYPE_CONTROL_STATUS, personEntity.getControlStatus()).getName());
            //被依法处理情况 主要诉求
            Optional<CommonExtentEntity> extentEntity = commonExtendRepository.findByRecordIdAndModule(personEntity.getId(), CommonExtentEntity.PERSON);
            extentEntity.ifPresent(e -> vo.setMainDemand(e.getMainDemand()));
            //管控派出所,责任民警
            controlRepository.findByPersonIdAndSubjectId(personEntity.getId(), WW_SUBJECT).ifPresent(control -> {
                vo.setStation(control.getPoliceStationName());
                vo.setPolice(control.getResponsibleName());
            });

            vo.setCreateTime(personEntity.getCrTime().format(DATE_FORMATTER));

            List<ClueEntity> clueEntityList = clueRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), WW_SUBJECT);
            String clue = clueEntityList.stream().map(ClueEntity::getDetail).collect(Collectors.joining("\n"));
            vo.setClue(clue);

            List<VisitRecordEntity> visitRecordEntityList = visitRecordRepository.findByPersonIdOrderByTimeDesc(personEntity.getId());
            String visit = visitRecordEntityList.stream().map(VisitRecordEntity::getInfo).collect(Collectors.joining("\n"));
            vo.setVisitRecord(visit);
            vos.add(vo);
        });
        EasyExcelFactory.write(response.getOutputStream(), PersonExportDto.class)
                .registerWriteHandler(new CustomCellWriteHandler())
                .sheet()
                .doWrite(vos);
    }

    /**
     * 导出维稳人员报表用vo
     */
    @Data
    public static class PersonExportDto {
        @ExcelProperty(value = "姓名")
        private String name;

        @ExcelProperty(value = "身份证号码")
        private String idNumber;

        @ExcelProperty(value = "户籍地址")
        private String registeredResidence;

        @ExcelProperty(value = "联系方式")
        private String contactInformation;

        @ExcelProperty(value = "主要诉求")
        private String mainDemand;

        @ExcelProperty(value = "管控状态")
        private String controlStatus;

        @ExcelProperty(value = "公安派出所")
        private String station;

        @ExcelProperty(value = "责任民警")
        private String police;

        @ExcelProperty(value = "添加时间")
        private String createTime;

        @ExcelProperty(value = "情报线索")
        private String clue;

        @ExcelProperty(value = "走访记录")
        private String visitRecord;
    }


    /**
     * 关系图谱查询请求
     */
    @Data
    public static class RelationGraphQuery {
        private String id;
        private String type;
    }

    /**
     * 关系图谱连接信息
     */
    @Data
    public static class RelationLink {

        private String id;
        private String source;
        private String target;
        private String value;
        private String type;
    }

    /**
     * 关系图谱响应结果
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class RelationGraphVo extends RelationGraphQuery {
        private String value;
        private List<RelationGraphVo> children = new ArrayList<>();
        private List<RelationLink> links = new ArrayList<>();
    }

    /**
     * 同步线索
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    @GetMapping("/syncClue")
    public void syncClue(String startTime, String endTime) {
        syncClueTask.syncClue(startTime, endTime);
    }
}
