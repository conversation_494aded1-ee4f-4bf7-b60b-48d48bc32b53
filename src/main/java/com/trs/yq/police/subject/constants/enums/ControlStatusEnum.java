package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 管控状态
 *
 * <AUTHOR>
 * @date 2021/08/17
 */
public enum ControlStatusEnum {
    /**
     * enums
     */
    NOT_CONTROL("0", "未列管"),
    IN_CONTROL("1", "已列管"),
    IN_ARCHIVE("3", "已归档");

    ControlStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * 控制状态枚举
     *
     * @param code 编码
     * @return 控制状态枚举
     */
    public static ControlStatusEnum codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (ControlStatusEnum controlStatus : ControlStatusEnum.values()) {
                if (StringUtils.equals(code, controlStatus.code)) {
                    return controlStatus;
                }
            }
        }
        return null;
    }
}
