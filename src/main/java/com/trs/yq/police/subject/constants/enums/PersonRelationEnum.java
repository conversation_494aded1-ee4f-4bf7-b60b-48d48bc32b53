package com.trs.yq.police.subject.constants.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 人员关系枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/15
 **/

@Getter
@AllArgsConstructor
public enum PersonRelationEnum {
    /**
     * enum
     */
    PHONE_OWNER("机主"),
    OWNER("所有人"),
    VEHICLE_OWNER("车主"),
    SHACK_UP("同户"),
    RELATIVE("亲属"),
    COLLEAGUE("同事"),
    SAME_CASE("同案"),
    SAME_PRISON_ROOM("监狱同房间"),
    VEHICLE_VIOLATION("车辆违章人"),
    LODGE("寄住关系"),
    SAME_ORDER("同订单"),
    SCHOOLMATE("同学关系"),
    GUARDIAN("监护人关系"),
    RENTAL("房屋租住关系"),
    PERSON_INNER_PARAM("人员内在属性"),
    UNKNOWN("未知"),

    ;
    private final String desc;

}
