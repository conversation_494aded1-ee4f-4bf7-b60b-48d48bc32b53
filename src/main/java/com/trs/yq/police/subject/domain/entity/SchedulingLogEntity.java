package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 定时任务调度记录实体类
 *
 * <AUTHOR>
 * @date 2021/10/15 10:52
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_SCHEDULING_LOG")
public class SchedulingLogEntity {
    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    private LocalDateTime schedulingTime;
    /**
     * 请求路径
     */
    private String url;
    /**
     * 请求参数
     */
    private String body;
    /**
     * 返回值
     */
    private String data;

    /**
     * @param schedulingTime 调度时间
     * @param url            路径
     * @param body           请求参数
     * @param data           返回数据
     */
    public SchedulingLogEntity(LocalDateTime schedulingTime, String url, String body, String data) {
        this.schedulingTime = schedulingTime;
        this.url = url;
        this.body = body;
        this.data = data;
    }
}
