package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.BureauVO;
import com.trs.yq.police.subject.domain.vo.ControlVO;
import com.trs.yq.police.subject.domain.vo.PoliceVO;
import com.trs.yq.police.subject.domain.vo.UnitVO;
import com.trs.yq.police.subject.service.ControlService;
import com.trs.yq.police.subject.validation.ControlGroup;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 管控信息接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/person")
@Validated
public class ControlController {

    @Resource
    private ControlService controlService;

    /**
     * 查询管控信息
     * http://192.168.200.192:3001/project/4897/interface/api/130005
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 管控信息
     */
    @GetMapping("{personId}/control")
    public ControlVO getControl(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId) {
        return controlService.getControl(personId, subjectId);
    }

    /**
     * 更新管控信息
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param controlVO 管控信息
     */
    @PutMapping("/{personId}/control")
    public void updateControl(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId, @RequestBody ControlVO controlVO) {
        controlService.updateControl(personId, subjectId, controlVO);
    }

    /**
     * 查询组织机构树
     *
     * @return {@link UnitVO}
     */
    @GetMapping("/unit")
    public List<UnitVO> getUnitTree() {
        return controlService.getListUnitTree();
    }

    /**
     * 查询分局分配
     * http://192.168.200.192:3001/project/4897/interface/api/130238
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 分配信息 {@link BureauVO}
     */
    @GetMapping("/{personId}/bureau")
    public BureauVO getBureau(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId) {
        return controlService.getBureau(personId, subjectId);
    }

    /**
     * 分配分局
     * http://192.168.200.192:3001/project/4897/interface/api/130241
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param bureauVO  分配信息 {@link BureauVO}
     */
    @PostMapping("/{personId}/bureau")
    public void assignBureau(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId, @Validated @RequestBody @NotNull(message = "分局信息不能为空") BureauVO bureauVO) {
        controlService.assignBureau(personId, subjectId, bureauVO);
    }

    /**
     * 分配派出所
     * http://192.168.200.192:3001/project/4897/interface/api/130244
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param controlVO 分配信息 {@link ControlVO}
     */
    @PostMapping("/{personId}/police-station")
    public void assignPoliceStation(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId, @Validated(ControlGroup.PoliceStation.class) @RequestBody @NotNull(message = "派出所信息不能为空") ControlVO controlVO) {
        controlService.assignPoliceStation(personId, subjectId, controlVO);
    }

    /**
     * 分配民警
     * http://192.168.200.192:3001/project/4897/interface/api/130247
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param controlVO 分配信息 {@link ControlVO}
     */
    @PostMapping("/{personId}/police")
    public void assignPolice(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId, @Validated(ControlGroup.Police.class) @RequestBody @NotNull(message = "民警信息不能为空") ControlVO controlVO) {
        controlService.assignPolice(personId, subjectId, controlVO);
    }

    /**
     * 查询分局列表
     * http://192.168.200.192:3001/project/4897/interface/api/130250
     *
     * @return 分局列表 {@link UnitVO}
     */
    @GetMapping("/bureau")
    public List<UnitVO> getBureauList() {
        return controlService.getBureauList();
    }

    /**
     * 查询派出所列表
     * http://192.168.200.192:3001/project/4897/interface/api/130253
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 派出所列表 {@link UnitVO}
     */
    @GetMapping("/{personId}/police-station")
    public List<UnitVO> getPoliceStationList(@PathVariable String personId, @NotBlank(message = "专题id不能为空") String subjectId) {
        return controlService.getPoliceStationList(personId, subjectId);
    }

    /**
     * 查询民警列表
     * http://192.168.200.192:3001/project/4897/interface/api/130256
     *
     * @param unitCode 单位代码
     * @param name     模糊查询姓名
     * @return 民警列表 {@link PoliceVO}
     */
    @GetMapping("/police")
    public List<PoliceVO> getPoliceList(@NotBlank(message = "部门编号不能为空") String unitCode, String name) {
        return controlService.getPoliceList(unitCode, name);
    }
}
