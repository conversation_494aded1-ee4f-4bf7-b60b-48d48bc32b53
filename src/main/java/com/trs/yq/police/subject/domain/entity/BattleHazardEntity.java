package com.trs.yq.police.subject.domain.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/04/13
 */
@Entity
@Table(name = "T_BATTLE_HAZARD")
@Data
public class BattleHazardEntity {
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;                /* 主键 */
    private String type;            //风险点类别
    private String subtype;            //风险点子类别
    private String confkeys;        //专刊类别标识
    private String title;            //标题
    private String detail;            //内容
    private String keyword;            //关键词
    private String fileids;            //附件ID
    private Integer priority;        //紧急程度
    private Integer ishappen;        //是否发生
    private Date eventstart;          //事件开始时间
    private Date eventend;            //事件结束时间
    private String eventaddr;        //事件发生地址
    private String subjects;           //涉事主体(多个逗号隔开)(字典:subjects)
    private String inducements;        //涉事诱因(多个逗号隔开)(字典:inducements)
    private String behaviors;        //表现形式(多个逗号隔开)(字典:behaviors)
    private String places;           //涉事地点(多个逗号隔开)(字典:locations)
    private String partakenum;        //涉事人数(字典:partakenum)
    private String cluefrom;         //线索来源
    private String phone;            //联系电话
    private String remarks;          //备注
    private String unitcode;         //创建单位ID
    private String unitname;         //创建单位名称
    private String crtby;            //创建人ID
    private String crtbyname;        //创建人姓名
    private LocalDateTime crttime;            //创建时间
    private String updby;            //更新人ID
    private String updbyname;        //更新人姓名
    private LocalDateTime updtime;            //更新时间
    private LocalDateTime reporttime;         //上报时间
    private LocalDateTime publishtime;        //发布时间
    private LocalDateTime adopttime;          //采用时间
    private Integer state;           //流程状态(1-草稿 2-上报 3-通过 4-采用 5-驳回)
    private String reportunits;           //上报单位代码（多个逗号隔开）
    private String openunits;            //公开单位代码
    private String openusers;            //公开用户ID
    private String nextunitcode;        //下一步审核单位代码(多个逗号隔开)
    private String nextunitname;        //下一步审核单位名称(多个逗号隔开)
    private String nextuserid;            //下一步审核用户id(多个逗号隔开)
    private String nextrealname;        //下一步审核用户姓名(多个逗号隔开)
    private String auditedunits;        //审核过的单位代码(多个逗号隔开)
    private String auditedusers;        //审核过的用户id(多个逗号隔开)
    private Integer dealstate;        //处置状态
    private String dealcontent;        //处置详情
    private String dealfileids;        //处置附件
    private LocalDateTime dealtime;            //处置时间
    private LocalDateTime dealopttime;        //处置填报时间
    private Integer reporttype;         //上报类型（1-上报区县 2-上报市局 3-上报省厅）
    private Integer upreport;          //是否再次上报
    private String fromid;             //开始报送信息ID
    private String hassc;             //是否省采
}
