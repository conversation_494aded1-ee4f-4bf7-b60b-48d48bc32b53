package com.trs.yq.police.subject.message;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.CountDownLatch;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FK_SUBJECT;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * kafka 消息消费
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/22 14:33
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "com.trs.bk.kafka.enable", havingValue = "true")
public class KafkaMessageConsumer {

    @Resource
    private SyncService syncService;

    private final CountDownLatch controlCountDown = new CountDownLatch(1);
    private final CountDownLatch commandCountDown = new CountDownLatch(1);
    private final CountDownLatch warningCountDown = new CountDownLatch(1);


    /**
     * 消费布控消息
     *
     * @param msg 布控消息
     */
    @KafkaListener(topics = {"${com.trs.bk.kafka.bk-sync-topic}"})
    public void listenBkStatus(String msg) {
        log.info("receive bk msg : {}", msg);
        if (StringUtils.isBlank(msg)) {
            log.error("receive msg is empty! time = {}", LocalDateTime.now());
            return;
        }
        // 消费布控message
        syncService.syncBkStatus(msg);
        controlCountDown.countDown();
    }

    /**
     * 消费指令消息
     *
     * @param msg 指令消息
     */
    @KafkaListener(topics = {"${com.trs.sync.kafka.command-topic}", "${com.trs.sync.kafka.battle-topic}"})
    public void listenDisposalStatus(String msg) {
        log.info("receive sync msg : {}", msg);
        if (StringUtils.isBlank(msg)) {
            log.error("receive msg is empty! time = {}", LocalDateTime.now());
            return;
        }
        // 消费指令message
        syncService.syncDisposalStatus(msg);
        commandCountDown.countDown();
    }


    /**
     * 消费预警信息
     *
     * @param message 预警消息
     */
    @KafkaListener(id = "${com.trs.sync.kafka.warning.group}", topics = {"${com.trs.sync.kafka.warning.topic}"}, autoStartup = "${com.trs.sync.kafka.warning.auto.startup}")
    public void listenWarningMessage(String message) {
        log.info("receive warning message : {}", message);
        if (StringUtils.isBlank(message)) {
            log.error("receive warning message is blank!");
        }
        syncService.syncWarning(message);
        warningCountDown.countDown();
    }

    /**
     * 消费反恐技侦手段预警
     *
     * @param msg 布控消息
     */
    @KafkaListener(topics = {"${com.trs.sync.kafka.warning.fk.topic}"}, groupId = "${com.trs.sync.kafka.warning.fk.group}")
    public void listenFkWarning(String msg) {
        log.info("消费反恐信令数据: {}", msg);
        if (StringUtils.isBlank(msg)) {
            log.error("receive msg is empty! time = {}", LocalDateTime.now());
            return;
        }
        // 消费反恐信令message
        syncService.syncJzsdWarning(msg, FK_SUBJECT);
    }

    /**
     * 消费治安技侦手段预警
     *
     * @param msg 布控消息
     */
    @KafkaListener(topics = {"${com.trs.sync.kafka.warning.ww.topic}"}, groupId = "${com.trs.sync.kafka.warning.ww.group}")
    public void listenWwWarning(String msg) {
        log.info("消费治安技侦手段预警msg : {}", msg);
        if (StringUtils.isBlank(msg)) {
            log.error("receive msg is empty! time = {}", LocalDateTime.now());
            return;
        }
        // 消费维稳信令message
        syncService.syncJzsdWarning(msg, WW_SUBJECT);
    }
}
