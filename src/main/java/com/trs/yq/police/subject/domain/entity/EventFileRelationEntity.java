package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/12/12 20:23
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_EVENT_FILE_RELATION")
public class EventFileRelationEntity extends BaseEntity {
    private static final long serialVersionUID = -9137052844531024153L;

    private String eventId;
    /**
     * 文件存储id
     */
    private String fileStorageId;
    /**
     * 文件类型 0 - 照片 ， 1 - 其他现场照片
     */
    private String type;
    /**
     * 模块
     */
    private String module;
    /**
     * 关联的记录主键
     */
    private String recordId;
}
