package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.io.FileUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/12/13 14:27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FileInfoVO extends AttachmentVO implements Serializable {
    private static final long serialVersionUID = -5009888017351744952L;
    /**
     * 上传者
     */
    private String uploader;
    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;
    /**
     * 线索材料类型
     */
    private String type;

    /**
     * 转VO
     *
     * @param fileStorageEntity 文件
     * @return {@link FileInfoVO}
     */
    public static FileInfoVO of(FileStorageEntity fileStorageEntity) {
        FileInfoVO fileInfoVO = new FileInfoVO();
        fileInfoVO.setUploader(fileStorageEntity.getCrDept()+"-"+fileStorageEntity.getCrByName());
        fileInfoVO.setUploadTime(fileStorageEntity.getCrTime());
        fileInfoVO.setType(fileStorageEntity.getExtensionName());
        fileInfoVO.setSize(FileUtils.byteCountToDisplaySize(fileStorageEntity.getFileSize()));
        fileInfoVO.setId(fileStorageEntity.getId());
        fileInfoVO.setUrl(fileStorageEntity.getUrl());
        fileInfoVO.setName(fileStorageEntity.getName());
        fileInfoVO.setPreviewImage(fileStorageEntity.getPreviewImage());
        return fileInfoVO;
    }
}
