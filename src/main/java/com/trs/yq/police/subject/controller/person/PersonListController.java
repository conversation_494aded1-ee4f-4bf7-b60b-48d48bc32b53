package com.trs.yq.police.subject.controller.person;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.PersonListVO;
import com.trs.yq.police.subject.service.PersonExcelService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.SubjectService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

/**
 * 批量导出excel接口类
 *
 * <AUTHOR>
 * @date 2021/7/29 17:27
 */
@RestController
@RequestMapping("/person")
@Validated
public class PersonListController {
    @Resource
    private PersonExcelService personExcelService;

    @Resource
    private PersonService personService;

    @Resource
    private SubjectService subjectService;

    /**
     * 导出excel
     *
     * @param response  响应体
     * @param request   导出请求
     * @throws IOException IO异常
     */
    @PostMapping("/list/export")
    public void exportPerson(HttpServletResponse response,
                             @Validated @RequestBody ExportParams request) throws IOException {
        personExcelService.downLoadExcel(response, request.getFieldNames(), request, request.getSubjectId());
    }

    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getPersonProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return personExcelService.getExportPropertyList(subjectId);
    }

    /**
     * 查询人员列表
     * http://192.168.200.192:3001/project/4897/interface/api/129575
     *
     * @param subjectId 专题id
     * @param params    检索条件
     * @return 人员列表
     */
    @PostMapping("/list")
    public PageResult<PersonListVO> pageQuery(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId, @RequestBody ListRequestVO params) {
        PageResult<PersonEntity> personList = personService.getGroupPersonList(subjectId, params);
        List<PersonListVO> items = personList.getItems().stream()
                .map(pe -> PersonListVO.of(pe, subjectId))
                .collect(Collectors.toList());
        return PageResult.of(items, personList.getPageNumber(), personList.getTotal(), personList.getPageSize());
    }

    /**
     * 查询人员列表筛选条件
     * http://192.168.200.192:3001/project/4897/interface/api/129570
     *
     * @param subjectId 专题id
     * @return 人员列表筛选条件
     */
    @GetMapping("/list/filters")
    public List<ListFilter> getListFilters(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return subjectService.getPersonListQueryFilters(subjectId);
    }
}
