package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.ClueExtendEntity;
import com.trs.yq.police.subject.domain.entity.CluePersonRelationEntity;
import com.trs.yq.police.subject.repository.ClueExtendRepository;
import com.trs.yq.police.subject.repository.CluePersonRelationRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CLUE_EMERGENCY_LEVEL;
import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CLUE_SOURCE;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 人员关联的群体VO
 *
 * <AUTHOR>
 * @date 2021/09/17
 */
@Data
public class PersonRelatedClueVO implements Serializable {
    private static final long serialVersionUID = 1618513743515487319L;

    /**
     * 线索id
     */
    private String clueId;
    /**
     * 线索名称
     */
    private String clueName;
    /**
     * 线索来源
     */
    private String clueSource;
    /**
     * 线索等级
     */
    private String clueLevel;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;
    /**
     * 关系id
     */
    private String relationId;
    /**
     * 维权行为
     */
    private String behaviour;
    /**
     * 维权方式
     */
    private String method;
    /**
     * 维权时间
     */
    private LocalDateTime occurrenceTime;

    /**
     * 构建vo
     *
     * @param clueEntity {@link ClueEntity}
     * @param personId   人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedClueVO}
     */
    public static PersonRelatedClueVO of(ClueEntity clueEntity, String personId, String subjectId) {
        PersonRelatedClueVO vo = new PersonRelatedClueVO();
        vo.setClueId(clueEntity.getId());
        vo.setClueName(clueEntity.getName());
        CluePersonRelationRepository cluePersonRelationRepository = BeanUtil.getBean(CluePersonRelationRepository.class);
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        vo.setClueLevel(dictRepository.findByTypeAndCode(DICT_TYPE_CLUE_EMERGENCY_LEVEL, clueEntity.getEmergencyLevel()).getName());
        if(subjectId.equals(WW_SUBJECT)) {
            vo.setClueSource(dictRepository.findByTypeAndCode("ps_ww_clue_source", clueEntity.getSource()).getName());
            ClueExtendRepository clueExtendRepository = BeanUtil.getBean(ClueExtendRepository.class);
            ClueExtendEntity clueExtendEntity = clueExtendRepository.findByClueId(clueEntity.getId()).orElse(new ClueExtendEntity());
            vo.setBehaviour(StringUtils.isBlank(clueExtendEntity.getBehaviour())?"":dictRepository.findByTypeAndCode("ps_clue_action_behaviour",clueExtendEntity.getBehaviour()).getName());
            vo.setMethod(StringUtils.isBlank(clueExtendEntity.getMethod())?"":dictRepository.findByTypeAndCode("ps_clue_action_method",clueExtendEntity.getMethod()).getName());
            vo.setOccurrenceTime(clueExtendEntity.getOccurrenceTime());
        } else {
            vo.setClueSource(dictRepository.findByTypeAndCode(DICT_TYPE_CLUE_SOURCE, clueEntity.getSource()).getName());
        }
        CluePersonRelationEntity relation = cluePersonRelationRepository.findByClueIdAndPersonId(clueEntity.getId(), personId);
        vo.setRelationId(relation.getId());
        vo.setCreateTime(relation.getCrTime());
        return vo;
    }
}
