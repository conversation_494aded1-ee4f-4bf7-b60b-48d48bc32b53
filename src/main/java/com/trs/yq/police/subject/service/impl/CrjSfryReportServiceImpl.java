package com.trs.yq.police.subject.service.impl;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.DateTimeConstants;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.dto.CrjSfrySearchValueDto;
import com.trs.yq.police.subject.domain.entity.CrjSfryReportEntity;
import com.trs.yq.police.subject.domain.entity.SubjectEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.CrjDispatchVO;
import com.trs.yq.police.subject.domain.vo.CrjRedispatchVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SfryExportVO;
import com.trs.yq.police.subject.domain.vo.SfryReportVO;
import com.trs.yq.police.subject.domain.vo.SfryReportVerifyVO;
import com.trs.yq.police.subject.exception.AppLogicException;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.mysqlDatasource.entity.CrjSfryReportSourceEntity;
import com.trs.yq.police.subject.mysqlDatasource.repository.CrjSfryReportSourceRepository;
import com.trs.yq.police.subject.repository.CrjSfryReportRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.SubjectRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.service.CrjCommonService;
import com.trs.yq.police.subject.service.CrjSfryReportService;
import com.trs.yq.police.subject.service.CrjSyncService;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 10:40
 */
@Slf4j
@Service
public class CrjSfryReportServiceImpl implements CrjSfryReportService {

    @Resource
    private CrjSfryReportSourceRepository crjSfryReportSourceRepository;

    @Resource
    private CrjSfryReportRepository crjSfryReportRepository;

    @Resource
    private UnitRepository unitRepository;

    @Resource
    private CrjCommonService crjCommonService;

    @Resource
    private DictRepository dictRepository;
    @Resource
    private SubjectRepository subjectRepository;

    @Resource
    private EntityManager entityManager;
    @Value("${com.trs.crj.palz.secretKey}")
    public String secretKey = "xiongdi.cn";

    @Resource
    private CrjSyncService crjSyncService;

    @Override
    public synchronized PageResult<SfryReportVO> getPage(ListRequestVO requestVO) {

        crjSyncService.syncSfry();
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        String sql = buildQuery(requestVO, currentUser);
        //查询oracle
        entityManager.clear();
        List<String> recordIds = entityManager.createNativeQuery(sql).getResultList();
        //为空表明无数据
        if (recordIds.isEmpty()) {
            return PageResult.empty(requestVO.getPageParams().getPageNumber(), requestVO.getPageParams().getPageSize());
        }

        //构建模糊查询
        CrjSfrySearchValueDto searchDto = buidSearchDto(requestVO);

        Page<CrjSfryReportSourceEntity> page = crjSfryReportSourceRepository.findPage(searchDto.getSearchValue(),
            searchDto.getFullText(), searchDto.getPhone(), searchDto.getContent()
            , recordIds, secretKey, requestVO.getPageParams().toPageable());
        //构建vo
        List<SfryReportVO> result = page.stream().map(e -> {
            String uuid = e.getUuid();
            CrjSfryReportEntity byId = crjSfryReportRepository.findByReportId(uuid);
            return SfryReportVO.of(e, byId, currentUser, secretKey);
        }).collect(Collectors.toList());

        return PageResult.of(result, requestVO.getPageParams().getPageNumber(), page.getTotalElements(),
            requestVO.getPageParams().getPageSize());
    }

    private CrjSfrySearchValueDto buidSearchDto(ListRequestVO requestVO) {
        CrjSfrySearchValueDto searchDto = new CrjSfrySearchValueDto();
        //searchValue为空表示不模糊查询
        SearchParams searchParams = requestVO.getSearchParams();
        if (StringUtils.isNotBlank(searchParams.getSearchField()) && StringUtils.isNotBlank(
            searchParams.getSearchValue())) {

            searchDto.setSearchValue(searchParams.getSearchValue());
            String searchField = searchParams.getSearchField();
            switch (searchField) {
                case "fullText":
                    searchDto.setFullText(searchField);
                    break;
                case "phone":
                    searchDto.setPhone(searchField);
                    break;
                case "content":
                    searchDto.setContent(searchField);
                    break;
                default:
                    searchDto.setSearchValue(null);
                    break;
            }
        }
        return searchDto;
    }


    private String buildQuery(ListRequestVO requestVO, LoginUser currentUser) {
        List<KeyValueVO> filterParams = requestVO.getFilterParams();

        StringBuilder sql = new StringBuilder("SELECT t.REPORT_ID FROM T_PS_CRJ_SFRY_REPORT t WHERE  1=1 ");

        String unitCode = currentUser.getUnitCode();
        UnitEntity unit = unitRepository.findByUnitCode(unitCode);
        //判断当前用户的可看部门   市局看全部
        if (!unit.getAreaCode().equals("510500")) {
            //三区四县看当自己及子部门
            if (CrjConstants.SQSXZD.contains(unitCode)) {
                sql.append("and t.ACCEPTOR like '").append(unit.getAreaCode()).append("%' ");
            } else {
                //其他看自己部门
                sql.append("and t.ACCEPTOR='").append(unitCode).append("' ");
            }
        }
        // 根据传递的对象来进行条件的构造
        for (KeyValueVO filterParam : filterParams) {
            String key = filterParam.getKey();
            String value = filterParam.getValue();
            if (StringUtils.isBlank(value) || CrjConstants.ALL.equals(value)) {
                continue;
            }
            switch (key) {
                case "isDispatched":
                    boolean isDispatched = Boolean.parseBoolean(value);
                    if (unit.getAreaCode().equals("510500")) {
                        if (isDispatched) {
                            sql.append("and t.DISPATCH_STATUS !='")
                                .append(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode())
                                .append("' ");
                        } else {
                            //未分派
                            sql.append("and t.DISPATCH_STATUS ='").append(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode())
                                .append("' ");
                        }
                    } else if (CrjConstants.SQSXZD.contains(unitCode)) {
                        if (isDispatched) {
                            //已分派
                            sql.append("and t.DISPATCH_STATUS ='").append(CrjDispatchStatusEnum.DISPATCHED.getCode())
                                .append("' ");
                        } else {
                            //未分派
                            sql.append("and t.DISPATCH_STATUS !='").append(CrjDispatchStatusEnum.DISPATCHED.getCode())
                                .append("' ");
                        }
                    }
                    break;
                case "isVerified":
                    boolean isVerified = Boolean.parseBoolean(value);
                    sql.append("and t.IS_VERIFIED ='").append(isVerified ? "1" : "0")
                        .append("' ");
                    break;
                case "acceptor":
                    sql.append("and t.ACCEPTOR ='").append(value).append("' ");
                    break;
                case "verifyResult":
                    sql.append("and t.VERIFY_RESULT ='").append(value).append("' ");
                    break;
                case "reportTime":
                    TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
                    sql.append(String.format(" and t.REPORT_TIME >to_date('%s','yyyy-mm-dd hh24:mi:ss') and t.REPORT_TIME < to_date('%s','yyyy-mm-dd hh24:mi:ss')", DateTimeConstants.DATE_TIME_FORMATTER.format(timeParams.getBeginTime()),DateTimeConstants.DATE_TIME_FORMATTER.format(timeParams.getEndTime())));
                    break;
                case "district":
                    String policeStationPrefix = StringUtil.getPoliceStationPrefix(value);
                    sql.append("and t.ACCEPTOR like '").append(policeStationPrefix).append("%' ");
                    break;
                default:
            }
        }

        //排序
        if (Objects.nonNull(requestVO.getSortParams())
            && CrjConstants.SORT_FIELD_UNREAD.equals(requestVO.getSortParams().getSortField())) {
            sql.append(
                    "ORDER BY (SELECT r.USER_ID from T_PS_CRJ_READ_RECORD r where RECORD_ID = t.REPORT_ID AND r.USER_ID ='")
                .append(currentUser.getId()).append("' AND r.MODULE ='").append(CrjConstants.SFRY_REPORT_RECORD_MODULE)
                .append("' ) ASC nulls first,t.UP_TIME desc").append(",t.CR_TIME desc");
        } else if (Objects.nonNull(requestVO.getSortParams()) && "reportTime".equals(requestVO.getSortParams().getSortField())) {
            sql.append(" ORDER BY t.REPORT_TIME ").append(requestVO.getSortParams().getSortDirection()).append(",t.CR_TIME desc");
        }
        else {
            sql.append(" ORDER BY t.UP_TIME desc").append(",t.CR_TIME desc");
        }

        return sql.toString();

    }

    @Transactional
    @Override
    public SfryReportVO getById(String id) {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;

        //获取详情
        CrjSfryReportSourceEntity byUuid = crjSfryReportSourceRepository.findByUuid(id);
        CrjSfryReportEntity byReportId = crjSfryReportRepository.findByReportId(id);

        //设置已读
        crjCommonService.addReadRecord(id,currentUser.getId(), CrjConstants.SFRY_REPORT_RECORD_MODULE);

        return SfryReportVO.of(byUuid, byReportId, currentUser, secretKey);
    }

    @Override
    public void deletedById(String id) {
        CrjSfryReportEntity byReportId = crjSfryReportRepository.findByReportId(id);
        crjSfryReportRepository.delete(byReportId);
    }

    @Transactional
    @Override
    public void dispatch(CrjDispatchVO dispatchVO) {
        String unitCode = dispatchVO.getDeptCode();
        //设置分派状态
        CrjDispatchStatusEnum dispatchStatus =
            CrjConstants.SQSXZD.contains(unitCode) ? CrjDispatchStatusEnum.DISPATCHED_TO_QX
                : CrjDispatchStatusEnum.DISPATCHED;

        //设置接受者,分发到派出所一级才算已分派
        crjSfryReportRepository.setAcceptor(dispatchVO.getRecordIds(), unitCode, dispatchStatus.getCode());
    }

    @Transactional
    @Override
    public void redispatch(CrjRedispatchVO dispatchVO) {
        String recordId = dispatchVO.getRecordId();
        CrjSfryReportEntity byReportId = crjSfryReportRepository.findByReportId(recordId);
        if (byReportId.getIsVerified()){
            throw new AppLogicException("该信息已经被核实,不可重新分派！");
        }
       this.dispatch(dispatchVO.toDispatchVO());
    }

    @Transactional
    @Override
    public void verify(String recordId, SfryReportVerifyVO verifyVO) {
        CrjSfryReportEntity byReportId = crjSfryReportRepository.findByReportId(recordId);
        if (byReportId.getIsVerified()){
            throw new AppLogicException("该信息已经被核实,不可重复核实！");
        }
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        crjSfryReportRepository.verify(recordId, currentUser.getId(), verifyVO.getResult(), verifyVO.getDescription(),
            currentUser.getUnitCode(), CrjDispatchStatusEnum.DISPATCHED.getCode());
    }
    @Override
    public void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request,
        String subjectId) throws IOException {
        String fileName = String.format("三非人员-%s.xlsx",
            LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<SfryExportVO> vos = crjSfryReportRepository.findByIds(request.getIds()).stream()
            .map(item->SfryExportVO.toExportVO(item,secretKey)).collect(Collectors.toList());
        EasyExcelFactory.write(response.getOutputStream(), SfryExportVO.class)
            .registerWriteHandler(new CustomCellWriteHandler())
            .includeColumnFiledNames(fieldNames)
            .sheet()
            .doWrite(vos);
    }

    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getCrjSfryListProperty());
    }
}
