package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/12/20 16:45
 */
public enum WeekEnums {

    MONDAY("1","星期一"),
    TUESDAY("2","星期二"),
    WEDNESDAY("3","星期三"),
    THURSDAY("4","星期四"),
    FRIDAY("5","星期五"),
    SATURDAY("6","周六"),
    SUNDAY("7","周天");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    WeekEnums(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 显示类别code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static WeekEnums codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (WeekEnums display : WeekEnums.values()) {
                if (StringUtils.equals(code, display.code)) {
                    return display;
                }
            }
        }
        return null;
    }
}
