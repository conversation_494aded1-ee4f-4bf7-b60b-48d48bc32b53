package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.entity.RelationEntity;
import com.trs.yq.police.subject.domain.vo.RelationInfoVO;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 人员关系业务类
 *
 * <AUTHOR>
 * @date 2021/7/27 17:32
 */
public interface RelationService {

    /**
     * 查询人员关系人的基本信息
     *
     * @param relationPersonId 关联人Id
     * @param type             类型
     * @return 人员关系基本信息
     */
    List<RelationInfoVO> getRelationInfo(@NotBlank(message = "关联人id缺失") String relationPersonId, String type);

    /**
     * 新增人员关系
     *
     * @param personId       人员主键
     * @param relationEntity 人员关系人信息
     */
    void saveRelation(@NotBlank(message = "人员主键缺失") String personId, @NotBlank(message = "关联人信息缺失") RelationEntity relationEntity);

    /**
     * 根据主键删除
     *
     * @param personId 关联人id
     * @param id       主键
     */
    void deleteRelation(String personId, String id);

    /**
     * 更新操作
     *
     * @param personId       人员主键
     * @param relationEntity 人员关系实体
     */
    void updateRelation(@NotBlank(message = "人员主键缺失") String personId, RelationEntity relationEntity);
}
