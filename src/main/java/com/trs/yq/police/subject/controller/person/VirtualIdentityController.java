package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.VirtualIdentityVO;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.VirtualIdentityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 虚拟身份类接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/person")
@Slf4j
public class VirtualIdentityController {

    @Resource
    private VirtualIdentityService virtualIdentityService;
    @Resource
    private PersonService personService;


    /**
     * 获取人员虚拟信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link VirtualIdentityVO}
     * <AUTHOR>
     */
    @GetMapping("/{personId}/virtual-identity")
    public List<VirtualIdentityVO> getAll(@NotBlank(message = "人员主键缺失") @PathVariable String personId) {
        personService.checkPersonExist(personId);
        return virtualIdentityService.getAll(personId);
    }


    /**
     * 删除人员的虚拟信息
     *
     * @param personId  人员主键
     * @param virtualId 虚拟id
     * <AUTHOR>
     */
    @DeleteMapping("/{personId}/virtual-identity")
    public void deleteOne(@NotBlank(message = "人员主键缺失") @PathVariable String personId, @NotBlank(message = "虚拟身份主键缺失") String virtualId) {
        personService.checkPersonExist(personId);
        virtualIdentityService.deleteOneById(personId, virtualId);
    }


    /**
     * 添加人员的虚拟信息
     *
     * @param personId          人员id
     * @param virtualIdentityVO 虚拟信息
     * <AUTHOR>
     */
    @PostMapping("/{personId}/virtual-identity")
    public void addOne(@NotBlank(message = "人员主键缺失") @PathVariable String personId, @Valid @RequestBody VirtualIdentityVO virtualIdentityVO) {
        personService.checkPersonExist(personId);
        virtualIdentityService.addOne(personId, virtualIdentityVO);
    }


    /**
     * 更新人员的虚拟信息
     *
     * @param personId          人员id
     * @param virtualIdentityVO 虚拟信息
     * <AUTHOR>
     */
    @PutMapping("/{personId}/virtual-identity")
    public void updateOne(@NotBlank(message = "人员主键缺失") @PathVariable String personId, @Valid @RequestBody VirtualIdentityVO virtualIdentityVO) {
        personService.checkPersonExist(personId);
        virtualIdentityService.updateOne(personId, virtualIdentityVO);
    }
}
