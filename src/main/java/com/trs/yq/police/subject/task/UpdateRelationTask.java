package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.*;
import com.trs.yq.police.subject.domain.dto.PersonAssociateRelationDTO;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.*;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.AUTOMATED;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 人员档案信息定时更新
 *
 * <AUTHOR>
 * @since 2021/10/13
 */
@Component
@Slf4j
@ConditionalOnProperty(value = "com.trs.update.relation.task.enable",havingValue = "true")
public class UpdateRelationTask{

    @Resource
    private PersonRepository personRepository;

    @Resource
    private VehicleRepository vehicleRepository;

    @Resource
    private VirtualIdentityRepository virtualIdentityRepository;

    @Resource
    private BankCardRepository bankCardRepository;

    @Resource
    private RelationRepository relationRepository;

    @Resource
    private PersonAssociateRelationService personAssociateRelationService;

    @Resource
    private MobilePhoneRepository mobilePhoneRepository;

    @Resource
    private PersonService personService;

    @Resource
    private OperationLogHandler operationLogHandler;

    private static final String FAMILY = "family";

    private static final String SOCIETY = "society";


    /**
     * 更新人员关联关系
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.update.relation.task}")
    public void updateAllPersonRelation() {
        List<PersonEntity> personList = personRepository.findAll();
        for(PersonEntity person : personList) {
            final String idNumber = person.getIdNumber();
            final String personId = person.getId();
            personService.checkPersonExist(personId);
            PersonAssociateRelationDTO dto = personAssociateRelationService.queryAssociateAndRelation(idNumber);
            addMobilePhone(dto.get(PersonAssociateAttributesEnum.PHONE_NUMBER), personId);
            addBankCard(dto.get(PersonAssociateAttributesEnum.BANK_CARD), personId);
            addVehicle(dto.get(PersonAssociateAttributesEnum.CAR_NUMBER), personId);
            addVirtualIdentity(PersonAssociateAttributesEnum.IMEI, dto.get(PersonAssociateAttributesEnum.IMEI), personId);
            addVirtualIdentity(PersonAssociateAttributesEnum.IMSI, dto.get(PersonAssociateAttributesEnum.IMSI), personId);
            addVirtualIdentity(PersonAssociateAttributesEnum.MAC,  dto.get(PersonAssociateAttributesEnum.MAC), personId);
            addFamilyAndSociety(dto.get(PersonAssociateAttributesEnum.OTHERS), personId);
        }
    }

    /**
     * 添加手机号信息
     *
     * @param relationPairs 关联集合
     * @param personId 人员id
     */
    private void addMobilePhone(Set<PersonAssociateRelationDTO.RelationPair> relationPairs, String personId) {
        for (PersonAssociateRelationDTO.RelationPair pair : relationPairs) {
            String phoneNumber = pair.getValue();
            if(Objects.nonNull(mobilePhoneRepository.findByPersonIdAndPhoneNumber(personId, phoneNumber))) {
                continue;
            }
            MobilePhoneVO vo = new MobilePhoneVO();
            vo.setPhoneNumber(phoneNumber);

            vo.setPersonId(personId);
            //常用
            vo.setPhoneStatus("1");
            //在用
            vo.setPhoneUseStatus("1");
            vo.setIsAutomated(AUTOMATED);
            log.info("auto update mobile-phone: "+vo);

            MobilePhoneEntity mobilePhoneEntity = new MobilePhoneEntity();
            BeanUtil.copyPropertiesIgnoreNull(vo, mobilePhoneEntity);
            mobilePhoneRepository.save(mobilePhoneEntity);
            // 操作记录
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.UPDATE)
                    .module(OperateModule.CONCAT_METHOD)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(personId)
                    .desc("新增手机号")
                    .newObj(JsonUtil.toJsonString(mobilePhoneEntity))
                    .build();
            if (Objects.nonNull(operationLogHandler)) {
                operationLogHandler.publishEvent(logRecord);
            }
        }
    }

    /**
     * 添加车辆信息
     *
     * @param relationPairs 关联集合
     * @param personId 人员id
     */
    private void addVehicle(Set<PersonAssociateRelationDTO.RelationPair> relationPairs, String personId) {
        for(PersonAssociateRelationDTO.RelationPair pair : relationPairs) {
            if(Boolean.TRUE.equals(vehicleRepository.existsByPersonIdAndVehicleNumber(personId, pair.getValue()))) {
                continue;
            }
            VehicleVO vo = new VehicleVO();
            vo.setPersonId(personId);
            vo.setVehicleNumber(pair.getValue());
            //本人
            vo.setType("1");
            vo.setIsAutomated(AUTOMATED);
            log.info("auto update vehicle: "+vo);

            VehicleEntity vehicleEntity = new VehicleEntity();
            BeanUtil.copyPropertiesIgnoreNull(vo, vehicleEntity);

            // 记录此操作
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.UPDATE)
                    .module(OperateModule.VEHICLE)
                    .newObj(JsonUtil.toJsonString(vehicleEntity))
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(personId)
                    .desc("新增车辆信息")
                    .build();
            vehicleRepository.save(vehicleEntity);
            if (Objects.nonNull(operationLogHandler)) {
                operationLogHandler.publishEvent(logRecord);
            }
        }

    }

    /**
     * 添加虚拟身份信息
     *
     * @param attribute 虚拟身份类别
     * @param relationPairs 关联集合
     * @param personId 人员id
     */
    private void addVirtualIdentity(PersonAssociateAttributesEnum attribute, Set<PersonAssociateRelationDTO.RelationPair> relationPairs, String personId) {
        final String virtualTypeCode = VirtualIdentityEnum.getCode(attribute.name());
        for(PersonAssociateRelationDTO.RelationPair pair : relationPairs) {
            if(Boolean.TRUE.equals(virtualIdentityRepository.existsByPersonIdAndVirtualNumberAndVirtualType(personId, pair.getValue(), virtualTypeCode))) {
                continue;
            }
            VirtualIdentityVO vo = new VirtualIdentityVO();
            vo.setPersonId(personId);
            vo.setVirtualNumber(pair.getValue());
            vo.setVirtualType(virtualTypeCode);
            vo.setIsAutomated(AUTOMATED);
            log.info("auto update virtual-identity: "+vo);

            VirtualIdentityEntity virtualIdentityEntity = voTransFormEntity(new VirtualIdentityEntity(), vo);

            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(personId)
                    .desc("新增虚拟身份")
                    .newObj(JsonUtil.toJsonString(virtualIdentityEntity))
                    .module(OperateModule.VIRTUAL_IDENTITY)
                    .operator(Operator.UPDATE)
                    .build();

            virtualIdentityRepository.save(virtualIdentityEntity);

            // 记录操作
            if (Objects.nonNull(operationLogHandler)) {
                operationLogHandler.publishEvent(logRecord);
            }
        }
    }

    private VirtualIdentityEntity voTransFormEntity(VirtualIdentityEntity virtualIdentityEntity, VirtualIdentityVO virtualIdentityVo) {
        virtualIdentityEntity.setVirtualNumber(virtualIdentityVo.getVirtualNumber());
        virtualIdentityEntity.setVirtualType(virtualIdentityVo.getVirtualType());
        virtualIdentityEntity.setPersonId(virtualIdentityVo.getPersonId());
        virtualIdentityEntity.setVirtualTypeName(virtualIdentityVo.getVirtualTypeName());
        return virtualIdentityEntity;
    }

    /**
     * 添加银行卡信息
     *
     * @param relationPairs 关联集合
     * @param personId 人员id
     */
    private void addBankCard(Set<PersonAssociateRelationDTO.RelationPair> relationPairs, String personId) {
        for(PersonAssociateRelationDTO.RelationPair pair : relationPairs) {
            final String cardNumber = pair.getValue();
            if (Boolean.TRUE.equals(bankCardRepository.existsByPersonIdAndBankCardNumber(personId, cardNumber))) {
                continue;
            }
            BankCardVO vo = new BankCardVO();
            vo.setBankCardNumber(cardNumber);
            //TODO 开户行
            vo.setBankOfDeposit("--");
            //在用
            vo.setUseType(1);
            vo.setIsAutomated(AUTOMATED);
            log.info("auto update bank-card: "+vo);

            BankCardEntity bankCard = new BankCardEntity();
            BeanUtil.copyPropertiesIgnoreNull(vo, bankCard);
            bankCard.setUseType(vo.getUseType().toString());
            bankCard.setPersonId(personId);
            bankCardRepository.save(bankCard);
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.UPDATE)
                    .module(OperateModule.BANK_CARD)
                    .newObj(JsonUtil.toJsonString(bankCard))
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(personId)
                    .desc("新增银行卡信息")
                    .build();
            if (Objects.nonNull(operationLogHandler)) {
                // 转发操作日志至队列
                operationLogHandler.publishEvent(logRecord);
            }
        }
    }

    /**
     * 添加家庭关系和社会关系信息
     *
     * @param relationPairs 关联集合
     * @param personId 人员id
     */
    private void addFamilyAndSociety(Set<PersonAssociateRelationDTO.RelationPair> relationPairs, String personId) {
        for(PersonAssociateRelationDTO.RelationPair pair : relationPairs) {
            String type;
            if(pair.getRelation() == PersonRelationEnum.RELATIVE || pair.getRelation() == PersonRelationEnum.GUARDIAN) {
                type = FAMILY;
            } else {
                type = SOCIETY;
            }
            if (Boolean.TRUE.equals(relationRepository.existsByPersonIdAndTypeAndIdNumber(personId, type, pair.getValue()))) {
                continue;
            }
            RelationEntity entity = new RelationEntity();
            entity.setType(type);
            entity.setRelation(pair.getRelation().getDesc());
            entity.setPersonId(personId);
            entity.setIdNumber(pair.getValue());
            entity.setIsAutomated(AUTOMATED);
            log.info("auto update relations: "+entity);

            final OperateModule module = entity.getType().equals(FAMILY)
                    ? OperateModule.FAMILY_RELATIONSHIP : OperateModule.SOCIAL_RELATIONSHIP;
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.UPDATE)
                    .module(module)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(personId)
                    .desc(Operator.ADD.getName() + module.getName())
                    .newObj(JsonUtil.toJsonString(entity))
                    .build();
            relationRepository.save(entity);
            // 记录操作
            if (Objects.nonNull(operationLogHandler)) {
                operationLogHandler.publishEvent(logRecord);
            }
        }
    }
}
