package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;


/**
 * 电话号码实体类
 *
 * <AUTHOR>
 * @date 2021/7/27 16:43
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_MOBILE_PHONE")
public class MobilePhoneEntity extends BaseEntity {

    private static final long serialVersionUID = 251313488101017625L;

    /**
     * 人员Id
     */
    private String personId;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 电话状态： 0 停用 1 启用
     */
    private String phoneStatus;

    /**
     * 是否常用: 0 不常用 1 常用
     */
    private String phoneUseStatus;

    /**
     * 是否为自动更新的
     */
    private String isAutomated;
}
