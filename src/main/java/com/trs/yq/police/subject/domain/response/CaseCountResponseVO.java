package com.trs.yq.police.subject.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 案事件数量统计
 *
 * <AUTHOR>
 * @date 2021/9/4 10:06
 */
@Getter
@Setter
@ToString
public class CaseCountResponseVO implements Serializable {

    private static final long serialVersionUID = -95300528307536616L;

    /**
     * 案件总数
     */
    private Integer caseCount;

    /**
     * 受案数量
     */
    private Integer acceptCount;

    /**
     * 立案数量
     */
    private Integer createCount;

    /**
     * 破案数量
     */
    private Integer closeCount;
}
