package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.entity.FilterValue;
import com.trs.yq.police.subject.service.DepartmentService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 单位相关查询
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@RestController
public class DepartmentController {

    @Resource
    private DepartmentService departmentService;

    /**
     * 获取所有单位列表
     *
     * @return 所有单位列表
     */
    @GetMapping("/department")
    List<FilterValue> getDepartmentList() {
        return departmentService.getAllUnitList();
    }

    /**
     * 获取派出所列表
     *
     * @return 派出所列表
     */
    @GetMapping("/stations")
    List<FilterValue> getPoliceStationList() {
        return departmentService.getPoliceStationList();
    }

    /**
     * 全部门的树，带层级
     *
     * @return 树
     */
    @GetMapping("/department/tree")
    List<FilterValue> getUnitTreeList() {
        return departmentService.getUnitTree();
    }
}
