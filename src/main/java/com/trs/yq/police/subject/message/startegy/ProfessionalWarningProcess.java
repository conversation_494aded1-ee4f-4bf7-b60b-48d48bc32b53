package com.trs.yq.police.subject.message.startegy;

import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.dto.ProfessionalWarningDTO;
import com.trs.yq.police.subject.domain.dto.ProfessionalWarningDTO.JzsdData;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.message.WarningPushService;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FK_SUBJECT;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;
import static com.trs.yq.police.subject.constants.WarningConstants.*;

/**
 * 技侦手段预警消费
 *
 * <AUTHOR>
 * @date 2023/7/21 10:30
 */
@Slf4j
@Service
public class ProfessionalWarningProcess {

    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;

    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private WarningTypeRepository warningTypeRepository;

    @Resource
    private PersonRepository personRepository;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private WarningPushService warningPushService;
    @Resource
    private WarningPushLogRepository warningPushLogRepository;

    private final Map<String, Map<String, WarningTypeEntity>> typeMap = new HashMap<>();

    @PostConstruct
    void init() {
        typeMap.put(FK_SUBJECT, getFkJzsdTypes());
        typeMap.put(WW_SUBJECT, getWwJzsdTypes());
    }

    @NotNull
    private Map<String, WarningTypeEntity> getFkJzsdTypes() {
        Map<String, WarningTypeEntity> map = new HashMap<>();
        WarningTypeEntity lkcs = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_LKCS);
        if (Objects.nonNull(lkcs)) {
            map.put("YJ_MB_LKCS", lkcs);
        }
        WarningTypeEntity ddcs = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_DDCS);
        if (Objects.nonNull(ddcs)) {
            map.put("YJ_MB_DDCS", ddcs);
        }
        WarningTypeEntity qjjj = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_QJJJ);
        if (Objects.nonNull(qjjj)) {
            map.put("YJ_QT_MBJJ", qjjj);
        }
        WarningTypeEntity hjhk = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_HJHK);
        if (Objects.nonNull(hjhk)) {
            map.put("YJ_MB_HJHK", hjhk);
        }
        WarningTypeEntity kuasheng = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_KUASHENG);
        if (Objects.nonNull(kuasheng)) {
            map.put("YJ_MB_KUASHENG", kuasheng);
        }
        WarningTypeEntity kuashi = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_KUASHI);
        if (Objects.nonNull(kuashi)) {
            map.put("YJ_MB_KUASHI", kuashi);
        }
        WarningTypeEntity kuaxian = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_KUAXIAN);
        if (Objects.nonNull(kuaxian)) {
            map.put("YJ_MB_KUAXIAN", kuaxian);
        }
        WarningTypeEntity lkczd = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_LKCZD);
        if (Objects.nonNull(lkczd)) {
            map.put("YJ_MB_FRCHDQY", lkczd);
        }
        WarningTypeEntity ymgdqtl = warningTypeRepository.findByEnName(WARNING_TYPE_FK_JZSDYJ_YMGDQTL);
        if (Objects.nonNull(ymgdqtl)) {
            map.put("YJ_MB_YMGDQTL", ymgdqtl);
        }
        return map;
    }

    @NotNull
    private Map<String, WarningTypeEntity> getWwJzsdTypes() {
        Map<String, WarningTypeEntity> map = new HashMap<>();
        WarningTypeEntity lkcs = warningTypeRepository.findByEnName(WARNING_TYPE_WW_JZSDYJ_LKCS);
        if (Objects.nonNull(lkcs)) {
            map.put("YJ_MB_LKCS", lkcs);
        }
        WarningTypeEntity kuasheng = warningTypeRepository.findByEnName(WARNING_TYPE_WW_JZSDYJ_KUASHENG);
        if (Objects.nonNull(kuasheng)) {
            map.put("YJ_MB_KUASHENG", kuasheng);
        }
        WarningTypeEntity kuashi = warningTypeRepository.findByEnName(WARNING_TYPE_WW_JZSDYJ_KUASHI);
        if (Objects.nonNull(kuashi)) {
            map.put("YJ_MB_KUASHI", kuashi);
        }
        WarningTypeEntity kuaxian = warningTypeRepository.findByEnName(WARNING_TYPE_WW_JZSDYJ_KUAXIAN);
        if (Objects.nonNull(kuaxian)) {
            map.put("YJ_MB_KUAXIAN", kuaxian);
        }
        WarningTypeEntity lkczd = warningTypeRepository.findByEnName(WARNING_TYPE_WW_JZSDYJ_LKCZD);
        if (Objects.nonNull(lkczd)) {
            map.put("YJ_MB_FRCHDQY", lkczd);
        }
        WarningTypeEntity ddcs = warningTypeRepository.findByEnName(WARNING_TYPE_WW_JZSDYJ_DDCS);
        if (Objects.nonNull(ddcs)) {
            map.put("YJ_MB_DDCS", ddcs);
        }
        return map;
    }

    /**
     * 处理消息
     *
     * @param message   消息
     * @param subjectId 主体id
     */
    public void process(String message, String subjectId) {
        final ProfessionalWarningDTO dto = JsonUtil.parseObject(message, ProfessionalWarningDTO.class);

        if (Objects.nonNull(dto)) {
            JzsdData detail = JsonUtil.parseObject(dto.getDATA(), JzsdData.class);
            if(Objects.isNull(detail)){
                log.error("专业手段预警数据解析失败，message:{}", message);
                return;
            }
            WarningTypeEntity type = typeMap.get(subjectId).get(detail.getSUBALARMTYPE());
            WarningEntity warningEntity = new WarningEntity();
            warningEntity.setWarningLevel(type.getDefaultLevel());
            warningEntity.setAreaCode(dto.getDISTRICT());
            warningEntity.setWarningDetails(detail.getRESULTDESC());
            warningEntity.setWarningTime(LocalDateTime.parse(detail.getSENDTIME(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            warningEntity.setSubjectId(type.getSubjectId());
            warningEntity.setWarningType(type.getId());
            warningEntity.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
            warningEntity.setWarningSource(Collections.singletonList(dto.getJOBNAME()));

            WarningTrajectoryEntity trajectoryEntity = new WarningTrajectoryEntity();
            trajectoryEntity.setSourceId(type.getId());
            trajectoryEntity.setLng(detail.getLONGITUDE());
            trajectoryEntity.setLat(detail.getLATITUDE());
            trajectoryEntity.setDateTime(LocalDateTime.parse(detail.getSENDTIME(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            trajectoryEntity.setAreaCode(dto.getDISTRICT());
            trajectoryEntity.setRawData(message);

            //匹配人员信息，如果匹配上就回填身份证信息
            String phoneNumber = detail.getTARGET();
            if (StringUtil.checkPhoneNumber(phoneNumber)) {
                PersonEntity person = personRepository.findByPhoneNumber(phoneNumber, subjectId);
                //如果匹配到人员就回填身份证
                if (Objects.nonNull(person)) {
                    trajectoryEntity.setIdNumber(person.getIdNumber());
                    controlRepository.findByPersonIdAndSubjectId(person.getId(), subjectId).ifPresent(control -> {
                        String areaCode = StringUtils.isNotBlank(control.getPoliceStationCode()) ? control.getPoliceStationCode()
                                .substring(0, 6) : "510500";
                        warningEntity.setAreaCode(areaCode);
                    });
                }
                //匹配不上就把手机号显示到预警信息里面
                else {
                    warningEntity.setWarningDetails("手机号：" + phoneNumber + " " + detail.getRESULTDESC());
                }
            }else{
                // 信临聚集预警轨迹数据处理
                // 手机号码正则表达式
                String mobileRegex = "(\\d{11})";
                // 身份证号码正则表达式
                String idNumberRegex = "(\\d{17}(\\d|x|X))";

                Pattern mobilePattern = Pattern.compile(mobileRegex);
                Pattern idNumberPattern = Pattern.compile(idNumberRegex);

                Matcher mobileMatcher = mobilePattern.matcher(detail.getRESULTDESC());
                Matcher idNumberMatcher = idNumberPattern.matcher(detail.getRESULTDESC());

                List<String> phones = new ArrayList<>();

                while (mobileMatcher.find() && idNumberMatcher.find()) {
                    String mobileNumber = mobileMatcher.group();
                    String idNumber = idNumberMatcher.group();
                    phones.add(mobileNumber);
                }

                List<WarningTrajectoryEntity> trajectories = phones.stream()
                        .map(phone -> personRepository.findByPhoneNumber(phone, subjectId))
                        .filter(Objects::nonNull).map(person -> {
                            WarningTrajectoryEntity trajectory = new WarningTrajectoryEntity();
                            trajectory.setSourceId(type.getId());
                            trajectory.setLng(detail.getLONGITUDE());
                            trajectory.setLat(detail.getLATITUDE());
                            trajectory.setDateTime(LocalDateTime.parse(detail.getSENDTIME(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            trajectory.setAreaCode(dto.getDISTRICT());
                            trajectory.setRawData(message);

                            trajectory.setIdNumber(person.getIdNumber());
                            controlRepository.findByPersonIdAndSubjectId(person.getId(), subjectId).ifPresent(control -> {
                                String areaCode = StringUtils.isNotBlank(control.getPoliceStationCode()) ? control.getPoliceStationCode()
                                        .substring(0, 6) : "510500";
                                warningEntity.setAreaCode(areaCode);
                            });
                            return trajectory;
                        }

                ).collect(Collectors.toList());

                //预警入库
                warningRepository.save(warningEntity);
                warningTrajectoryRepository.saveAll(trajectories);

                //关系入库
                List<WarningTraceRelationEntity> relationEntityList = trajectories.stream().map(trajectory -> {
                    WarningTraceRelationEntity warningTraceRelationEntity = new WarningTraceRelationEntity();
                    warningTraceRelationEntity.setWarningId(warningEntity.getId());
                    warningTraceRelationEntity.setTrajectoryId(trajectory.getId());
                    warningTraceRelationEntity.setCreateTime(LocalDateTime.now());
                    return warningTraceRelationEntity;
                }).collect(Collectors.toList());

                warningTraceRelationRepository.saveAll(relationEntityList);

            }

            //预警入库
            warningRepository.save(warningEntity);

            //轨迹入库
            warningTrajectoryRepository.save(trajectoryEntity);

            //关系入库
            WarningTraceRelationEntity warningTraceRelationEntity = new WarningTraceRelationEntity();
            warningTraceRelationEntity.setWarningId(warningEntity.getId());
            warningTraceRelationEntity.setTrajectoryId(trajectoryEntity.getId());
            warningTraceRelationEntity.setCreateTime(LocalDateTime.now());
            warningTraceRelationRepository.save(warningTraceRelationEntity);

            //发短信
            //技侦手段预警推短信
            WarningPushLogEntity pushLog = new WarningPushLogEntity();
            pushLog.setWarningType(type.getEnName());
            pushLog.setSubjectId(type.getSubjectId());
            pushLog.setTime(LocalDateTime.now());
            pushLog.setIdType(trajectoryEntity.getIdType());
            pushLog.setIdValue(trajectoryEntity.getIdValue());
            warningPushLogRepository.save(pushLog);
            warningPushService.pushJzsdMessage(warningEntity, trajectoryEntity.getIdNumber());
        }
    }
}
