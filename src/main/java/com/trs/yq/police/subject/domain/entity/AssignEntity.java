package com.trs.yq.police.subject.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 指派分局信息表实体类
 *
 * <AUTHOR>
 * @since 2021/8/6
 */
@Entity
@Table(name = "T_PS_PERSON_ASSIGN")
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class AssignEntity extends BaseEntity{

    private static final long serialVersionUID = 3585737630208113358L;

    /**
     * 人员id
     */
    private String personId;
    /**
     * 专题id
     */
    private String subjectId;
    /**
     * 指派分局code
     */
    private String bureauCode;
    /**
     * 指派分局名称
     */
    private String bureauName;
}
