package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 显示类别枚举
 *
 * <AUTHOR>
 * @since 2021/9/7
 */
public enum DisplayTypeEnum {

    /**
     * enum
     */
    NO_TRACK("0", "无轨迹预警"),
    SINGLE_PERSON("1", "单人"),
    MULTI_PERSON("2", "多人"),
    CALL_DETAIL_RECORD("3", "话单"),
    CAR_DETAIL_RECORD("4","车辆"),
    PROFESSIONAL("5","专业手段"),
    CRJ("6", "出入境"),
    ;

    DisplayTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * 显示类别code转换枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static DisplayTypeEnum codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (DisplayTypeEnum display : DisplayTypeEnum.values()) {
                if (StringUtils.equals(code, display.code)) {
                    return display;
                }
            }
        }
        return null;
    }
}
