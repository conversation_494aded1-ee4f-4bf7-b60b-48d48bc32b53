package com.trs.yq.police.subject.constants.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Objects;
import lombok.Getter;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/22 17:55
 */
public enum CrjJwrySourceTypeEnum {
    JWRY(1, "自主申报"),
    LDY(2, "旅店登记"),
    MANUAL_ENROLLMENT(3, "民警登记"),
    IMPORT(4, "系统导入");

    CrjJwrySourceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    @Getter
    private final Integer code;

    @Getter
    private final String name;

    /**
     * 通过模块码获取枚举
     *
     * @param code 操作符码
     * @return 操作符 {@link Operator}
     * <AUTHOR>
     * @since 2021/7/27 17:55
     */
    public static CrjJwrySourceTypeEnum codeOf(Integer code) {

        if (Objects.nonNull(code)) {
            for (CrjJwrySourceTypeEnum syncStatus : CrjJwrySourceTypeEnum.values()) {
                if (code.equals(syncStatus.getCode())) {
                    return syncStatus;
                }
            }
        }
        return CrjJwrySourceTypeEnum.IMPORT;
    }
}
