package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 流动信息视图层
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Validated
public class MobilityVO implements Serializable {

    private static final long serialVersionUID = 1225646277350960558L;

    /**
     * 流动信息id
     */
    private String id;
    /**
     * 流动时间
     */
    @NotNull(message = "流入时间不能为空")
    private Long moveTime;
    /**
     * 流动类型 流入=1 流出=2
     */
    @NotBlank(message = "流动类型不能为空")
    private String moveType;
    /**
     * 地址
     */
    private String location;
    /**
     * 备注
     */
    private String note;
}
