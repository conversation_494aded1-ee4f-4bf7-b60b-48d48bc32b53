package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 话单预警信息
 *
 * <AUTHOR>
 * @date 2021/9/8 21:58
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_CALL")
public class WarningCallEntity implements Serializable {

    private static final long serialVersionUID = 1006567512756956937L;

    /**
     * 主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * TRS 表
     */
    private String trsTable;

    /**
     * TRS 主键
     */
    private String trsId;

    /**
     * 命中的电话号码
     */
    private String phoneNumber;

    /**
     * 呼叫时间
     */
    private LocalDateTime callTime;

    /**
     * 呼叫时长，单位：秒
     */
    private Long callDuration;

    /**
     * 呼叫类型
     */
    private String callType;

    /**
     * 涉毒人员身份证号码
     */
    private String idNumber;

    /**
     * 预警时间
     */
    private LocalDateTime warningTime;

    /**
     * 预警号码
     */
    private String warningNumber;

    /**
     * 预警类型
     */
    private String type;
}
