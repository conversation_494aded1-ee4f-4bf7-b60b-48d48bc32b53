package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 角色-操作权限映射实体类
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_ADMIN_ROLE_OPERATION")
public class RoleOperationEntity extends BaseEntity {

    private static final long serialVersionUID = 6460249081578271458L;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 操作名称
     */
    private String operationName;
    /**
     * 微服务模块名称
     */
    private String service;
}
