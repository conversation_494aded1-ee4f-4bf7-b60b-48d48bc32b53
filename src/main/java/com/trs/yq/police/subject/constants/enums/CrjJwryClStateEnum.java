package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 * 处理状态
 */
public enum CrjJwryClStateEnum {
    /**
     * enums
     */
    FAIL_HANDLE("-1", "处理失败"),
    NOT_HANDLE("0", "未处理"),
    HANDLED("1", "已处理");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    CrjJwryClStateEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
