package com.trs.yq.police.subject.domain.params;

import java.util.Locale;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Sort;

import java.io.Serializable;

/**
 * 排序参数
 *
 * <AUTHOR>
 * @date 2021/08/10
 */
@Data
public class SortParams implements Serializable {

    private static final long serialVersionUID = 1260023244042563824L;

    private String sortField;
    private String sortDirection;

    /**
     * 转换为spring data的Sort对象
     *
     * @return {@link Sort}
     */
    public Sort toSort() {
        if (StringUtils.isNotBlank(sortDirection) && StringUtils.isNotBlank(sortField)) {
            return Sort.by(Sort.Direction.valueOf(sortDirection.toUpperCase(Locale.ROOT)), sortField);
        }
        return Sort.unsorted();
    }
}
