package com.trs.yq.police.subject.domain.request;

import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 数据视图数量统计请求参数
 *
 * <AUTHOR>
 * @date 2021/9/3 20:05
 */
@Getter
@Setter
@ToString
public class CountRequestVO implements Serializable {

    private static final long serialVersionUID = -6404950875104022247L;

    /**
     * 时间范围
     */
    private TimeParams timeParams;

    /**
     * 专题id
     */
    private String subjectId;
}
