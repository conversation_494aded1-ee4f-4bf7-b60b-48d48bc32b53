package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.ClueModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileTypeEnum;
import com.trs.yq.police.subject.domain.entity.ClueFileRelationEntity;
import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import com.trs.yq.police.subject.domain.entity.PersonFileRelationEntity;
import com.trs.yq.police.subject.domain.vo.AttachmentVO;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.exception.RemoteStorageException;
import com.trs.yq.police.subject.repository.ClueFileRelationRepository;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.repository.PersonFileRelationRepository;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.utils.VideoUtil;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 远端存储业务层
 *
 * <AUTHOR>
 * @date 2021/7/30 9:49
 */
@Service
@Slf4j
public class RemoteStorageServiceMinioImpl implements RemoteStorageService {

    @Resource
    private MinioClient minioClient;

    @Resource
    private ClueFileRelationRepository clueFileRelationRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;

    @Resource
    private PersonFileRelationRepository personFileRelationRepository;

    @Value("${com.trs.fastdfs.group_name}")
    private String groupName;

    @Resource(name = "uploadExecutor")
    private Executor uploadExecutor;

    @Override
    public String[] uploadFile(byte[] buff, String groupName, String fileExtension) {
        try {

            final String md5 = DigestUtils.md5Hex(buff);

            String filePath = StringUtils.join(new String[]{"yq", "policesubject", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM"))}, "/");

            minioClient.putObject(PutObjectArgs.builder().bucket(groupName).object("/" + filePath + "/" + md5 + "." + fileExtension).stream(new ByteArrayInputStream(buff), buff.length, -1).build());
            return new String[]{groupName, filePath + "/" + md5 + "." + fileExtension};
        } catch (Exception e) {
            log.error("upload file to minio failed! error code: ", e);
            throw new RemoteStorageException("上传文件到Minio失败!", groupName, fileExtension);
        }
    }


    @Override
    public void deleteFile(String groupName, String fileId) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(groupName).object(fileId).build());
        } catch (Exception exception) {
            log.error("delete file to minio failed! ", exception);
            throw new RemoteStorageException("从Minio删除文件失败!", groupName, fileId);
        }
    }

    @Override
    public byte[] downloadFile(String groupName, String fileId) {
        try {
            final InputStream inputStream = minioClient.getObject(GetObjectArgs.builder().bucket(groupName).object(fileId).build());

            return IOUtils.toByteArray(inputStream);
        } catch (Exception exception) {
            log.error("download file from minio failed! ", exception);
            throw new RemoteStorageException("从Minio获取文件失败!", groupName, fileId);
        }
    }

    @Override
    public ImageVO uploadImage(MultipartFile image) throws IOException {
        FileStorageEntity entity = createFileStorageEntity(image, "2");
        fileStorageRepository.save(entity);
        ImageVO vo = new ImageVO();
        vo.setImageId(entity.getId());
        vo.setUrl(entity.getUrl());
        return vo;
    }

    @Override
    public ImageVO uploadImage(byte[] bytes, String fileName) {
        final String extension = FilenameUtils.getExtension(fileName);
        final String[] paths = uploadFile(bytes, groupName, extension);
        FileStorageEntity entity = new FileStorageEntity();
        entity.setName(fileName);
        entity.setExtensionName(extension);
        entity.setMd5(DigestUtils.md5Hex(bytes));
        entity.setType(FileTypeEnum.IMAGE.getCode());
        entity.setFileSize((long) bytes.length);
        entity.setGroupName(paths[0]);
        entity.setPath(paths[1]);
        final String url = "/police-subject/file/".concat(String.join("/", paths[0], paths[1]));
        entity.setUrl(url);
        fileStorageRepository.save(entity);
        ImageVO vo = new ImageVO();
        vo.setImageId(entity.getId());
        vo.setUrl(entity.getUrl());
        return vo;
    }

    @NotNull
    private FileStorageEntity createFileStorageEntity(MultipartFile file, String type) throws IOException {
        final String filename = file.getOriginalFilename();
        final String extension = FilenameUtils.getExtension(filename);
        FileStorageEntity entity = new FileStorageEntity();
        entity.setName(filename);
        entity.setExtensionName(extension);
        entity.setFileSize(file.getSize());
        entity.setType(StringUtils.isNotBlank(type) ? type : FileTypeEnum.ofExtension(extension).getCode());
        final byte[] bytes = getBytes(file, entity.getType());
        final String[] paths = uploadFile(bytes, groupName, extension);
        entity.setMd5(DigestUtils.md5Hex(bytes));
        entity.setGroupName(paths[0]);
        entity.setPath(paths[1]);
        final String url = "/police-subject/file/".concat(String.join("/", paths[0], paths[1]));
        //视频文件设置预览图片
        if (entity.getType().equals(FileTypeEnum.VIDEO.getCode())) {
            byte[] previewImage = VideoUtil.extractImgFromVideo(file.getInputStream(), 3);
            final String[] previewImagePaths = uploadFile(previewImage, groupName, "jpg");
            final String previewImageUrl = "/police-subject/file/".concat(String.join("/", previewImagePaths[0], previewImagePaths[1]));
            entity.setPreviewImage(previewImageUrl);
        }
        entity.setUrl(url);
        return entity;
    }

    /**
     * 读取文件内容，如果是视频，需要转码
     *
     * @param file 原始文件
     * @param type 文件类别
     * @return 文件内容
     * @throws IOException io异常
     */
    private byte[] getBytes(MultipartFile file, String type) throws IOException {
        if (type.equals(FileTypeEnum.VIDEO.getCode())) {
            byte[] bytes = VideoUtil.covertVideoToMp4(file);
            if (Objects.nonNull(bytes)) {
                return bytes;
            }
        }
        return file.getBytes();
    }

    @Override
    public List<ImageVO> uploadImages(List<MultipartFile> images) {

        AtomicInteger index = new AtomicInteger(0);
        ConcurrentMap<Integer, MultipartFile> files = images.stream().collect(Collectors.toConcurrentMap(image -> index.incrementAndGet(), image -> image));

        ConcurrentMap<Integer, ImageVO> result = new ConcurrentHashMap<>(16);

        CompletableFuture.allOf(files.entrySet().stream().map(entry -> CompletableFuture.completedFuture(entry).thenRunAsync(() -> {
            try {
                result.put(entry.getKey(), uploadImage(entry.getValue()));
            } catch (IOException e) {
                log.error("文件上传失败！", e);
                result.put(entry.getKey(), new ImageVO());
            }
        }, uploadExecutor)).toArray(CompletableFuture[]::new)).join();

        return result.entrySet().stream().sorted(Map.Entry.comparingByKey()).map(Map.Entry::getValue).collect(Collectors.toList());
    }

    @Override
    public void savePersonImageRelations(String personId, List<ImageVO> images, FileModuleEnum module, @Nullable String recordId) {

        final List<String> existImages = personFileRelationRepository.findAllByPersonIdAndModuleAndRecordId(personId, module.getCode(), StringUtils.trimToNull(recordId)).stream().filter(Objects::nonNull).map(PersonFileRelationEntity::getFileStorageId).collect(Collectors.toList());

        final List<PersonFileRelationEntity> relations = images.stream().filter(image -> Objects.nonNull(image) && existImages.stream().noneMatch(imageId -> image.getImageId().equals(imageId))).map(image -> {
            PersonFileRelationEntity relation = new PersonFileRelationEntity();
            relation.setPersonId(personId);
            relation.setFileStorageId(image.getImageId());
            relation.setModule(module.getCode());
            relation.setType(FileTypeEnum.IMAGE.getCode());
            if (StringUtils.isNotBlank(recordId)) {
                relation.setRecordId(recordId);
            }
            return relation;
        }).collect(Collectors.toList());
        personFileRelationRepository.saveAll(relations);
    }

    @Override
    public void updatePersonImageRelations(String personId, List<ImageVO> images, FileModuleEnum module, @Nullable String recordId) {
        if (Objects.isNull(images)) {
            return;
        }
        List<PersonFileRelationEntity> existImages = personFileRelationRepository.findAll(personId, FileTypeEnum.IMAGE.getCode(), module.getCode(), recordId);
        // 需要删除的图片
        List<PersonFileRelationEntity> imagesToRemove = existImages.stream().filter(image -> images.stream().noneMatch(img -> img.getImageId().equals(image.getFileStorageId()))).collect(Collectors.toList());
        personFileRelationRepository.deleteAll(imagesToRemove);

        // 需要新增的图片
        List<ImageVO> imagesToAdd = images.stream().filter(image -> existImages.stream().noneMatch(img -> img.getFileStorageId().equals(image.getImageId()))).collect(Collectors.toList());
        savePersonImageRelations(personId, imagesToAdd, module, recordId);
    }

    @Override
    public void deletePersonImageRelations(String personId, FileModuleEnum module, @Nullable String recordId) {
        List<PersonFileRelationEntity> exists = personFileRelationRepository.findAll(personId, FileTypeEnum.IMAGE.getCode(), module.getCode(), recordId);
        personFileRelationRepository.deleteAll(exists);
    }

    @Override
    public void deleteClueImageRelation(String clueId, ClueModuleEnum module, @Nullable String recordId) {
        List<ClueFileRelationEntity> exists = clueFileRelationRepository.findAll(clueId, FileTypeEnum.IMAGE.getCode(), module.getCode(), recordId);
        if (Objects.isNull(exists)) {
            return;
        }
        clueFileRelationRepository.deleteAll(exists);
    }

    @Override
    public List<AttachmentVO> uploadAttachments(List<MultipartFile> files) {
        return files.stream().map(file -> {
            try {
                FileStorageEntity entity = createFileStorageEntity(file, null);
                fileStorageRepository.save(entity);
                AttachmentVO vo = new AttachmentVO();
                vo.setId(entity.getId());
                vo.setUrl(entity.getUrl());
                vo.setName(entity.getName());
                vo.setSize(FileUtils.byteCountToDisplaySize(file.getSize()));
                vo.setPreviewImage(entity.getPreviewImage());
                return vo;
            } catch (IOException exception) {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public ResponseEntity<ByteArrayResource> downLoadFile(String fileId) throws UnsupportedEncodingException {
        FileStorageEntity fileEntity = fileStorageRepository.findById(fileId).orElse(null);
        if (Objects.isNull(fileEntity)) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }

        final byte[] data = downloadFile(groupName, fileEntity.getPath());
        final ByteArrayResource resource = new ByteArrayResource(data);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(fileEntity.getName(), StandardCharsets.UTF_8.name()));
        headers.add(HttpHeaders.PRAGMA, "no-cache");
        headers.add(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");

        return ResponseEntity.ok().headers(headers).contentLength(resource.contentLength()).contentType(MediaType.APPLICATION_OCTET_STREAM).body(resource);
    }

    @Override
    public byte[] downloadPhoto(String idNumber) {
        try {
            GetObjectArgs args = GetObjectArgs.builder().bucket("photo").object(idNumber + ".jpg").build();
            final InputStream inputStream = minioClient.getObject(args);
            return IOUtils.toByteArray(inputStream);
        } catch (Exception ignore) {
            try {
                GetObjectArgs args = GetObjectArgs.builder().bucket("photo").object(idNumber + ".png").build();
                final InputStream inputStream = minioClient.getObject(args);
                return IOUtils.toByteArray(inputStream);
            } catch (Exception exception) {
                log.error("download file from minio failed! ", exception);
                throw new RemoteStorageException("从Minio获取文件失败!", groupName, idNumber);
            }
        }
    }
}
