package com.trs.yq.police.subject.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.regex.Pattern;

import static java.util.regex.Pattern.compile;

/**
 * 字符串工具类
 *
 * <AUTHOR>
 * @date 2021/3/22 13:27
 */
@Slf4j
public class StringUtil {

    private StringUtil() {
    }

    /**
     * 身份证前17位系数
     */
    private static final int[] COEFFICIENT = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    /**
     * 尾数校验位
     */
    private static final char[] LAST = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};

    /**
     * 身份证长度
     */
    private static final int ID_NUMBER_STANDARD_LENGTH = 18;

    /**
     * 默认时间： 1880-01-01 00:00:00
     */
    public static final long DEFAULT_DATE_TIME_UTC = -2840169943000L;

    public static final Pattern VALIDATE_ID_NUMBER_PATTERN = compile("^[0-9]{17}[0-9Xx]$");

    /**
     * 判断身份证号码是否满足要求
     *
     * @param value 输入字符串
     * @return boolean 是否满足要求
     */
    public static boolean checkIdNumber(String value) {

        final int lastIndex = 17;

        if (StringUtils.isBlank(value)
                || value.length() != ID_NUMBER_STANDARD_LENGTH
                || !VALIDATE_ID_NUMBER_PATTERN.matcher(value).matches()) {
            // 非18位直接返回false
            return false;
        }
        // 前17位加权和
        int sum = 0;
        for (int i = 0; i < lastIndex; i++) {
            if (!Character.isDigit(value.charAt(i))) {
                // 前17位必须是数字,如果不是直接返回false
                return false;
            }
            // 对前17位进行加权求和
            sum += Integer.parseInt(value.charAt(i) + "") * COEFFICIENT[i];
        }

        return value.charAt(lastIndex) == LAST[sum % LAST.length];
    }

    /**
     * 从身份证获取出生日期
     *
     * @param idNumber 身份证好
     * @return 出生日期
     */
    public static long parseBirthdayFromIdNumber(String idNumber) {
        if (StringUtils.isBlank(idNumber) || idNumber.length() < ID_NUMBER_STANDARD_LENGTH) {
            return DEFAULT_DATE_TIME_UTC;
        }
        String date = idNumber.substring(6, 14);
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.BASIC_ISO_DATE);
        return localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 从身份证中获取性别
     *
     * @param str          输入
     * @param outputCnName 输出中文名
     * @return 性别
     */
    public static String parseGenderFromIdNumber(String str, boolean outputCnName) {
        final String man = outputCnName ? "男" : "0";
        final String woman = outputCnName ? "女" : "1";
        if (StringUtils.isBlank(str) || str.length() < ID_NUMBER_STANDARD_LENGTH) {
            return "";
        }
        int gender = Integer.parseInt(str.substring(16, 17));
        return gender % 2 == 0 ? woman : man;
    }

    /**
     * 获取部门前缀(最多取前6位)
     *
     * @param text 部门编号
     * @return 部门前缀
     */
    public static String getDepartmentIdPrefix(String text) {

        if (text == null || text.isEmpty()) {
            return "";
        }
        return text.substring(0, 6).replaceAll("00$", "");
    }

    /**
     * code去00
     *
     * @param text code
     * @return code
     */
    public static String getPrefixCode(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }
        return compile("(00)+$").matcher(text).replaceAll("");
    }

    /**
     * 获取派出所code前缀(去0)
     *
     * @param text 部门编号
     * @return 部门前缀
     */
    public static String getPoliceStationPrefix(String text) {

        if (text == null || text.isEmpty()) {
            return "";
        } else if (text.length() < 12) {
            return text;
        }
        return text.substring(0, 6) + text.substring(6, 12).replaceAll("00", "");
    }

    /**
     * AES + base64 解密
     *
     * @param content 解密内容
     * @param sKey    密钥
     * @return {@link String}
     */
    public static String aesDecrypt(String content, String sKey) {
        try {
            final byte[] finalKey = new byte[16];
            int i = 0;
            for (byte b : sKey.getBytes(StandardCharsets.US_ASCII))
                finalKey[i++ % 16] ^= b;
            SecretKeySpec key = new SecretKeySpec(finalKey, "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] cleartext = Base64.getDecoder().decode(content);
            byte[] ciphertextBytes = cipher.doFinal(cleartext);
            return new String(ciphertextBytes, StandardCharsets.UTF_8);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidKeyException | IllegalBlockSizeException
                 | BadPaddingException e) {
            log.error("解密失败！", e);
            return "";
        }
    }

    /**
     * 校验手机号是否合法
     *
     * @param phoneNumber 手机号
     * @return boolean
     */
    public static boolean checkPhoneNumber(String phoneNumber) {
        return StringUtils.isNotBlank(phoneNumber)
                && phoneNumber.matches("^1(3\\d|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8\\d|9[0-35-9])\\d{8}$");
    }

    /**
     * 去除字符串中的特殊字符（防止sql注入）
     *
     * @param targetString 需要去除特殊字符的字符串
     * @return {@link String}
     */
    public static String removeSpecialCharacters(String targetString) {
        if (StringUtils.isBlank(targetString)) {
            return null;
        }
        return targetString.replace("\\", "\\\\")
                .replace("%", "\\%")
                .replace("_", "\\_");
    }
}
