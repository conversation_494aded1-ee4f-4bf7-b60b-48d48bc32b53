package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.vo.IdNameVO;
import com.trs.yq.police.subject.domain.vo.LabelQueryVO;
import com.trs.yq.police.subject.domain.vo.LabelVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.PersonLabelRelationRepository;
import com.trs.yq.police.subject.service.LabelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;

/**
 * 标签管理业务层实现
 *
 * <AUTHOR>
 * @date 2021/7/31 19:07
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class LabelServiceImpl implements LabelService {

    @Resource
    private LabelRepository labelRepository;
    @Resource
    private PersonLabelRelationRepository personLabelRelationRepository;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String createLabel(LabelVO vo) {

        LabelEntity label = new LabelEntity();
        label.setName(vo.getName());
        label.setRemark(vo.getRemark());
        label.setSubjectId(vo.getSubjectId());
        label.setSubjectId(vo.getSubjectId());
        label.setCreateType("0");

        labelRepository.save(label);

        return label.getId();
    }

    @Override
    public LabelVO getLabel(String labelId) {

        final LabelEntity label = labelRepository.findById(labelId).orElse(null);

        if (Objects.isNull(label)) {
            throw new ParamValidationException("标签不存在，请核实！");
        }
        return new LabelVO(label);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteLabel(String labelId) {
        // 先移除关联关系
        personLabelRelationRepository.removeAllByLabelId(labelId);
        labelRepository.removeAllById(labelId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateLabel(LabelVO label) {

        final LabelEntity entity = labelRepository.findById(label.getId()).orElse(null);

        if (Objects.isNull(entity)) {
            throw new ParamValidationException("标签不存在，请核实！");
        }

        BeanUtils.copyProperties(label, entity,
                "createType",
                "id",
                "crBy",
                "crByName",
                "crDept",
                "crDeptCode",
                "crTime");

        labelRepository.save(entity);
    }

    @Override
    public PageResult<LabelVO> listLabels(LabelQueryVO query, Pageable pageable) {

        final String subjectId = StringUtils.trimToEmpty(query.getSubjectId());

        final Page<LabelEntity> page = labelRepository.findPage(subjectId, pageable);

        final List<LabelVO> labelVOList = page.getContent()
                .stream()
                .map(LabelVO::new)
                .collect(Collectors.toList());

        return PageResult.of(new PageImpl<>(labelVOList, page.getPageable(), page.getTotalElements()));
    }

    @Override
    public List<IdNameVO> getLabels(String subjectId, String module) {
        return labelRepository.findAllByModuleAndSubjectId(module, subjectId)
                .stream()
                .filter(Objects::nonNull)
                .map(labelEntity -> new IdNameVO(labelEntity.getId(), labelEntity.getName()))
                .collect(Collectors.toList());
    }
}
