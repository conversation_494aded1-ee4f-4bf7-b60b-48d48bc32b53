package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 线索服务层接口
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
public interface ClueService {
    /**
     * 根据人员id查询线索详情
     *
     * @param clueId 人员id
     * @return 线索详情vo
     */
    ClueVO getClueBasicInfo(String clueId);

    /**
     * 添加一条线索信息
     *
     * @param clueVO 线索详情
     * @return id
     */
    String addClue(ClueVO clueVO);

    /**
     * 删除一条线索以及线索关联的人员和文件
     *
     * @param clueId 线索详情
     */
    void deleteOne(String clueId);

    /**
     * 更新一条线索信息
     *
     * @param clueVO 线索详情
     */
    void updateOne(ClueVO clueVO);

    /**
     * 线索列表查询
     *
     * @param subjectId     专题id
     * @param listRequestVO {@link ListRequestVO}
     * @return 线索列表
     */
    PageResult<ClueListVO> getClueList(String subjectId, ListRequestVO listRequestVO);

    /**
     * 分页获取涉及线索的人员
     *
     * @param clueId     线索id
     * @param pageParams 分页参数
     * @return 人员信息列表
     */
    PageResult<ClueRelatedPersonVO> getRelatedPersonList(@NotBlank(message = "线索Id缺失") String clueId, PageParams pageParams);

    /**
     * 分页获取涉及线索中的群体
     *
     * @param clueId     线索id
     * @param pageParams 分页参数
     * @return 群体信息列表
     */
    PageResult<ClueRelatedGroupVO> getRelatedGroupList(@NotBlank(message = "线索Id缺失") String clueId, PageParams pageParams);

    /**
     * 删除一条线索以及线索关联的人员和文件
     *
     * @param clueIds 线索id
     */
    void deleteClues(@NotEmpty(message = "线索Id缺失") List<String> clueIds);

    /**
     * 批量关联线索 和群体
     *
     * @param clueGroupRelationVO {@link ClueGroupRelationVO}
     */
    void updateGroupClueRelation(@NotNull(message = "关联id缺失") ClueGroupRelationVO clueGroupRelationVO);

    /**
     * 批量关联线索 和人员
     *
     * @param cluePersonRelationVO {@link CluePersonRelationVO }
     */
    void updateCluePersonRelations(CluePersonRelationVO cluePersonRelationVO);

    /**
     * 取消线索-人员关联
     *
     * @param module     模块
     * @param relationId 关系id
     */
    void removePersonFromClue(ModuleEnum module, String relationId);

    /**
     * 取消线索-群体关联
     *
     * @param relationId 关系id
     */
    void removeGroupFromClue(String relationId);

    /**
     * 不分页查询线索已经关联的人员列表
     *
     * @param clueId 线索id
     * @return {@link ClueRelatedPersonVO}
     */
    List<ClueRelatedPersonVO> getPersonsFromClue(String clueId);

    /**
     * 不分页查询线索已经关联的群体列表
     *
     * @param clueId 线索id
     * @return {@link ClueRelatedGroupVO}
     */
    List<ClueRelatedGroupVO> getGroupsFromClue(String clueId);

    /**
     * 群体关联人员对话框中查询人员列表
     *
     * @param request {@link DialogClueListRequestVO}
     * @return {@link DialogClueListVO}
     */
    PageResult<DialogClueListVO> getDialogClueList(DialogClueListRequestVO request);

    /**
     * 根据线索id查询合成作战信息
     *
     * @param clueId 线索id
     * @return {@link BattleRecordCommandListVO}
     */
    List<BattleRecordCommandListVO> getBattleRecordList(String clueId);

    /**
     * 根据线索id查询指令信息
     *
     * @param clueId 线索id
     * @return {@link BattleRecordCommandListVO}
     */
    List<BattleRecordCommandListVO> getBattleCommandList(String clueId);

    /**
     * 获取线索文件
     *
     * @param clueId 线索id
     * @return {@link FileInfoVO}
     */
    List<FileInfoVO> getClueFileInfo(String clueId);

    /**
     * 添加线索所料关联信息
     *
     * @param clueId           线索
     * @param attachmentVOList 材料信息
     */
    void addClueFileRelation(String clueId, List<AttachmentVO> attachmentVOList);

    /**
     * 删除线索采莲关联
     *
     * @param clueId 线索id
     * @param fileId 材料id
     */
    void deleteClueFileRelation(String clueId, String fileId);

    /**
     * 上报线索
     *
     * @param clueId      线索id
     * @param currentUser 当前登录用户
     * @return {@link ReportInfoVO}
     */
    ReportInfoVO reportToSuperior(String clueId, LoginUser currentUser);

    /**
     * 获取线索能展示的按钮
     *
     * @param clueId 线索id
     * @return {@link ButtonVO}
     */
    ButtonVO getButton(String clueId);

    /**
     * 线索id
     *
     * @param clueId 线索id
     * @param status 状态
     */
    void changeDisposeStatus(String clueId, String status);
}
