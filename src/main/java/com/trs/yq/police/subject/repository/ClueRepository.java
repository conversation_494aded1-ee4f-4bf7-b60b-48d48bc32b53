package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 线索表查询接口
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Repository
public interface ClueRepository extends BaseRepository<ClueEntity, String> {

    /**
     * 根据专题id查询所有线索实体
     *
     * @param subjectId   专题id
     * @param typeId      线索类别id
     * @param createDept  创建单位id
     * @param searchValue 检索文字
     * @param pageable    {@link org.springframework.data.domain.Pageable}
     * @return {@link ClueEntity}
     */
    @Query("SELECT t1 " +
            "FROM ClueEntity t1 " +
            "WHERE t1.subjectId=:subjectId " +
            "AND (:typeId is null or exists (SELECT t2 FROM ClueLabelRelationEntity t2 WHERE t2.labelId=:typeId AND t2.clueId=t1.id)) " +
            "AND (:searchValue is null or t1.name like concat('%',:searchValue,'%')) " +
            "AND (:createDept is null or t1.crDeptCode like concat(:createDept,'%') ) " +
            "ORDER BY t1.upTime DESC")
    Page<ClueEntity> findAllBySubjectId(@Param("subjectId") String subjectId,
                                        @Param("typeId") String typeId,
                                        @Param("createDept") String createDept,
                                        @Param("searchValue") String searchValue,
                                        Pageable pageable);

    /**
     * 通过线索线索id查询所有线索
     *
     * @param clueIds 线索id列表
     * @return {@link ClueEntity}
     */
    List<ClueEntity> findAllByIdIn(List<String> clueIds);

    /**
     * 查询群体关联的所有线索
     *
     * @param groupId 群体id
     * @return {@link ClueEntity}
     */
    @Query("SELECT t2 FROM GroupClueRelationEntity t1 JOIN ClueEntity t2 ON t1.clueId=t2.id WHERE t1.groupId=:groupId")
    List<ClueEntity> findAllByGroupId(@Param("groupId") String groupId);

    /**
     * 查询群体关联的所有线索
     *
     * @param groupId  群体id
     * @param pageable {@link Pageable}
     * @return {@link ClueEntity}
     */
    @Query("SELECT t2 FROM GroupClueRelationEntity t1 JOIN ClueEntity t2 ON t1.clueId=t2.id WHERE t1.groupId=:groupId ORDER BY t2.upTime DESC")
    Page<ClueEntity> findAllByGroupId(String groupId, Pageable pageable);

    /**
     * 根据人员id和专题id查询线索列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param pageable  {@link Pageable}
     * @return {@link ClueEntity}
     */
    @Query("SELECT t2 FROM CluePersonRelationEntity t1 JOIN ClueEntity t2 ON t1.clueId=t2.id WHERE t1.personId=:personId AND t2.subjectId=:subjectId order by t1.upTime desc")
    Page<ClueEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId,
                                                   @Param("subjectId") String subjectId,
                                                   Pageable pageable);

    /**
     * 根据人员id和专题id查询线索列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link ClueEntity}
     */
    @Query("SELECT t2 FROM CluePersonRelationEntity t1 JOIN ClueEntity t2 ON t1.clueId=t2.id WHERE t1.personId=:personId AND t2.subjectId=:subjectId")
    List<ClueEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId);

    /**
     * 根据区域和时间统计线索数量
     *
     * @param subjectId 专题id
     * @param areaCode  区域编码
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 统计结果
     */
    @Query(nativeQuery = true, value = "SELECT COUNT(1) " +
            "FROM T_PS_CLUE clue " +
            "WHERE clue.SUBJECT_ID=:subjectId " +
            "AND (:areaCode is null or instr(clue.cr_dept_code,:areaCode)>0 )" +
            "AND clue.cr_time>=:beginTime " +
            "AND clue.cr_time<=:endTime")
    Long countAllByAreaAndTimeBetween(@Param("subjectId") String subjectId,
                                      @Param("areaCode") String areaCode,
                                      @Param("beginTime") LocalDateTime beginTime,
                                      @Param("endTime") LocalDateTime endTime);

    /**
     * 更新上报状态
     *
     * @param clueId       线索id
     * @param reportStatus 上报状态
     */
    @Modifying
    @Query("update ClueEntity c set c.reportStatus = :reportStatus where c.id = :clueId")
    void updateStatusByClueId(@Param("clueId") String clueId, @Param("reportStatus") String reportStatus);


    /**
     * 当当前时间大于“维权时间”时，未处置完成的线索自动变为“处置完毕”
     *
     * @param now 当前时间
     */
    @Modifying
    @Query(nativeQuery = true, value = "update T_PS_CLUE t1 " +
            "set t1.DISPOSAL_STATUS='2' " +
            "where t1.DISPOSAL_STATUS = '0' " +
            "  and t1.id in (select t2.CLUE_ID from T_PS_CLUE_EXTEND t2 where t2.OCCURRENCE_TIME < :now)")
    void updateDisposalStatusByOccurrenceTime(@Param("now") LocalDateTime now);

    /**
     * 查询已发指令的未处置线索
     *
     * @return 线索id列表
     */
    @Modifying
    @Query(value = "select c.ID from T_PS_CLUE c " +
            "where c.DISPOSAL_STATUS = '0' " +
            "and exists(select 1 from T_BATTLE_EVENT be, T_BATTLE_COMMAND bc " +
            "    where be.SRCTABLE='system.t_ps_clue' " +
            "    and be.KEYVAL = c.ID " +
            "    and INSTR(bc.EVENTIDS, be.ID)> 0" +
            ")", nativeQuery = true)
    List<String> selectDisposalStatusByCommand();

    /**
     * 查询已发合成的未处置线索
     *
     * @return 线索id列表
     */
    @Modifying
    @Query(value = "select c.ID from T_PS_CLUE c " +
            "where c.DISPOSAL_STATUS = '0' " +
            "and exists(select 1 from T_BATTLE_EVENT be, T_BATTLE_RECORD br " +
            "    where be.SRCTABLE='system.t_ps_clue' " +
            "    and be.KEYVAL = c.ID " +
            "    and INSTR(br.EVENTIDS, be.ID)> 0" +
            ")", nativeQuery = true)
    List<String> selectDisposalStatusByRecord();

    /**
     * 更新处置状态
     *
     * @param clueId  线索id
     * @param status 处置状态
     */
    @Modifying
    @Query("update ClueEntity c set c.disposalStatus = :status where c.id = :clueId")
    void updateDisposalStatusByClueId(@Param("clueId") String clueId, @Param("status") String status);

    /**
     * 根据线索编码查询线索
     *
     * @param code 线索编码
     * @return 线索
     */
    @Query("select c from ClueEntity c where c.code = :code")
    ClueEntity findByCode(@Param("code") String code);
}
