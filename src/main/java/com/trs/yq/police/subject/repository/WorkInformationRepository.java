package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WorkInformationEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

/**
 * 工作信息类持久层
 *
 * <AUTHOR>
 */
public interface WorkInformationRepository extends BaseRepository<WorkInformationEntity, String> {


    /**
     * 获取人员工作信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link WorkInformationEntity}
     * <AUTHOR>
     */
    @Query("select w from WorkInformationEntity w where w.personId = :personId order by w.workBeginTime")
    List<WorkInformationEntity> findAllByPersonId(@Param("personId") String personId);


    /**
     * 根据人员id查询所有的图片id和url
     *
     * @param personId 人员id
     * @return 图片信息
     */
    @Query(value = "select s.path,s.group_name,s.extension_name "
            + "from T_PS_FILE_STORAGE s inner join  T_PS_PERSON_FILE_RELATION f "
            + "on  s.id = f.file_storage_id "
            + " where f.person_id = :personId", nativeQuery = true)
    List<Map<String, String>> getPic(@Param("personId") String personId);
}
