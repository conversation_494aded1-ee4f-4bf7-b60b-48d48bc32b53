package com.trs.yq.police.subject.builder;

import static com.trs.yq.police.subject.utils.StringUtil.getPoliceStationPrefix;

import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.entity.WarningTraceRelationEntity;
import com.trs.yq.police.subject.domain.entity.WarningTrajectoryEntity;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.repository.WarningTraceRelationRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaBuilder.In;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/09/08
 */
public class WarningListPredicatesBuilder {


    /**
     * 创建模糊检索Predicates
     *
     * @param searchParams      模糊搜索参数
     * @param warningEntityRoot 群体记录
     * @param criteriaBuilder   criteriaBuilder
     * @return 模糊检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildSearchPredicates(SearchParams searchParams, Root<WarningEntity> warningEntityRoot, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(searchParams.getSearchField()) && StringUtils.isNotBlank(searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            Predicate warningContentPredicate = criteriaBuilder.like(warningEntityRoot.get("warningDetails").as(String.class), "%" + searchValue + "%");
            Predicate warningSourcePredicate = criteriaBuilder.like(warningEntityRoot.get("warningSource").as(String.class), "%" + searchValue + "%");
            Predicate idNumberPredicate = getIdNumberPredicate(warningEntityRoot, searchValue, criteriaBuilder);
            Predicate namePredicate = getNamePredicate(warningEntityRoot, searchValue, criteriaBuilder);
            switch (searchField) {
                case "warningContent":
                    predicates.add(warningContentPredicate);
                    break;
                case "warningSource":
                    predicates.add(warningSourcePredicate);
                    break;
                case "idNumber":
                    predicates.add(idNumberPredicate);
                    break;
                case "name":
                    predicates.add(namePredicate);
                    break;
                case "fullText":
                    predicates.add(criteriaBuilder.or(warningContentPredicate, warningSourcePredicate, idNumberPredicate, namePredicate));
                    break;
                default:
                    break;
            }
        }
        return predicates;
    }

    private static Predicate getIdNumberPredicate(Root<WarningEntity> warningEntityRoot, String searchValue, CriteriaBuilder criteriaBuilder) {
        Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
        Root<WarningTraceRelationEntity> relationRoot = subquery.from(WarningTraceRelationEntity.class);
        Root<WarningEntity> warningRoot = subquery.from(WarningEntity.class);
        Root<WarningTrajectoryEntity> traceRoot = subquery.from(WarningTrajectoryEntity.class);
        Predicate p1 = criteriaBuilder.equal(relationRoot.get("warningId"), warningRoot.get("id"));
        Predicate p2 = criteriaBuilder.equal(relationRoot.get("trajectoryId"), traceRoot.get("id"));
        Predicate p3 = criteriaBuilder.like(traceRoot.get("idNumber").as(String.class), "%" + searchValue + "%");
        subquery.select(warningRoot.get("id")).where(criteriaBuilder.and(p1, p2, p3));
        return criteriaBuilder.in(warningEntityRoot.get("id")).value(subquery);
    }

    private static Predicate getNamePredicate(Root<WarningEntity> warningEntityRoot, String searchValue,
                                              CriteriaBuilder criteriaBuilder) {
        Subquery<Long> subquery = criteriaBuilder.createQuery().subquery(Long.class);
        Root<WarningTraceRelationEntity> relationRoot = subquery.from(WarningTraceRelationEntity.class);
        Root<WarningEntity> warningRoot = subquery.from(WarningEntity.class);
        Root<WarningTrajectoryEntity> traceRoot = subquery.from(WarningTrajectoryEntity.class);
        Predicate p1 = criteriaBuilder.equal(relationRoot.get("warningId"), warningRoot.get("id"));
        Predicate p2 = criteriaBuilder.equal(relationRoot.get("trajectoryId"), traceRoot.get("id"));
        Subquery<String> subqueryPerson = criteriaBuilder.createQuery(PersonEntity.class).subquery(String.class);
        Root<PersonEntity> personRoot = subqueryPerson.from(PersonEntity.class);
        subqueryPerson.select(personRoot.get("idNumber")).where(criteriaBuilder.like(personRoot.get("name").as(String.class), "%" + searchValue + "%"));
        Predicate p3 = criteriaBuilder.in(traceRoot.get("idNumber")).value(subqueryPerson);
        subquery.select(warningRoot.get("id")).where(criteriaBuilder.and(p1, p2, p3));
        return criteriaBuilder.in(warningEntityRoot.get("id")).value(subquery);
    }

    /**
     * 创建人员列表动态检索Predicates
     *
     * @param areas           区域
     * @param subjectId       专题id
     * @param filterParams    检索参数
     * @param root            线索实体
     * @param criteriaBuilder criteriaBuilder
     * @return 检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildListFilterPredicates(List<String> areas, String subjectId, List<KeyValueVO> filterParams, Root<WarningEntity> root, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();

        // 联表查询专题和群体的关系
        predicates.add(criteriaBuilder.equal(root.get("subjectId").as(String.class), subjectId));
        // 筛选符合查看区域权限的
        if (!areas.contains("5105")) {
            predicates.add(getViewRangePredicates(root, areas, criteriaBuilder));

        }

        //动态查询参数
        filterParams.forEach(kv -> {
            switch (kv.getKey()) {
                case "warningStatus":
                    predicates.add(getWarningStatusPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "warningTime":
                    predicates.add(getWarningTimePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "warningLevel":
                    predicates.add(getWarningLevelPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "warningType":
                    predicates.add(getWarningTypePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "areaCode":
                    predicates.add(getWarningAreaPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "personType":
                    predicates.add(getPersonTypePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                default:
                    break;
            }
        });

        return predicates;
    }

    private static Predicate getPersonTypePredicates(Root<WarningEntity> root, String value, CriteriaBuilder criteriaBuilder) {
        WarningTraceRelationRepository warningTraceRelationRepository = BeanUtil.getBean(WarningTraceRelationRepository.class);
        List<String> idNumbers = warningTraceRelationRepository.findWarningIdByPersonLabel(value);
        In<String> id = criteriaBuilder.in(root.get("id").as(String.class));
        idNumbers.forEach(id::value);
        return id;
    }

    private static Predicate getWarningLevelPredicates(Root<WarningEntity> warningEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(warningEntityRoot.get("warningLevel").as(String.class), value);
    }

    private static Predicate getWarningTimePredicates(Root<WarningEntity> warningEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
        if (timeParams != null) {
            Predicate p1 = criteriaBuilder.greaterThanOrEqualTo(warningEntityRoot.get("warningTime").as(LocalDateTime.class), timeParams.getBeginTime());
            Predicate p2 = criteriaBuilder.lessThanOrEqualTo(warningEntityRoot.get("warningTime").as(LocalDateTime.class), timeParams.getEndTime());
            return criteriaBuilder.and(p1, p2);
        }
        return null;
    }

    private static Predicate getWarningStatusPredicates(Root<WarningEntity> warningEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(warningEntityRoot.get("warningStatus").as(String.class), value);
    }

    private static Predicate getWarningTypePredicates(Root<WarningEntity> warningEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(warningEntityRoot.get("warningType").as(String.class), value);
    }

    private static Predicate getWarningAreaPredicates(Root<WarningEntity> warningEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.like(warningEntityRoot.get("areaCode").as(String.class), getPoliceStationPrefix(value) + "%");
    }

    private static Predicate getViewRangePredicates(Root<WarningEntity> warningEntityRoot, List<String> values, CriteriaBuilder criteriaBuilder) {
        CriteriaBuilder.In<String> in = criteriaBuilder.in(warningEntityRoot.get("areaCode").as(String.class));
        values.forEach(in::value);
        return in;
    }
}
