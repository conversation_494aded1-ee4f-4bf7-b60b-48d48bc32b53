package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2022/12/28 10:24
 */
public enum ControlLevelEnum {

    RED("10","红"),
    YELLOW("20","黄"),
    BLUE("30","蓝");


    ControlLevelEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * 控制状态枚举
     *
     * @param code 编码
     * @return 控制状态枚举
     */
    public static ControlLevelEnum codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (ControlLevelEnum controlStatus : ControlLevelEnum.values()) {
                if (StringUtils.equals(code, controlStatus.code)) {
                    return controlStatus;
                }
            }
        }
        return null;
    }
}
