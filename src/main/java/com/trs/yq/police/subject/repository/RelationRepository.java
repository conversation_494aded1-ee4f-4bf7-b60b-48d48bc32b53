package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.RelationEntity;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/27 15:16
 */
@Repository
public interface RelationRepository extends BaseRepository<RelationEntity, String> {

    /**
     * 查询关联人员信息
     *
     * @param personId 关联人id
     * @param type     关系类型
     * @return 关联人员信息
     */
    List<RelationEntity> findByPersonIdAndType(@Param("personId") String personId, @Param("type") String type);

    /**
     * 根据人员id和类型和身份证号查询人员关联是否存在
     *
     * @param personId 人员id
     * @param type 类型
     * @param idNumber 身份证号
     * @return 结果
     */
    Boolean existsByPersonIdAndTypeAndIdNumber(String personId, String type, String idNumber);
}
