package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */

public enum CrjCzryFieldEnum {

    /**
     * 证件类型
     */
    certificateType("certificateType", "证件种类"),

    certificateNumber("certificateNumber", "证件号码"),

    en_name("enName", "英文姓名"),

    cn_name("cnName", "中文姓名"),

    gender("gender", "性别"),

    country("country", "国家地区"),

    birthday("birthday", "出生日期"),

    livingPoliceStation("livingPoliceStation", "居住地派出所"),

    livingCounty("livingCounty", "居住地所在区县"),

    workUnit("workUnit", "工作单位"),

    livingAddress("livingAddress", "居住地详细地址"),

    visaType("visaType", "签证种类"),

    visaNumber("visaNumber", "居留证许可(签证)号码"),

    entryTime("entryTime", "入境日期"),

    livingCheck("livingCheck", "居住地核实标志"),

    workCheck("workCheck", "工作地核实标志"),

    entryReason("entryReason", "入境事由"),

    stayReason("stayReason", "居留事由"),

    livingStatus("livingStatus", "居住状态"),

    createUser("createUser", "录入人"),

    createTime("createTime", "录入时间"),

    visaSignDate("visaSignDate", "居留许可(签)签发日期"),

    visaValidity("visaValidity", "居留许(签证)有效期至"),

    tel("tel", "联系电话"),

    entryExitTime("entryExitTime", "出入境时间"),
    remark("remark", "备注"),
    entryExitMark("entryExitMark", "出入境信息核查标志"),
    residenceMark("residenceMark", "常住标志"),
    residencePersonType("residencePersonType", "常住境外人员类别"),
    workPoliceStation("workPoliceStation", "工作地派出所"),
    checkStatus("checkStatus", "核查状态"),
    archiveDept("archiveDept", "建档单位"),
    archiveDeptType("archiveDeptType", "建档单位类别"),
    receiveMark("receiveMark", "接收标志"),
    processStatus("processStatus", "流程状态标志"),
    policeStation("policeStation", "派出所名称"),
    business("business", "派出所常住境外人员业务编号"),
    signDept("signDept", "签发机关"),
    signDate("signDate", "签发日期"),
    personId("personId", "人员id"),
    personNumber("personNumber", "人员编号"),
    areaType("areaType", "人员地域类别"),
    personType("personType", "人员种类"),
    personStatus("personStatus", "人员状态"),
    deleted("deleted", "删除标志"),
    identity("identity", "身份"),
    valid("valid", "当前有效状态"),
    idNumber("idNumber", "身份证号"),
    reportMark("reportMark", "上送标志"),
    inputDept("inputDept", "填表单位编号"),
    readMark("readMark", "阅读标志"),
    processId("processId", "外国人办证id"),
    licenseId("licenseId", "常住境外人员业务id"),
    lastName("lastName", "英文姓"),
    firstName("firstName", "英文名"),
    importantPersonMark("importantPersonMark", "重点注意人员标识"),
    lastModified("lastModified", "最后修改人"),
    certificatePersonCount("certificatePersonCount", "证件内人数"),
    certificateValidity("certificateValidity", "证件有效期至"),
    ;

    /**
     * 属性名
     */
    @Getter
    private final String field;

    /**
     * 中文名
     */
    @Getter
    private final String cnName;


    /**
     * 私有构造方法
     *
     * @param field  属性名
     * @param cnName 中文名
     */
    CrjCzryFieldEnum(String field, String cnName) {
        this.field = field;
        this.cnName = cnName;
    }

    /**
     * 通过中文转换
     *
     * @param cnName 中文名
     * @return 人员信息表结构枚举
     */
    public static CrjCzryFieldEnum cnNameOf(String cnName) {
        if (StringUtils.isNotBlank(cnName)) {
            cnName = cnName.replace("（必填）", "");
            for (CrjCzryFieldEnum person : CrjCzryFieldEnum.values()) {
                if (StringUtils.equals(person.cnName, cnName)) {
                    return person;
                }
            }
        }
        return null;
    }
}
