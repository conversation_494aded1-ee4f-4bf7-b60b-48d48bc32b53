package com.trs.yq.police.subject.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.entity.CrjCzryEntity;
import com.trs.yq.police.subject.domain.entity.SubjectEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.request.ListParamsRequest;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.excel.ReadCrjCzryExcelListener;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.exception.SystemException;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.repository.CrjCzryRepository;
import com.trs.yq.police.subject.repository.JwzhDictRepository;
import com.trs.yq.police.subject.repository.SubjectRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.service.CrjCzryService;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.servlet.http.HttpServletResponse;
import java.time.format.DateTimeFormatter;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;

/**
 * 出入境常住人员
 *
 * <AUTHOR>
 */
@Service
public class CrjCzryServiceImpl implements CrjCzryService {

    @Resource
    private CrjCzryRepository crjCzryRepository;
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private PersonServiceImpl personService;
    @Resource
    private SubjectRepository subjectRepository;
    @Resource
    private JwzhDictRepository jwzhDictRepository;

    final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    @Override
    public PageResult<CrjResidenceListVO> getResidenceList(ListParamsRequest request) {
        final PageParams pageParams = request.getPageParams();
        Page<CrjCzryEntity> page = getPage(request);
        List<CrjResidenceListVO> list = page.getContent().stream().map(this::toVO).collect(Collectors.toList());
        return PageResult.of(list, pageParams.getPageNumber(), page.getTotalElements(), pageParams.getPageSize());
    }

    private CrjResidenceListVO toVO(CrjCzryEntity entity) {
        CrjResidenceListVO vo = new CrjResidenceListVO();
        vo.setId(entity.getId());
        vo.setCertificateNumber(entity.getCertificateNumber());
        vo.setName(entity.getEnName());
        vo.setGender(entity.getGender());
        vo.setCountry(entity.getCountry());
        vo.setLivingStatus(processVisaValid(entity.getVisaValidity()));
        vo.setVisaSignDate(processDate(entity.getVisaSignDate()));
        vo.setVisaValidity(processDate(entity.getVisaValidity()));
        vo.setAddress(entity.getLivingAddress());
        vo.setWorkAddress(entity.getWorkUnit());
        vo.setEntryTime(processDate(entity.getEntryTime()));
        UnitEntity livingPolice = unitRepository.findByUnitCode(entity.getLivingPoliceStation());
        vo.setDept(livingPolice == null ? null : livingPolice.getShortname());
        return vo;
    }

    private Page<CrjCzryEntity> getPage(ListParamsRequest request) {
        TimeParams timeParams = KeyValueTypeVO.getSingleFilterParam(request.getFilterParams(), "createTime", TimeParams.class);
        if (timeParams == null) {
            timeParams = new TimeParams();
            timeParams.setRange("0");
        }
        final PageParams pageParams = request.getPageParams();
        Specification<CrjCzryEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = buildListFilterPredicates(request.getFilterParams(), root, criteriaBuilder);
            if (Objects.nonNull(request.getSearchParams())) {
                predicates.addAll(buildSearchPredicates(request.getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        //排序
        List<Sort.Order> orders = new ArrayList<>();
        orders.add(new Sort.Order(Sort.Direction.DESC, "crTime"));
        SortParams sortParams = request.getSortParams();
        if (StringUtils.isNotBlank(sortParams.getSortField())) {
            Sort.Direction direction = Sort.Direction.valueOf(sortParams.getSortDirection().toUpperCase(Locale.ROOT));
            orders.add(new Sort.Order(direction, sortParams.getSortField()));
        }
        Sort sort = Sort.by(orders);
        Pageable pageable = pageParams.toPageable(sort);

        return crjCzryRepository.findAll(specification, pageable);
    }

    private List<Predicate> buildListFilterPredicates(List<KeyValueTypeVO> filterParams,
                                                      Root<CrjCzryEntity> root, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        filterParams.forEach(kv -> {
            switch (kv.getKey()) {
                case "district":
                    predicates.add(criteriaBuilder.like(root.get("livingPoliceStation").as(String.class), kv.getValue().toString() + "%"));
                    break;
                // 居留许可状态
                case "livingStatus":
                    //有效
                    LocalDateTime now = LocalDateTime.now();
                    if (Objects.equals(kv.getValue().toString(), "1")) {
                        Predicate greater = criteriaBuilder.greaterThanOrEqualTo(root.get("visaValidity").as(LocalDateTime.class), now);
                        Predicate isNull = criteriaBuilder.isNull(root.get("visaValidity"));
                        predicates.add(criteriaBuilder.or(greater, isNull));
                        //到期
                    } else if (kv.getValue().toString().equals("0")) {
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("visaValidity").as(LocalDateTime.class), now));
                    }
                    break;
                case "createTime":
                    TimeParams timeParams = JsonUtil.parseObject(kv.getValue().toString(), TimeParams.class);
                    if (timeParams == null) {
                        timeParams = new TimeParams();
                        timeParams.setRange("0");
                    }
                    LocalDateTime beginTime = timeParams.getBeginTime().toLocalDate().atStartOfDay();
                    predicates.add(criteriaBuilder.between(root.get("createTime").as(LocalDateTime.class), beginTime, timeParams.getEndTime()));
                default:
                    break;
            }
        });
        return predicates;
    }

    private List<Predicate> buildSearchPredicates(SearchParams searchParams, Root<CrjCzryEntity> root,
                                                        CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(searchParams.getSearchField())
                && StringUtils.isNotBlank(searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = StringUtil.removeSpecialCharacters(searchParams.getSearchValue().trim());
            Predicate namePredicate = criteriaBuilder.like(root.get("enName").as(String.class),
                    "%" + searchValue + "%");
            Predicate idNumberPredicate = criteriaBuilder.like(root.get("certificateNumber").as(String.class),
                    "%" + searchValue + "%");
            switch (searchField) {
                case "name":
                    predicates.add(namePredicate);
                    break;
                case "certificateNumber":
                    predicates.add(idNumberPredicate);
                    break;
                case "fullText":
                    predicates.add(criteriaBuilder.or(namePredicate, idNumberPredicate));
                    break;
                default:
                    break;
            }
        }
        return predicates;
    }

    private String processVisaValid(LocalDateTime visaValidity) {
        LocalDateTime now = LocalDateTime.now();
        if (visaValidity == null || visaValidity.isAfter(now)) {
            return "有效";
        } else {
            return "到期";
        }
    }

    private String processDate(LocalDateTime time) {
        if (time == null) {
            return "--";
        } else {
            return time.format(formatter);
        }
    }

    @Override
    public CrjResidenceDetailVO getResidenceDetail(String id) {
        CrjCzryEntity czryEntity = crjCzryRepository.findById(id).orElse(null);
        if (czryEntity == null) {
            throw new SystemException("常住人员不存在，请核实！");
        }

        CrjResidenceDetailVO.BasicInfo basicInfo = new CrjResidenceDetailVO.BasicInfo();
        basicInfo.setCertificateType(czryEntity.getCertificateType());
        basicInfo.setCertificateNumber(czryEntity.getCertificateNumber());
        basicInfo.setCnName(czryEntity.getCnName());
        basicInfo.setEnName(czryEntity.getEnName());
        basicInfo.setGender(czryEntity.getGender());
        basicInfo.setCountry(czryEntity.getCountry());

        basicInfo.setBirthday(processDate(czryEntity.getBirthday()));
        //居住状态 有效/到期
        basicInfo.setLivingStatus(processVisaValid(czryEntity.getVisaValidity()));
        //code
        UnitEntity livingPolice = unitRepository.findByUnitCode(czryEntity.getLivingPoliceStation());
        basicInfo.setPoliceStation(livingPolice == null ? null : livingPolice.getUnitName());
        basicInfo.setWorkPlace(czryEntity.getWorkUnit());
        basicInfo.setLivingAddress(czryEntity.getLivingAddress());
        basicInfo.setVisaSignDate(processDate(czryEntity.getVisaSignDate()));
        basicInfo.setVisaValidity(processDate(czryEntity.getVisaValidity()));
        CrjResidenceDetailVO detailVO = new CrjResidenceDetailVO();
        detailVO.setBasicInfo(basicInfo);

        CrjResidenceDetailVO.VisaInfo visaInfo = new CrjResidenceDetailVO.VisaInfo();
        visaInfo.setVisaType(czryEntity.getVisaType());
        visaInfo.setVisaNumber(czryEntity.getVisaNumber());
        visaInfo.setEntryTime(processDate(czryEntity.getEntryTime()));
        //r签证
        visaInfo.setVisaLabel(processVisaType(czryEntity.getVisaType()));
        detailVO.setVisaInfo(visaInfo);

        CrjResidenceDetailVO.ResidenceInfo residenceInfo = new CrjResidenceDetailVO.ResidenceInfo();
        residenceInfo.setLivePlaceCheck(czryEntity.getLivingCheck());
        residenceInfo.setWorkPlaceCheck(czryEntity.getWorkCheck());
        residenceInfo.setStayReason(czryEntity.getStayReason());
        residenceInfo.setEnterReason(czryEntity.getEntryReason());
        residenceInfo.setCreateUser(czryEntity.getCreateUser());
        residenceInfo.setCreateTime(processDate(czryEntity.getCreateTime()));
        detailVO.setResidenceInfo(residenceInfo);

        return detailVO;
    }

    private String processVisaType(String visaType) {
        if (visaType.toLowerCase().contains("r")) {
            return "R签证";
        }
        return null;
    }

    @Override
    @Transactional
    public ImportResultVO importCzry(ImportVO importVO) {
        final MultipartFile excel = importVO.getExcel();
        final String repeatStrategy = importVO.getRepeatStrategy();
        final String subjectId = importVO.getSubjectId();

        // 批量导入人员基本信息
        final String extension = FilenameUtils.getExtension(excel.getOriginalFilename());
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        final String filename = "批量导入常住人员-" + date + "." + extension;
        final String xlsx = "xlsx";
        if (StringUtils.isBlank(filename) || StringUtils.isBlank(extension) || !(xlsx.equals(extension) || "xls".equals(extension))) {
            throw new ParamValidationException("暂只支持xlsx文件，请重新上传，谢谢！");
        }
        ReadCrjCzryExcelListener listener = ReadCrjCzryExcelListener
                .builder()
                .crjCzryRepository(crjCzryRepository)
                .jwzhDictRepository(jwzhDictRepository)
                .unitRepository(unitRepository)
                .currentUser(AuthHelper.getCurrentUser())
                .repeatStrategy(repeatStrategy)
                .build();
        try (InputStream inputStream = excel.getInputStream()) {
            EasyExcelFactory.read(inputStream, listener).sheet().headRowNumber(1).doRead();
            // 记录历史信息
            String initialId = personService.recordInitial(subjectId, filename, excel);
            // 记录失败
            personService.recordCrjCzryResult(initialId, listener.getFailRows(), listener.getColumnMap());
            return new ImportResultVO(initialId, listener.getFailResult());
        } catch (IOException e) {
            throw new SystemException(
                String.format("import czry fail! subjectId = %s, fileName = %s", subjectId, filename), e);
        }
    }

    @Override
    public void deleteCzry(List<String> ids) {
        crjCzryRepository.deleteAllById(ids);
    }

    @Override
    public void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request, String subjectId) throws IOException {
        String fileName = String.format("常住人员-%s.xlsx",
                LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<CzryExportVO> vos = crjCzryRepository.findByIds(request.getIds()).stream()
                .map(this::toExportVO).collect(Collectors.toList());
        EasyExcelFactory.write(response.getOutputStream(), CzryExportVO.class)
                .registerWriteHandler(new CustomCellWriteHandler())
                .includeColumnFiledNames(fieldNames)
                .sheet()
                .doWrite(vos);
    }

    /**
     * 生成vo
     *
     * @param entity CrjResidenceListVO
     * @return 结果
     */
    public CzryExportVO toExportVO(CrjCzryEntity entity) {
        CzryExportVO vo = new CzryExportVO();
        BeanUtil.copyPropertiesIgnoreNull(entity, vo);
        vo.setLivingStatus(processVisaValid(entity.getVisaValidity()));
        vo.setVisaSignDate(processDate(entity.getVisaSignDate()));
        vo.setVisaValidity(processDate(entity.getVisaValidity()));
        vo.setAddress(entity.getLivingAddress());
        vo.setEntryTime(processDate(entity.getEntryTime()));
        vo.setBirthday(processDate(entity.getBirthday()));
        UnitEntity livingPolice = unitRepository.findByUnitCode(entity.getLivingPoliceStation());
        vo.setDept(livingPolice == null ? null : livingPolice.getUnitName());
        return vo;
    }

    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getCrjPermanentListProperty());
    }
}
