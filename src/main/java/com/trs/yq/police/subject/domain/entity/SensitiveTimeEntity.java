package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

/**
 * 敏感时间节点
 *
 * <AUTHOR>
 * @date 2021/12/28 14:02
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "t_sensitive_time")
public class SensitiveTimeEntity extends BaseEntity {
    private static final long serialVersionUID = -2662746690937482826L;


    public static final int SCOPE_ALL = 0;
    public static final int SCOPE_CHOOSE = 1;

    public static final int STATUS_DELETE = -1;
    public static final int STATUS_DISABLED = 0;
    public static final int STATUS_ENABLED = 1;

    public static final int TIME_TYPE_YEAR = 0;
    public static final int TIME_TYPE_FIXED = 1;

    /**
     * 敏感节点名称
     */
    private String name;
    /**
     * 敏感节点备注
     */
    private String remark;
    /**
     * 开始时间
     */
    private LocalDate startTime;
    /**
     * 结束时间
     */
    private LocalDate endTime;
    /**
     * 有效范围 0 默认有效 1 选择有效
     */
    @Column(name = "SCOPE")
    private Integer nodeType;
    /**
     * 0 停用 1 启用 -1 删除
     */
    private Integer status;
    /**
     * 0 每年 1 固定时间
     */
    @Column(name = "TIME_TYPE")
    private Integer prescription;

}
