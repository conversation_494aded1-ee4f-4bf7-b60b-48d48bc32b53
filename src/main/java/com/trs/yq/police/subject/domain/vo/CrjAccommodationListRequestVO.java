package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Data;

import java.io.Serializable;

/**
 * [专题首页-出入境] 住宿登记记录请求参数VO
 *
 * <AUTHOR>
 * @date 2021/9/22 17:46
 */
@Data
public class CrjAccommodationListRequestVO implements Serializable {
    private static final long serialVersionUID = 6482523268974145071L;
    /**
     * 分页参数
     */
    private PageParams pageParams;
    /**
     * 时间参数
     */
    private TimeParams timeParams;
    /**
     * 其他参数
     */
    private CrjAccommodationListRequestVO.OtherParams otherParams;

    /**
     * 其他筛选条件
     */
    @Data
    public static class OtherParams implements Serializable {
        private static final long serialVersionUID = -2163749026871774347L;
        private String searchValue;
    }
}
