package com.trs.yq.police.subject.message;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

/**
 * kafka配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/22 16:53
 */
@Configuration
@EnableKafka
@ConditionalOnProperty(value = "com.trs.bk.kafka.enable",havingValue = "true")
public class KafKaConsumerConfig {

    @Resource
    private Environment environment;

    /**
     * 创建 {@link org.springframework.kafka.config.KafkaListenerContainerFactory}
     *
     * @return org.springframework.kafka.config.KafkaListenerContainerFactory 实例
     */
    @Bean
    public KafkaListenerContainerFactory<?> kafkaListenerContainerFactory() {
        ConcurrentKafkaListenerContainerFactory<String, String> listenerContainerFactory = new ConcurrentKafkaListenerContainerFactory<>();
        listenerContainerFactory.setConsumerFactory(new DefaultKafkaConsumerFactory<>(consumerConfigs()));
        listenerContainerFactory.setBatchListener(false);
        return listenerContainerFactory;
    }

    /**
     * 创建kafka consumer的属性类
     *
     * @return kafka consumer property
     */
    public Map<String, Object> consumerConfigs() {
        Map<String, Object> props = new HashMap<>(16);
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, environment.getProperty("spring.kafka.consumer.bootstrap-servers"));
        props.put(ConsumerConfig.GROUP_ID_CONFIG, environment.getProperty("spring.kafka.consumer.group-id"));
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, environment.getProperty("spring.kafka.consumer.auto-offset-reset"));
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, environment.getProperty("spring.kafka.consumer.max-poll-records"));
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
        return props;
    }


}
