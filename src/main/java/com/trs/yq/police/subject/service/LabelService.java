package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.IdNameVO;
import com.trs.yq.police.subject.domain.vo.LabelQueryVO;
import com.trs.yq.police.subject.domain.vo.LabelVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签管理业务接口
 *
 * <AUTHOR>
 * @date 2021/7/31 19:04
 */
@Validated
public interface LabelService {

    /**
     * 创建标签
     *
     * @param vo 创建标签
     * @return 标签id
     */
    String createLabel(@NotNull(message = "创建标签所需数据不可缺失") LabelVO vo);

    /**
     * 查询标签
     *
     * @param labelId 标签id
     * @return 标签信息
     */
    LabelVO getLabel(@NotBlank(message = "标签主键不可为空") String labelId);

    /**
     * 删除标签
     *
     * @param labelId 标签id
     */
    void deleteLabel(@NotBlank(message = "标签主键不可为空") String labelId);

    /**
     * 更新标签
     *
     * @param label 更新标签所需数据
     */
    void updateLabel(@NotNull(message = "更新标签所需数据不可缺失") LabelVO label);

    /**
     * 标签列表查询
     *
     * @param query    列表查询参数
     * @param pageable 分页参数
     * @return 分页查询结果
     */
    PageResult<LabelVO> listLabels(LabelQueryVO query, Pageable pageable);

    /**
     * 查询专题标签
     *
     * @param subjectId 专题主键
     * @param module 模块
     * @return 标签列表
     */
    List<IdNameVO> getLabels(String subjectId, String module);
}
