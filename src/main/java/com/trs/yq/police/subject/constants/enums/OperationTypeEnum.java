package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 *
 * <AUTHOR>
 * @since 2021/10/27
 */
public enum OperationTypeEnum {
    /**
     * enums
     */
    RECORD("1", "合成"),
    COMMAND("2", "指令"),
    DEMAND("3", "协作");

    @Getter
    private final String code;

    @Getter
    private final String name;

    OperationTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code获取枚举
     *
     * @param code code
     * @return enum
     */
    public static OperationTypeEnum codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            return Arrays.stream(OperationTypeEnum.values())
                    .filter(module -> module.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
}
