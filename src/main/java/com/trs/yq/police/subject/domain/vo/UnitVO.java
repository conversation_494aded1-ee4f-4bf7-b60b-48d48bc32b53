package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 单位信息 分局/派出所
 *
 * <AUTHOR>
 * @since 2021/8/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnitVO implements Serializable {

    private static final long serialVersionUID = -5619424144163763638L;

    /**
     * 名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;
    /**
     * 子单位
     */
    private List<UnitVO> children;

    /**
     * 构造器
     *
     * @param code code
     * @param name name
     */
    public UnitVO(String code, String name) {
        this.name = name;
        this.code = code;
    }
}
