package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.VirtualIdentityEntity;
import com.trs.yq.police.subject.domain.vo.VirtualIdentityVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.VirtualIdentityRepository;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.VirtualIdentityService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.AUTOMATED;

/**
 * 虚拟身份类业务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class VirtualIdentityServiceImpl implements VirtualIdentityService {

    @Resource
    private VirtualIdentityRepository virtualIdentityRepository;
    @Resource
    private PersonService personService;
    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public List<VirtualIdentityVO> getAll(String personId) {
        List<VirtualIdentityEntity> list = virtualIdentityRepository.findAllByPersonId(personId);
        List<VirtualIdentityVO> virtualIdentityVOList = new ArrayList<>();
        for (VirtualIdentityEntity virtualIdentityEntity : list) {
            VirtualIdentityVO virtualIdentityVo = new VirtualIdentityVO();
            virtualIdentityVo.setVirtualId(virtualIdentityEntity.getId());
            virtualIdentityVo.setVirtualNumber(virtualIdentityEntity.getVirtualNumber());
            virtualIdentityVo.setVirtualType(virtualIdentityEntity.getVirtualType());
            virtualIdentityVo.setVirtualTypeName(virtualIdentityEntity.getVirtualTypeName());
            virtualIdentityVo.setIsAutomated(virtualIdentityEntity.getIsAutomated());
            virtualIdentityVOList.add(virtualIdentityVo);
        }
        return virtualIdentityVOList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteOneById(String personId, String id) {
        // 校验人员是否存在
        personService.checkPersonExist(personId);

        final VirtualIdentityEntity identity = virtualIdentityRepository.findById(id).orElse(null);
        if (Objects.isNull(identity)) {
            throw new ParamValidationException("虚拟身份不存在，请刷新核实");
        }

        if(StringUtils.equals(identity.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可删除");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除虚拟身份")
                .oldObj(JsonUtil.toJsonString(identity))
                .module(OperateModule.VIRTUAL_IDENTITY)
                .operator(Operator.DELETE)
                .build();
        virtualIdentityRepository.deleteById(id);

        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addOne(String personId, VirtualIdentityVO virtualIdentityVo) {
        // 校验人员
        personService.checkPersonExist(personId);

        VirtualIdentityEntity virtualIdentityEntity = voTransFormEntity(new VirtualIdentityEntity(), virtualIdentityVo);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增虚拟身份")
                .newObj(JsonUtil.toJsonString(virtualIdentityEntity))
                .module(OperateModule.VIRTUAL_IDENTITY)
                .operator(Operator.ADD)
                .build();

        virtualIdentityRepository.save(virtualIdentityEntity);

        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    /**
     * vo数据转entity
     *
     * @param virtualIdentityVo     vo虚拟信息
     * @param virtualIdentityEntity entity虚拟信息
     * <AUTHOR>
     */
    private VirtualIdentityEntity voTransFormEntity(VirtualIdentityEntity virtualIdentityEntity, VirtualIdentityVO virtualIdentityVo) {
        virtualIdentityEntity.setVirtualNumber(virtualIdentityVo.getVirtualNumber());
        virtualIdentityEntity.setVirtualType(virtualIdentityVo.getVirtualType());
        virtualIdentityEntity.setPersonId(virtualIdentityVo.getPersonId());
        virtualIdentityEntity.setVirtualTypeName(virtualIdentityVo.getVirtualTypeName());
        return virtualIdentityEntity;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateOne(String personId, VirtualIdentityVO virtualIdentityVo) {

        // 校验人员
        personService.checkPersonExist(personId);

        VirtualIdentityEntity virtualIdentityEntity = voTransFormEntity(new VirtualIdentityEntity(), virtualIdentityVo);
        virtualIdentityEntity.setId(virtualIdentityVo.getVirtualId());
        VirtualIdentityEntity virtualIdentity = virtualIdentityRepository.findById(virtualIdentityEntity.getId()).orElse(null);

        if (Objects.isNull(virtualIdentity)) {
            throw new ParamValidationException("虚拟身份信息不存在，请刷新核实!");
        }

        if(StringUtils.equals(virtualIdentity.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可修改");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.VIRTUAL_IDENTITY)
                .desc("编辑虚拟身份")
                .primaryKey(personId)
                .currentUser(AuthHelper.getCurrentUser())
                .oldObj(JsonUtil.toJsonString(virtualIdentity))
                .build();


        virtualIdentity.setVirtualType(virtualIdentityEntity.getVirtualType());
        virtualIdentity.setVirtualNumber(virtualIdentityEntity.getVirtualNumber());
        virtualIdentity.setVirtualTypeName(virtualIdentityEntity.getVirtualTypeName());
        virtualIdentityRepository.save(virtualIdentity);

        logRecord.setNewObj(JsonUtil.toJsonString(virtualIdentity));
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }
}
