package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.domain.entity.SchedulingLogEntity;
import com.trs.yq.police.subject.repository.SchedulingLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/14 20:43
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.program.Scheduling.task.enable", havingValue = "true")
public class ProgramSchedulingTask {

    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${com.trs.program.Scheduling.basic_url}")
    private String basicUrl;

    @Value("${com.trs.program.Scheduling.drug_warn_url}")
    private String drugWarnUrl;

    @Value("${com.trs.program.Scheduling.drug_area_warn_url}")
    private String drugAreaWarnUrl;

    @Value("${com.trs.program.Scheduling.zb_cluster_warn_url}")
    private String zbClusterWarnUrl;

    @Value("${com.trs.program.Scheduling.drug_warn_param}")
    private String drugWarnParam;
    @Value("${com.trs.program.Scheduling.drug_area_warn_param}")
    private String drugAreaWarnParam;
    @Value("${com.trs.program.Scheduling.zb_cluster_warn_param}")
    private String zbClusterWarnParam;
    @Resource
    private SchedulingLogRepository schedulingLogRepository;

    /**
     * 定时调度隐形涉毒人员接口
     */
    @Scheduled(cron = "${com.trs.program.Scheduling.task}")
    @Transactional(rollbackFor = RuntimeException.class)
    public void drugWarnSchedulingTask() {
        String url = basicUrl + drugWarnUrl;
        savaSchedulingLog(url, drugWarnParam);

    }

    /**
     * 定时调度隐形涉毒窝点接口
     */
    @Scheduled(cron = "${com.trs.program.Scheduling.task}")
    @Transactional(rollbackFor = RuntimeException.class)
    public void drugAreaWarnSchedulingTask() {
        String url = basicUrl + drugAreaWarnUrl;
        savaSchedulingLog(url, drugAreaWarnParam);
    }

    /**
     * 定时调度ZB聚集接口
     */
    @Scheduled(cron = "${com.trs.program.Scheduling.task}")
    @Transactional(rollbackFor = RuntimeException.class)
    public void zbClusterWarnSchedulingTask() {
        String url = basicUrl + zbClusterWarnUrl;
        savaSchedulingLog(url, zbClusterWarnParam);
    }

    /**
     * @param url  路径
     * @param body 请求参数
     */
    public void savaSchedulingLog(String url, String body) {
        //设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        //请求参数
        Map<String, List<String>> params = new LinkedMultiValueMap<>();
        params.put("hd_conf", Collections.singletonList(body));

        //远程调用
        HttpEntity<Map<String, List<String>>> httpEntity = new HttpEntity<>(params, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
        //存储远程调用参数
        SchedulingLogEntity schedulingLogEntity = new SchedulingLogEntity(LocalDateTime.now(), url, body, String.valueOf(responseEntity));
        schedulingLogRepository.save(schedulingLogEntity);
    }

}
