package com.trs.yq.police.subject.operation.handler;

import com.lmax.disruptor.WorkHandler;
import com.trs.yq.police.subject.operation.OperationLogService;
import com.trs.yq.police.subject.operation.event.OperationLogEvent;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 操作日志消费者
 * <p>{@code EventHandler} & {@code WorkHandler} 均为消费者
 * <p>{@code EventHandler} 类似于 EventBus
 * <p>{@code WorkHandler} 需要池化 & 类似于Kafka中的ConsumerGroup
 *
 * <AUTHOR>
 * @date 2021/8/18 11:06
 */
@Slf4j
@Component
@Scope("prototype")
public class OperationLogWorkHandler implements WorkHandler<OperationLogEvent> {

    @Resource
    private OperationLogService operationLogService;

    @Override
    public void onEvent(OperationLogEvent event) throws Exception {
        final OperationLogRecord operationLogRecord = event.getValue();
        operationLogService.recordOperationLog(operationLogRecord);
    }
}
