package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 关注人员风险警情列表vo
 *
 * <AUTHOR>
 * @since 2021/9/11
 */
@Data
public class FocusPersonWarningVO implements Serializable {
    private static final long serialVersionUID = -4661330067570520493L;
    /**
     * id
     */
    private String warningId;
    /**
     * 预警级别
     */
    private String warningLevel;
    /**
     * 预警标签
     */
    private String warningTag;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 预警地点
     */
    private String warningPlace;
    /**
     * 预警时间
     */
    private LocalDateTime warningTime;
    /**
     * 照片列表
     */
    private List<ImageVO> images;
}
