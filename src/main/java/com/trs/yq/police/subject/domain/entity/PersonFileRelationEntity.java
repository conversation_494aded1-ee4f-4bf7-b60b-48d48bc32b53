package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 人员与文件存储关系表
 *
 * <AUTHOR>
 * @date 2021/7/30 13:17
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_FILE_RELATION")
public class PersonFileRelationEntity extends BaseEntity {

    private static final long serialVersionUID = 7492398818869670259L;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 文件存储id
     */
    private String fileStorageId;

    /**
     * 文件类型 0 - 照片 ， 1 - 其他现场照片
     */
    private String type;

    /**
     * 模块
     */
    private String module;

    /**
     * 关联的记录主键
     */
    private String recordId;
}
