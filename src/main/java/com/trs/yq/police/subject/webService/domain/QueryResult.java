package com.trs.yq.police.subject.webService.domain;

import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 查询结果
 */
@Data
public class QueryResult {


    private StayInfo[] stayinfo;


    /**
     * 居住信息
     */
    @Slf4j
    @Data
    public static class StayInfo {

        private String gjdq;

        private String zjzl;

        private String zjhm;

        private String ywxm;

        private String xb;

        private String csrq;

        private String xp;

        private String bz;

        /**
         * 转走访vo
         *
         * @return 走访vo
         */
        public CrjJwryVisitVO toVisitVO() {
            if (StringUtils.isBlank(bz)) {
                log.info("查询结果，住宿信息备注为空，无法提取相关信息！");
            }
            String[] split = bz.split(",",-1);
            CrjJwryVisitVO visitVO = new CrjJwryVisitVO();
            visitVO.setLiveAddress(split[7]);
            visitVO.setVisaType(split[2]);
            visitVO.setVisaNumber(split[3]);
            try {

                if (StringUtils.isNotBlank(split[4])) {
                    Date date = new SimpleDateFormat("yyyyMMdd").parse(split[4]);
                    visitVO.setInChinaTime(date);
                }
            } catch (Exception e) {
                log.error("入境时间格式化失败!", e);
            }
            try {

                if (StringUtils.isNotBlank(split[9])) {
                    Date date = new SimpleDateFormat("yyyyMMdd").parse(split[9]);
                    visitVO.setVisitTime(date);
                }
            } catch (Exception e) {
                log.error("走访时间格式化失败!", e);
            }
            try {
                if (StringUtils.isNotBlank(split[10])) {
                    Date date = new SimpleDateFormat("yyyyMMdd").parse(split[10]);
                    visitVO.setPlanLeaveTime(date);
                }
            } catch (Exception e) {
                log.error("离开时间格式化失败!", e);
            }
            return visitVO;
        }
    }
}

