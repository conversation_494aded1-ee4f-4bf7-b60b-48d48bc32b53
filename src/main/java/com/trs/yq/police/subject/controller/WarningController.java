package com.trs.yq.police.subject.controller;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.request.CarTrajectoryRequest;
import com.trs.yq.police.subject.domain.vo.CarTrajectoryVO;
import com.trs.yq.police.subject.domain.vo.CarWarningVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.HomePageWarningVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.StabilityWarningCountVO;
import com.trs.yq.police.subject.domain.vo.StabilityWarningListVO;
import com.trs.yq.police.subject.domain.vo.WarningCarJudgeVO;
import com.trs.yq.police.subject.domain.vo.WarningCarTrajectoryVO;
import com.trs.yq.police.subject.domain.vo.WarningDetailVO;
import com.trs.yq.police.subject.domain.vo.WarningListVO;
import com.trs.yq.police.subject.domain.vo.WarningPersonVO;
import com.trs.yq.police.subject.service.SubjectService;
import com.trs.yq.police.subject.service.WarningExcelService;
import com.trs.yq.police.subject.service.WarningService;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模型预警接口
 *
 * <AUTHOR>
 * @since 2021/9/1
 */
@RestController
@RequestMapping("/warning")
@Validated
public class WarningController {

    @Resource
    private WarningService warningService;

    @Resource
    private SubjectService subjectService;

    @Resource
    private WarningExcelService warningExcelService;

    /**
     * [专题首页/预警详情]获取预警详情基础信息 http://192.168.200.192:3001/project/4897/interface/api/130957
     *
     * @param warningId 预警id
     * @return 详情 {@link WarningDetailVO}
     */
    @GetMapping("/{warningId}")
    public WarningDetailVO getWarningDetail(@PathVariable String warningId) {
        return warningService.getWarningDetail(warningId);
    }

    /**
     * [专题首页/预警详情]获取预警详情人员信息 http://192.168.200.192:3001/project/4897/interface/api/130963
     *
     * @param warningId 预警id
     * @return 详情 {@link WarningDetailVO}
     */
    @GetMapping("/{warningId}/person")
    public List<WarningPersonVO> getWarningPersonDetail(@PathVariable String warningId) {
        return warningService.getWarningPersonDetail(warningId);
    }

    /**
     * [预警档案-预警列表] 查询列表筛选条件 http://192.168.200.192:3001/project/4897/interface/api/130771
     *
     * @param subjectId 专题id
     * @return {@link ListFilter}
     */
    @GetMapping("/list/filters")
    public List<ListFilter> getListFilters(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return subjectService.getWarningListQueryFilters(subjectId);
    }

    /**
     * [预警档案-预警列表] 列表查询 http://192.168.200.192:3001/project/4897/interface/api/130777
     *
     * @param subjectId 专题id
     * @param request   {@link ListRequestVO}
     * @return {@link WarningListVO}
     */
    @PostMapping("/list")
    public PageResult<WarningListVO> getList(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId,
        @RequestBody @Valid ListRequestVO request) {
        return warningService.getWarningList(subjectId, request);
    }

    /**
     * [预警档案-预警列表] 批量导出列表 http://192.168.200.192:3001/project/4897/interface/api/130789
     *
     * @param response  {@link HttpServletResponse}
     * @param request   {@link ExportParams}
     * @throws IOException io异常
     */
    @PostMapping("/list/export")
    public void listExport(HttpServletResponse response,
        @Validated @RequestBody ExportParams request) throws IOException {
        warningExcelService.downLoadExcel(response, request, request.getSubjectId());
    }

    /**
     * 根据专题id出查询可导出的字段信息 http://192.168.200.192:3001/project/4897/interface/api/130783
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getExportPropertyList(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return warningExcelService.getExportPropertyList(subjectId);
    }


    /**
     * 维稳专题进京赴省预警列表查询接口
     *
     * @param status 进京/赴省
     * @return {@link StabilityWarningListVO}
     */
    @GetMapping("/list/stability")
    public List<StabilityWarningListVO> getStabilityWarningList(@NotBlank(message = "status 不能为空！") String status) {
        return warningService.getStabilityWarningList(status);
    }

    /**
     * 查询维稳专题进京赴省预警签收/未签收统计
     *
     * @return {@link StabilityWarningCountVO}
     */
    @GetMapping("/count/stability")
    public StabilityWarningCountVO getStabilityWarningCount() {
        return warningService.getStabilityWarningCount();
    }

    /**
     * 未签收预警弹窗 http://192.168.200.192:3001/project/4897/interface/api/133507
     *
     * @param subjectId 专题id
     * @return 预警
     */
    @GetMapping("/new-warning")
    public HomePageWarningVO getHomePageWarningList(String subjectId) {
        return warningService.getHomePageWarningList(subjectId);
    }


    /**
     * 高风险警情类预警增加跳转到对比档案的url
     *
     * @param warningId 预警id
     * @return 对比档案url
     */
    @GetMapping("/{warningId}/event-url")
    public String getEventUrl(@PathVariable("warningId") String warningId) {
        return warningService.getEventUrl(warningId);
    }

    /**
     * 查询车辆全量轨迹
     *
     * @param warningId 预警id
     * @return {@link WarningCarTrajectoryVO}
     */
    @GetMapping("/car/trajectory/{warningId}")
    public List<WarningCarTrajectoryVO> getCarTrajectory(@PathVariable("warningId") String warningId) {
        return warningService.getCarTrajectory(warningId);
    }

    /**
     * 查询车辆信息
     *
     * @param warningId 预警id
     * @return {@link CarWarningVO}
     */
    @GetMapping("/{warningId}/car/detail")
    public CarWarningVO getCarDetail(@PathVariable("warningId") String warningId) {
        return warningService.getCarDetail(warningId);
    }

    /**
     * 黑车研判
     *
     * @param judgeVO 研判结果
     */
    @PostMapping("/car/judge")
    public void judgeCar(@RequestBody @Validated WarningCarJudgeVO judgeVO){
        warningService.judgeCar(judgeVO);
    }

    /**
     * 查询车辆全量轨迹
     *
     * @param request 请求参数
     * @return {@link CarTrajectoryVO}
     */
    @PostMapping("/track-info")
    public CarTrajectoryVO getCarTrajectoryByCarNumber(@RequestBody @Validated CarTrajectoryRequest request) {
        return warningService.getCarTrajectoryByCarNumber(request.getCarNumber(), request.getTimeParams());
    }

    /**
     * 手动触发无证驾驶定时分析任务
     */
    @GetMapping ("/drive/manual")
    public void driveNoLicenseAnalysisByManual(){
        warningService.driveNoLicenseAnalysisByManual();
    }
}
