package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjReadRecordEntity;
import com.trs.yq.police.subject.domain.entity.CrjSfryReportEntity;
import com.trs.yq.police.subject.domain.entity.GridMangerEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.mysqlDatasource.entity.CrjSfryReportSourceEntity;
import com.trs.yq.police.subject.repository.CrjReadRecordRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.GridMangerRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 10:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SfryReportVO {

    private String id;

    private String content;

    private String phone;

    private String address;

    private Date reportTime;

    private String acceptor;

    /**
     * 核实人
     */
    private String verifyUser;
    private String verifyResult;

    /**
     * 备注
     */
    private String description;

    /**
     * 核实时间
     */
    private Date verifyTime;

    private Boolean isDispatched;

    private Boolean isVerified;

    private Boolean isRead;

    private List<String> attachment;

    private String reportName;
    private String policeName;
    private String policeDept;
    private String policePhone;
    /**
     * 转vo
     *
     * @param reportSourceEntity 实体类
     * @param reportEntity       实体类
     * @param currentUser        当前用户
     * @param key                密钥
     * @return {@link SfryReportVO}
     */
    public static SfryReportVO of(CrjSfryReportSourceEntity reportSourceEntity, CrjSfryReportEntity reportEntity,
        LoginUser currentUser, String key) {
        SfryReportVO sfryReportVO = new SfryReportVO();
        sfryReportVO.setId(reportSourceEntity.getUuid());
        sfryReportVO.setContent(reportSourceEntity.getContent());
        //手机号解密
        sfryReportVO.setPhone(StringUtil.aesDecrypt(reportSourceEntity.getPhone(), key));
        sfryReportVO.setAddress(reportSourceEntity.getAddress());
        sfryReportVO.setReportTime(reportEntity.getReportTime());
        sfryReportVO.setIsRead(false);
        sfryReportVO.setIsVerified(false);
        sfryReportVO.setVerifyResult("0");
        sfryReportVO.setAttachment(Collections.emptyList());
        if (StringUtils.isNotBlank(reportEntity.getAcceptor())) {
            UnitRepository unit = BeanUtil.getBean(UnitRepository.class);
            sfryReportVO.setPoliceDept(unit.getAreaNameByUnitCode(reportEntity.getAcceptor()));
        }
        CrjReadRecordRepository readRecordRepository = BeanUtil.getBean(CrjReadRecordRepository.class);
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        GridMangerRepository gridMangerRepository = BeanUtil.getBean(GridMangerRepository.class);
        GridMangerEntity gridManger = gridMangerRepository.findByGridManagerPhoneNumber(sfryReportVO.getPhone());
        if (Objects.nonNull(gridManger)) {
            sfryReportVO.setPoliceName(gridManger.getPoliceName());
            sfryReportVO.setReportName(gridManger.getGridManagerName());
            sfryReportVO.setPolicePhone(gridManger.getPolicePhoneNumber());
        }
        if (Objects.nonNull(reportEntity)) {
            if (StringUtils.isNotBlank(reportEntity.getAcceptor())) {
                UnitRepository unitRepository = BeanUtil.getBean(UnitRepository.class);
                UnitEntity byUnitCode = unitRepository.findByUnitCode(reportEntity.getAcceptor());
                sfryReportVO.setAcceptor(byUnitCode.getUnitName());
            }
            sfryReportVO.setIsVerified(reportEntity.getIsVerified());
            sfryReportVO.setVerifyResult(reportEntity.getVerifyResult());
            sfryReportVO.setVerifyTime(reportEntity.getVerifyTime());
            sfryReportVO.setDescription(reportEntity.getDescription());
            //附件
            sfryReportVO.setAttachment(JsonUtil.parseArray(reportEntity.getAttachment(), String.class));
            //是否已读
            Optional<CrjReadRecordEntity> readRecord = readRecordRepository.findByRecordIdAndModuleAndUserId(
                reportEntity.getReportId(), CrjConstants.SFRY_REPORT_RECORD_MODULE, currentUser.getId());
            sfryReportVO.setIsRead(readRecord.isPresent());
            //是否分配
            CrjDispatchStatusEnum dispatchStatus = CrjDispatchStatusEnum.codeOf(reportEntity.getDispatchStatus());
            String unitCode = currentUser.getUnitCode();
            if (CrjConstants.SJZD.equals(unitCode)) {
                //分到区县就算已分派
                sfryReportVO.setIsDispatched(dispatchStatus == CrjDispatchStatusEnum.DISPATCHED
                    || dispatchStatus == CrjDispatchStatusEnum.DISPATCHED_TO_QX);

            } else {
                //分到派出所才算已分派
                sfryReportVO.setIsDispatched(dispatchStatus == CrjDispatchStatusEnum.DISPATCHED);
            }
            //核实结果
            sfryReportVO.setVerifyResult(
                dictRepository.findByTypeAndCode(CrjConstants.SFRY_VERIFY_STATUS, reportEntity.getVerifyResult())
                    .getName());
        }
        return sfryReportVO;
    }
}
