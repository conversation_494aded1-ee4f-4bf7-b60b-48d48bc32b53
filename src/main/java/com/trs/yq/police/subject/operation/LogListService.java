package com.trs.yq.police.subject.operation;

import com.trs.yq.police.subject.domain.dto.OperationLogQueryVO;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.OperationLogVo;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.WarningLogsVO;
import org.springframework.data.domain.Pageable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 操作记录列表查询接口
 *
 * <AUTHOR>
 * @since 2021/10/22
 */
public interface LogListService {

    /**
     * 操作日志分页查询
     *
     * @param queryKey 查询主键
     * @param query    查询参数
     * @param pageable 分页参数
     * @return 操作日志列表查询
     */
    PageResult<OperationLogVo> getOperationLogList(@NotBlank(message = "检索主键缺失") String queryKey,
                                                   OperationLogQueryVO query,
                                                   @NotNull(message = "分页参数缺失") Pageable pageable);


    /**
     * 查询预警操作日志
     *
     * @param warningId 预警id
     * @param order 顺序
     * @return 操作日志 {@link WarningLogsVO}
     */
    WarningLogsVO getWarningOperationLogList(String warningId, String order);

    /**
     * 维稳操作日志分页查询
     *
     * @param id       查询主键
     * @param query    查询参数
     * @param pageParams 分页参数
     * @return 操作日志列表查询
     */
    PageResult<OperationLogVo> getStabilityOperationLogList(@NotBlank(message = "检索主键缺失") String id,
                                                   OperationLogQueryVO query,
                                                   @NotNull(message = "分页参数缺失") PageParams pageParams);
}
