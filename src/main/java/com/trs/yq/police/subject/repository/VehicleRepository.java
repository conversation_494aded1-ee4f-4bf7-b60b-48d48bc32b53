package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.VehicleEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人员车辆信息查询接口
 *
 * <AUTHOR>
 * @date 2021/7/28 9:23
 */
@Repository
public interface VehicleRepository extends BaseRepository<VehicleEntity, String> {
    /**
     * 查询人员有关联的车辆信息
     *
     * @param personId 人员Id
     * @return 车辆信息
     */
    List<VehicleEntity> findByPersonId(String personId);

    /**
     * 获取所有车牌号
     *
     * @return 车牌号
     */
    @Query("select v.vehicleNumber from VehicleEntity v")
    List<String> findAllVehicleNumber();

    /**
     * 检查该人员车牌号是否存在
     *
     * @param personId 人员id
     * @param vehicleNumber 车牌号
     * @return 结果
     */
    Boolean existsByPersonIdAndVehicleNumber(String personId, String vehicleNumber);
}
