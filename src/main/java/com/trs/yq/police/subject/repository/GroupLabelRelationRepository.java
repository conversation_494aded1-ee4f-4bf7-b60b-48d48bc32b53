package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.GroupLabelRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 * @date 2021/9/2 14:55
 */
@Repository
public interface GroupLabelRelationRepository extends BaseRepository<GroupLabelRelationEntity, String> {
    /**
     * 根据groupId移除关联关系
     *
     * @param groupId 群体Id
     */
    @Modifying
    void removeAllByGroupId(String groupId);

    /**
     * 根据groupId删除所有关系
     *
     * @param groupId 群体id
     */
    void deleteAllByGroupId(String groupId);
}
