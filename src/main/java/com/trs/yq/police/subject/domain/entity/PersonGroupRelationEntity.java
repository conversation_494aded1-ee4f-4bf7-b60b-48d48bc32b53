package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_GROUP_RELATION")
public class PersonGroupRelationEntity extends BaseEntity {

    private static final long serialVersionUID = -6461871283855375053L;

    /**
     * 构造器
     *
     * @param personId 人员id
     * @param groupId  群体id
     */
    public PersonGroupRelationEntity(String personId, String groupId) {
        this.personId = personId;
        this.groupId = groupId;
        this.activityLevel = "1";
    }

    /**
     * 人员ID
     */
    private String personId;

    /**
     * 群体ID
     */
    private String groupId;
    /**
     * 活跃程度
     */
    private String activityLevel;
}

