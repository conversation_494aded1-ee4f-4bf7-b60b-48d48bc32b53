package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.VehicleVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 人员车辆信息业务层接口
 *
 * <AUTHOR>
 * @date 2021/7/28 9:46
 */
public interface VehicleService {

    /**
     * 查询人员有关车辆信息
     *
     * @param personId 人员Id
     * @return 人员车辆信息
     */
    List<VehicleVO> getVehicleInfo(@NotBlank(message = "人员id缺失") String personId);

    /**
     * 新增车辆信息
     *
     * @param personId  人员id
     * @param vehicleVO 车辆信息
     */
    void saveVehicle(@NotBlank(message = "人员id缺失") String personId, @NotNull(message = "车辆基础信息缺失") VehicleVO vehicleVO);

    /**
     * 删除车辆信息
     *
     * @param personId 人员id
     * @param id       主键
     */
    void deleteVehicle(@NotBlank(message = "人员id缺失") String personId, @NotBlank(message = "车辆id缺失") String id);

    /**
     * 更新操作
     *
     * @param personId  人员id
     * @param vehicleVO 车辆信息实体
     */
    void updateVehicle(String personId, @NotNull(message = "车辆信息缺失") VehicleVO vehicleVO);
}
