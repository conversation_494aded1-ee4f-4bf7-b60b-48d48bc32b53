package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.DisplayTypeEnum;
import com.trs.yq.police.subject.domain.dto.CarWarningDTO;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.*;

import java.util.List;

/**
 * 预警服务层接口
 *
 * <AUTHOR>
 * @since 2021/9/1
 */
public interface WarningService {

    /**
     * 获取预警基础详情
     *
     * @param warningId 预警id
     * @return 详情 {@link WarningDetailVO}
     */
    WarningDetailVO getWarningDetail(String warningId);

    /**
     * 获取预警详情人员列表
     *
     * @param warningId 预警id
     * @return 详情 {@link WarningPersonVO}
     */
    List<WarningPersonVO> getWarningPersonDetail(String warningId);

    /**
     * 根据预警类型获取显示类型
     *
     * @param warningType 预警类型
     * @return 显示类型
     */
    DisplayTypeEnum getDisplayTypeByWarningType(String warningType);

    /**
     * [预警档案-预警列表] 列表查询
     *
     * @param subjectId 专题id
     * @param request   {@link ListRequestVO}
     * @return {@link WarningListVO}
     */
    PageResult<WarningListVO> getWarningList(String subjectId, ListRequestVO request);

    /**
     * 维稳专题进京赴省预警列表查询接口
     *
     * @param status 状态
     * @return {@link StabilityWarningListVO}
     */
    List<StabilityWarningListVO> getStabilityWarningList(String status);

    /**
     * 未签收预警弹窗
     *
     * @param subjectId 专题id
     * @return {@link HomePageWarningVO}
     */
    HomePageWarningVO getHomePageWarningList(String subjectId);

    /**
     * 查询维稳专题进京赴省预警签收/未签收统计
     *
     * @return {@link StabilityWarningCountVO}
     */
    StabilityWarningCountVO getStabilityWarningCount();

    /**
     * 分页查询人员相关预警列表
     *
     * @param pageParams 分页参数
     * @param subjectId  专题id
     * @param personId   人员id
     * @return {@link  WarningListVO}
     */
    PageResult<WarningListVO> getPersonWarningList(PageParams pageParams, String subjectId, String personId);

    /**
     * 高风险警情类预警增加跳转到对比档案的url
     *
     * @param warningId 预警id
     * @return 对比档案url
     */
    String getEventUrl(String warningId);


    /**
     * 查询车辆全量轨迹
     *
     * @param warningId 车牌号
     * @return {@link WarningCarTrajectoryVO}
     */
    List<WarningCarTrajectoryVO> getCarTrajectory(String warningId);

    /**
     * 查询车辆信息
     *
     * @param warningId 预警id
     * @return {@link CarWarningDTO}
     */
    CarWarningVO getCarDetail(String warningId);

    /**
     * 根据车牌号获取车辆轨迹
     *
     * @param carNumber  车牌号
     * @param timeParams 时间参数
     * @return {@link  CarTrajectoryVO}
     */
    CarTrajectoryVO getCarTrajectoryByCarNumber(String carNumber, TimeParams timeParams);

    /**
     * 黑车研判
     *
     * @param judgeVO 研判结果
     */
    void judgeCar(WarningCarJudgeVO judgeVO);

    /**
     * 手动触发黑车研判
     */
    void driveNoLicenseAnalysisByManual();

    /**
     * testDriveNoLicenseAnalysisSql<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 09:51
     */
    String testDriveNoLicenseAnalysisSql();
}
