package com.trs.yq.police.subject.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 人员统计数据传输
 *
 * <AUTHOR>
 * @date 2021/9/3 20:54
 */
@AllArgsConstructor
@Getter
@Setter
@ToString
public class PersonCountDTO implements Serializable {

    private static final long serialVersionUID = -7225841608627466777L;

    /**
     * 管控状态
     */
    private String controlStatus;

    /**
     * 数量
     */
    private Long count;
}
