package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.MobilityPageWithTotal;
import com.trs.yq.police.subject.domain.vo.MobilityVO;
import com.trs.yq.police.subject.service.MobilityService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 流动信息接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/person")
@Validated
public class MobilityController {

    @Resource
    MobilityService mobilityService;

    /**
     * http://192.168.200.192:3001/project/4897/interface/api/130055
     * 分页查询流动信息 额外返回流入信息统计结果
     *
     * @param personId   人员id
     * @param pageParams 分页参数
     * @return 流动信息列表
     */
    @PostMapping("{personId}/mobility/list")
    public MobilityPageWithTotal getMovement(@PathVariable String personId, @RequestBody PageParams pageParams) {
        return mobilityService.getMovement(personId, pageParams);
    }

    /**
     * 增加流动信息
     * http://192.168.200.192:3001/project/4897/interface/api/130040
     *
     * @param personId   人员id
     * @param mobilityVO 流动信息
     */
    @PostMapping("{personId}/mobility")
    public void addMovement(@PathVariable String personId, @Valid @RequestBody MobilityVO mobilityVO) {
        mobilityService.addMovement(personId, mobilityVO);
    }

    /**
     * 修改流动信息
     * http://192.168.200.192:3001/project/4897/interface/api/130050
     *
     * @param personId   人员id
     * @param mobilityVO 流动信息
     */
    @PutMapping("{personId}/mobility")
    public void updateMovement(@PathVariable String personId, @Valid @RequestBody MobilityVO mobilityVO) {
        mobilityService.updateMovement(personId, mobilityVO);
    }

    /**
     * 删除流动信息
     * http://192.168.200.192:3001/project/4897/interface/api/130045
     *
     * @param personId 人员id
     * @param id       流动信息id
     */
    @DeleteMapping("{personId}/mobility")
    public void deleteMovement(@PathVariable String personId, @NotBlank(message = "id不能为空！") String id) {
        mobilityService.deleteMovement(personId, id);
    }

}
