package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 新流入人员预警VO
 *
 * <AUTHOR>
 * @date 2021/9/6 20:35
 */
@Data
public class NewPersonInWarningVO implements Serializable {
    private static final long serialVersionUID = 4688543513111222658L;
    /**
     * 预警id
     */
    private String warningId;
    /**
     * 预警等级：1: 红色 2: 橙色 3:黄色 4:浪色
     */
    private String warningLevel;
    /**
     * 专题id
     */
    private String personId;
    /**
     * 姓名
     */
    private String personName;
    /**
     * 头像
     */
    private List<ImageVO> images;
    /**
     * 预警地点
     */
    private String warningPlace;
    /**
     * 预警时间
     */
    private LocalDateTime warningTime;

}
