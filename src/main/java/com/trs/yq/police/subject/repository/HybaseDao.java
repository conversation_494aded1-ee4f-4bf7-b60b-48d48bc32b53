package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CameraEntity;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.FaceHitEntity;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.WarningCarTrajectoryVO;
import java.util.List;

/**
 * hybase 数据访问接口
 *
 * <AUTHOR>
 */
public interface HybaseDao {

    /**
     * 查询海康视频探头数据
     *
     * @param lng      经度
     * @param lat      纬度
     * @param distance 距离 单位：公里
     * @return {@link CameraEntity}
     */
    List<CameraEntity> getVideoCameras(String lng, String lat, Double distance);

    /**
     * 查询视频摄像头详情
     *
     * @param cameraId cameraId
     * @return {@link CameraEntity}
     */
    CameraEntity getVideoCamera(String cameraId);

    /**
     * 查询图像探头数据
     *
     * @return {@link CameraEntity}
     */
    List<CameraEntity> getImageCameras();

    /**
     * 查询事件中人像命中数据
     *
     * @param event 事件
     * @return {@link FaceHitEntity}
     */
    List<FaceHitEntity> getFaceHits(EventEntity event);

    /**
     * 查询 车辆轨迹数据
     *
     * @param carNumber    车牌号
     * @param warningLevel 预警级别
     * @param timeParams 时间参数
     * @param maxRecord 最大参数
     * @return {@link WarningCarTrajectoryVO}
     */
    List<WarningCarTrajectoryVO> getWarningCarTrajectory(String carNumber, String warningLevel, TimeParams timeParams,Integer maxRecord);
}
