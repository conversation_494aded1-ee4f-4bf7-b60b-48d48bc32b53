package com.trs.yq.police.subject.builder;

import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.EventLabelRelationEntity;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.trs.yq.police.subject.utils.StringUtil.getPoliceStationPrefix;

/**
 * <AUTHOR>
 * @date 2021/12/14 16:18
 */
public class EventListPredicatesBuilder {
    /**
     * 创建人员列表动态检索Predicates
     *
     * @param subjectId       专题id
     * @param filterParams    检索参数
     * @param root            事件实体
     * @param criteriaBuilder criteriaBuilder
     * @return 检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildListFilterPredicates(String subjectId, List<KeyValueVO> filterParams, Root<EventEntity> root, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();

        // 联表查询专题和事件的关系
        predicates.add(criteriaBuilder.equal(root.get("subjectId").as(String.class), subjectId));

        //动态查询参数
        filterParams.forEach(kv -> {
            switch (kv.getKey()) {
                case "disposalStatus":
                    predicates.add(getEventDisposalStatusPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "eventSource":
                    predicates.add(getEventSourcePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "eventType":
                    predicates.add(getEventTypePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "timeParams":
                    predicates.add(getEventOccurrenceTimePredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "reportStatus":
                    predicates.add(getEventReportStatusPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                case "department":
                    predicates.add(getEventControlDepartmentPredicates(root, kv.getValue(), criteriaBuilder));
                    break;
                default:
                    break;
            }
        });
        return predicates;
    }

    /**
     * 创建事件处置状态查询条件
     *
     * @param root            事件实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getEventDisposalStatusPredicates(Root<EventEntity> root, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(root.get("disposalStatus").as(String.class), value);
    }

    /**
     * 创建事件来源查询条件
     *
     * @param eventEntityRoot 事件实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getEventSourcePredicates(Root<EventEntity> eventEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(eventEntityRoot.get("source").as(String.class), value);
    }

    /**
     * 创建事件类型查询条件
     *
     * @param eventEntityRoot 事件实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getEventTypePredicates(Root<EventEntity> eventEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        Subquery<EventLabelRelationEntity> subQuery = criteriaBuilder.createQuery().subquery(EventLabelRelationEntity.class);
        Root<EventLabelRelationEntity> eventLabelRelationEntityRoot = subQuery.from(EventLabelRelationEntity.class);
        Predicate predicate1 = criteriaBuilder.equal(eventLabelRelationEntityRoot.get("labelId").as(String.class), value);
        Predicate predicate2 = criteriaBuilder.equal(eventLabelRelationEntityRoot.get("eventId"), eventEntityRoot.get("id"));
        subQuery.select(eventLabelRelationEntityRoot).where(predicate1, predicate2);
        return criteriaBuilder.exists(subQuery);
    }

    /**
     * 创建事件创建时间查询条件
     *
     * @param eventEntityRoot 事件实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getEventOccurrenceTimePredicates(Root<EventEntity> eventEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        TimeParams timeParams = JsonUtil.parseObject(value, TimeParams.class);
        if (timeParams != null) {
            Predicate p1 = criteriaBuilder.greaterThanOrEqualTo(eventEntityRoot.get("occurrenceTime").as(LocalDateTime.class), timeParams.getBeginTime());
            Predicate p2 = criteriaBuilder.lessThanOrEqualTo(eventEntityRoot.get("occurrenceTime").as(LocalDateTime.class), timeParams.getEndTime());
            return criteriaBuilder.and(p1, p2);
        }
        return null;
    }

    /**
     * 创建事件上报状态查询条件
     *
     * @param root            事件实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getEventReportStatusPredicates(Root<EventEntity> root, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.equal(root.get("reportStatus").as(String.class), value);
    }

    /**
     * 创建事件管控部门查询条件
     *
     * @param eventEntityRoot 事件实体
     * @param value           检索值
     * @param criteriaBuilder criteriaBuilder
     * @return 查询条件 {@link Predicate}
     */
    private static Predicate getEventControlDepartmentPredicates(Root<EventEntity> eventEntityRoot, String value, CriteriaBuilder criteriaBuilder) {
        return criteriaBuilder.like(eventEntityRoot.get("controlDept").as(String.class), getPoliceStationPrefix(value) + "%");
    }

    /**
     * 创建模糊检索Predicates
     *
     * @param searchParams    模糊搜索参数
     * @param eventEntityRoot 事件记录
     * @param criteriaBuilder criteriaBuilder
     * @return 模糊检索Predicates {@link Predicate}
     */
    public static List<Predicate> buildSearchPredicates(SearchParams searchParams, Root<EventEntity> eventEntityRoot, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();
        if (StringUtils.isNotBlank(searchParams.getSearchField()) && StringUtils.isNotBlank(searchParams.getSearchValue())) {
            String searchField = searchParams.getSearchField();
            String searchValue = searchParams.getSearchValue().trim();
            Predicate namePredicate = criteriaBuilder.like(eventEntityRoot.get("title").as(String.class), "%" + searchValue + "%");
            Predicate detailPredicate = criteriaBuilder.like(eventEntityRoot.get("address").as(String.class), "%" + searchValue + "%");
            switch (searchField) {
                case "title":
                    predicates.add(namePredicate);
                    break;
                case "address":
                    predicates.add(detailPredicate);
                    break;
                case "fullText":
                    predicates.add(criteriaBuilder.or(namePredicate, detailPredicate));
                    break;
                default:
                    break;
            }
        }
        return predicates;
    }
}
