package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.CameraEntity;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2021/11/23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CameraVO extends CameraEntity {

    private static final long serialVersionUID = -7204766313948259515L;

    /**
     * 播放视频的url
     */
    private String url;

    /**
     * entity to vo
     *
     * @param cameraEntity entity
     * @return vo
     */
    public static CameraVO of(CameraEntity cameraEntity) {
        CameraVO vo = new CameraVO();
        BeanUtil.copyPropertiesIgnoreNull(cameraEntity, vo);
        return vo;
    }
}
