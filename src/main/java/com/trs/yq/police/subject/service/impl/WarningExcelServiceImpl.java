package com.trs.yq.police.subject.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.WarningListPredicatesBuilder;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.RoleDataEntity;
import com.trs.yq.police.subject.domain.entity.SubjectEntity;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.WarningExportListVO;
import com.trs.yq.police.subject.domain.vo.WarningListExportRequest;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.WarningExcelService;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.util.stream.Collectors;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_WARNING_STATUS;

/**
 * 预警列表批量导出服务实现类
 *
 * <AUTHOR>
 * @date 2021/09/15
 */
@Service
public class WarningExcelServiceImpl implements WarningExcelService {

    @Resource
    private SubjectRepository subjectRepository;

    @Resource
    private WarningRepository warningRepository;

    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;

    @Resource
    private DictRepository dictRepository;

    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private RoleDataRepository roleDataRepository;

    /**
     * 根据传来的属性导出excel
     *
     * @param response  响应体
     * @param request   {@link WarningListExportRequest}
     * @param subjectId 主题id
     * @throws IOException IO异常
     */
    @Override
    public void downLoadExcel(HttpServletResponse response, ExportParams request, String subjectId) throws IOException {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElseThrow(IOException::new);
        String fileName = String.format("%s-预警档案-%s.xlsx", subjectEntity.getName(), LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        LoginUser user = AuthHelper.getCurrentUser();
        List<RoleDataEntity> roleData = roleDataRepository.findViewRangesByUserId(user.getId());
        List<String> viewRanges = roleData.stream().map(RoleDataEntity::getViewRange).filter(Objects::nonNull)
            .collect(Collectors.toList());
        List<WarningEntity> warningList =request.getIsAll()
            ? warningRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = WarningListPredicatesBuilder.buildListFilterPredicates(viewRanges, subjectId,
                    request.getListParams().getFilterParams(), root, criteriaBuilder).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(request.getListParams().getSearchParams())) {
                predicates.addAll(WarningListPredicatesBuilder.buildSearchPredicates(request.getListParams().getSearchParams(), root,
                    criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        })
        :warningRepository.findAllByIdIn(request.getIds());
        List<WarningExportListVO> vos = new ArrayList<>();
        warningList.forEach(warning -> {
            WarningExportListVO vo = new WarningExportListVO();
            vo.setWarningContent(warning.getWarningDetails());
            vo.setWarningSource(String.join("、", warning.getWarningSource()));
            vo.setWarningType(warningTypeRepository.getById(warning.getWarningType()).getCnName());
            vo.setWarningTime(warning.getWarningTime().format(DATE_TIME_FORMATTER));
            vo.setWarningStatus(dictRepository.findByTypeAndCode(DICT_TYPE_WARNING_STATUS, warning.getWarningStatus()).getName());
            vos.add(vo);
        });
        EasyExcelFactory.write(response.getOutputStream(), WarningExportListVO.class)
                .registerWriteHandler(new CustomCellWriteHandler())
                .includeColumnFiledNames(request.getFieldNames())
                .sheet()
                .doWrite(vos);
    }

    /**
     * 获得不同专题下批量导出需要的属性
     *
     * @param subjectId 专题Id
     * @return 属性json
     */
    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getWarningListProperty());
    }
}
