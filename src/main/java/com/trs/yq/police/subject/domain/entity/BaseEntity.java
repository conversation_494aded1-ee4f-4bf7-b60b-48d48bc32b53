package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.jpa.listener.CustomJpaAuditListener;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 9:53
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@MappedSuperclass
@EntityListeners({AuditingEntityListener.class, CustomJpaAuditListener.class})
public abstract class BaseEntity implements Serializable {

    private static final long serialVersionUID = -9150934301952183155L;
    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 数据创建用户id
     */
    @CreatedBy
    private String crBy;
    /**
     * 数据创建用户姓名
     */
    private String crByName;

    /**
     * 数据创建时间
     */
    @CreatedDate
    private LocalDateTime crTime;

    /**
     * 数据最后更新人
     */
    @LastModifiedBy
    private String upBy;

    /**
     * 数据最后更新人
     */
    private String upByName;

    /**
     * 数据最后更新时间
     */
    @LastModifiedDate
    private LocalDateTime upTime;

    /**
     * 创建部门名称
     */
    private String crDept;

    /**
     * 创建部门单位代码
     */
    private String crDeptCode;
}
