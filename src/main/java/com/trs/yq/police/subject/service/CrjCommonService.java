package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.vo.UnitVO;
import com.trs.yq.police.subject.domain.vo.UnreadRecordVO;
import java.util.List;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/24 14:03
 */
public interface CrjCommonService {

    /**
     * 获取接受部门
     *
     * @return {@link UnitVO}
     */
    List<UnitVO> getAcceptor();


    /**
     * 列表未读统计
     *
     * @return {@link UnreadRecordVO}
     */
    UnreadRecordVO statisticUnread();

    /**
     * 设置以读记录消息
     *
     * @param recordId 记录id
     * @param userId   用户id
     * @param module   模块
     */
    void addReadRecord(String recordId, String userId, String module);

    /**
     * 获取列表筛选条件
     *
     * @param listName 列表名称
     * @return {@link ListFilter}
     */
    List<ListFilter> getListFilter(String listName);

    /**
     * 获取模块
     *
     * @return {@link String}
     */
    List<String> getModule();

}
