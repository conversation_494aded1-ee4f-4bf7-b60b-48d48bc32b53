package com.trs.yq.police.subject.domain.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 导入参数
 *
 * <AUTHOR>
 * @date 2021/8/9 10:09
 */
@Getter
@Setter
@ToString
public class ImportVO implements Serializable {

    private static final long serialVersionUID = -4069655438626687423L;

    /**
     * 主题主键
     */
    @NotBlank(message = "主题主键缺失")
    private String subjectId;

    /**
     * 身份证重复策略
     */
    private String repeatStrategy;

    /**
     * 人员类型
     */
    private String personType;

    /**
     * excel 文件
     */
    private transient MultipartFile excel;
}
