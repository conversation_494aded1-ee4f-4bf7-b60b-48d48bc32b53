package com.trs.yq.police.subject.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/11/02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class IdNameCountVO extends IdNameVO {

    private static final long serialVersionUID = 8012319686312338766L;

    private Integer count;

    /**
     * 构造器
     *
     * @param id    id
     * @param name  name
     * @param count count
     */
    public IdNameCountVO(String id, String name, int count) {
        super(id, name);
        this.count = count;
    }
}
