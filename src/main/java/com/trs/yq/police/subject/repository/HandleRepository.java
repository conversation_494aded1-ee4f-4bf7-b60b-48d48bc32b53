package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.HandleEntity;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 处警数据库v_cjdb接口
 *
 * <AUTHOR>
 * @since 2021/7/21
 */
@Repository
public interface HandleRepository extends BaseRepository<HandleEntity, String> {

    /**
     * 查询处警反馈
     *
     * @param receiveNum 主键
     * @return {@link HandleEntity}
     */
    HandleEntity findByReceiveNum(@Param("receivenum") String receiveNum);
}
