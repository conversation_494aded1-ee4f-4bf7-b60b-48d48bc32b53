package com.trs.yq.police.subject.task;

import com.alibaba.fastjson.JSON;
import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.DrivingNoLicenseAnalyzeVO;
import com.trs.yq.police.subject.mapper.DriveMapper;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.TimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 * DriveNoLicenseAnalysisTask
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/18 20:40
 * @since 1.0
 */
@Component
@Slf4j
@ConditionalOnProperty(value = "com.trs.schedule.driveNoLicenseAnalysis.task.enable", havingValue = "true")
public class DriveNoLicenseAnalysisTask {

    @Resource
    private WarningRepository warningRepository;

    @Resource
    private DriveMapper driveMapper;

    @Resource
    private WarningTypeRepository warningTypeRepository;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private PersonSubjectRelationRepository subjectRelationRepository;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private PersonLabelRelationRepository personLabelRelationRepository;

    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;

    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;

    @Resource
    private WarningUniqueIdRelationRepository warningUniqueIdRelationRepository;

    @Value("${com.trs.schedule.driveNoLicenseAnalysis.task.sql.file.path}")
    private String driveNoLicenseAnalysisSqlFilePath;

    private final static String WARNING_TYPE_EN_NAME = "jj_wzjs";
    private final static String PERSON_LABEL_NAME = "无驾照人员";
    private final static String TRAFFIC_POLICE_SUBJECT_ID = "4";

    /**
     * 无证驾驶定时分析任务（每天5点执行一次）
     */
    @Scheduled(cron = "${com.trs.schedule.driveNoLicenseAnalysis.task}")
    public void run() {
        try {
            analyze();
        } catch (Exception e) {
            log.error("无证驾驶定时分析失败", e);
        }
    }

    private void analyze() {
        String sql = findDriveNoLicenseAnalysisSqlInFile();
        List<DrivingNoLicenseAnalyzeVO> collect;
        // 预警结果
        if (StringUtils.isNotEmpty(sql)) {
            log.info("执行自定义SQL:{}", sql);
            collect = driveMapper.customSql(sql);
        } else {
            log.info("未配置自定义SQL，将执行内置SQL");
            collect = driveMapper.timingAnalyze();
        }
        //已入库的预警信息（根据预警编号去重）
        for (DrivingNoLicenseAnalyzeVO data : collect) {
            try {
                doTransaction(data);
            } catch (Exception e) {
                log.error("预警信息唯一ID[{}]，人员身份证号[{}]，数据入库异常：", data.getYjbh(), data.getZjhm(), e);
            }

        }
    }

    /**
     * findDriveNoLicenseAnalysisSqlInFile<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 10:00
     */
    private String findDriveNoLicenseAnalysisSqlInFile() {
        if (StringUtils.isNotEmpty(driveNoLicenseAnalysisSqlFilePath)) {
            try {
                File file = new File(driveNoLicenseAnalysisSqlFilePath);
                if (!file.exists()) {
                    log.warn("文件[{}]不存在", driveNoLicenseAnalysisSqlFilePath);
                    return null;
                } else {
                    String sql = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
                    log.info("从文件[{}]，读取的SQL为:{}", driveNoLicenseAnalysisSqlFilePath, sql);
                    return sql;
                }
            } catch (Throwable e) {
                log.error("读取文件[{}]出错", driveNoLicenseAnalysisSqlFilePath, e);
            }
        }
        return null;
    }

    /**
     * testDriveNoLicenseAnalysisSql<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 09:53
     */
    public String testDriveNoLicenseAnalysisSql() {
        String sql = findDriveNoLicenseAnalysisSqlInFile();
        if (StringUtils.isEmpty(sql)) {
            return "未配置自定义SQL，将使用内置SQL";
        }
        log.info("开始执行自定义SQL:{}", sql);
        try {
            log.info("自定义SQL的执行结果为:{}", driveMapper.customSql(sql));
            return "自定义SQL正常执行，请在日志中查看响应结果是否符合要求";
        } catch (Throwable e) {
            log.error("自定义SQL执行异常", e);
            return "自定义SQL执行异常，Err=" + e.getMessage();
        }
    }

    /**
     * 事务控制 数据写入
     *
     * @param data 预警信息
     */
    @Transactional(rollbackFor = Exception.class)
    void doTransaction(DrivingNoLicenseAnalyzeVO data) {
        //人员档案入库
        savePerson(data);
        //预警信息入库
        saveWarning(data);
        //预警人员轨迹入库
        saveTrajectory(data);
    }

    /**
     * 预警人员信息入库
     *
     * @param data 预警信息
     */
    private void savePerson(DrivingNoLicenseAnalyzeVO data) {
        //新增人员档案（已存在则跳过）
        PersonEntity byIdNumber = personRepository.findByIdNumber(data.getZjhm());
        if (byIdNumber == null) {
            byIdNumber = new PersonEntity();
            byIdNumber.setName(data.getXm());
            byIdNumber.setIdNumber(data.getZjhm());
            byIdNumber.setControlStatus("1");
            byIdNumber.setMonitorStatus("1");
            personRepository.save(byIdNumber);
        }
        //人员专题关联关系
        PersonSubjectRelationEntity subjectRelationEntity = subjectRelationRepository.findByPersonIdAndSubjectId(byIdNumber.getId(), TRAFFIC_POLICE_SUBJECT_ID);
        if (Objects.isNull(subjectRelationEntity)) {
            subjectRelationEntity = new PersonSubjectRelationEntity();
            subjectRelationEntity.setSubjectId(TRAFFIC_POLICE_SUBJECT_ID);
            subjectRelationEntity.setPersonId(byIdNumber.getId());
            subjectRelationEntity.setCrTime(LocalDateTime.now());
            subjectRelationRepository.save(subjectRelationEntity);
        }
        //人员标签关联关系
        LabelEntity label = labelRepository.findByNameAndSubjectId(PERSON_LABEL_NAME, TRAFFIC_POLICE_SUBJECT_ID);
        PersonLabelRelationEntity relationEntity = personLabelRelationRepository.findByPersonIdAndLabelId(byIdNumber.getId(), label.getId())
                .orElse(null);
        if (Objects.isNull(relationEntity)) {
            relationEntity = new PersonLabelRelationEntity();
            relationEntity.setPersonId(byIdNumber.getId());
            relationEntity.setLabelId(label.getId());
            relationEntity.setCrTime(LocalDateTime.now());
            personLabelRelationRepository.save(relationEntity);
        }
    }

    /**
     * 预警入库
     *
     * @param data 预警信息
     */
    private void saveWarning(DrivingNoLicenseAnalyzeVO data) {
        Integer count = warningUniqueIdRelationRepository.selectByUniqueId(data.getYjbh());
        if (count > 0) {
            return;
        }
        WarningEntity warning = new WarningEntity();
        warning.setWarningKey(data.getZjhm());
        //预警类型
        final WarningTypeEntity warningType = warningTypeRepository.findByEnName(WARNING_TYPE_EN_NAME);
        warning.setWarningTime(LocalDateTime.now());
        warning.setWarningLevel(warningType.getDefaultLevel());
        warning.setWarningSource(null);
        warning.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
        //预警内容字符串模版
        //  {xm}（证件号码:{zjhm}）于{违法时间}驾驶车辆{}通过{违法地点},准驾车型:{zjcx}，驾驶证状态:{jszzt}
        String warningDetail = String.format("%s（%s）于%s驾驶车辆%s通过%s,准驾车型:%s，驾驶证状态:%s",
                data.getXm(),
                data.getZjhm(),
                DrivingNoLicenseAnalyzeVO.splitTime(data.getWfsj()),
                data.getHphm(),
                data.getWfdd(),
                Optional.ofNullable(data.getZjcx()).orElse(""),
                Optional.ofNullable(data.getJszzt()).orElse("")
        );
        warning.setPlace(data.getWfdd());
        warning.setWarningDetails(warningDetail);
        warning.setSubjectId(warningType.getSubjectId());
        warning.setWarningType(warningType.getId());
        //根据违法地组织单位编号  将预警信息推送到指定单位
        warning.setAreaCode(data.getGxdwbh());
        //原始数据
        warning.setRawData(JSON.toJSONString(data));
        WarningEntity save = warningRepository.save(warning);
        data.setWarningId(save.getId());

        //保存唯一ID
        WarningUniqueRelationEntity entity = new WarningUniqueRelationEntity();
        entity.setWarningId(save.getId());
        entity.setUniqueId(data.getYjbh());
        entity.setCreateTime(LocalDateTime.now());
        warningUniqueIdRelationRepository.save(entity);
    }

    /**
     * 预警人员轨迹入库
     *
     * @param data 数预警信息
     */
    private void saveTrajectory(DrivingNoLicenseAnalyzeVO data) {
        List<WarningTrajectoryEntity> collect = warningTrajectoryRepository.findAllByWarningId(data.getWarningId());
        if (CollectionUtils.isNotEmpty(collect)) {
            return;
        }
        WarningTrajectoryEntity trajectory = new WarningTrajectoryEntity();
        trajectory.setIdNumber(data.getZjhm());
        //经纬度
        if (StringUtils.isNotEmpty(data.getWfddZt())) {
            String[] split = data.getWfddZt().split(",");
            trajectory.setLng(split[0]);
            trajectory.setLat(split[1]);
        }
        trajectory.setPlace(data.getWfdd());
        if (StringUtils.isNotEmpty(data.getCjt())) {
            trajectory.setImageUrl(data.getCjt());
        }
        if (StringUtils.isNotEmpty(data.getRlt())) {
            trajectory.setCropUrl(data.getRlt());
        }
        //转换时间格式
        trajectory.setDateTime(
                DateUtil.utcToLocalDateTime(
                        TimeUtils.stringToDate(
                                DrivingNoLicenseAnalyzeVO.splitTime(data.getWfsj())
                        ).getTime()
                )
        );
        trajectory.setAreaCode(data.getGxdwbh());
        WarningTrajectoryEntity save = warningTrajectoryRepository.save(trajectory);

        //保存预警信息及其轨迹的关联关系
        WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
        relation.setCreateTime(LocalDateTime.now());
        relation.setWarningId(data.getWarningId());
        relation.setTrajectoryId(save.getId());
        warningTraceRelationRepository.save(relation);
    }

    /**
     * 处理历史数据
     */
    public void dealHistoryPerson() {
        LabelEntity label = labelRepository.findByNameAndSubjectId(PERSON_LABEL_NAME, TRAFFIC_POLICE_SUBJECT_ID);
        List<String> personIds = personLabelRelationRepository.selectByLabelId(label.getId());
        for (String personId : personIds) {
            PersonSubjectRelationEntity byPersonIdAndSubjectId = subjectRelationRepository.findByPersonIdAndSubjectId(personId, TRAFFIC_POLICE_SUBJECT_ID);
            if (Objects.isNull(byPersonIdAndSubjectId)) {
                byPersonIdAndSubjectId = new PersonSubjectRelationEntity();
                byPersonIdAndSubjectId.setPersonId(personId);
                byPersonIdAndSubjectId.setSubjectId(TRAFFIC_POLICE_SUBJECT_ID);
                byPersonIdAndSubjectId.setCrTime(LocalDateTime.now());
                subjectRelationRepository.save(byPersonIdAndSubjectId);
            }
        }
    }
}
