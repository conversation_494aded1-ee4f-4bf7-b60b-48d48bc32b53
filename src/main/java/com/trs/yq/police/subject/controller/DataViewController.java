package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.constants.enums.WarningTypeEnum;
import com.trs.yq.police.subject.domain.request.CountRequestVO;
import com.trs.yq.police.subject.domain.response.BasicCountResponseVO;
import com.trs.yq.police.subject.domain.response.CountDistributeMobilityResponseVO;
import com.trs.yq.police.subject.domain.response.MobilityCountResponseVO;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.repository.WarningTypeRepository;
import com.trs.yq.police.subject.service.DataViewService;
import com.trs.yq.police.subject.service.WarningService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.FK_SUBJECT;


/**
 * 数据视图HTTP接口类
 *
 * <AUTHOR>
 * @date 2021/9/3 19:36
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data-view")
public class DataViewController {

    @Resource
    private DataViewService dataViewService;

    @Resource
    private WarningService warningService;

    @Resource
    private WarningTypeRepository warningTypeRepository;


    /**
     * [专题首页] 预警轨迹查询
     * http://192.168.200.192:3001/project/4897/interface/api/130507
     *
     * @param warningId 预警id
     * @return 异常预警行为列表
     */
    @GetMapping("/warning/{warningId}/trajectory")
    public List<WarningTrajectoryVO> findWarningTrajectory(@NotEmpty(message = "预警id不能为空") @PathVariable String warningId) {
        return dataViewService.findWarningTrajectory(warningId);
    }


    /**
     * [专题首页] 预警轨迹查询
     * http://192.168.200.192:3001/project/4897/interface/api/130507
     *
     * @param warningId 预警id
     * @return 异常预警行为列表
     */
    @GetMapping("/warning/{warningId}/tel-trajectory")
    public List<WarningTrajectoryVO> findFkWarningTrajectory(@NotEmpty(message = "预警id不能为空") @PathVariable String warningId) {
        return dataViewService.findFkWarningTrajectory(warningId);
    }
    /**
     * [专题首页-禁毒] 模型挖掘信息
     * http://192.168.200.192:3001/project/4897/interface/api/131227
     *
     * @param status    预警状态
     * @return 挖掘信息列表
     */
    @GetMapping("/warning/mining/information")
    public WarningScrollListVO getMiningInformation(@NotEmpty(message = "预警状态不能为空") String status) {
        return dataViewService.getMiningInformation(status);
    }

    /**
     * [专题首页-禁毒] 注销驾照人员驾车
     * http://192.168.200.192:3001/project/4897/interface/api/131035
     *
     * @param status 预警状态
     * @return 注销驾照人员驾车列表
     */
    @GetMapping("/anti-drug/warning/person/driver")
    public WarningScrollListVO getDrugDriver(@NotEmpty(message = "预警状态不能为空") String status) {
        return dataViewService.getDrugDriver(status);
    }

    /**
     * [专题首页-交警] 注销驾照人员驾车
     * http://192.168.200.192:3001/project/4897/interface/api/131401
     *
     * @param status 预警状态
     * @return 注销驾照人员驾车列表
     */
    @GetMapping("/traffic-police/warning/person/driver")
    public WarningScrollListVO getTrafficPoliceDriver(@NotEmpty(message = "预警状态不能为空") String status) {
        return dataViewService.getTrafficPoliceDriver(status);
    }


    /**
     * [专题首页] 发现预警行为VO
     * http://192.168.200.192:3001/project/4897/interface/api/130429
     *
     * @param subjectId 专题id
     * @param status    预警状态
     * @return 异常预警行为列表
     */
    @GetMapping("/warning/person/action")
    public List<FindWarningBehaviorVO> findWarningBehavior(@NotEmpty(message = "专题id不能为空") String subjectId,
                                                           @NotEmpty(message = "预警状态不能为空") String status) {
        return dataViewService.getWarningBehavior(subjectId, status);
    }

    /**
     * [专题首页] 基本数量统计
     * http://192.168.200.192:3001/project/4897/interface/api/130489
     *
     * @param request 请求参数
     * @return 响应参数
     */
    @PostMapping("/count")
    public BasicCountResponseVO getBasicCount(@RequestBody CountRequestVO request) {

        return dataViewService.getBasicCount(request);
    }

    /**
     * [专题首页] 获取流动数量统计
     * http://192.168.200.192:3001/project/4897/interface/api/130687
     *
     * @param request 请求参数
     * @return 流动统计
     */
    @PostMapping("/count/mobility")
    public MobilityCountResponseVO getMobilityCount(@RequestBody CountRequestVO request) {

        request.setSubjectId(FK_SUBJECT);
        return dataViewService.getMobilityCount(request);
    }

    /**
     * 区县人员流动情况
     * http://192.168.200.192:3001/project/4897/interface/api/130495
     *
     * @param request 参数请求
     * @return 区县分布情况
     */
    @PostMapping("/statistics/distribute-mobility")
    public List<CountDistributeMobilityResponseVO> countDistributeMobility(@RequestBody CountRequestVO request) {

        return dataViewService.countDistributeMobility(request);
    }


    /**
     * 区县分布统计
     * http://192.168.200.192:3001/project/4897/interface/api/131011
     *
     * @param request 统计请求
     * @return 统计结果
     */
    @PostMapping("/statistics/distribute")
    public List<CountDistributeResponseVO> countDistribute(@RequestBody CountRequestVO request) {

        return dataViewService.countDistribute(request);
    }

    /**
     * [专题首页-反恐]获取新流入人员预警
     * http://192.168.200.192:3001/project/4897/interface/api/130417
     *
     * @param status 预警状态
     * @return {@link NewPersonInWarningVO }
     */
    @GetMapping("/counter-terrorism/warning/person/new")
    public WarningScrollListVO getNewPersonInWarning(@NotBlank(message = "预警状态缺失") String status) {
        return dataViewService.getNewPersonInWarningList(status);
    }

    /**
     * [专题首页-反恐-预警]查询群体违安人员聚集预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/131341
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/counter-terrorism/warning/group/gather")
    public WarningScrollListVO getTerrorismGatherList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getGatherWarningList(WarningTypeEnum.FK_QTJJ.getCode(), status);
    }

    /**
     * [专题首页-政保-预警]查询群体危安人员聚集预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/131347
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/political-guarantee/warning/group/gather")
    public WarningScrollListVO getPoliticalGatherList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getGatherWarningList(WarningTypeEnum.ZB_WARYJJYJ.getCode(), status);
    }

    /**
     * [专题首页-交警-预警]查询赛摩人员聚集预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/131353
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/traffic-police/warning/group/gather")
    public WarningScrollListVO getTrafficGatherList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getGatherWarningList(WarningTypeEnum.JJ_SMRYJJ.getCode(), status);
    }

    /**
     * [专题首页-禁毒-预警]查询涉毒人员聚集预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/130423
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/anti-drug/warning/group/gather")
    public WarningScrollListVO getDrugGatherList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getGatherWarningList(WarningTypeEnum.JD_RYJJ.getCode(), status);
    }

    /**
     * [专题首页-反恐-预警]查询关注人员风险警情预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/131029
     *
     * @param status 处置状态
     * @return 预警列表 {@link FocusPersonWarningVO}
     */
    @GetMapping("/counter-terrorism/warning/focus-person")
    public WarningScrollListVO getTerrorismFocusPeopleWarningList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getFocusPersonWarningList(WarningTypeEnum.FK_GZRYFXYJ.getCode(), status);
    }

    /**
     * [专题首页-政保-预警]查询关注人员风险警情预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/131389
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/political-guarantee/warning/focus-person")
    public WarningScrollListVO getPoliticalFocusPeopleWarningList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getFocusPersonWarningList(WarningTypeEnum.ZB_GZRYFXYJ.getCode(), status);
    }

    /**
     * [专题首页-交警-预警]查询关注人员风险警情预警列表
     * http://192.168.200.192:3001/project/4897/interface/api/131383
     *
     * @param status 处置状态
     * @return 预警列表 {@link WarningScrollListVO}
     */
    @GetMapping("/traffic-police/warning/focus-person")
    public WarningScrollListVO getTrafficFocusPeopleWarningList(@NotEmpty(message = "处置状态不能为空") String status) {
        return dataViewService.getFocusPersonWarningList(WarningTypeEnum.JJ_GZRYFXYJ.getCode(), status);
    }

    /**
     * [专题首页-政保]  人员预警
     * http://192.168.200.192:3001/project/4897/interface/api/131365
     *
     * @param status 预警状态
     * @return {@link WarningScrollListVO}
     */
    @GetMapping("/political-guarantee/warning/person")
    public WarningScrollListVO getWarningPersonControlVoList(@NotBlank(message = "预警状态缺失") String status) {
        return dataViewService.getWarningPersonInfoList(WarningTypeEnum.ZB_RYYJ.getCode(), status);
    }

    /**
     * [专题首页-刑侦] 人员预警
     * http://192.168.200.192:3001/project/4897/interface/api/131359
     *
     * @param status 预警状态
     * @return {@link WarningScrollListVO}
     */
    @GetMapping("/criminal-investigation/warning/person")
    public WarningScrollListVO getCriminalInvestigationVOList(@NotBlank(message = "预警状态缺失") String status) {
        return dataViewService.getCriminalInvestigationVOList(status);
    }

    /**
     * [专题首页-刑侦] 入泸预警
     * http://192.168.200.192:3001/project/4897/interface/api/131395
     *
     * @param status 预警状态
     * @return {@link WarningPersonCommonVO}
     */
    @GetMapping("/criminal-investigation/warning/in/area")
    public WarningScrollListVO getPersonInArea(@NotBlank(message = "预警状态缺失") String status) {
        return dataViewService.getWarningPersonInOrOutArea(WarningTypeEnum.XZ_ZDRYRJ.getCode(), status);
    }

    /**
     * [专题首页-刑侦] 出泸预警
     * http://192.168.200.192:3001/project/4897/interface/api/131377
     *
     * @param status 预警状态
     * @return {@link WarningScrollListVO}
     */
    @GetMapping("/criminal-investigation/warning/out/area")
    public WarningScrollListVO getPersonOutArea(@NotBlank(message = "预警状态缺失") String status) {
        return dataViewService.getWarningPersonInOrOutArea(WarningTypeEnum.XZ_ZDRYCJ.getCode(), status);
    }
}
