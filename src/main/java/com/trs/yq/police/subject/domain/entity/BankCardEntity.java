package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 银行卡信息实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_BANK_CARD")
public class BankCardEntity extends BaseEntity {

    private static final long serialVersionUID = -7738514387000611092L;

    /**
     * 人员ID
     */
    private String personId;

    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 开户行
     */
    private String bankOfDeposit;

    /**
     * 使用状态
     */
    private String useType;

    /**
     * 是否为自动更新导入
     */
    private String isAutomated;
}
