package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Resource not found exception
 *
 * <AUTHOR>
 */
@ResponseStatus(value = HttpStatus.NOT_FOUND)
public class ResourceNotFoundException extends SystemException {
    
    private static final long serialVersionUID = -8050421455568254702L;

    /**
     *
     */
    public ResourceNotFoundException() {
    }

    /**
     * @param message 异常消息
     */
    public ResourceNotFoundException(String message) {
        super(message);
    }

    /**
     * @param message 异常消息
     * @param cause   引发异常的异常
     */
    public ResourceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * @param cause 引发异常的异常
     */
    public ResourceNotFoundException(Throwable cause) {
        super(cause);
    }

}
