package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.PersonGroupRelationEntity;
import com.trs.yq.police.subject.repository.PersonGroupRelationRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员关联的群体列表查询VO
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class PersonRelatedGroupVO implements Serializable {

    private static final long serialVersionUID = -1860923329688015457L;

    /**
     * 群体id
     */
    private String groupId;
    /**
     * 群体名称
     */
    private String groupName;
    /**
     * 活跃度
     */
    private String activityLevel;
    /**
     * 关联id
     */
    private String relationId;

    /**
     * 构建vo
     *
     * @param personId 人员id
     * @param group    群体
     * @return {@link PersonRelatedGroupVO}
     */
    public static PersonRelatedGroupVO of(String personId, GroupEntity group) {
        PersonRelatedGroupVO vo = new PersonRelatedGroupVO();
        vo.setGroupId(group.getId());
        vo.setGroupName(group.getName());
        PersonGroupRelationRepository personGroupRelationRepository = BeanUtil.getBean(PersonGroupRelationRepository.class);
        PersonGroupRelationEntity relation = personGroupRelationRepository.findByGroupIdAndPersonId(group.getId(), personId);
        vo.setRelationId(relation.getId());
        vo.setActivityLevel(relation.getActivityLevel());
        return vo;
    }
}
