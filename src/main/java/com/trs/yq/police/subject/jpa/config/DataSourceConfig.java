package com.trs.yq.police.subject.jpa.config;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionManager;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.sql.DataSource;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 18:55
 */
@Configuration
@Slf4j
public class DataSourceConfig implements TransactionManagementConfigurer {

    @Qualifier("oracleTransactionManager")
    @Autowired
    PlatformTransactionManager transactionManager;

    /**
     * oracle数据源(主数据源)
     *
     * @return {@link DataSource}
     */
    @Bean(name = "oracleDatasource")
    @Qualifier("oracleDatasource")
    @ConfigurationProperties(prefix = "spring.datasource.oracle")
    @Order(0)
    public DataSource userDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * mysql数据源
     *
     * @return {@link DataSource}
     */
    @Bean(name = "mysqlDatasource")
    @Qualifier("mysqlDatasource")
    @ConfigurationProperties(prefix = "spring.datasource.mysql")
    @Order(1)
    public DataSource systemDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * postgres数据源
     *
     * @return {@link DataSource}
     */
    @Bean(name = "mppDatasource")
    @Qualifier("mppDatasource")
    @ConfigurationProperties(prefix = "spring.datasource.mpp")
    @Order(2)
    public DataSource mppDataSource() {
        return DataSourceBuilder.create().build();
    }

    /**
     * 集成了mybatis-plus的多数据源切换，为了方便数据源的注入，将其置为主数据源，在nacos中通过配置将oracle设置为动态的主数据源<BR>
     * 为了兼容之前的配置，默认将原有数据源加载到其中，后续也可以通过符合mybaits-plus的配置方式修改数据源配置
     *
     * @param oracle     数据源
     * @param mysql      数据源
     * @param mpp        数据源
     * @param properties 动态数据源配置
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/24 12:33
     */
    @Bean
    @Primary
    @Order(3)
    public DataSource dataSource(
            @Qualifier("oracleDatasource") DataSource oracle,
            @Qualifier("mysqlDatasource") DataSource mysql,
            @Qualifier("mppDatasource") DataSource mpp,
            DynamicDataSourceProperties properties
    ) {
        log.info("开始加载mybatis-plus的多数据源");
        DynamicRoutingDataSource dataSource = new DynamicRoutingDataSource();
        dataSource.setPrimary(properties.getPrimary());
        dataSource.setStrict(properties.getStrict());
        dataSource.setStrategy(properties.getStrategy());
        dataSource.setP6spy(properties.getP6spy());
        dataSource.setSeata(properties.getSeata());
        // 塞入主数据源
        dataSource.addDataSource("master", oracle);
        dataSource.addDataSource("oracle", oracle);
        dataSource.addDataSource("mysql", mysql);
        dataSource.addDataSource("mpp", mpp);
        log.info("完成加载mybatis-plus的多数据源[{}]", dataSource.getDataSources().keySet());
        return dataSource;
    }

    @NotNull
    @Override
    public TransactionManager annotationDrivenTransactionManager() {
        return transactionManager;
    }
}
