package com.trs.yq.police.subject.operation;

import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作日志注解
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 15:16
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface OperationLog {

    /**
     * 操作符
     *
     * @return 操作符
     */
    Operator operator();

    /**
     * 操作模块
     *
     * @return 模块
     */
    OperateModule module();

    /**
     * 描述
     *
     * @return 描述信息
     */
    String desc();

    /**
     * 获取主键名
     *
     * @return 主键名
     */
    String primaryKey();

    /**
     * 获取返回值
     *
     * @return 是否需要返回值
     */
    boolean needReturnVal() default false;

    /**
     * 详情键值
     *
     * @return 详情键值
     */
    String detailKey() default "";

    /**
     * 目标对象类型
     *
     * @return 0=person, 1=group, 2=clue, 3=warning, 4=event
     */
    TargetObjectTypeEnum targetObjectType() default TargetObjectTypeEnum.PERSON;
}
