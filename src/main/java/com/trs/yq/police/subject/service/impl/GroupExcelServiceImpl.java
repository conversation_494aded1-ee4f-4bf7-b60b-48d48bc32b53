package com.trs.yq.police.subject.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.builder.GroupListPredicatesBuilder;
import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.SubjectEntity;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.GroupExportListVO;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.repository.CommonExtendRepository;
import com.trs.yq.police.subject.repository.GroupRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.SubjectRepository;
import com.trs.yq.police.subject.service.GroupExcelService;
import com.trs.yq.police.subject.utils.JsonUtil;
import javax.persistence.criteria.Predicate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;

/**
 * 群体批量导出服务实现类
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Service
public class GroupExcelServiceImpl implements GroupExcelService {

    @Resource
    private SubjectRepository subjectRepository;

    @Resource
    private GroupRepository groupRepository;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private CommonExtendRepository commonExtendRepository;

    /**
     * 根据传来的属性导出excel
     *
     * @param response   响应体
     * @param exportParams 导出条件
     * @param subjectId  主题id
     * @throws IOException IO异常
     */
    @Override
    public void downLoadExcel(HttpServletResponse response, ExportParams exportParams, String subjectId) throws IOException {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElseThrow(IOException::new);
        String fileName = String.format("%s-群体档案-%s.xlsx", subjectEntity.getName(), LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<GroupEntity> groupList =exportParams.getIsAll()
            ? groupRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = GroupListPredicatesBuilder.buildListFilterPredicates(subjectId, exportParams.getListParams().getFilterParams(), root, criteriaBuilder).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(exportParams.getListParams().getSearchParams())) {
                predicates.addAll(GroupListPredicatesBuilder.buildSearchPredicates(exportParams.getListParams().getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        })
           :groupRepository.findAllByIdIn(exportParams.getIds());
        List<GroupExportListVO> vos = new ArrayList<>();
        groupList.forEach(group -> {
            GroupExportListVO vo = new GroupExportListVO();
            vo.setGroupName(group.getName());
            List<LabelEntity> types = labelRepository.findByGroupIdAndSubjectId(group.getId(), subjectId);
            vo.setGroupType(types.stream().map(LabelEntity::getName).collect(Collectors.joining(",")));
            vo.setBasicInfo(group.getBasicInfo());
            vo.setCreateDeptName(group.getCrDept());
            vo.setCreateTime(group.getCrTime().format(DATE_TIME_FORMATTER));
            vo.setUpdateTime(group.getUpTime().format(DATE_TIME_FORMATTER));
            CommonExtentEntity extend = commonExtendRepository.findByRecordIdAndModule(group.getId(), "group").orElse(null);
            if (Objects.nonNull(extend)) {
                vo.setDemand(extend.getMainDemand());
            }
            vos.add(vo);
        });
        EasyExcelFactory.write(response.getOutputStream(), GroupExportListVO.class)
                .registerWriteHandler(new CustomCellWriteHandler())
                .includeColumnFiledNames(exportParams.getFieldNames())
                .sheet()
                .doWrite(vos);
    }

    /**
     * 获得不同专题下批量导出需要的属性
     *
     * @param subjectId 专题Id
     * @return 属性json
     */
    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getGroupListProperty());
    }
}
