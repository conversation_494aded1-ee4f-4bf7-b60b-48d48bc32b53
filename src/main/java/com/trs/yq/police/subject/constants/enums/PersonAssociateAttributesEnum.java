package com.trs.yq.police.subject.constants.enums;

/**
 * 表示人员关系的属性枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 */
public enum PersonAssociateAttributesEnum {

    /**
     * 身份证号
     */
    ID_NUMBER,

    /**
     * 车牌号
     */
    CAR_NUMBER,

    /**
     * 手机号
     */
    PHONE_NUMBER,

    /**
     * 手机IMEI
     */
    IMEI,

    /**
     * 手机IMSI
     */
    IMSI,

    /**
     * 手机MAC
     */
    MAC,

    /**
     * 银行卡号
     */
    BANK_CARD,

    /**
     * QQ
     */
    QQ,

    /**
     * 微信
     */
    WECHAT,

    /**
     * 姓名
     */
    NAME,

    /**
     * 性别
     */
    GENDER,

    /**
     * 国籍
     */
    NATION,

    /**
     * 绰号
     */
    NICK_NAME,

    /**
     * 曾用名
     */
    FORMER_NAME,

    /**
     * 文化程度
     */
    EDUCATION,

    /**
     * 婚姻状况
     */
    MARITAL_STATUS,

    /**
     * 政治面貌
     */
    POLITICS_STATUS,

    /**
     * 宗教信仰
     */
    RELIGIOUS_BELIEF,

    /**
     * 现职业
     */
    CURRENT_JOB,

    /**
     * 户籍地
     */
    REGISTERED_RESIDENCE,

    /**
     * 当前住址
     */
    CURRENT_RESIDENCE,

    /**
     * 他人
     */
    OTHERS
}
