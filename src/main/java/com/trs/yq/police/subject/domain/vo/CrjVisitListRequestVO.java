package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 出入境走访记录请求VO
 *
 * <AUTHOR>
 * @date 2021/9/17 14:36
 */
@Data
public class CrjVisitListRequestVO implements Serializable {
    private static final long serialVersionUID = 3109494252653093113L;
    /**
     * 分页参数
     */
    private PageParams pageParams;
    /**
     * 时间参数
     */
    private TimeParams timeParams;
    /**
     * 其他参数
     */
    private CrjVisitListRequestVO.OtherParams otherParams;

    /**
     * 其他筛选条件
     */
    @Data
    public static class OtherParams implements Serializable {
        private static final long serialVersionUID = 2851874793678037279L;
        private String searchValue;
        private String visitType;
        private String deptName;
    }
}
