// CHECKSTYLE:OFF
package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.ImmutableMap;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.protobuf.ServiceException;
import com.trs.dubbo.provide.VirtualService;
import com.trs.dubbo.provide.dto.RequestUser;
import com.trs.dubbo.provide.dto.VirtualInfoDTO;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.PersonAssociateAttributesEnum;
import com.trs.yq.police.subject.constants.enums.PersonRelationEnum;
import com.trs.yq.police.subject.controller.TestController;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.dto.PersonAssociateRelationDTO;
import com.trs.yq.police.subject.domain.entity.UserEntity;
import com.trs.yq.police.subject.exception.SystemException;
import com.trs.yq.police.subject.repository.UserRepository;
import com.trs.yq.police.subject.service.FiberHomePersonLibraryService;
import com.trs.yq.police.subject.service.PersonAssociateRelationService;
import com.trs.yq.police.subject.utils.StringUtil;
import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.Relationship;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.InvalidValueException;
import com.vesoft.nebula.client.graph.net.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 查询关联&关系服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 */
@Slf4j
@Service
public class PersonAssociateRelationServiceImpl implements PersonAssociateRelationService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private FiberHomePersonLibraryService fiberHomePersonLibraryService;

    @DubboReference(check = false, version = "1.0.0")
    private VirtualService virtualService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ObjectFactory<Session> nebulaSessionFactory;

    @Override
    public PersonAssociateRelationDTO queryAssociateAndRelation(String idNumber) {
        PersonAssociateRelationDTO dto = PersonAssociateRelationDTO.newInstance();

        // 烽火人员主题库
        PersonAssociateRelationDTO personLibraryResultDTO =
                fiberHomePersonLibraryService.queryPersonLibrary(
                        PersonAssociateAttributesEnum.ID_NUMBER, idNumber);
        log.info("查询烽火人员主题库结果：{}", personLibraryResultDTO);
        dto.join(personLibraryResultDTO);

        // 烽火要素关系库
        PersonAssociateRelationDTO relationLibraryResultDTO =
                fiberHomePersonLibraryService.queryRelationLibrary(
                        PersonAssociateAttributesEnum.ID_NUMBER, idNumber);
        log.info("查询烽火要素关系库结果：{}", relationLibraryResultDTO);
        dto.join(relationLibraryResultDTO);

        // Dubbo虚拟身份接口,根据烽火接口查询出来的手机号、IMEI、IMSI、MAC再次关联查询
        try {
            PersonAssociateRelationDTO virtualResultDTO = this.queryDubboVirtualSource(dto);
            log.info("查询Dubbo虚拟身份接口结果：{}", virtualResultDTO);
            dto.join(virtualResultDTO);
        } catch (Exception e) {
            log.error("Dubbo查询异常");
            return dto;
        }

        log.info("ready to insert nebula graph");
        try {
            insertNebulaGraph(dto);
        } catch (Exception e) {
            log.error("nebula insert error:", e);
        }
        return dto;
    }

    /**
     * 保存nebula graph
     *
     * @param jointDTO 合并后的结果DTO
     */
    private void insertNebulaGraph(PersonAssociateRelationDTO jointDTO)
            throws UnsupportedEncodingException {
        Optional<PersonAssociateRelationDTO.RelationPair> idNumberFirstPair =
                jointDTO.get(PersonAssociateAttributesEnum.ID_NUMBER).stream().findFirst();
        if (!idNumberFirstPair.isPresent()) {
            return;
        }

        List<String> vertexList = new ArrayList<>();
        List<String> edgeList = new ArrayList<>();
        final String vertexInsertTemplate = "INSERT VERTEX %s(%s) VALUES '%s':(%s)";
        final String edgeInsertTemplate = "INSERT EDGE %s(%s) VALUES '%s'->'%s':(%s)";
        final String queryEdgeRelationTypeNGQL = "FETCH PROP ON Relation '%s'->'%s' YIELD Relation.relation_type";
        final String tagPerson = "Person";
        final String tagPhone = "Phone";
        final String tagImei = "Imei";
        final String tagImsi = "Imsi";
        final String tagMac = "Mac";
        final String tagBankCard = "BankCard";
        final String tagCar = "Car";
        final String tagQQ = "QQ";
        final String tagWechat = "Wechat";
        final String edgeTypeOwn = "Own";
        final String edgeTypeRelation = "Relation";
        final String emptyString = "";
        final String space = "trs";
        final String[] props = new String[]{"nick_name", "name", "gender", "nation", "former_name", "education", "marital_status", "politics_status", "religious_belief", "current_job", "registered_residence", "current_residence"};
        List<String> preparedPropNameList = new ArrayList<>();
        List<String> preparedPropValueList = new ArrayList<>();
        String personIdNumber = idNumberFirstPair.get().getValue();

        ResultSet existPerson = this.executeNGQL(space, String.format("FETCH PROP ON Person '%s'", personIdNumber));
        HashMap<String, ValueWrapper> propertiesMap = null;
        if (Objects.nonNull(existPerson) && existPerson.isSucceeded()) {
            if (existPerson.rowsSize() > 0) {
                Node node = existPerson.rowValues(0).get(0).asNode();
                propertiesMap = node.properties("Person");
            }
        }
        for (String prop : props) {
            Set<PersonAssociateRelationDTO.RelationPair> relationPairSet = jointDTO.get(
                    PersonAssociateAttributesEnum.valueOf(prop.toUpperCase(Locale.ROOT)));
            if (Objects.nonNull(propertiesMap)
                    && (prop.equals("nick_name") || prop.equals("former_name"))) {
                Set<String> tempJsonSet = new HashSet<>();
                preparedPropNameList.add(prop);
                relationPairSet.stream()
                        .filter(
                                pair ->
                                        StringUtils.isNotBlank(pair.getValue())
                                                && !pair.getValue().equals("无曾用名"))
                        .forEach(pair -> tempJsonSet.add(pair.getValue()));
                String propString =
                        propertiesMap.get(prop).isString()
                                ? propertiesMap.get(prop).asString()
                                : null;
                if (StringUtils.isNotBlank(propString)) {
                    if (propString.startsWith("[") && propString.endsWith("]")) {
                        JsonArray propArray = new Gson().fromJson(propString, JsonArray.class);
                        for (JsonElement jsonElement : propArray) {
                            tempJsonSet.add(jsonElement.getAsString());
                        }
                    } else {
                        tempJsonSet.add(propString);
                    }
                }
                preparedPropValueList.add(new Gson().toJson(tempJsonSet));
            } else {
                relationPairSet.stream()
                        .filter(pair -> StringUtils.isNotBlank(pair.getValue()))
                        .findFirst()
                        .ifPresent(
                                pair -> {
                                    preparedPropNameList.add(prop);
                                    preparedPropValueList.add(pair.getValue());
                                });
            }
        }

        vertexList.add(
                String.format(
                        vertexInsertTemplate,
                        tagPerson,
                        String.join(",", preparedPropNameList),
                        personIdNumber,
                        preparedPropValueList.stream()
                                .map(value -> "'" + value + "'")
                                .reduce((v1, v2) -> v1 + ", " + v2)
                                .orElse("")));
        for (Map.Entry<PersonAssociateAttributesEnum, Set<PersonAssociateRelationDTO.RelationPair>>
                entry : jointDTO.entrySet()) {
            PersonAssociateAttributesEnum keyEnum = entry.getKey();
            Set<PersonAssociateRelationDTO.RelationPair> pairSet = entry.getValue();
            switch (keyEnum) {
                case PHONE_NUMBER:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagPhone,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case IMEI:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagImei,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case IMSI:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagImsi,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case MAC:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagMac,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case BANK_CARD:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagBankCard,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case CAR_NUMBER:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagCar,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case QQ:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagQQ,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case WECHAT:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagWechat,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            edgeList.add(
                                                    String.format(
                                                            edgeInsertTemplate,
                                                            edgeTypeOwn,
                                                            emptyString,
                                                            personIdNumber,
                                                            pair.getValue(),
                                                            emptyString)));
                    break;
                case OTHERS:
                    pairSet.stream()
                            .filter(this::filterBlankPair)
                            .forEach(
                                    pair ->
                                            vertexList.add(
                                                    String.format(
                                                            vertexInsertTemplate,
                                                            tagPerson,
                                                            emptyString,
                                                            pair.getValue(),
                                                            emptyString)));
                    for (PersonAssociateRelationDTO.RelationPair pair : pairSet) {
                        HashSet<String> relationTypeJsonSet = null;
                        ResultSet resultSet = this.executeNGQL(space, String.format(queryEdgeRelationTypeNGQL, personIdNumber, pair.getValue()));
                        if (Objects.nonNull(resultSet) && resultSet.isSucceeded()) {
                            if (resultSet.rowsSize() > 0) {
                                String relationTypeString = resultSet.rowValues(0).get(3).asString();
                                log.info("nGQL result:{}", relationTypeString);
                                if (StringUtils.isNotBlank(relationTypeString)) {
                                    relationTypeJsonSet = new Gson().fromJson(relationTypeString, HashSet.class);
                                    relationTypeJsonSet.add(pair.getRelation().getDesc());
                                }
                            } else {
                                relationTypeJsonSet = new HashSet<>();
                                relationTypeJsonSet.add(pair.getRelation().getDesc());
                            }
                        } else if (Objects.nonNull(resultSet)) {
                            log.error(
                                    "nebula query relation_type failed.{}:{}",
                                    resultSet.getErrorCode(),
                                    resultSet.getErrorMessage());
                        }
                        if (Objects.nonNull(relationTypeJsonSet)) {
                            String edge = String.format(edgeInsertTemplate, edgeTypeRelation, "relation_type", personIdNumber, pair.getValue(), "'" + new Gson().toJson(relationTypeJsonSet) + "'");
                            edgeList.add(edge);
                        }
                    }
                    break;
                default:
            }
        }
        String allInsertNGQL = String.join(";", vertexList) + ";" + String.join(";", edgeList);
        log.info("ready nGQL:{}", allInsertNGQL);
        ResultSet executeResult = this.executeNGQL(space, allInsertNGQL);
        log.info("nGQL execute {}.", executeResult.isSucceeded() ? "succeed" : "failed");
        if (!executeResult.isSucceeded()) {
            log.info("nGQL execute failed reason:{}, {}", executeResult.getErrorCode(), executeResult.getErrorMessage());
        }
    }

    /**
     * 合并两个list
     *
     * @param l1 第一个list
     * @param l2 第二个list
     * @return 合并后的list
     */
    private static List<VirtualInfoDTO> apply(List<VirtualInfoDTO> l1, List<VirtualInfoDTO> l2) {
        l1.addAll(l2);
        return l1;
    }

    /**
     * 过滤空RelationPair
     *
     * @param pair RelationPair实例
     * @return true：不为空 false：为空
     */
    private boolean filterBlankPair(PersonAssociateRelationDTO.RelationPair pair) {
        return StringUtils.isNotBlank(pair.getValue());
    }

    /**
     * 查询dubbo虚拟身份接口
     *
     * @param dto 已经查询出来的手机号、IMEI、IMSI、MAC等信息
     * @return 人员属性关联关系列表
     */
    private PersonAssociateRelationDTO queryDubboVirtualSource(PersonAssociateRelationDTO dto)
            throws ServiceException, UnknownHostException {
        PersonAssociateRelationDTO resultDTO = PersonAssociateRelationDTO.newInstance();
        RequestUser requestUser = buildRequestUser();
        for (Map.Entry<PersonAssociateAttributesEnum, Set<PersonAssociateRelationDTO.RelationPair>>
                entry : dto.entrySet()) {
            switch (entry.getKey()) {
                case PHONE_NUMBER:
                    Set<VirtualInfoDTO> tempSet = new HashSet<>();
                    tempSet.addAll(entry.getValue().stream().map(pair -> virtualService.getImsiByPhone(pair.getValue(), requestUser))
                            .reduce(PersonAssociateRelationServiceImpl::apply)
                            .orElse(Collections.emptyList()));
                    tempSet.addAll(
                            entry.getValue().stream()
                                    .map(pair -> virtualService.getImeiByPhone(pair.getValue(), requestUser))
                                    .reduce(PersonAssociateRelationServiceImpl::apply)
                                    .orElse(Collections.emptyList()));
                    tempSet.addAll(
                            entry.getValue().stream()
                                    .map(pair -> virtualService.getMacByPhone(pair.getValue(), requestUser))
                                    .reduce(PersonAssociateRelationServiceImpl::apply)
                                    .orElse(Collections.emptyList()));
                    for (VirtualInfoDTO infoDTO : tempSet) {
                        resultDTO.setRelation(PersonAssociateAttributesEnum.IMSI, PersonRelationEnum.OWNER, infoDTO.getImsi());
                        resultDTO.setRelation(PersonAssociateAttributesEnum.IMEI, PersonRelationEnum.OWNER, infoDTO.getImei());
                        resultDTO.setRelation(PersonAssociateAttributesEnum.MAC, PersonRelationEnum.OWNER, infoDTO.getMac());
                    }
                    break;
                case IMEI:
                    for (VirtualInfoDTO infoDTO :
                            entry.getValue().stream()
                                    .map(pair -> virtualService.getPhoneByImei(pair.getValue(), requestUser))
                                    .reduce(PersonAssociateRelationServiceImpl::apply)
                                    .orElse(Collections.emptyList())) {
                        resultDTO.setRelation(
                                PersonAssociateAttributesEnum.PHONE_NUMBER,
                                PersonRelationEnum.OWNER,
                                infoDTO.getPhone());
                    }
                    break;
                case IMSI:
                    for (VirtualInfoDTO infoDTO :
                            entry.getValue().stream()
                                    .map(pair -> virtualService.getPhoneByImsi(pair.getValue(), requestUser))
                                    .reduce(PersonAssociateRelationServiceImpl::apply)
                                    .orElse(Collections.emptyList())) {
                        resultDTO.setRelation(
                                PersonAssociateAttributesEnum.PHONE_NUMBER,
                                PersonRelationEnum.OWNER,
                                infoDTO.getPhone());
                    }
                    break;
                case MAC:
                    for (VirtualInfoDTO infoDTO :
                            entry.getValue().stream()
                                    .map(pair -> virtualService.getPhoneByMac(pair.getValue(), requestUser))
                                    .reduce(PersonAssociateRelationServiceImpl::apply)
                                    .orElse(Collections.emptyList())) {
                        resultDTO.setRelation(
                                PersonAssociateAttributesEnum.PHONE_NUMBER,
                                PersonRelationEnum.OWNER,
                                infoDTO.getPhone());
                    }
                    break;
                default:
            }
        }
        return resultDTO;
    }

    /**
     * 根据UserEntity构造RequestUser类
     *
     * @return RequestUser实例
     */
    private RequestUser buildRequestUser() throws ServiceException, UnknownHostException {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser) || StringUtils.isBlank(currentUser.getId())) {
            throw new ServiceException("用户信息获取失败");
        }
        Optional<UserEntity> userEntity = userRepository.findById(currentUser.getId());
        if (!userEntity.isPresent()) {
            throw new ServiceException("用户信息获取失败");
        }
        RequestUser requestUser = new RequestUser();
        requestUser.setUserNm(userEntity.get().getRealName());
        requestUser.setIdcardNo(userEntity.get().getIdCard());
        requestUser.setPoliceNo(userEntity.get().getPoliceCode());
        requestUser.setOrgCode(userEntity.get().getUnitCode());
        requestUser.setIp(InetAddress.getLocalHost().getHostAddress());
        return requestUser;
    }

    /**
     * 建立nebula链接并查询nGQL结果
     *
     * @param space nebula空间
     * @param nGQL  要执行的nGQL
     * @return nGQL查询出的结果集
     */
    private ResultSet executeNGQL(String space, String nGQL) {
        Session session = null;

        try {
            session = nebulaSessionFactory.getObject();
            if (session.ping() && session.execute("USE " + space).isSucceeded()) {
                return session.execute(nGQL);
            } else {
                throw new SystemException("nebula 连接错误");
            }
        } catch (Exception e) {
            log.error("nebula query failed.", e);
            throw new SystemException("ngql执行错误", e);
        } finally {
            if (Objects.nonNull(session)) {
                session.release();
            }
        }
    }

    @Override
    public void importPersonFromAds(List<CSVRecord> csvRecords) {

        // 存储csv解析出来的人员信息信息
        List<Map<String, Object>> csvRecordMapList = new ArrayList<>();
        // 用于保存从数据中提取出来的地址信息
        Set<String> addressSet = new HashSet<>();
        // 用户保存人员与地址的关系信息
        Map<String, Set<String>> personAddressMap = new HashMap<>(csvRecords.size());

        for (CSVRecord csvRecord : csvRecords) {
            if (StringUtil.checkIdNumber(csvRecord.get("idNumber"))) {
                // 解析保存人员信息
                Set<String> name = Arrays.stream(StringUtils.split(StringUtils.trimToEmpty(csvRecord.get("names")), ","))
                        .map(s -> StringUtils.remove(s, "\\"))
                        .map(s -> StringUtils.remove(s, "\""))
                        .map(s -> StringUtils.remove(s, "'"))
                        .collect(Collectors.toSet());
                Set<String> educations = Arrays.stream(StringUtils.split(StringUtils.trimToEmpty(csvRecord.get("educations")), ","))
                        .map(s -> StringUtils.remove(s, "\\"))
                        .map(s -> StringUtils.remove(s, "\""))
                        .map(s -> StringUtils.remove(s, "'"))
                        .collect(Collectors.toSet());
                csvRecordMapList.add(ImmutableMap.of("idNumber", csvRecord.get("idNumber"), "name", name, "education", educations));

                Set<String> address = Arrays.stream(StringUtils.split(StringUtils.trimToEmpty(csvRecord.get("address")), ","))
                        .collect(Collectors.toSet());

                if (CollectionUtils.isNotEmpty(address)) {
                    // 保存地址信息集合
                    addressSet.addAll(address);

                    // 解析保存人员地址关系
                    personAddressMap.put(csvRecord.get("idNumber"), addressSet);
                }
            }
        }
        // 将人员信息分批保存/更新
        this.executeSmallBatch(csvRecordMapList);
        // 分批保存地址信息
        this.executeSaveAddress(new ArrayList<>(addressSet));
        // 分批保存人员地址关联关系
        this.executeSavePerson2Address(personAddressMap);
    }

    private void executeSmallBatch(List<Map<String, Object>> smallList) {
        try {
            Set<String> ids = smallList.stream()
                    .map(r -> (String) r.get("idNumber"))
                    .collect(Collectors.toSet());

            String matchQl = String.format("match (p:Person) where id(p) in %s return p;", objectMapper.writeValueAsString(ids));

            ResultSet rs = this.executeNGQL("trs", matchQl);

            Set<String> foundIdSet = new HashSet<>();
            // 逐一检查查询结果并与待插入数据做合并 同时记录哪些id是在数据库中已经存在的
            for (int i = 0; i < Objects.requireNonNull(rs).rowsSize(); i++) {
                ResultSet.Record foundRecord = rs.rowValues(i);
                for (ValueWrapper value : foundRecord.values()) {
                    if (value.isVertex()) {
                        String idNumber = value.asNode().getId().asString();
                        foundIdSet.add(idNumber);
                        String nameValue = null;
                        try {
                            nameValue = value.asNode().properties("Person").get("name").asString();
                        } catch (Exception ignored) {
                        }

                        Set<String> nameSet = Arrays.stream(StringUtils.trimToEmpty(nameValue).split(","))
                                .map(s -> StringUtils.remove(s, "\\"))
                                .map(s -> StringUtils.remove(s, "\""))
                                .map(s -> StringUtils.remove(s, "'"))
                                .map(s -> StringUtils.remove(s, "["))
                                .map(s -> StringUtils.remove(s, "]"))
                                .collect(Collectors.toSet());

                        String educationValue = null;
                        try {
                            educationValue =
                                    value.asNode().properties("Person").get("education").asString();
                        } catch (Exception ignored) {

                        }

                        Set<String> eduSet = Arrays.stream(StringUtils.trimToEmpty(educationValue).split(","))
                                .map(s -> StringUtils.remove(s, "\\"))
                                .map(s -> StringUtils.remove(s, "\""))
                                .map(s -> StringUtils.remove(s, "'"))
                                .map(s -> StringUtils.remove(s, "["))
                                .map(s -> StringUtils.remove(s, "]"))
                                .collect(Collectors.toSet());

                        smallList.stream().filter(m -> idNumber.equals(m.get("idNumber")))
                                .findFirst()
                                .ifPresent(m -> {
                                    ((Set<String>) m.get("name")).addAll(nameSet);
                                    ((Set<String>) m.get("education")).addAll(eduSet);
                                });
                    }
                }
            }

            // 根据最新的合并数据插入数据
            String insertSqls = smallList.stream()
                    .map(r -> {
                        try {
                            if (!foundIdSet.contains(r.get("idNumber").toString())) {
                                // 在数据库中不存在的使用insert into 语句
                                return String.format("INSERT VERTEX Person (name,education) VALUES '%s':('%s','%s')", r.get("idNumber"),
                                        objectMapper.writeValueAsString(r.get("name")),
                                        objectMapper.writeValueAsString(r.get("education")));
                            } else {
                                // 在数据库中已经存在的使用update 语句避免其他字段被修改
                                return String.format("update VERTEX on Person '%s' set name = '%s',education = '%s'", r.get("idNumber"),
                                        objectMapper.writeValueAsString(r.get("name")),
                                        objectMapper.writeValueAsString(r.get("education")));
                            }
                        } catch (IOException e) {
                            log.error("json转换错误", e);
                            throw new SystemException(e);
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining(";"));

            this.executeNGQL("trs", insertSqls);
        } catch (UnsupportedEncodingException e) {
            log.error("nebula 编码异常", e);
            throw new SystemException(e);
        } catch (JsonProcessingException e) {
            log.error("json转换错误", e);
            throw new SystemException(e);
        }
    }

    private void executeSaveAddress(List<String> addressSet) {
        String insertSql = addressSet.stream()
                .map(p -> String.format("INSERT VERTEX Place (address) VALUES '%s':('%s')", p, DigestUtils.md5Hex(p)))
                .collect(Collectors.joining(";"));
        this.executeNGQL("trs", insertSql);
    }

    private void executeSavePerson2Address(Map<String, Set<String>> personAddressMap) {
        String insertSql = personAddressMap.entrySet().stream()
                .map(entry -> entry.getValue().stream()
                        .map(v -> String.format("INSERT EDGE Catch() VALUES '%s'->'%s':()",
                                entry.getKey(),
                                DigestUtils.md5Hex(v)))
                        .collect(Collectors.joining(";")))
                .collect(Collectors.joining(";"));
        this.executeNGQL("trs", insertSql);
    }

    @Override
    public void importPhoneFromAds(List<CSVRecord> csvRecords) {
        // 批量插入节点ngql例 :INSERT VERTEX Phone() VALUES "1":(), "2":();
        String phones =
                csvRecords.stream()
                        .filter(r -> !StringUtils.containsAny(r.get("phone"), "\\", "\"", "'"))
                        .filter(r -> NumberUtils.isDigits(r.get("phone")))
                        .map(r -> String.format("'%s':()", r.get("phone")))
                        .collect(Collectors.joining(","));
        this.executeNGQL("trs", "INSERT VERTEX Phone() VALUES " + phones);

        // 批量插入边ngql例: INSERT EDGE Catch(times) VALUES '%s'->'%s':(%s)
        String catchs = csvRecords.stream()
                .filter(r -> !StringUtils.containsAny(r.get("phone"), "\\", "\"", "'"))
                .filter(r -> !StringUtils.containsAny(r.get("idNUmber"), "\\", "\"", "'"))
                .filter(r -> NumberUtils.isDigits(r.get("phone")))
                .map(r -> String.format("'%s'->'%s':(%s)", r.get("idNUmber"), r.get("phone"), r.get("times")))
                .collect(Collectors.joining(","));
        this.executeNGQL("trs", "INSERT EDGE Catch(times) VALUES " + catchs);
    }

    @Override
    public void importRelationFromAds(List<CSVRecord> csvRecords) throws JsonProcessingException {

        csvRecords =
                csvRecords.stream()
                        .filter(
                                r ->
                                        StringUtil.checkIdNumber(r.get("idNumber1"))
                                                && StringUtil.checkIdNumber(r.get("idNumber2")))
                        .collect(Collectors.toList());
        String fetchIds =
                csvRecords.stream()
                        .map(
                                r ->
                                        String.format(
                                                "'%s' -> '%s'",
                                                r.get("idNumber1"), r.get("idNumber2")))
                        .collect(Collectors.joining(","));
        ResultSet rs = this.executeNGQL("trs", "fetch PROP on Relation " + fetchIds);

        HashBasedTable<String, String, Set<String>> relation = HashBasedTable.create();
        csvRecords.forEach(
                r ->
                        relation.put(
                                r.get("idNumber1"),
                                r.get("idNumber1"),
                                Arrays.stream(StringUtils.split(r.get("relation"), ","))
                                        .map(s -> StringUtils.remove(s, "\\"))
                                        .map(s -> StringUtils.remove(s, "\""))
                                        .map(s -> StringUtils.remove(s, "'"))
                                        .collect(Collectors.toSet())));

        for (int i = 0; i < rs.rowsSize(); i++) {
            ResultSet.Record foundRecord = rs.rowValues(i);
            for (ValueWrapper value : foundRecord.values()) {
                if (value.isEdge()) {
                    try {
                        Relationship relationship = value.asRelationship();
                        String srcId = relationship.srcId().asString();
                        String dstId = relationship.dstId().asString();

                        relation.get(srcId, dstId)
                                .addAll(objectMapper.readValue(relationship.properties().get("relation_type").asString(),
                                        objectMapper.getTypeFactory().constructParametricType(HashSet.class, String.class)));
                    } catch (JsonProcessingException e) {
                        log.warn("nebula脏数据不是json数组", e);
                        throw new SystemException(e);
                    } catch (UnsupportedEncodingException e) {
                        log.warn("nebula数据转换错误,编码不符合", e);
                        throw new SystemException(e);
                    }
                }
            }
        }

        // insert edge Relation(relation_type) values '1' -> '2':('test'),'3'->'4':('test')
        StringJoiner joiner = new StringJoiner(",");
        for (CSVRecord r : csvRecords) {
            String format =
                    String.format(
                            "'%s' -> '%s':('%s')",
                            r.get("idNumber1"),
                            r.get("idNumber2"),
                            objectMapper.writeValueAsString(
                                    relation.get(r.get("idNumber1"), r.get("idNumber2"))));
            joiner.add(format);
        }
        String insertIds = joiner.toString();

        this.executeNGQL("trs", "insert edge Relation(relation_type) values " + insertIds);
    }

    @Override
    public TestController.RelationGraphVo graph(final String id, final String type) {
        final TestController.RelationGraphVo result = new TestController.RelationGraphVo();

        ResultSet tagResult =
                this.executeNGQL(
                        "trs",
                        String.format("match (v:%s) where id(v) == '%s' return v", type, id));
        if (tagResult.rowsSize() <= 0) {
            return null;
        }
        tagResult.rowValues(0).values().stream()
                .filter(ValueWrapper::isVertex)
                .findFirst()
                .ifPresent(
                        r -> {
                            try {
                                extractValue(type, r.asNode().properties(type))
                                        .ifPresent(result::setValue);
                            } catch (UnsupportedEncodingException e) {
                                throw new SystemException(e);
                            }
                        });

        // 查询语句示例: match (v:Person)-[e:Relation|:Catch]->(v2) WHERE id(v) == '130627199310060854'
        // RETURN e,v2;
        ResultSet rs = this.executeNGQL("trs", String.format("match (s:%s)-[r:Relation|:Call|:Own|:Catch|:Foothold]->(t) where id(s) == '%s' and id(s) != id(t) return r,t", type, id));

        for (int i = 0; i < rs.rowsSize(); i++) {
            ResultSet.Record rowRecord = rs.rowValues(i);

            rowRecord.values().forEach(column -> {
                try {
                    // 点
                    if (column.isVertex()) {
                        Node node = column.asNode();
                        Optional<String> tagNameOptional = node.tagNames().stream().findFirst();
                        tagNameOptional.ifPresent(tagName -> {
                            try {
                                TestController.RelationGraphVo child = new TestController.RelationGraphVo();
                                child.setId(node.getId().asString());
                                child.setType(tagName);
                                child.setValue(extractValue(tagName, node.properties(tagName)).orElseGet(() -> {
                                    try {
                                        return node.getId().asString();
                                    } catch (UnsupportedEncodingException e) {
                                        throw new SystemException(e);
                                    }
                                }));
                                result.getChildren().add(child);
                            } catch (UnsupportedEncodingException e) {
                                throw new SystemException(e);
                            }
                        });
                    }
                    // 边
                    if (column.isEdge()) {
                        Relationship relationship = column.asRelationship();
                        TestController.RelationLink link = new TestController.RelationLink();
                        link.setSource(relationship.srcId().asString());
                        link.setTarget(relationship.dstId().asString());
                        link.setType(relationship.edgeName());
                        extractValue(
                                relationship.edgeName(),
                                relationship.properties())
                                .ifPresent(link::setValue);
                        result.getLinks().add(link);
                    }
                } catch (UnsupportedEncodingException e) {
                    log.warn("nebula编码错误", e);
                }
            });
        }

        return result;
    }

    /**
     * 提取节点名称
     *
     * @param tagName    节点类型
     * @param properties 属性
     * @return 节点的名称
     * @throws UnsupportedEncodingException 图数据库错误
     */
    private Optional<String> extractValue(String tagName, Map<String, ValueWrapper> properties)
            throws UnsupportedEncodingException {

        switch (tagName) {
            case "Person":
                try {
                    return Optional.ofNullable(properties.get("name").asString());
                } catch (InvalidValueException e) {
                    return Optional.empty();
                }
            case "Place":
                try {
                    return Optional.ofNullable(properties.get("address").asString());
                } catch (InvalidValueException e) {
                    return Optional.empty();
                }
            case "Relation":
                try {
                    return Optional.ofNullable(properties.get("relation_type").asString());
                } catch (InvalidValueException e) {
                    return Optional.empty();
                }
            case "Call":
                return Optional.of("通联");
            case "Own":
                return Optional.of("拥有");
            case "Catch":
                return Optional.of("同捕获");
            case "Foothold":
                return Optional.of("落脚点");
            default:
                return Optional.empty();
        }
    }
}
