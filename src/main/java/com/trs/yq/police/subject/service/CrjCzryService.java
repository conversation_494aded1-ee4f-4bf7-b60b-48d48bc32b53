package com.trs.yq.police.subject.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.request.ListParamsRequest;
import com.trs.yq.police.subject.domain.vo.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 出入境常住人员
 *
 * <AUTHOR>
 */
public interface CrjCzryService {

    /**
     * 常住人员列表查询
     *
     * @param request 参数
     * @return 结果
     */
    PageResult<CrjResidenceListVO> getResidenceList(ListParamsRequest request);

    /**
     * 常住人员详情查询
     *
     * @param id 人员id
     * @return 结果
     */
    CrjResidenceDetailVO getResidenceDetail(String id);

    /**
     * 导入
     *
     * @param importVO 导入
     * @return 导入结果
     */
    ImportResultVO importCzry(ImportVO importVO);

    /**
     * 批量删除
     *
     * @param ids id
     */
    void deleteCzry(List<String> ids);

    /**
     * 导出excel
     *
     * @param response   response
     * @param fieldNames fieldNames
     * @param request    request
     * @param subjectId  subjectId
     */
    void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request, String subjectId)
            throws IOException;

    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    JsonNode getExportPropertyList(String subjectId);
}
