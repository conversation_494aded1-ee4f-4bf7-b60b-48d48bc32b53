package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.ControlStatusEnum;
import com.trs.yq.police.subject.constants.enums.MonitorStatusEnum;
import com.trs.yq.police.subject.domain.entity.ControlEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.MobilityEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.request.CountRequestVO;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DataViewCountService;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;

/**
 * 专题首页数据统计业务层实现
 *
 * <AUTHOR>
 * @since 2021/9/14
 */
@Service
@Slf4j
public class DataViewCountServiceImpl implements DataViewCountService {

    /**
     * 泸州地区编码
     */
    private static final String AREA_CODE = "5105";
    @Resource
    private PersonRepository personRepository;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private DictService dictService;
    @Resource
    private MobilityRepository mobilityRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private CaseEventRepository caseEventRepository;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private ClueRepository clueRepository;
    @Resource
    private GroupRepository groupRepository;

    private static final String DICT_TYPE_DISTRICT = "district";
    private static final long MONTH_NUM = 11L;
    @Value("${com.trs.police.subject.district.code:5105}")
    private String districtCodePrefix;

    @Override
    public List<AreaDistributeVO> getAreaDistribute(CountRequestVO request) {
        long startTime = System.currentTimeMillis();
        //获得所有地区
        List<AreaVO> areaList = getAreaInfo();
        List<AreaDistributeVO> collect = areaList.stream().map(areaVO -> {
            //封装区县分布类
            AreaDistributeVO areaDistributeVO = new AreaDistributeVO();
            areaDistributeVO.setAreaCode(areaVO.getAreaCode());
            areaDistributeVO.setAreaName(areaVO.getAreaName());
            switch (request.getSubjectId()) {
                case JJ_SUBJECT:
                    //获取交警的地区分布
                    areaDistributeVO.setCountPersonType(
                        warningTypeRepository.countByAreaCodeAndSubjectId(request.getSubjectId(),
                                request.getTimeParams().getBeginTime(), request.getTimeParams().getEndTime(),
                                areaVO.getAreaCode()).stream()
                            .map(m -> new CountTypeResponseVO(m.get("personType").toString(),
                                Long.parseLong(m.get("personCount").toString())))
                            .collect(Collectors.toList()));
                    break;
                case XZ_SUBJECT:
                    //获取刑侦的地区分布
                    List<CountTypeResponseVO> types = labelRepository.countByAreaCodeAndSubjectId(
                            request.getSubjectId(), areaVO.getAreaCode()).stream()
                        .map(m -> new CountTypeResponseVO(m.get("personType").toString(),
                            Long.parseLong(m.get("personCount").toString())))
                        .collect(Collectors.toList());
                    CountTypeResponseVO other = new CountTypeResponseVO();
                    other.setPersonType("其他");
                    other.setPersonCount(
                        (long) personRepository.findAllOtherPerson(request.getSubjectId(), areaVO.getAreaCode())
                            .size());
                    types.add(other);
                    areaDistributeVO.setCountPersonType(types);
                    break;
                default:
                    areaDistributeVO.setCountPersonType(Collections.emptyList());
                    break;
            }
            return areaDistributeVO;
        }).collect(Collectors.toList());
        log.info("XZ_SUBJECT_TIME:  " + (System.currentTimeMillis() - startTime) + "ms");
        return collect;
    }

    @Override
    public PersonTypeCountVO countType(String subjectId) {
        PersonTypeCountVO result = new PersonTypeCountVO();
        List<CountTypeResponseVO> types = countPersonTypes(subjectId);
        result.setTypes(types);
        result.setTotal((long) personRepository.findAllBySubjectId(subjectId).size());
        return result;
    }

    private List<CountTypeResponseVO> countPersonTypes(String subjectId) {
        List<CountTypeResponseVO> types = labelRepository.countByAreaCodeAndSubjectId(subjectId, null).stream()
            .map(m -> new CountTypeResponseVO(m.get("personType").toString(),
                ((BigDecimal) m.get("personCount")).longValue()))
            .sorted(Comparator.comparing(CountTypeResponseVO::getPersonCount).reversed())
            .collect(Collectors.toList());
        CountTypeResponseVO other = new CountTypeResponseVO();
        other.setPersonType("其他");
        other.setPersonCount((long) personRepository.findAllOtherPerson(subjectId, null).size());
        types.add(other);
        return types;
    }

    @Override
    public List<AreaCountVO> getCancelDriveCount(TimeParams timeParams) {
        List<AreaCountVO> result = warningRepository.getCancelDriveCount(timeParams.getBeginTime(),
            timeParams.getEndTime());
        //截取排序后的前10条数据
        return result.size() < 10 ? result : result.subList(0, 10);
    }

    @Override
    public List<AreaVO> getAreaInfo() {
        return dictService.getDictEntitiesByType(DICT_TYPE_DISTRICT).stream()
            .filter(d -> StringUtils.startsWith(d.getCode(), districtCodePrefix))
            .map(dictEntity -> {
                AreaVO areaVO = new AreaVO();
                areaVO.setAreaCode(dictEntity.getCode());
                areaVO.setAreaName(dictEntity.getName());
                return areaVO;
            }).collect(Collectors.toList());
    }

    /**
     * 获取人员变化率
     *
     * @param subjectId 专题id
     * @return {@link PersonChangeRateVO}
     */
    @Override
    public List<PersonChangeRateVO> getPersonChangeRateVOList(String subjectId) {

        List<PersonChangeRateVO> personChangeRateVOList = new ArrayList<>();
        //泸州的区域信息
        List<AreaVO> areaInfo = getAreaInfo();
        //近12个月的时间节点
        List<LocalDateTime> dateTimes = getDateTimes();

        LocalDateTime endTime = LocalDateTime.now();

        for (LocalDateTime beginTime : dateTimes) {

            //TODO 后期优化效率

            PersonChangeRateVO personChangeRateVO = new PersonChangeRateVO();
            //设置时间节点
            personChangeRateVO.setDateTime(beginTime);
            List<AreaDataVO> areaDataVOList = new ArrayList<>();
            //所有人员
            List<PersonEntity> allPerson = personRepository.getPersonListBySubject(subjectId, endTime);
            //所有人员轨迹
            List<MobilityEntity> allMobility = mobilityRepository.findAllBySubjectIdAndMoveTimeBetween(subjectId, beginTime, endTime);
//                    mobilityRepository.findAllByPersonIdInAndMoveTimeBetween(
//                allPerson.stream().map(PersonEntity::getId).collect(Collectors.toList()), beginTime, endTime);

            for (AreaVO areaVO : areaInfo) {
                AreaDataVO areaDataVO = new AreaDataVO();
                areaDataVO.setAreaName(areaVO.getAreaName());
                areaDataVO.setAreaCode(areaVO.getAreaCode());
                AtomicInteger inCount = new AtomicInteger();
                AtomicInteger outCount = new AtomicInteger();
                //泸州市人员变化路统计
                if (areaVO.getAreaCode().equals(districtCodePrefix)) {
                    //统计该时间点流入流出数量
                    allMobility.stream()
                        .collect(Collectors.groupingBy(MobilityEntity::getPersonId))
                        .values()
                        .stream()
                        .map(mobilityEntities -> {
                            MobilityEntity result = new MobilityEntity();
                            mobilityEntities.stream().max(Comparator.comparing(MobilityEntity::getMoveTime))
                                .ifPresent(mobility -> BeanUtil.copyPropertiesIgnoreNull(mobility, result));
                            return result;
                        })
                        .forEach(mobility -> {
                            final String inType = "1";
                            if (StringUtils.equals(inType, mobility.getMoveType())) {
                                inCount.incrementAndGet();
                            } else {
                                outCount.incrementAndGet();
                            }
                        });

                    addAreaDataVoList(areaDataVOList, areaDataVO, inCount, outCount, allPerson.size());
                } else {
                    //区县统计需要查询人员管控信息表
                    List<String> allPersonId = controlRepository.findAllBySubjectIdAndAreaCode(subjectId,
                        areaDataVO.getAreaCode()).stream().map(ControlEntity::getPersonId).collect(Collectors.toList());
                    allMobility.stream()
                        .filter(mobilityEntity -> allPersonId.contains(mobilityEntity.getPersonId()))
                        .collect(Collectors.groupingBy(MobilityEntity::getPersonId))
                        .values()
                        .stream()
                        .map(mobilityEntities -> {
                            MobilityEntity result = new MobilityEntity();
                            mobilityEntities.stream().max(Comparator.comparing(MobilityEntity::getMoveTime))
                                .ifPresent(mobility -> BeanUtil.copyPropertiesIgnoreNull(mobility, result));
                            return result;
                        })
                        .forEach(mobility -> {
                            final String inType = "1";
                            if (StringUtils.equals(inType, mobility.getMoveType())) {
                                inCount.incrementAndGet();
                            } else {
                                outCount.incrementAndGet();
                            }
                        });
                    addAreaDataVoList(areaDataVOList, areaDataVO, inCount, outCount, allPersonId.size());
                }
            }
            personChangeRateVO.setAreaData(areaDataVOList);
            personChangeRateVOList.add(personChangeRateVO);
            endTime = beginTime;

        }

        return personChangeRateVOList;
    }

    /**
     * 向AreaDataVO列表添加数据
     *
     * @param areaDataVOList {@link AreaDataVO}
     * @param areaDataVO     {@link AreaDataVO}
     * @param inCount        流入数量
     * @param outCount       流出数量
     * @param size           总人数
     */
    private void addAreaDataVoList(List<AreaDataVO> areaDataVOList, AreaDataVO areaDataVO, AtomicInteger inCount,
        AtomicInteger outCount, int size) {
        //上一月总人数 = 当前月总人数-流入+流出
        int lastMonthCount = size - inCount.get() + outCount.get();
        double changeRate = (lastMonthCount == 0 || (inCount.get() - outCount.get()) == 0) ? 0
            : (inCount.get() - outCount.get()) / (double) lastMonthCount;
        areaDataVO.setChangeRate(changeRate);
        areaDataVOList.add(areaDataVO);
    }

    /**
     * 获取近一年共12个月的时间节点
     *
     * @return {@link LocalDateTime} 近12个月的时间节点
     */
    private List<LocalDateTime> getDateTimes() {
        List<LocalDateTime> dateTimes = new ArrayList<>();
        LocalDate today = LocalDate.now();
        for (long i = 0; i <= MONTH_NUM; i++) {
            //时间节点
            LocalDateTime dateTime = today.minusMonths(i).with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            dateTimes.add(dateTime);
        }
        return dateTimes;
    }

    @Override
    public List<HeatMapVO> getHeatMap(String subjectId, String areaCode, String personType, TimeParams timeParams) {
        List<Map<String, Object>> result;
        if (StringUtils.isNotBlank(personType)) {
            result = warningTrajectoryRepository.getHeatMapData(timeParams.getBeginTime(), timeParams.getEndTime(),
                subjectId, areaCode, personType);
        } else {
            result = warningTrajectoryRepository.getHeatMapData(timeParams.getBeginTime(), timeParams.getEndTime(),
                subjectId, areaCode);
        }
        List<HeatMapVO> heatMapVOList = new ArrayList<>();
        result.forEach(item -> {
            HeatMapVO heatMapVO = new HeatMapVO();
            heatMapVO.setX(String.valueOf(item.get("lng")));
            heatMapVO.setY(String.valueOf(item.get("lat")));
            heatMapVO.setValue(item.get("count"));
            heatMapVOList.add(heatMapVO);
        });
        return heatMapVOList;
    }

    @Override
    public CountDistributeCaseVO getCaseDistrictCounty(String caseType, TimeParams timeParams) {
        //获取地区信息和开始时间、结束时间
        List<AreaVO> areaList = getAreaInfo();
        //获取地区柱状图信息
        List<AreaDistributeVO> countCaseArea = areaList.stream()
            .map(areaVO -> {
                //封装各个地区对应的类
                AreaDistributeVO areaDistributeVO = new AreaDistributeVO();
                areaDistributeVO.setAreaCode(areaVO.getAreaCode());
                areaDistributeVO.setAreaName(areaVO.getAreaName());
                List<CountTypeResponseVO> countPersonType = getCountPersonType(caseType, areaVO.getAreaCode(),
                    timeParams);
                areaDistributeVO.setCountPersonType(countPersonType);
                return areaDistributeVO;
            }).collect(Collectors.toList());
        //封装返回类
        CountDistributeCaseVO countDistributeCaseVO = new CountDistributeCaseVO();
        countDistributeCaseVO.setCountCaseArea(countCaseArea);
        return countDistributeCaseVO;
    }

    @Override
    public List<DistributePersonCountVO> getDistributePersonCountList(TimeParams timeParams, String subjectId) {
        List<PersonEntity> personEntityList = personRepository.findAllBySubjectId(subjectId);
        List<AreaVO> areaInfo = getAreaInfo();
        return areaInfo.stream().map(areaVO -> {
            DistributePersonCountVO distributePersonCount = new DistributePersonCountVO();
            distributePersonCount.setAreaCode(areaVO.getAreaCode());
            distributePersonCount.setAreaName(areaVO.getAreaName());
            if (areaVO.getAreaCode().equals(AREA_CODE)) {
                distributePersonCount.setPersonCount(personEntityList.size());
            } else {
                List<ControlEntity> controlEntityList = controlRepository.findAllBySubjectIdAndAreaCode(subjectId,
                    areaVO.getAreaCode());
                distributePersonCount.setPersonCount(controlEntityList.size());
            }
            return distributePersonCount;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CountTypeResponseVO> getDrugCaseTotal(TimeParams timeParams) {
        //刑事案件总数
        CountTypeResponseVO criminalTotal = new CountTypeResponseVO();
        criminalTotal.setPersonType("1");
        criminalTotal.setPersonCount(
            caseEventRepository.getCriminal(AREA_CODE, timeParams.getBeginTime(), timeParams.getEndTime()));
        //刑事案件打处人数
        CountTypeResponseVO criminalPersonTotal = new CountTypeResponseVO();
        criminalPersonTotal.setPersonType("2");
        criminalPersonTotal.setPersonCount(
            caseEventRepository.getCriminalPerson(AREA_CODE, timeParams.getBeginTime(), timeParams.getEndTime()));
        //行政案件总数
        CountTypeResponseVO administrationTotal = new CountTypeResponseVO();
        administrationTotal.setPersonType("3");
        administrationTotal.setPersonCount(
            caseEventRepository.getAdministration(AREA_CODE, timeParams.getBeginTime(), timeParams.getEndTime()));
        //行政案件打处人数
        CountTypeResponseVO administrationPersonTotal = new CountTypeResponseVO();
        administrationPersonTotal.setPersonType("4");
        administrationPersonTotal.setPersonCount(
            caseEventRepository.getAdministrationPerson(AREA_CODE, timeParams.getBeginTime(), timeParams.getEndTime()));
        //线索数
        CountTypeResponseVO clueTotal = new CountTypeResponseVO();
        clueTotal.setPersonType("5");
        clueTotal.setPersonCount(
            clueRepository.countAllByAreaAndTimeBetween(JD_SUBJECT, null, timeParams.getBeginTime(),
                timeParams.getEndTime()));
        //封装返回值
        List<CountTypeResponseVO> list = new ArrayList<>();
        list.add(criminalTotal);
        list.add(criminalPersonTotal);
        list.add(administrationTotal);
        list.add(administrationPersonTotal);
        list.add(clueTotal);
        return list;
    }

    @Override
    public List<CountTypeResponseVO> getDoneDrugWarningPerson() {
        //获取总数、省内数量、泸州数量
        List<PersonEntity> personEntities = personRepository.getPersonByPersonTypeName("涉毒前科人员");
        List<PersonEntity> inProvince = personEntities.stream()
            .filter(person -> "51".equals(person.getIdNumber().substring(0, 2))).collect(Collectors.toList());
        List<PersonEntity> inCity = inProvince.stream()
            .filter(person -> "5105".equals(person.getIdNumber().substring(0, 4))).collect(Collectors.toList());

        //泸州
        long city = inCity.size();
        CountTypeResponseVO cityCount = new CountTypeResponseVO();
        cityCount.setPersonType("1");
        cityCount.setPersonCount(city);

        //市外省内
        long province = inProvince.size();
        CountTypeResponseVO provinceCount = new CountTypeResponseVO();
        provinceCount.setPersonType("2");
        provinceCount.setPersonCount((province - city));

        //省外
        long total = personEntities.size();
        CountTypeResponseVO other = new CountTypeResponseVO();
        other.setPersonType("3");
        other.setPersonCount(total - province);

        //封装返回值
        List<CountTypeResponseVO> list = new ArrayList<>();
        list.add(cityCount);
        list.add(provinceCount);
        list.add(other);
        return list;
    }

    /**
     * 获取刑事案件，行政案件，线索对应的柱状图
     *
     * @param timeParams 时间范围
     * @param areaCode   地区编码
     * @param caseType   类型：0（刑事案件），1（刑侦案件），2（线索）
     * @return 区县分布情况
     */
    private List<CountTypeResponseVO> getCountPersonType(String caseType, String areaCode, TimeParams timeParams) {
        switch (caseType) {
            case "0":
                //获取刑事案件柱状图
                return getCriminal(areaCode, timeParams);
            case "1":
                //获取行政案件柱状图
                return getAdministration(areaCode, timeParams);
            case "2":
                //线索
                return getClue(areaCode, timeParams);
            default:
                return Collections.emptyList();
        }
    }

    /**
     * 获取线索柱状图信息
     *
     * @param areaCode   地区编码
     * @param timeParams 时间范围
     * @return 区县分布情况
     */
    private List<CountTypeResponseVO> getClue(String areaCode, TimeParams timeParams) {
        Long count = clueRepository.countAllByAreaAndTimeBetween(JD_SUBJECT, areaCode, timeParams.getBeginTime(),
            timeParams.getEndTime());
        CountTypeResponseVO clue = new CountTypeResponseVO();
        clue.setPersonType("线索数");
        clue.setPersonCount(count);
        return Collections.singletonList(clue);
    }

    /**
     * 获取行政案件柱状图信息
     *
     * @param timeParams 时间范围
     * @param areaCode   地区编码
     * @return 区县分布情况
     */
    private List<CountTypeResponseVO> getAdministration(String areaCode, TimeParams timeParams) {
        //案件数量
        CountTypeResponseVO administration = new CountTypeResponseVO();
        administration.setPersonType("案件数");
        administration.setPersonCount(
            caseEventRepository.getAdministration(areaCode, timeParams.getBeginTime(), timeParams.getEndTime()));
        //打处人数
        CountTypeResponseVO person = new CountTypeResponseVO();
        person.setPersonType("打处人数");
        person.setPersonCount(
            caseEventRepository.getAdministrationPerson(areaCode, timeParams.getBeginTime(), timeParams.getEndTime()));
        //封装返回值
        return Arrays.asList(administration, person);
    }

    /**
     * 获取刑事案件柱状图信息
     *
     * @param timeParams 时间范围
     * @param areaCode   地区编码
     * @return 区县分布情况
     */
    private List<CountTypeResponseVO> getCriminal(String areaCode, TimeParams timeParams) {
        //案件数量
        CountTypeResponseVO criminal = new CountTypeResponseVO();
        criminal.setPersonType("案件数");
        criminal.setPersonCount(
            caseEventRepository.getCriminal(areaCode, timeParams.getBeginTime(), timeParams.getEndTime()));
        //打处人数
        CountTypeResponseVO person = new CountTypeResponseVO();
        person.setPersonType("打处人数");
        person.setPersonCount(
            caseEventRepository.getCriminalPerson(areaCode, timeParams.getBeginTime(), timeParams.getEndTime()));
        //封装返回值
        return Arrays.asList(criminal, person);
    }

    @Override
    public PersonTypeCountVO countTag(String subjectId) {
        List<CountTypeResponseVO> tags = labelRepository.countPersonByTag(subjectId).stream().map(result -> {
            CountTypeResponseVO vo = new CountTypeResponseVO();
            vo.setPersonType((String) result.get("personType"));
            vo.setPersonCount(Long.parseLong(result.get("personCount").toString()));
            vo.setPersonTypeId((String) result.get("personTypeId"));
            return vo;
        }).collect(Collectors.toList());
        long total = personRepository.findAllBySubjectId(subjectId).size();
        PersonTypeCountVO result = new PersonTypeCountVO();
        result.setTotal(total);
        result.setTypes(tags);
        return result;
    }

    @Override
    public List<GroupPersonStatisticsVO> statisticsPoliticalGroup(String subjectId) {
        return groupRepository.findTop20ByPersonCountDesc(subjectId).stream().map(group -> {
            GroupPersonStatisticsVO vo = new GroupPersonStatisticsVO();
            vo.setId(group.getId());
            vo.setName(group.getName());
            List<LabelEntity> types = labelRepository.findByGroupIdAndSubjectId(group.getId(), subjectId);
            vo.setType(types.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));
            //成员数量
            List<PersonEntity> personList = personRepository.findAllByGroupId(group.getId());
            vo.setPersonCount(personList.size());
            vo.setPersonControlCount((int) personList.stream()
                .filter(person -> ControlStatusEnum.IN_CONTROL.getCode().equals(person.getControlStatus())).count());
            vo.setPersonMonitorCount((int) personList.stream()
                .filter(person -> MonitorStatusEnum.IN_MONITOR.getCode().equals(person.getMonitorStatus())).count());
            return vo;
        }).collect(Collectors.toList());
    }
}
