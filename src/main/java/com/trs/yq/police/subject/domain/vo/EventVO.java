package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/13 16:52
 */
@Data
public class EventVO implements Serializable {
    private static final long serialVersionUID = 3777538681191021052L;
    private String id;
    /**
     * 事件标题
     */
    @NotBlank(message = "事件名称不能为空！")
    private String title;
    /**
     * 事件发生时间
     */
    @NotNull(message = "事件发生时间不能为空！")
    private LocalDateTime occurrenceTime;
    /**
     * 参见码表ps_event_type
     */
    @NotNull(message = "事件类型不能为空！")
    private IdNameVO type;
    /**
     * 估计人数
     */
    @NotNull(message = "估计人数不能为空！")
    private Integer estimatePersonCount;
    /**
     * 管控单位名称
     */
    private String controlDeptName;
    /**
     * 管控单位
     */
    private List<String> controlDeptCode;
    /**
     * 上报状态
     * 码表 report_status
     */
    private String reportStatus;
    /**
     * 事件详细内容
     */
    @NotBlank(message = "事件详情不能为空！")
    private String content;

    /**
     * 事件数据唯一标志
     */
    private String sourceKey;
    /**
     * 事件发生地
     */
    private String address;

    /**
     * 诉求地
     */
    @NotBlank(message = "诉求地不能为空！")
    private String appealPlace;
    /**
     * 处置状态 参见码表(event_disposal_status)
     */
    private String disposalStatus;
    /**
     * 风险等级，码表 risk_level
     */
    @NotBlank(message = "风险等级不能为空！")
    private String riskLevel;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;

    /**
     * 相关要求
     */
    private String claim;
    /**
     * 专题id
     */
    @NotBlank(message = "专题id不能为空！")
    private String subjectId;

    /**
     * 事件来源
     */
    private String source;

    private String lat;

    private String lng;

    /**
     * 相关人数
     */
    private Integer relatedPersonCount;

}
