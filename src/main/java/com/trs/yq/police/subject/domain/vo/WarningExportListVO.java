package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/09/15
 */
@Data
public class WarningExportListVO implements Serializable {

    private static final long serialVersionUID = -3523942566848660621L;

    /**
     * 预警时间
     */
    @ExcelProperty(value = "预警时间", order = 0)
    private String warningTime;

    /**
     * 预警类型
     */
    @ExcelProperty(value = "预警类别", order = 1)
    private String warningType;

    /**
     * 预警内容
     */
    @ExcelProperty(value = "预警内容", order = 2)
    private String warningContent;

    /**
     * 预警来源
     */
    @ExcelProperty(value = "预警来源", order = 3)
    private String warningSource;

    /**
     * 预警状态
     */
    @ExcelProperty(value = "预警状态", order = 4)
    private String warningStatus;
}
