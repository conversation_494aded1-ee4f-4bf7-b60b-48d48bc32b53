package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.PersonAssociateAttributesEnum;
import com.trs.yq.police.subject.domain.dto.PersonAssociateRelationDTO;

/**
 * 烽火通信（FiberHome）人员主题库&要素关系库查询服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 */
public interface FiberHomePersonLibraryService {

    /**
     * 查询人员主题库
     *
     * @param attributesEnum  查询属性类型
     * @param attributesValue 查询属性值
     * @return 人员属性关联关系列表
     */
    PersonAssociateRelationDTO queryPersonLibrary(PersonAssociateAttributesEnum attributesEnum, String attributesValue);

    /**
     * 查询要素关系库
     *
     * @param attributesEnum  查询属性类型
     * @param attributesValue 查询属性值
     * @return 人员属性关联关系列表
     */
    PersonAssociateRelationDTO queryRelationLibrary(
            PersonAssociateAttributesEnum attributesEnum, String attributesValue);
}
