package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 群体人员活跃度vo
 *
 * <AUTHOR>
 * @date 2021/09/07
 */
@Data
public class GroupPersonActivityLevelVO implements Serializable {

    private static final long serialVersionUID = -4132760111464350097L;

    /**
     * 关系表主键
     */
    private String relationId;

    /**
     * 活跃度
     */
    @NotBlank(message = "活跃度缺失!")
    private String activityLevel;
}
