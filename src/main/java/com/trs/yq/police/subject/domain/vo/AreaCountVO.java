package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * [专题首页-交警] 失驾人员TOP10 统计VO
 *
 * <AUTHOR>
 * @date 2021/9/15 11:05
 */
@Data
public class AreaCountVO implements Serializable {
    private static final long serialVersionUID = 1537339429478577116L;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 数量
     */
    private Long count;

    /**
     *
     * @param areaName 区域名称
     * @param count 数量
     */
    public AreaCountVO(String areaName, Long count) {
        this.areaName = areaName;
        this.count = count;
    }
}
