package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/14 17:16
 */
@Data
public class EventListExportRequest implements Serializable {
    private static final long serialVersionUID = 2688697315719250467L;
    @NotEmpty(message = "字段名称不能为空！")
    private List<String> fieldNames;

    @NotEmpty(message = "事件id不能为空！")
    private List<String> eventIds;
}
