package com.trs.yq.police.subject.common;

import org.springframework.stereotype.Component;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 跳过返回参数判断
 *
 * <AUTHOR>
 * @date 2021/1/8 17:36
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Component
public @interface SkipResponseBodyAdvice {

}
