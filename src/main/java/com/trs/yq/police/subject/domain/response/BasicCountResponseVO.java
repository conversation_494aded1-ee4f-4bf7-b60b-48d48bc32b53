package com.trs.yq.police.subject.domain.response;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 数据视图统计数量响应参数
 *
 * <AUTHOR>
 * @date 2021/9/3 20:07
 */
@Getter
@Setter
@ToString
public class BasicCountResponseVO implements Serializable {

    private static final long serialVersionUID = 578119275148006714L;

    /**
     * 人员总数
     */
    private Integer personCount;

    /**
     * 群体数量
     */
    private Integer groupCount;

    /**
     * 已列管
     */
    private Long inList;

    /**
     * 已布控
     */
    private Long inControl;

    /**
     * 已归档
     */
    private Long inArchive;

    /**
     * constructor with no arguments
     */
    public BasicCountResponseVO() {
        this.personCount = 0;
        this.groupCount = 0;
        this.inList = 0L;
        this.inControl = 0L;
        this.inArchive = 0L;
    }
}
