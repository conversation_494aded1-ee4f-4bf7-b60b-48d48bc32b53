package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入境：未办理住宿预警、免签超期预警
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/17 15:06
 **/
@Data
public class RegisterOverdueVO {

    /**
     * 预警id
     */
    private String warningId;
    /**
     * 预警级别：1: 红色 2: 橙色 3:黄色 4:蓝色
     */
    private String warningLevel;
    /**
     * 护照号
     */
    private String warningPassportNo;
    /**
     * 头像
     */
    private List<ImageVO> images;
    /**
     * 预警国家
     */
    private String warningCountry;
    /**
     * 预警时间
     */
    private LocalDateTime warningTime;
    /**
     * 全参构造函数
     *
     * @param warningId 预警id
     * @param warningLevel 预警级别
     * @param warningPassportNo 护照号
     * @param images 头像
     * @param warningCountry 预警国家
     * @param warningTime 预警时间
     *
     */
    public RegisterOverdueVO(String warningId, String warningLevel, String warningPassportNo, List<ImageVO> images, String warningCountry, LocalDateTime warningTime) {
        this.warningId = warningId;
        this.warningLevel = warningLevel;
        this.warningPassportNo = warningPassportNo;
        this.images = images;
        this.warningCountry = warningCountry;
        this.warningTime = warningTime;
    }
    /**
     * 无参构造函数
     *
     */
    public RegisterOverdueVO() {
    }
}
