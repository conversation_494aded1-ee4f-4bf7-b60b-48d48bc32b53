package com.trs.yq.police.subject.mysqlDatasource.repository;


import com.trs.yq.police.subject.mysqlDatasource.entity.CrjSfryReportSourceEntity;
import com.trs.yq.police.subject.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 20:22
 */
@Repository
public interface CrjSfryReportSourceRepository extends BaseRepository<CrjSfryReportSourceEntity, String> {

    /**
     * 获取详情
     *
     * @param uuid uuid
     * @return {@link CrjSfryReportSourceEntity}
     */
    CrjSfryReportSourceEntity findByUuid(String uuid);

    /**
     * 查询未同步消息
     *
     * @param reportIds id
     * @return {@link String}
     */
    @Query("select t from CrjSfryReportSourceEntity t where t.uuid not in (:reportIds)")
    List<CrjSfryReportSourceEntity> findNotSyncIds(@Param("reportIds") List<String> reportIds);

    /**
     * 分页查询
     *
     * @param searchValue 搜索内容
     * @param fullText    不限
     * @param phone       手机号
     * @param content     举报内容
     * @param recordIds   uuids
     * @param secretKey   密钥
     * @param pageable    分页参数
     * @return {@link CrjSfryReportSourceEntity}
     */
    @Query("select t from CrjSfryReportSourceEntity t "
            + "where 1=1 "
            + "and (:fullText is null or ( AES_DECRYPT(FROM_BASE64(t.phone),:secretKey) like concat('%',:searchValue,'%') or t.content like concat('%',:searchValue,'%') ) ) "
            + "and (:phone is null or ( AES_DECRYPT(FROM_BASE64(t.phone),:secretKey) like concat('%',:searchValue,'%') ) ) "
            + "and (:content is null or t.content like concat('%',:searchValue,'%') ) "
            + "and (coalesce(:recordIds, null) is null or t.uuid in (:recordIds) ) order by field(t.uuid,:recordIds)")
    Page<CrjSfryReportSourceEntity> findPage(@Param("searchValue") String searchValue,
                                             @Param("fullText") String fullText, @Param("phone") String phone,
                                             @Param("content") String content, @Param("recordIds") List<String> recordIds,
                                             @Param("secretKey") String secretKey, Pageable pageable);

    /**
     * 地址模糊查询
     *
     * @param address 地址
     * @return {@link String}
     */
    @Query("select t.uuid from CrjSfryReportSourceEntity t where t.address like concat('%',:address,'%') ")
    List<String> findAllByAddressLike(@Param("address") String address);
}
