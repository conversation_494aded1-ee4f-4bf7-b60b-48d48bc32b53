package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 模块属性表
 *
 * <AUTHOR>
 * @date 2021/8/19 17:20
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_MODULE_FIELD")
public class ModuleFieldEntity {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 模块id
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 属性名称
     */
    private String fieldName;

    /**
     * 属性中文名
     */
    private String fieldCnName;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 目标对象类型
     * 0-person,1-group,2-clue
     */
    private Integer type;
}
