package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.*;
import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import com.trs.yq.police.subject.domain.entity.VisitRecordEntity;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.domain.vo.VisitRecordVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.repository.VisitRecordRepository;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.service.VisitRecordService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 走访记录服务层
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@Service
public class VisitRecordServiceImpl implements VisitRecordService {

    @Resource
    private VisitRecordRepository visitRecordRepository;

    @Resource
    private RemoteStorageService remoteStorageService;

    @Resource
    private FileStorageRepository fileStorageRepository;

    @Resource
    private OperationLogHandler operationLogHandler;

    /**
     * 查询人员走访记录
     *
     * @param personId 人员id
     * @return 走访记录
     */
    @Override
    public List<VisitRecordVO> getVisitRecords(String personId) {
        List<VisitRecordEntity> entities = visitRecordRepository.findByPersonIdOrderByTimeDesc(personId);
        return entities.stream().map(entity -> {
            VisitRecordVO vo = new VisitRecordVO();
            BeanUtil.copyPropertiesIgnoreNull(entity, vo);
            // 图片
            List<FileStorageEntity> files = fileStorageRepository.findAllByPersonIdAndModule(personId, FileTypeEnum.IMAGE.getCode(), FileModuleEnum.VISIT_RECORD_PHOTO.getCode(), vo.getId());
            final List<ImageVO> images = files.stream()
                    .filter(Objects::nonNull)
                    .map(ImageVO::of)
                    .collect(Collectors.toList());
            vo.setImages(images);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 添加走访记录
     *
     * @param personId 人员id
     * @param vo       vo
     */
    @Override
    public void addVisitRecord(String personId, VisitRecordVO vo) {
        VisitRecordEntity entity = new VisitRecordEntity();
        BeanUtil.copyPropertiesIgnoreNull(vo, entity);
        entity.setPersonId(personId);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增走访记录")
                .newObj(JsonUtil.toJsonString(entity))
                .module(OperateModule.VISIT_RECORD)
                .operator(Operator.ADD)
                .build();

        visitRecordRepository.save(entity);
        remoteStorageService.savePersonImageRelations(personId, vo.getImages(), FileModuleEnum.VISIT_RECORD_PHOTO, entity.getId());

        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    /**
     * 编辑走访记录
     *
     * @param personId 人员id
     * @param vo       vo
     */
    @Override
    public void updateVisitRecord(String personId, VisitRecordVO vo) {

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑走访记录")
                .oldObj(JsonUtil.toJsonString(getVisitRecord(vo.getId())))
                .module(OperateModule.VISIT_RECORD)
                .operator(Operator.EDIT)
                .build();

        VisitRecordEntity entity = new VisitRecordEntity();
        BeanUtil.copyPropertiesIgnoreNull(vo, entity);
        //由失控变为在控的，需要清除掉失控时间的值
        if (InControlEnum.IN_CONTROL.equals(InControlEnum.codeOf(vo.getInControl()))) {
            entity.setOutOfControlTime(null);
        }
        entity.setPersonId(personId);
        visitRecordRepository.save(entity);
        remoteStorageService.updatePersonImageRelations(personId, vo.getImages(), FileModuleEnum.VISIT_RECORD_PHOTO, vo.getId());

        logRecord.setNewObj(JsonUtil.toJsonString(vo));
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    /**
     * 删除走访记录
     *
     * @param personId 人员id
     * @param visitId  走访记录id
     */
    @Override
    public void deleteVisitRecord(String personId, String visitId) {
        remoteStorageService.deletePersonImageRelations(personId, FileModuleEnum.VISIT_RECORD_PHOTO, visitId);

        final VisitRecordEntity visitRecord = visitRecordRepository.findById(visitId).orElse(null);
        if (Objects.isNull(visitRecord)) {
            throw new ParamValidationException("走访记录不存在，请刷新核实");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除走访记录")
                .oldObj(JsonUtil.toJsonString(visitRecord))
                .module(OperateModule.VISIT_RECORD)
                .operator(Operator.DELETE)
                .build();

        visitRecordRepository.deleteById(visitId);

        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    private VisitRecordVO getVisitRecord(String visitId) {
        final VisitRecordEntity visitRecord = visitRecordRepository.findById(visitId).orElse(null);
        if (Objects.isNull(visitRecord)) {
            throw new ParamValidationException("走访记录不存在，请刷新核实");
        }

        VisitRecordVO vo = new VisitRecordVO();
        BeanUtil.copyPropertiesIgnoreNull(visitRecord, vo);
        // 图片
        final List<ImageVO> images = fileStorageRepository.findAllByPersonIdAndModule(visitRecord.getPersonId(),
                        FileTypeEnum.IMAGE.getCode(),
                        FileModuleEnum.VISIT_RECORD_PHOTO.getCode(),
                        vo.getId())
                .stream()
                .filter(Objects::nonNull)
                .map(ImageVO::of)
                .collect(Collectors.toList());
        vo.setImages(images);
        return vo;
    }
}
