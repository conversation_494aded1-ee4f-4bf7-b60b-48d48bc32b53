package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningUniqueRelationEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 预警轨迹关联表
 *
 * <AUTHOR>
 * @date 2021/9/7 16:00
 */
@SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
@Repository
public interface WarningUniqueIdRelationRepository extends BaseRepository<WarningUniqueRelationEntity, String> {

    /**
     * 获取数据
     *
     * @param uniqueId 唯一标识
     * @return 结果
     */
    @Query(value = "select count(*) from  T_PS_WARNING_UNIQUE_RELATION where unique_id = :uniqueId", nativeQuery = true)
    Integer selectByUniqueId(@Param("uniqueId") String uniqueId);
}
