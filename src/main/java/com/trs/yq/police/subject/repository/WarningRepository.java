package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.vo.AreaCountVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


/**
 * 模型预警持久层接口
 *
 * <AUTHOR>
 * @since 2021/9/1
 */
@Repository
public interface WarningRepository extends BaseRepository<WarningEntity, String> {

    /**
     * 根据预警类型查询预警列表
     *
     * @param warningType   预警类型
     * @param warningStatus 处置状态
     * @return 预警列表
     */
    List<WarningEntity> findByWarningTypeAndWarningStatus(String warningType, String warningStatus);

    /**
     * 查询某些预警类型未签收预警
     *
     * @param warningType 预警类型
     * @return 分页预警列表
     */
    @Query(value = "select * from t_ps_warning where warning_status = '1' and warning_type in :warningType", nativeQuery = true)
    List<WarningEntity> findUnsignedByWarningTypeIn(@Param("warningType") List<String> warningType);

    /**
     * 根据专题id查询预警
     *
     * @param subjectId 专题id
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return {@link WarningEntity}
     */
    @Query("select t from WarningEntity t where t.subjectId=?1 and t.warningTime> ?2 and t.warningTime< ?3")
    List<WarningEntity> findAllBySubjectId(String subjectId, LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 分组计数失驾人员地点
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return {@link AreaCountVO}
     */
    @Query("select new com.trs.yq.police.subject.domain.vo.AreaCountVO(w.place,count(w.place)) from WarningEntity w where w.warningType ='14'and w.warningTime> ?1 and w.warningTime<?2 group by w.place order by count(w.place) DESC ")
    List<AreaCountVO> getCancelDriveCount(LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 通过预警id查询所有线索
     *
     * @param warningIds 预警id列表
     * @return {@link WarningEntity}
     */
    List<WarningEntity> findAllByIdIn(List<String> warningIds);

    /**
     * 查找出入境人员预警，境外人员、回泸涉疑人员、非法出入境
     *
     * @param status   预警状态
     * @param pageable 分页参数
     * @return 数量
     * <AUTHOR>
     */
    @Query(value = "SELECT a FROM WarningEntity  a WHERE a.warningStatus = :status AND a.warningType IN ('22','23','24') order by a.warningTime desc")
    Page<WarningEntity> findAllBySubjects(@Param("status") String status, Pageable pageable);

    /**
     * 维稳专题进京赴省预警列表查询接口
     *
     * @param types  预警类型
     * @param status 预警状态
     * @return {@link WarningEntity}
     */
    @Query("select w from WarningEntity w where w.warningType in (:types) and w.warningStatus=:status order by w.warningTime desc")
    List<WarningEntity> findAllByWarningTypeInAndStatus(@Param("types") List<String> types, @Param("status") String status);

    /**
     * 维稳专题进京赴省预警列表查询接口
     *
     * @param types    预警类型
     * @param status   预警状态
     * @param pageable 分页
     * @return {@link WarningEntity}
     */
    @Query("select w from WarningEntity w where w.warningType = :types and w.warningStatus=:status order by w.warningTime desc")
    Page<WarningEntity> findAllByWarningTypeAndStatusPage(@Param("types") String types, @Param("status") String status, Pageable pageable);

    /**
     * 根据预警类型查询预警列表
     *
     * @param warningType   预警类型
     * @param warningStatus 处置状态
     * @param pageable      分页参数
     * @return 预警列表
     */
    @Query("select w from WarningEntity w where w.warningType=:warningType and w.warningStatus =:warningStatus  order by w.warningTime desc")
    Page<WarningEntity> findWarningPage(String warningType, String warningStatus, Pageable pageable);

    /**
     * 查询警情的经纬度
     *
     * @param jqbh 警情编号
     * @return 经纬度
     */
    @Query(nativeQuery = true, value = "SELECT to_char(j.latitude) as lat,to_char(j.longitude) as lng from v_jjdb j where j.receivenum=:jqbh")
    Map<String, String> findLatLngFromJq(@Param("jqbh") String jqbh);


    /**
     * 查询人员关联预警列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param pageable  分页参数
     * @return 分页结果
     */
    @Query("SELECT w FROM WarningTraceRelationEntity r LEFT JOIN WarningEntity w ON w.id=r.warningId" +
            " LEFT JOIN WarningTrajectoryEntity t ON t.id=r.trajectoryId " +
            " WHERE w.subjectId=:subjectId and " +
            " t.idNumber = (SELECT  idNumber FROM PersonEntity where id=:personId) " +
            " ORDER BY w.warningTime desc")
    Page<WarningEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId, Pageable pageable);

    /**
     * 获取最近的风险警情
     *
     * @return {@link WarningEntity}
     */
    @Query(nativeQuery = true, value = "SELECT w.* FROM T_PS_WARNING w WHERE w.WARNING_TYPE IN ('25','26') AND ROWNUM< 10 ORDER BY w.WARNING_TIME DESC")
    List<WarningEntity> getRecentList();

    /**
     * 根据预警编号查询关联的人员身份证号
     *
     * @param warningId 预警id
     * @return 人员身份证号
     */
    @Query(nativeQuery = true, value = "SELECT t.id_number FROM T_PS_WARNING_TRAJECTORY t WHERE t.ID IN (SELECT r.id FROM T_PS_WARNING_TRACE_RELATION r WHERE r.WARNING_ID=:warningId) AND ROWNUM < 1")
    String getRelatedPersonIdNumber(@Param("warningId") String warningId);

    /**
     * 统计预警数量
     *
     * @param start       开始
     * @param end         结束
     * @param warningType 预警类别
     * @return 数量
     */
    @Query("SELECT count(w.id) FROM WarningEntity w where w.warningTime>=:start and w.warningTime<:end and w.warningType=:warningType")
    Long getWarningCount(@Param("start") LocalDateTime start, @Param("end") LocalDateTime end, @Param("warningType") String warningType);

    /**
     * @param warningKey warningKey
     * @return {@link WarningEntity}
     */
    WarningEntity findByWarningKey(String warningKey);

    /**
     * 查询飙车警情
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 结果
     */
    @Query(nativeQuery = true, value = "select receivenum, contents, receivetime, address, districtcode from V_JJDB " +
            "where RECEIVETIME between :beginTime and :endTime " +
            "and (CONTENTS like '%飙车%' or CONTENTS like '%轰鸣%' or " +
            "(CONTENTS like '%车%' and (CONTENTS like '%噪音%' or CONTENTS like '%扰民%' or CONTENTS like '%声音%' or CONTENTS like '%影响休息%')))")
    List<Map<String, Object>> selectJjdbRacing(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据rawdata查询预警
     *
     * @param id rawdata
     * @return 数量
     */
    @Query(nativeQuery = true, value = "select count(1) from T_PS_WARNING_TRAJECTORY where RAW_DATA = :id")
    Integer selectByRawData(@Param("id") String id);

    /**
     * 根据预警id查询已入库的预警信息
     *
     * @param ids ID
     * @return 结果
     */
    @Query("SELECT a.id FROM WarningEntity a where a.id in (:ids) ")
    List<String> selectExistWarning(@Param("ids") List<String> ids);
}

