package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 热力图数据请求VO
 *
 * <AUTHOR>
 * @date 2021/9/11 17:15
 */
@Data
public class HeatMapRequestVO implements Serializable {
    private static final long serialVersionUID = 2516039355441126561L;
    /**
     * 专题id
     */
    @NotBlank(message = "专题id缺失")
    private String subjectId;
    /**
     * 地区code
     */
    @NotBlank(message = "地区编码缺失")
    private String areaCode;
    /**
     * 人员类别
     */
    private String personType;
    /**
     * 时间参数
     */
    private TimeParams timeParams;
}
