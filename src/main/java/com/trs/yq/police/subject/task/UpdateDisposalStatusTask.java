package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.repository.ClueRepository;
import com.trs.yq.police.subject.repository.EventRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 变更线索处置状态
 * 当当前时间大于“维权时间”时，未处置完成的线索自动变为“处置完毕”状态未实现
 *
 * <AUTHOR>
 * @date 2022/1/4 10:09
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.update.disposal.status.task.enable", havingValue = "true")
public class UpdateDisposalStatusTask {

    @Resource
    private ClueRepository clueRepository;
    @Resource
    private EventRepository eventRepository;
    /**
     * 处置状态：处置中
     */
    private static final String UNDER_DISPOSAL = "1";

    /**
     * 定时更新线索处置状态
     */
    @Scheduled(fixedDelay = 10 * 1000)
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateDisposalStatus() {
        final LocalDateTime now = LocalDateTime.now();
        clueRepository.updateDisposalStatusByOccurrenceTime(now);
    }

    /**
     * 线索发出指令、发起合成时，线索处置状态自动变为处置中
     */
    @Scheduled(fixedDelay = 10 * 1000)
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateClueDisposalStatus() {
        List<String> commandClues = clueRepository.selectDisposalStatusByCommand();
        List<String> recordClues = clueRepository.selectDisposalStatusByRecord();
        commandClues.addAll(recordClues);
        commandClues = commandClues.stream().distinct().collect(Collectors.toList());
        commandClues.forEach(clueId -> clueRepository.updateDisposalStatusByClueId(clueId, UNDER_DISPOSAL));
    }

    /**
     * 事件发出指令、发起合成时，事件处置状态自动变为处置中
     */
    @Scheduled(fixedDelay = 10 * 1000)
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateEventDisposalStatus() {
        List<String> commandEvents = eventRepository.selectDisposalStatusByCommand();
        List<String> recordEvents = eventRepository.selectDisposalStatusByRecord();
        commandEvents.addAll(recordEvents);
        commandEvents = commandEvents.stream().distinct().collect(Collectors.toList());
        commandEvents.forEach(eventId -> eventRepository.updateDisposalStatusByEventId(eventId, UNDER_DISPOSAL));
    }
}
