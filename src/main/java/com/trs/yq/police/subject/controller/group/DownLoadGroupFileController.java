package com.trs.yq.police.subject.controller.group;

import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.operation.OperationLog;
import com.trs.yq.police.subject.service.DownloadGroupFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/8 11:51
 **/
@RestController
@RequestMapping("/group")
@Slf4j
@Validated
public class DownLoadGroupFileController {

    @Resource
    private DownloadGroupFileService downloadGroupFileService;

    /**
     * 群体档案下载
     * http://192.168.200.192:3001/project/4897/interface/api/130879
     *
     * @param response  响应体
     * @param groupId   群体id
     * @param subjectId 主题id
     * <AUTHOR>
     * @date 2021/09/08
     */
    @GetMapping("/{groupId}/export/{subjectId}")
    @OperationLog(operator = Operator.EXPORT, module = OperateModule.GROUP_ARCHIVE_MANAGE, desc = "下载群体档案", primaryKey = "groupId", targetObjectType = TargetObjectTypeEnum.GROUP)
    public void downloadArchives(HttpServletResponse response, @PathVariable String groupId, @PathVariable String subjectId) {
        downloadGroupFileService.getFile(response, groupId, subjectId);
    }
}
