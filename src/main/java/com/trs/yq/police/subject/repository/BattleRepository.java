package com.trs.yq.police.subject.repository;


import com.trs.yq.police.subject.domain.entity.BattleEventLogEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * 合成/指令/协作 数据库接口
 *
 * <AUTHOR>
 * @since 2021/9/15
 */
@Component
public interface BattleRepository extends BaseRepository<BattleEventLogEntity, String> {


    /**
     * 查询该预警进行的合成
     *
     * @param warningId 预警id
     * @param srcTable 数据表
     * @return 查询结果
     */
    @Query(value = "with map as (select a.ID as bh, title, CRTBY as username, CRTBYNAME as name, UNITNAME as unitname, CRTTIME as time, regexp_substr(a.EVENTIDS,'[^,]+',1,level) code from T_BATTLE_RECORD a " +
            "connect by level <= nvl(regexp_count(a.EVENTIDS,','),0)+1 " +
            "and prior a.ID = a.ID " +
            "and prior DBMS_RANDOM.VALUE is not null) " +
            "select MAP.bh, MAP.title, MAP.name, MAP.time, map.username from T_BATTLE_EVENT a, map " +
            "where SRCTABLE=:srcTable " +
            "and a.KEYVAL=:warningId " +
            "and a.id=MAP.CODE", nativeQuery = true)
    List<Map<String, Object>> getRecord(@Param("warningId") String warningId, @Param("srcTable") String srcTable);

    /**
     * 查询该预警进行的指令
     *
     * @param warningId 预警id
     * @param srcTable 数据表
     * @return 查询结果
     */
    @Query(value = "with map as (select a.ID as bh, title, CRTBYNAME as name, CRTBY as username, UNITNAME as unitname, CRTTIME as time, regexp_substr(a.EVENTIDS,'[^,]+',1,level) code from T_BATTLE_COMMAND a " +
            "connect by level <= nvl(regexp_count(a.EVENTIDS,','),0)+1 " +
            "and prior a.ID = a.ID " +
            "and prior DBMS_RANDOM.VALUE is not null) " +
            "select MAP.bh, MAP.title, MAP.name, MAP.time, map.username from T_BATTLE_EVENT a, map " +
            "where SRCTABLE=:srcTable " +
            "and a.KEYVAL=:warningId " +
            "and a.id=MAP.CODE", nativeQuery = true)
    List<Map<String, Object>> getCommand(@Param("warningId") String warningId, @Param("srcTable") String srcTable);

    /**
     * 查询该预警进行的协作
     *
     * @param warningId 预警id
     * @param srcTable 数据表
     * @return 查询结果
     */
    @Query(value = "with map as (select a.ID as bh, title, CRTBY as username, CRTBYNAME as name, UNITNAME as unitname, CRTTIME as time, regexp_substr(a.EVENTIDS,'[^,]+',1,level) code from T_BATTLE_DEMAND a " +
            "connect by level <= nvl(regexp_count(a.EVENTIDS,','),0)+1 " +
            "and prior a.ID = a.ID " +
            "and prior DBMS_RANDOM.VALUE is not null) " +
            "select MAP.bh, MAP.title, MAP.name, MAP.time, map.username from T_BATTLE_EVENT a, map " +
            "where SRCTABLE=:srcTable " +
            "and a.KEYVAL=:warningId " +
            "and a.id=MAP.CODE", nativeQuery = true)
    List<Map<String, Object>> getDemand(@Param("warningId") String warningId, @Param("srcTable") String srcTable);

}
