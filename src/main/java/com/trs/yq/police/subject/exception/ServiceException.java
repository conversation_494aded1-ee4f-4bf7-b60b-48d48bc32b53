package com.trs.yq.police.subject.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Service Logic Exception
 *
 * <AUTHOR>
 */
@ResponseStatus(value = HttpStatus.SERVICE_UNAVAILABLE)
public class ServiceException extends RuntimeException {
    
    private static final long serialVersionUID = -8967169154011332051L;

    /**
     *
     */
    public ServiceException() {
    }

    /**
     * @param message 异常信息
     */
    public ServiceException(String message) {
        super(message);
    }

    /**
     * @param message 异常信息
     * @param cause   异常
     */
    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * @param cause 异常
     */
    public ServiceException(Throwable cause) {
        super(cause);
    }

    /**
     * @param message            异常信息
     * @param cause              异常
     * @param enableSuppression  是否警告压制
     * @param writableStackTrace 打印堆栈
     */
    public ServiceException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

}
