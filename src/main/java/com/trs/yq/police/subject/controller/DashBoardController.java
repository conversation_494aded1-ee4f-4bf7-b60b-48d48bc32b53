package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.service.DashBoardService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/20 11:16
 */
@RestController
@RequestMapping("/dash-board")
public class DashBoardController {

    @Resource
    private DashBoardService dashBoardService;

    /**
     * 通过人员类型统计数量
     *
     * @param statisticRequestVO 请求参数
     * @return 统计vo
     */
    @PostMapping("/zdry/rylb")
    List<BigScreenStatisticVO> statisticByRylb(@RequestBody StatisticRequestVO statisticRequestVO) {
        return dashBoardService.statisticByRylb(statisticRequestVO);
    }

    /**
     * 重点人员-人员预警
     *
     * @param statisticRequestVO 请求参数
     * @return 统计vo
     */
    @PostMapping("/zdry/ryyj")
    List<BigScreenStatisticVO> statisticByYjjb(@RequestBody StatisticRequestVO statisticRequestVO) {
        return dashBoardService.statisticByYjjb(statisticRequestVO);
    }

    /**
     * 机制赋能-案件合成率
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jzfn/ajhcl")
    BigScreenStatisticVO getAjhcl(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getAjhcl(requestVO);
    }
    /**
     * 接警top5
     *
     * @param requestVO 请求vo
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jqfx/jjtop5")
    List<BigScreenStatisticVO> getJjtop(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJqfxJjtop(requestVO);
    }

    /**
     * 区县接警情况
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jqfx/qxjjqk")
    List<BigScreenStatisticVO> getQxjjqk(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getQxjjqk(requestVO);
    }

    /**
     * 警情类别
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jqfx/jqlb")
    List<BigScreenStatisticVO> getJqlb(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJqlb(requestVO);
    }

    /**
     * 警情趋势
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jqfx/jqqs")
    List<BigScreenStatisticVO> getJqqs(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJqqs(requestVO);
    }

    /**
     * 机制赋能-警务协作
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jzfn/jwxz")
    List<BigScreenStatisticVO> getJwxz(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJwxz(requestVO);
    }

    /**
     * 机制赋能-警务合作
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jzfn/jwhz")
    List<BigScreenStatisticVO> getJwhz(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJwhz(requestVO);
    }

    /**
     * 机制赋能-协作单位
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/jzfn/xzdw")
    List<BigScreenStatisticVO> getXzdw(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getXzdw(requestVO);
    }

    /**
     * 案情分析-刑事案件
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/aqfx/xsaj")
    List<BigScreenStatisticVO> getXsaj(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getXsaj(requestVO);
    }

    /**
     * 案情分析-治安案件
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/aqfx/zaaj")
    List<BigScreenStatisticVO> getZaaj(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getZaaj(requestVO);
    }

    /**
     * 案情分析-街面六类
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/aqfx/jmll")
    List<BigScreenStatisticVO> getJmll(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJmll(requestVO);
    }

    /**
     * 案情分析-案情分析-打击质量
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/aqfx/djzl")
    List<BigScreenStatisticVO> getDjzl(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getDjzl(requestVO);
    }

    /**
     * 风险管控-治安风险
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/fxgk/zafx")
    List<BigScreenStatisticVO> getZafx(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getZafx(requestVO);
    }

    /**
     * 风险管控-风险上报
     *
     * @param requestVO 请求参数
     * @return {@link FxsbVO}
     */
    @PostMapping("/fxgk/fxsb")
    List<FxsbVO> getFxsb(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getFxsb(requestVO);
    }

    /**
     * 风险管控-热点分析
     *
     * @param requestVO 请求参数
     * @return {@link FxsbVO}
     */
    @PostMapping("/fxgk/rdfx")
    List<BigScreenStatisticVO> getRdfx(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getRdfx(requestVO);
    }

    /**
     * 日志检测-在线人数日志量
     *
     * @param requestVO 请求参数
     * @return {@link SystemLogVO}
     */
    @PostMapping("/rzjc/zxrsrzl")
    SystemLogVO getZxrsrzl(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getZxrsrzl(requestVO);
    }

    /**
     * 日志检测-使用功能
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/rzjc/sygn")
    List<BigScreenStatisticVO> getSygn(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getSygn(requestVO);
    }

    /**
     * 日志检测-访问分布
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/rzjc/fwfb")
    List<BigScreenStatisticVO> getFwfb(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getFwfb(requestVO);
    }

    /**
     * 日志检测-平台活跃趋势
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/rzjc/pthyqs")
    List<BigScreenStatisticVO> getPthyqs(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getPthyqs(requestVO);
    }

    /**
     * 日常工作-警务合成-合成占比
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/rcgz/jwhz/hczb")
    List<BigScreenStatisticVO> getHczb(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJwhz(requestVO,"battletype");
    }

    /**
     * 日常工作-警务合成-协作手段
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/rcgz/jwhz/xzsd")
    List<BigScreenStatisticVO> getXzsd(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getJwhz(requestVO,"demand_methodtype");
    }

    /**
     * 日常工作-指令中心
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/rcgz/zlzx")
    List<BigScreenStatisticVO> getZlzx(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getZlzx(requestVO);
    }


    /**
     * 预警信息
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    @PostMapping("/yjxx")
    List<YjxxVO> getYjxx(@RequestBody StatisticRequestVO requestVO) {
        return dashBoardService.getYjxx(requestVO);
    }
}
