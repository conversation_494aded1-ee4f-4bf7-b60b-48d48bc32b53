package com.trs.yq.police.subject.domain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * <AUTHOR>
 * @date 2021/11/24
 */
@Data
@Entity
@Table(name = "T_BATTLE_REPLY")
public class BattleReplyEntity implements Serializable {
    private static final long serialVersionUID = -435872968793257704L;

    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 合成作战ID
     */
    @Column(name = "RECORDID")
    private String recordId;

    /**
     * 反馈内容
     */
    @Column(name = "DETAIL")
    private String detail;

    /**
     * 反馈附件
     */
    @Column(name = "FILEIDS")
    private String fileIds;

    /**
     * 反馈人ID
     */
    @Column(name = "REPLYBY")
    private String replyBy;

    /**
     * 反馈人姓名
     */
    @Column(name = "REPLYBYNAME")
    private String replyByName;

    /**
     * 反馈单位名称
     */
    @Column(name = "UNITNAME")
    private String unitName;

    /**
     * 反馈单位代码
     */
    @Column(name = "UNITCODE")
    private String unitCode;

    /**
     * 反馈时间
     */
    @Column(name = "REPLYTIME")
    private LocalDateTime replyTime;

    /**
     * 回复数量
     */
    @Column(name = "COMMENTNUM")
    private String commentNum;

    /**
     * @ 用户ID
     */
    @Column(name = "ATUSERID")
    private String atUserId;

    /**
     * @ 用户姓名
     */
    @Column(name = "ATREALNAME")
    private String atRealName;

    /**
     * @ 反馈时限
     */
    @Column(name = "ATREPLYLIMIT")
    private LocalDateTime atReplyLimit;

    /**
     * 相关协作ID
     */
    @Column(name = "DEMANDIDS")
    private String demandIds;

    /**
     * 修改时间
     */
    @Column(name = "UPDTIME")
    private LocalDateTime updTime;

    /**
     * 相关指令ID
     */
    @Column(name = "COMMANDIDS")
    private String commandIds;

    /**
     * 布控信息主键
     */
    @Column(name = "CONTROLID")
    private String controlId;
}
