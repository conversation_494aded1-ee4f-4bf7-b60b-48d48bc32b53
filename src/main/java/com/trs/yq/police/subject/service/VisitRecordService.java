package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.VisitRecordVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/08/04
 */
public interface VisitRecordService {
    /**
     * 查询人员走访记录
     *
     * @param personId 人员id
     * @return 走访记录
     */
    List<VisitRecordVO> getVisitRecords(String personId);

    /**
     * 新增走访记录
     *
     * @param personId 人员id
     * @param vo       vo
     */
    void addVisitRecord(String personId, VisitRecordVO vo);

    /**
     * 编辑走访记录
     *
     * @param personId 人员id
     * @param vo       vo
     */
    void updateVisitRecord(String personId, VisitRecordVO vo);

    /**
     * 删除走访记录
     *
     * @param personId 人员id
     * @param visitId  走访记录id
     */
    void deleteVisitRecord(String personId, String visitId);
}
