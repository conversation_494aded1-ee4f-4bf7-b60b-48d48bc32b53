package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 人员表枚举 需要额外处理的value字段
 *
 * <AUTHOR>
 * @date 2021/7/31 17:18
 */
public enum PersonFieldEnum {

    /*
     * 基础属性 enums
     */
    ID("id", "主键"),
    CR_BY("crBy", "创建人主键"),
    CR_BY_NAME("crByName", "创建人姓名"),
    CR_TIME("crTime", "创建时间"),
    UP_BY("upBy", "更新人主键"),
    UP_BY_NAME("upByName", "更新人姓名"),
    UP_TIME("upTime", "更新时间"),
    CR_DEPT("crDept", "创建部门"),
    CR_DEPT_CODE("crDeptCode", "创建部门编号"),

    NAME("name", "姓名"),
    ID_NUMBER("idNumber", "身份证号"),
    ID_TYPE("idType", "证件类型"),
    GENDER("gender", "性别"),
    FORMER_NAME("formerName", "曾用名"),
    NICK_NAME("nickName", "绰号"),
    NATION("nation", "民族"),
    POLITICAL_STATUS("politicalStatus", "政治面貌"),
    RELIGIOUS_BELIEF("religiousBelief", "宗教信仰"),
    MARITAL_STATUS("maritalStatus", "婚姻状况"),
    CURRENT_JOB("currentJob", "现职业"),
    CONTACT_INFORMATION("contactInformation", "联系方式"),
    REGISTERED_RESIDENCE("registeredResidence", "户籍地"),
    CURRENT_RESIDENCE("currentResidence", "现住址"),
    CONTROL_STATUS("controlStatus", "管控状态"),
    CONTROL_LEVEL("controlLevel", "管控级别"),
    BASIC_INFO("basicInfo", "基本情况"),
    ADJUDICATION_REASON("reason", "吊销原因"),
    JUDGEMENT_DATE("judgementDate", "裁决时间"),
    LIMIT_TIME("limitTime", "限制年限"),
    LIMIT_UNIT("limitUnit", "限制年限单位"),
    END_DATE("endDate", "截止日期"),

    IMAGES("images", "走访图片"),
    LABELS("labels", "标签"),
    GROUPS("groups", "群体类别"),
    TYPES("types", "人员类别"),
    MOVE_TYPE("moveType", "流动类型"),
    WORK_END_TIME("workEndTime", "结束时间"),
    END_TIME("endTime", "结束时间"),
    TIME("time", "走访时间"),
    WORK_BEGIN_TIME("workBeginTime", "开始时间"),
    OUT_OF_CONTROL_TIME("outOfControlTime", "失控时间"),
    START_TIME("startTime", "开始时间"),
    MOVE_TIME("moveTime", "流动时间"),
    HAPPEN_TIME("happenTime", "发生时间"),
    BEGIN_TIME("beginTime", "开始时间"),

    PERSON_ID("personId", "人员编号"),
    EVENT_ID("eventId", "事件编号"),
    GROUP_ID("groupId", "群体编号"),
    CLUE_ID("clueId", "线索编号"),
    FILE_ID("fileId", "文件编号"),

    PERSON_NAME("personName", "人员姓名"),
    GROUP_NAME("groupName", "群体名称"),
    CLUE_NAME("clueName", "线索名称"),

    GROUP_TYPE("groupTypes", "群体类别"),
    CLUE_TYPE("clueTypes", "线索级别"),
    RELATED_PERSONS("relatedPersons", "关联人员"),
    ATTACHMENTS("attachments", "附件"),
    SENSITIVE_TIME("sensitiveTimeId", "敏感时间节点"),

    OCCURRENCE_TIME("occurrenceTime", "事发时间"),
    CREATE_TIME("createTime", "录入时间"),
    BEHAVIOUR("behaviour", "涉事行为"),
    EVENT_IMAGE("image", "涉事照片"),
    SOURCE("source", "来源"),
    EVENT_TYPE("type", "事件类型"),
    QQ("QQ", "QQ"),
    WECHAT("微信", "微信"),
    WEIBO("微博", "微博"),
    OTHER_INFORMATION("other_information", "其他电子信息"),
    NATIVE_PLACE("nativePlace", "籍贯"),
    BIRTHDAY("birthday", "出生日期"),
    MAIN_DEMAND("mainDemand", "主要诉求"),
    TREATMENT("treatment", "被依法处理情况"),
    GOVERNMENT_DEPARTMENT("governmentDepartment", "政府行业主管部门"),
    GOVERNMENT_LEADER_NAME("governmentLeaderName", "政府责任领导"),
    GOVERNMENT_LEADER_JOB("governmentLeaderJob", "政府责任领导职务"),
    GOVERNMENT_LEADER_TELEPHONE("governmentLeaderTelephone", "政府责任领导联系电话"),
    GOVERNMENT_DUTY_NAME("governmentDutyName", "政府主管部门责任人"),
    GOVERNMENT_DUTY_JOB("governmentDutyJob", "政府主管部门责任人职务"),
    GOVERNMENT_DUTY_TELEPHONE("governmentDutyTelephone", "政府主管部门责任人联系电话"),
    POLICE_STATION_NAME("policeStationName", "公安机关派出所"),
    LEADER_NAME("leaderName", "派出所责任领导"),
    LEADER_JOB("leaderJob", "派出所责任领导职务"),
    LEADER_CONTACT("leaderContact", "派出所责任领导联系电话"),
    RESPONSIBLE_NAME("responsibleName", "派出所责任民警"),
    RESPONSIBLE_JOB("responsibleJob", "派出所责任民警职务"),
    RESPONSIBLE_CONTACT("responsibleContact", "派出所责任民警联系电话"),
    WORK_UNIT("workUnit", "工作单位"),
    TOPOLOGY_GRAPH("topologyGraph", "拓扑图"),
    CALL_FILE("callFile", "话单文件"),
    CALL_TIME_FEATURE("callTimeFeature", "通话时间特点"),
    CALL_POSITION_TRACK("callPositionTrack", "通话位置轨迹"),
    ;


    /**
     * 属性名
     */
    @Getter
    private final String field;

    /**
     * 中文名
     */
    @Getter
    private final String cnName;


    /**
     * 私有构造方法
     *
     * @param field  属性名
     * @param cnName 中文名
     */
    PersonFieldEnum(String field, String cnName) {
        this.field = field;
        this.cnName = cnName;
    }

    /**
     * 通过中文转换
     *
     * @param cnName 中文名
     * @return 人员信息表结构枚举
     */
    public static PersonFieldEnum cnNameOf(String cnName) {

        if (StringUtils.isNotBlank(cnName)) {
            cnName = cnName.replace("（必填）", "");
            for (PersonFieldEnum person : PersonFieldEnum.values()) {
                if (StringUtils.equals(person.cnName, cnName)) {
                    return person;
                }
            }
        }
        return null;
    }

    /**
     * 英文属性名转换表结构枚举
     *
     * @param field 英文属性名
     * @return 人员信息表结构枚举
     */
    public static PersonFieldEnum fieldOf(String field) {

        if (StringUtils.isNotBlank(field)) {
            for (PersonFieldEnum fieldEnum : PersonFieldEnum.values()) {
                if (StringUtils.equals(fieldEnum.field, field)) {
                    return fieldEnum;
                }
            }
        }
        return null;
    }
}
