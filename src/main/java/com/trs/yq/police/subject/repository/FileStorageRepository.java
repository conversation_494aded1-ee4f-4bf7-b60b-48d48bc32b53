package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 文件存储信息持久层
 *
 * <AUTHOR>
 * @date 2021/7/30 13:31
 */
@Repository
public interface FileStorageRepository extends BaseRepository<FileStorageEntity, String> {

    /**
     * 查询文件信息
     *
     * @param personId 人员id
     * @param type     关联文件类型
     * @param module   模块
     * @param recordId 关联记录id
     * @return 文件信息
     */
    @Query("select f from"
            + " PersonFileRelationEntity pf  JOIN FileStorageEntity f ON pf.fileStorageId = f.id "
            + " WHERE pf.type = :type"
            + " AND pf.personId = :personId "
            + " AND pf.module = :module"
            + " AND (:recordId IS NULL OR pf.recordId=:recordId)")
    List<FileStorageEntity> findAllByPersonIdAndModule(@Param("personId") String personId,
                                                       @Param("type") String type,
                                                       @Param("module") String module,
                                                       @Param("recordId") String recordId);

    /**
     * 查询文件信息
     *
     * @param clueId   线索id
     * @param type     关联文件类型
     * @param module   模块
     * @param recordId 关联记录id
     * @return 文件信息
     */
    @Query("select f from"
            + " ClueFileRelationEntity cf  JOIN FileStorageEntity f ON cf.fileStorageId = f.id "
            + " WHERE cf.type = :type"
            + " AND cf.clueId = :clueId "
            + " AND cf.module = :module"
            + " AND (:recordId IS NULL OR cf.recordId=:recordId)")
    List<FileStorageEntity> findAllByClueIdAndModule(@Param("clueId") String clueId,
                                                     @Param("type") String type,
                                                     @Param("module") String module,
                                                     @Param("recordId") String recordId);

    /**
     * 查询线索关联的所有文件
     *
     * @param clueId 线索id
     * @return {@link FileStorageEntity}
     */
    @Query("select f from ClueFileRelationEntity r join FileStorageEntity f ON r.fileStorageId=f.id and r.clueId=:clueId")
    List<FileStorageEntity> findAllByClueId(@Param("clueId") String clueId);


    /**
     * 查询文件信息
     *
     * @param crjId  出入境信息id
     * @param type   关联文件类型
     * @param module 模块
     * @return 文件信息
     */
    @Query("select f from"
            + " CrjFileRelationEntity cf  JOIN FileStorageEntity f ON cf.fileStorageId = f.id "
            + " WHERE cf.type = :type"
            + " AND cf.crjId = :crjId "
            + " AND cf.module = :module")
    List<FileStorageEntity> findAllByCrjIdAndModule(@Param("crjId") String crjId,
                                                    @Param("type") String type,
                                                    @Param("module") String module);

    /**
     * 查询事件关联的所有文件
     *
     * @param eventId 线索id
     * @return {@link FileStorageEntity}
     */
    @Query("select f from EventFileRelationEntity r join FileStorageEntity f ON r.fileStorageId=f.id and r.eventId=:eventId")
    List<FileStorageEntity> findAllByEventId(@Param("eventId") String eventId);

    /**
     * 查询文件
     *
     * @param fileIds 文件id
     * @return {@link FileStorageEntity}
     */
    @Query("select f from FileStorageEntity  f where f.id in :fileIds")
    List<FileStorageEntity> findAllByFileIds(@Param("fileIds") List<String> fileIds);
}
