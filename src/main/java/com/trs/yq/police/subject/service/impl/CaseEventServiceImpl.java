package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.CaseEventEntity;
import com.trs.yq.police.subject.domain.vo.CaseEventVO;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.CaseEventRepository;
import com.trs.yq.police.subject.service.CaseEventService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 案事件信息业务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class CaseEventServiceImpl implements CaseEventService {

    @Resource
    private CaseEventRepository caseEventRepository;
    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public List<CaseEventVO> getAllCaseEvent(String personId) {
        List<CaseEventEntity> list = caseEventRepository.findAllByPersonIdOrderByHappenTimeDesc(personId);
        List<CaseEventVO> caseEventVOList = new ArrayList<>();
        for (CaseEventEntity caseEventEntity : list) {
            CaseEventVO caseEventVO = new CaseEventVO();
            caseEventVO.setName(caseEventEntity.getName());
            caseEventVO.setCaseType(caseEventEntity.getCaseType());
            caseEventVO.setHappenTime(caseEventEntity.getHappenTime());
            caseEventVO.setHappenSituation(caseEventEntity.getHappenSituation());
            caseEventVO.setPoliceSituation(caseEventEntity.getPoliceSituation());
            caseEventVOList.add(caseEventVO);
        }
        return caseEventVOList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addCaseEvent(String personId, CaseEventVO caseEventVO) {
        CaseEventEntity caseEvent = new CaseEventEntity();
        BeanUtil.copyPropertiesIgnoreNull(caseEventVO, caseEvent);
        caseEvent.setPersonId(personId);
        // 记录此操作
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.CASE_EVENT)
                .newObj(JsonUtil.toJsonString(caseEvent))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增案事件信息")
                .build();
        caseEventRepository.save(caseEvent);
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }
    }
}
