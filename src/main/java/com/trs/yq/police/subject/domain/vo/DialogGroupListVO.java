package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 情报档案关联群体中群体列表查询接口
 *
 * <AUTHOR>
 * @date 2021/9/6 14:19
 */
@Data
public class DialogGroupListVO implements Serializable {

    private static final long serialVersionUID = -5586867651082904382L;

    /**
     * 群体id
     */
    private String groupId;
    /**
     * 群体名称
     */
    private String groupName;
    /**
     * 群体类别
     */
    private String groupType;
    /**
     * 录入单位
     */
    private String createDept;

    /**
     * 构建vo
     *
     * @param groupEntity {@link GroupEntity}
     * @return {@link DialogGroupListVO}
     */
    public static DialogGroupListVO of(GroupEntity groupEntity) {
        DialogGroupListVO dialogGroupListVO = new DialogGroupListVO();
        dialogGroupListVO.setGroupId(groupEntity.getId());
        dialogGroupListVO.setGroupName(groupEntity.getName());
        dialogGroupListVO.setCreateDept(groupEntity.getCrDept());
        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        List<LabelEntity> groupTypes = labelRepository.findByGroupIdAndSubjectId(groupEntity.getId(), groupEntity.getSubjectId());
        dialogGroupListVO.setGroupType(groupTypes.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));
        return dialogGroupListVO;
    }
}
