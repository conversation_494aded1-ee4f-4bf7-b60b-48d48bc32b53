package com.trs.yq.police.subject.service.impl;


import com.trs.yq.police.subject.repository.UserRoleRepository;
import com.trs.yq.police.subject.service.UserRoleService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/07/23
 */
@Service
public class UserRoleServiceImpl implements UserRoleService {

    @Resource
    private UserRoleRepository roleRepository;

    /**
     * 判断用户是否不包含某种角色
     *
     * @param userId   用户id
     * @param roleName 角色名
     * @return 是否不包含
     */
    @Override
    public boolean checkUserNotContainsRole(String userId, String roleName) {
        return roleRepository.findRolesByUserId(userId, roleName).isEmpty();
    }

    /**
     * 判断用户是否包含某种角色
     *
     * @param userId   用户id
     * @param roleName 角色名
     * @return 是否包含
     */
    @Override
    public boolean checkUserContainsRole(String userId, String roleName) {
        return !checkUserNotContainsRole(userId, roleName);
    }
}
