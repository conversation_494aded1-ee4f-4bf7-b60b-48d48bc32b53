package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ClueFileRelationEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 线索和文件关联表查询接口
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Repository
public interface ClueFileRelationRepository extends BaseRepository<ClueFileRelationEntity, String> {
    /**
     * 根据线索id和module和关键id查询线索文件关系
     *
     * @param clueId   线索详情
     * @param code     模块类型
     * @param recordId 关联记录主键
     * @return 线索文件关联列表
     */
    List<ClueFileRelationEntity> findAllByClueIdAndModuleAndRecordId(String clueId, String code, @Nullable String recordId);

    /**
     * 根据线索id和module和关键id查询线索文件关系
     *
     * @param clueId   线索详情
     * @param type     文件类型
     * @param module   模块类型
     * @param recordId 关联记录主键
     * @return 线索文件关联列表
     */
    @Query("SELECT r FROM ClueFileRelationEntity r"
            + " WHERE r.clueId=:clueId"
            + " AND r.type = :type"
            + " AND r.module=:module"
            + " AND (:recordId is null or r.recordId=:recordId)")
    List<ClueFileRelationEntity> findAll(@Param("clueId") String clueId,
                                         @Param("type") String type,
                                         @Param("module") String module,
                                         @Param("recordId") String recordId);

    /**
     * 根据线索id删除关联
     *
     * @param clueId 线索id
     */
    void deleteAllByClueId(String clueId);

    /**
     * 根据线索id删除关联
     *
     * @param clueId        线索id
     * @param fileStorageId 材料id
     */
    void deleteByClueIdAndFileStorageId(String clueId, String fileStorageId);
}
