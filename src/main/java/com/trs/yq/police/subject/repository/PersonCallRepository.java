package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonCallEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * 话单分析
 *
 * <AUTHOR>
 */
@Repository
public interface PersonCallRepository extends BaseRepository<PersonCallEntity, String> {

    /**
     * 查询话单列表
     *
     * @param personId 人员id
     * @param pageable 分页参数
     * @return 结果
     */
    @Query("select call from PersonCallEntity call where call.personId = :personId order by call.upTime desc ")
    Page<PersonCallEntity> findAllByPersonId(String personId, Pageable pageable);
}
