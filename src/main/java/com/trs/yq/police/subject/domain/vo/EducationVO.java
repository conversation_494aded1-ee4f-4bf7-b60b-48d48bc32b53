package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 教育信息vo
 *
 * <AUTHOR>
 */
@Data
public class EducationVO implements Serializable {

    private static final long serialVersionUID = 65147707890838485L;

    /**
     * 教育id
     */
    private String educationId;

    /**
     * 起始时间
     */
    @NotNull(message = "校验信息起始时间缺失")
    private LocalDate beginTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 毕业院校
     */
    @NotBlank(message = "毕业院校缺失")
    private String graduationSchool;

    /**
     * 专业
     */
    private String subject;
}
