package com.trs.yq.police.subject.domain.params;

import java.io.Serializable;
import lombok.Data;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import javax.validation.constraints.NotNull;

/**
 * 分页请求参数类
 *
 * <AUTHOR>
 * @date 2021/07/30
 */
@Data
public class PageParams implements Serializable {

    private static final long serialVersionUID = 3662651931881553806L;
    /**
     * 页号
     */
    @NotNull(message = "页码不能为空！")
    private Integer pageNumber;

    /**
     * 页尺寸
     */
    @NotNull(message = "页大小不能为空！")
    private Integer pageSize;

    /**
     * 转换为jpa pageable
     *
     * @return pageable
     */
    public PageRequest toPageable() {
        return PageRequest.of(this.pageNumber - 1, this.pageSize);
    }

    /**
     * 转换为带排序条件的jpa pageable
     *
     * @param sort 排序条件
     * @return pageable
     */
    public PageRequest toPageable(Sort sort) {
        return PageRequest.of(this.pageNumber - 1, this.pageSize, sort);
    }

    /**
     * @return 偏移
     */
    public int getOffset() {
        return pageSize * (pageNumber - 1);
    }
}
