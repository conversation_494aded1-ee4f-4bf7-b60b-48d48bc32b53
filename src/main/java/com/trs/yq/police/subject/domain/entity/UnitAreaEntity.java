package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 单位区域实体表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25
 **/

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_UNIT_AREA")
public class UnitAreaEntity implements Serializable {

    private static final long serialVersionUID = 703969300355755322L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 单位代码
     */
    @Column(name = "unitcode")
    private String unitCode;

    /**
     * 单位简称
     */
    @Column(name = "shortname")
    private String shortName;

    /**
     * 区域多边形坐标
     */
    @Column(name = "polygon")
    private String polygon;

    /**
     * 区域中心经度
     */
    @Column(name = "centerx")
    private Double centerX;

    /**
     * 区域中心纬度
     */
    @Column(name = "centery")
    private Double centerY;
}
