package com.trs.yq.police.subject.service.impl;

import com.trs.dubbo.provide.CZRKService;
import com.trs.yq.police.subject.service.PhotoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Base64;

/**
 * 照片服务
 *
 * <AUTHOR>
 * @since 2022/4/21 16:01
 **/
@Service
public class PhotoServiceImpl implements PhotoService {
    @DubboReference(check = false)
    CZRKService czrkService;

    @Override
    public byte[] findPhoto(String idCard) {

        String requestIdcard = "513030199103156414";
        String requestName = "雍欢";
        String requestDept = "513030";
        byte[] photo = null;
        try {

            String base64 = czrkService.findPhoto(idCard, requestIdcard, requestName, requestDept);
            if (StringUtils.isBlank(base64)) {
                return null;
            }
            photo = Base64.getDecoder().decode(base64);
        } catch (Exception e) {
            String err = String.join("", "接口获取人员照片出错 {}, idCard=", idCard, "  requestUserIdCard=", requestIdcard, "  requestUserName=", requestName, "  requestUserDept=", requestDept);
        }
        return photo;
    }
}
