package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.BureauVO;
import com.trs.yq.police.subject.domain.vo.ControlVO;
import com.trs.yq.police.subject.domain.vo.PoliceVO;
import com.trs.yq.police.subject.domain.vo.UnitVO;

import java.util.List;

/**
 * 管控信息业务层接口
 *
 * <AUTHOR>
 */
public interface ControlService {

    /**
     * 查询管控信息
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @return 管控信息
     */
    ControlVO getControl(String personId, String subjectId);

    /**
     * 修改管控信息
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param controlVO 管控信息
     */
    void updateControl(String personId, String subjectId, ControlVO controlVO);
    /**
     * 查询分局分配
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @return 分配信息 {@link BureauVO}
     */
    BureauVO getBureau(String personId, String subjectId);

    /**
     * 分配分局
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param bureauVO 分配信息 {@link BureauVO}
     */
    void assignBureau(String personId, String subjectId, BureauVO bureauVO);

    /**
     * 分配派出所
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param controlVO 分配信息 {@link ControlVO}
     */
    void assignPoliceStation(String personId, String subjectId, ControlVO controlVO);

    /**
     * 分配民警
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @param controlVO 分配信息 {@link ControlVO}
     */
    void assignPolice(String personId, String subjectId, ControlVO controlVO);

    /**
     * 查询分局列表
     *
     * @return 分局列表 {@link UnitVO}
     */
    List<UnitVO> getBureauList();

    /**
     * 查询派出所列表
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @return 派出所列表 {@link UnitVO}
     */
    List<UnitVO> getPoliceStationList(String personId, String subjectId);

    /**
     * 查询民警列表
     *
     * @param unitCode 单位代码
     * @param name 模糊查询姓名
     * @return 民警列表 {@link PoliceVO}
     */
    List<PoliceVO> getPoliceList(String unitCode, String name);

    /**
     * 查询单位树状结构
     *
     * @return 单位列表
     */
    List<UnitVO> getListUnitTree();

}
