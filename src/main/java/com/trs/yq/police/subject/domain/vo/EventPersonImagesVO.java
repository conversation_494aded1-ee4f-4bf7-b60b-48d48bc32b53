package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 事件人员涉事图片
 *
 * <AUTHOR>
 * @date 2022/01/12
 */
@Data
public class EventPersonImagesVO implements Serializable {
    private static final long serialVersionUID = 9164717539720592258L;

    @NotBlank(message = "relationId不能为空")
    private String relationId;

    @NotNull(message = "images不能为空")
    private List<ImageVO> images;
}
