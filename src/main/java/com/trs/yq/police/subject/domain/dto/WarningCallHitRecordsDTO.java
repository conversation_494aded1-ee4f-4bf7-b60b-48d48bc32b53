package com.trs.yq.police.subject.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 话单
 *
 * <AUTHOR>
 * @date 2021/9/8 22:50
 */
@Getter
@Setter
@ToString
public class WarningCallHitRecordsDTO implements Serializable {

    private static final long serialVersionUID = 3109110982772177625L;

    /**
     * 海贝表名
     */
    private String trsTable;

    /**
     * 海贝主键
     */
    private String trsId;

    /**
     * 命中的电话号码
     */
    private String phoneNumber;

    /**
     * 通话时间
     */
    private String callTime;

    /**
     * 呼叫时长单位，秒
     */
    private String callDuration;

    /**
     * 呼叫类型, 0:主叫,1:被叫
     */
    private String callType;
}
