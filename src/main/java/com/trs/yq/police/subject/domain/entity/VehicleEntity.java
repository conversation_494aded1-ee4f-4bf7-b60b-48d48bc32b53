package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 车辆实体
 *
 * <AUTHOR>
 * @date 2021/7/27 16:40
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_VEHICLE")
public class VehicleEntity extends BaseEntity {

    private static final long serialVersionUID = -5352725486424238408L;
    
    /**
     * 人员Id
     */
    private String personId;
    /**
     * 车辆类型
     */
    private String type;
    /**
     * 车牌号
     */
    private String vehicleNumber;
    /**
     * 所属人
     */
    private String owner;
    /**
     * 是否为自动更新的
     */
    private String isAutomated;

}
