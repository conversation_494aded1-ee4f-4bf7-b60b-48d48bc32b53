package com.trs.yq.police.subject.operation;

import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 操作日志业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 18:40
 */
@Validated
public interface OperationLogService {

    /**
     * 记录操作日志
     *
     * @param operationLog 操作日志
     * @param paramsMap    参数列表
     * @param ipAddress    ip地址
     */
    void recordOperationLog(@NotNull(message = "操作记录注解缺失") OperationLog operationLog,
                            Map<String, Object> paramsMap,
                            String ipAddress);

    /**
     * 记录操作日志
     *
     * @param operationLog 操作记录
     */
    void recordOperationLog(OperationLogRecord operationLog);

}
