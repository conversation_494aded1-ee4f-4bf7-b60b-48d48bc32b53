package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 常住人员entity
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_CZRY")
public class CrjCzryEntity extends BaseEntity {

    private static final long serialVersionUID = 3310225852650445828L;

    private String certificateType;

    private String certificateNumber;

    private String enName;

    private String cnName;

    private String gender;

    private String country;

    private LocalDateTime birthday;

    private String livingPoliceStation;

    private String livingCounty;

    private String workUnit;

    private String livingAddress;

    private String visaType;

    private String visaNumber;

    private LocalDateTime entryTime;

    private String livingCheck;

    private String workCheck;

    private String entryReason;

    private String stayReason;

    private String livingStatus;

    private String createUser;

    private LocalDateTime createTime;

    private LocalDateTime visaSignDate;

    private LocalDateTime visaValidity;

    private String tel;
}
