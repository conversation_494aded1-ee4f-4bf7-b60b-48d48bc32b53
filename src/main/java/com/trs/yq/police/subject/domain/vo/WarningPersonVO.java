package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 预警人员详情vo
 *
 * <AUTHOR>
 * @since 2021/9/7
 */
@Data
public class WarningPersonVO implements Serializable {

    private static final long serialVersionUID = -4196792391725728039L;

    /**
     * id
     */
    private String personId;
    /**
     * 人员姓名
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 人员类别
     */
    private List<String> personType;
    /**
     * 身份证号码
     */
    private String idNumber;
    /**
     * 联系方式
     */
    private String phoneNumber;
    /**
     * 通联次数
     */
    private Integer contactsCount;
    /**
     * 布控状态
     */
    private String controlStatus;
    /**
     * 照片
     */
    private ImageVO photo;
    /**
     * 活动详情
     */
    private WarningActivityVO activityDetail;
}
