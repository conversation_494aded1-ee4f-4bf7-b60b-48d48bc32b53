package com.trs.yq.police.subject.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.*;

/**
 * 当前登录用户
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 10:07
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class LoginUser {

    /**
     * 用户id唯一不变
     */
    private String id;

    /**
     * 用户名 唯一 一般为身份号码
     */
    @JsonAlias("username")
    private String userName;

    /**
     * 真实姓名
     */
    @JsonAlias("realname")
    private String realName;

    /**
     * 身份证号码 唯一不变
     */
    @JsonAlias(value = "idcard")
    private String idCard;

    /**
     * 所属单位代码
     */
    @JsonAlias(value = "unitcode")
    private String unitCode;

    /**
     * 所属单位名称
     */
    @JsonAlias(value = "unitname")
    private String unitName;

    /**
     * 手机号码
     */
    @JsonAlias(value = "mobilephone")
    private String mobilePhone;

    /**
     * 照片
     */
    @JsonAlias(value = "photo")
    private String photo;
}
