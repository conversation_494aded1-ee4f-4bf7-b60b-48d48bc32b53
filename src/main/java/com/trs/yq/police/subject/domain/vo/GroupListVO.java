package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.SpringContextUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.enums.MonitorStatusEnum.NOT_MONITOR;

/**
 * 群体列表查询结果
 *
 * <AUTHOR>
 * @date 2021/8/3 9:31
 */
@Getter
@Setter
@ToString
public class GroupListVO implements Serializable {

    private static final long serialVersionUID = 8212614516353056141L;
    /**
     * 群体id
     */
    private String groupId;

    /**
     * 群体名
     */
    private String groupName;

    /**
     * 群体类别
     */
    private String groupType;

    /**
     * 群体人数
     */
    private Integer memberCount;

    /**
     * 关联线索数
     */
    private Integer clueCount;

    /**
     * 录入单位
     */
    private String createDeptName;

    /**
     * 录入时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 成员状态
     */
    private String membersSituation;

    /**
     * 涉事数量
     */
    private Integer eventCount;

    /**
     * 是否允许删除
     */
    private Boolean canDelete = false;

    /**
     * 群体列表根据entity和subjectId生成vo
     *
     * @param groupEntity entity
     * @param subjectId   专题id
     * @return vo
     */
    public static GroupListVO of(GroupEntity groupEntity, String subjectId) {
        GroupListVO vo = new GroupListVO();

        //id
        vo.setGroupId(groupEntity.getId());

        //名称
        vo.setGroupName(groupEntity.getName());

        //类别
        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        List<LabelEntity> types = labelRepository.findByGroupIdAndSubjectId(groupEntity.getId(), subjectId);
        vo.setGroupType(types.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));

        //成员数量
        PersonGroupRelationRepository personGroupRelationRepository = SpringContextUtil.getBean(PersonGroupRelationRepository.class);
        vo.setMemberCount(personGroupRelationRepository.countAllByGroupId(groupEntity.getId()));

        //线索数量
        GroupClueRelationRepository groupClueRelationRepository = SpringContextUtil.getBean(GroupClueRelationRepository.class);
        vo.setClueCount(groupClueRelationRepository.countAllByGroupId(groupEntity.getId()));

        //录入单位
        vo.setCreateDeptName(groupEntity.getCrDept());

        //录入时间
        vo.setCreateTime(groupEntity.getCrTime().format(DATE_TIME_FORMATTER));

        //更新时间
        vo.setUpdateTime(groupEntity.getUpTime().format(DATE_TIME_FORMATTER));

        //涉事数量
        EventRepository eventRepository = BeanUtil.getBean(EventRepository.class);
        vo.setEventCount(eventRepository.findAllByGroupId(groupEntity.getId()).size());

        //成员情况
        PersonRepository personRepository = BeanUtil.getBean(PersonRepository.class);
        List<PersonEntity> allPerson = personRepository.findAllByGroupId(groupEntity.getId());
        //未布控人数=已列管+已归档
        long notControl = allPerson.stream().filter(p -> NOT_MONITOR.getCode().equals(p.getMonitorStatus())).count();
        vo.setMembersSituation(String.format("%d/%d", notControl, allPerson.size()));

        //是否允许删除
        Optional.ofNullable(AuthHelper.getCurrentUser()).ifPresent(user -> vo.setCanDelete(user.getId().equals(groupEntity.getCrBy())));
        return vo;
    }
}
