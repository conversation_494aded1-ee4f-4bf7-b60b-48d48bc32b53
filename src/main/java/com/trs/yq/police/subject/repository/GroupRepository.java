package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.GroupEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 群体查询接口
 *
 * <AUTHOR>
 * @date 2021/07/28
 */
@Repository
public interface GroupRepository extends BaseRepository<GroupEntity, String> {

    /**
     * 根据人员和主题查询群体
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 群体
     */
    @Query("SELECT t2 FROM PersonGroupRelationEntity t1 JOIN GroupEntity t2 ON  t1.groupId=t2.id WHERE t1.personId=:personId and t2.subjectId=:subjectId")
    List<GroupEntity> findByPersonIdAndSubjectId(@Param("personId") String personId,
        @Param("subjectId") String subjectId);

    /**
     * 查询主题下面所有群体
     *
     * @param subjectId 专题id
     * @return 群体
     */
    List<GroupEntity> findAllBySubjectId(String subjectId);

    /**
     * 根据专题id分页查询所有群体实体
     *
     * @param subjectId   专题id
     * @param typeId      类别id
     * @param createDept  录入单位
     * @param searchValue 检索文字
     * @param pageable    {@link Pageable}
     * @return {@link GroupEntity}
     */
    @Query("select t1 from GroupEntity t1 " +
        "where t1.subjectId =:subjectId " +
        "and (:typeId is null  or exists (SELECT t2 FROM GroupLabelRelationEntity t2 WHERE t2.labelId=:typeId AND t2.groupId=t1.id)) "
        +
        "and (:createDept is null or t1.crDeptCode like concat(:createDept,'%') ) " +
        "and (:searchValue is null or t1.name like concat('%',:searchValue,'%')) " +
        "order by t1.upTime desc ")
    Page<GroupEntity> findAllBySubjectId(@Param("subjectId") String subjectId,
        @Param("typeId") String typeId,
        @Param("createDept") String createDept,
        @Param("searchValue") String searchValue,
        Pageable pageable);

    /**
     * 群体计数
     *
     * @param subjectId 专题数量
     * @return 统计数量
     */
    int countDistinctBySubjectId(String subjectId);

    /**
     * 根据id批量查询
     *
     * @param groupIds id
     * @return {@link GroupEntity}
     */
    List<GroupEntity> findAllByIdIn(List<String> groupIds);

    /**
     * 根据线索id查询
     *
     * @param clueId 线索id
     * @return {@link GroupEntity}
     */
    @Query("SELECT t2 FROM GroupClueRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id WHERE t1.clueId=:clueId")
    List<GroupEntity> findAllByClueId(@Param("clueId") String clueId);

    /**
     * 根据线索id查询(分页)
     *
     * @param clueId   线索id
     * @param pageable {@link Pageable}
     * @return {@link GroupEntity}
     */
    @Query("SELECT t2 FROM GroupClueRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id WHERE t1.clueId=:clueId")
    Page<GroupEntity> findAllByClueId(@Param("clueId") String clueId, Pageable pageable);

    /**
     * 查询人员关联的所有群体列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link GroupEntity}
     */
    @Query("SELECT t2 FROM PersonGroupRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id WHERE t1.personId=:personId AND t2.subjectId=:subjectId")
    List<GroupEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId,
        @Param("subjectId") String subjectId);

    /**
     * 根据群体id不分页查询上级群体
     *
     * @param groupId 群体id
     * @return {@link GroupEntity}
     */
    @Query("SELECT t2 FROM GroupParentEntity t1 JOIN GroupEntity t2 ON t1.parentId = t2.id WHERE t1.groupId = :groupId")
    List<GroupEntity> findAllParentByGroupId(@Param("groupId") String groupId);

    /**
     * 分页查询与事件相关联的群体id
     *
     * @param eventId  事件id
     * @param pageable 分页参数
     * @return {@link GroupEntity}
     */
    @Query("SELECT t2 FROM EventGroupRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id WHERE t1.eventId=:eventId")
    Page<GroupEntity> findAllByEventId(@Param("eventId") String eventId, Pageable pageable);

    /**
     * 不分页查询与事件已关联的群体
     *
     * @param eventId 事件id
     * @return {@link GroupEntity}
     */
    @Query("SELECT t2 FROM EventGroupRelationEntity t1 JOIN GroupEntity t2 ON t1.groupId=t2.id WHERE t1.eventId=:eventId")
    List<GroupEntity> findAllByEventId(@Param("eventId") String eventId);

    /**
     * 获取群体人数前20的群体
     *
     * @param subjectId 专题id
     * @return 群体
     */
    @Query(nativeQuery = true, value = "select  * from (select * from t_ps_group t where t.subject_id=:subjectId order by (select count(0) from t_ps_person_group_relation where group_id=t.id) desc)  where rownum <=20")
    List<GroupEntity> findTop20ByPersonCountDesc(@Param("subjectId") String subjectId);
}
