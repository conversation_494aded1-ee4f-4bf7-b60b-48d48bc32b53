package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BattleUserEntity;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public interface BattleUserRepository extends BaseRepository<BattleUserEntity, String> {

    /**
     * 查询事件合成所有用户
     *
     * @param eventId 事件id
     * @return {@link BattleUserEntity}
     */
    @Query(nativeQuery = true, value = "select *  " +
            "from T_BATTLE_USER  " +
            "where RECORDID in (SELECT br.id  " +
            "                   FROM T_BATTLE_RECORD br  " +
            "                   WHERE br.state = 2  " +
            "                     AND EXISTS(  " +
            "                           SELECT 1  " +
            "                           FROM T_BATTLE_EVENT be  " +
            "                           WHERE be.SRCTABLE = 'system.t_event'  " +
            "                             AND be.KEYNAME = 'ID'  " +
            "                             AND be.KEYVAL =  :eventId " +
            "                             AND INSTR(br.eventids, be.ID) > 0))")
    List<BattleUserEntity> findAllByEventId(@Param("eventId") String eventId);

}
