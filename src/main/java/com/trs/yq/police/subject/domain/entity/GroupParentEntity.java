package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/7/27 15:17
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_GROUP_PARENT_RELATION")
public class GroupParentEntity extends BaseEntity {
    private static final long serialVersionUID = 2188518833425316292L;
    /**
     * 群体id
     */
    private String groupId;
    /**
     * 上级群体id
     */
    private String parentId;
}
