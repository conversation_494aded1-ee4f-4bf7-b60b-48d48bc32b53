package com.trs.yq.police.subject.service.impl;

import com.trs.dubbo.provide.MsgGateway;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.domain.entity.CrjSfryReportEntity;
import com.trs.yq.police.subject.domain.entity.GridMangerEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.entity.UserEntity;
import com.trs.yq.police.subject.mysqlDatasource.entity.CrjJwrySourceEntity;
import com.trs.yq.police.subject.mysqlDatasource.entity.CrjSfryReportSourceEntity;
import com.trs.yq.police.subject.mysqlDatasource.repository.CrjJwrySourceRepository;
import com.trs.yq.police.subject.mysqlDatasource.repository.CrjSfryReportSourceRepository;
import com.trs.yq.police.subject.repository.CrjJwryDetailRepository;
import com.trs.yq.police.subject.repository.CrjJwryRepository;
import com.trs.yq.police.subject.repository.CrjSfryReportRepository;
import com.trs.yq.police.subject.repository.GridMangerRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.repository.UserRepository;
import com.trs.yq.police.subject.service.CrjSyncService;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import com.trs.yq.police.subject.webService.QgCrjWebService;
import com.trs.yq.police.subject.webService.domain.StayInfoArgs;
import com.trs.yq.police.subject.webService.domain.StayInfoArgs.StayInfo;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/12/19 15:36
 */
@Service
@Slf4j
public class CrjSyncServiceImpl implements CrjSyncService {

    @Value("${com.trs.crj.palz.secretKey}")
    public String secretKey = "xiondi.cn";
    @Resource
    private CrjJwrySourceRepository crjJwrySourceRepository;

    @Resource
    private UnitRepository unitRepository;

    @Resource
    private CrjJwryRepository crjJwryRepository;

    @Resource
    private CrjSfryReportSourceRepository crjSfryReportSourceRepository;

    @Resource
    private RemoteStorageService remoteStorageService;

    @Value("${com.trs.fastdfs.group_name}")
    private String groupName;

    @Resource
    private CrjSfryReportRepository crjSfryReportRepository;

    @Resource
    private CrjJwryDetailRepository crjJwryDetailRepository;

    @Resource
    private QgCrjWebService qgCrjWebService;

    @Resource
    private GridMangerRepository gridMangerRepository;
    @Resource
    private UserRepository userRepository;
    @DubboReference(check = false)
    private MsgGateway msgGateway;
    SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");

    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");


    @Transactional
    @Override
    public void syncJwry() {
        //获取已经同步的信息
        List<String> allId = crjJwryRepository.findAllId();
        List<CrjJwrySourceEntity> allByYqclState;
        allByYqclState = crjJwrySourceRepository.findAll();
//        if (allId.isEmpty()) {
//            allByYqclState = crjJwrySourceRepository.findAll();
//        } else {
//            allByYqclState = crjJwrySourceRepository.findAllNotSync(allId);
//        }

        if (allByYqclState.isEmpty()) {
            return;
        }
        //入库
        List<CrjJwryEntity> jwryEntities = new ArrayList<>(allByYqclState.size());

        //设置接收者
        for (CrjJwrySourceEntity sourceEntity : allByYqclState) {
            if(!allId.contains(sourceEntity.getId())){
                CrjJwryEntity entity = sourceEntity.toCrjJwryEntity(secretKey);
                jwryEntities.add(entity);
                String unitCode = sourceEntity.getGxdwbm();
                //存在管辖单位，且管辖单位code正确
                if (StringUtils.isNotBlank(unitCode)) {
                    UnitEntity unit = unitRepository.findByUnitCode(sourceEntity.getGxdwbm());
                    if (Objects.nonNull(unit)) {
                        if (CrjConstants.SQSXZD.contains(unitCode)) {
                            entity.setAcceptor(unitCode);
                            entity.setDispatchStatus(CrjDispatchStatusEnum.DISPATCHED_TO_QX.getCode());
                        } else if (CrjConstants.PCS_UNIT_TYPE.equals(unit.getType())) {
                            entity.setAcceptor(unitCode);
                            entity.setDispatchStatus(CrjDispatchStatusEnum.DISPATCHED.getCode());
                        }
                    }

                }
                //数据来源：旅店业
                if (entity.getSourceType() == 2) {
                    //退房时间存在，登记状态为离开
                    String registrationStatus =
                        Objects.isNull(entity.getLeaveTime()) ? CrjJwryRegistrationStatusEnum.LIVING.getCode()
                            : CrjJwryRegistrationStatusEnum.LEAVE.getCode();
                    entity.setRegistrationStatus(registrationStatus);
                    //将人员信息存入detail表，存在就覆盖，不存在就插入
                    CrjJwryDetailEntity detailEntity = sourceEntity.toJwryDetailEntity(secretKey);
                    crjJwryDetailRepository.findByIdTypeAndIdNumber(detailEntity.getIdType(), detailEntity.getIdNumber())
                        .ifPresent(e -> {
                            crjJwryDetailRepository.deleteById(e.getId());
                        });
                    crjJwryDetailRepository.save(detailEntity);
                    //将信息存入全国系统
                    StayInfo stayInfo = new StayInfo();
                    stayInfo.setYwx(detailEntity.getEnLastName());
                    stayInfo.setYwm(detailEntity.getEnFirstName());
                    stayInfo.setYwxm(detailEntity.getEnName());
                    stayInfo.setZwxm(detailEntity.getCnName());
                    stayInfo.setXb(
                        StringUtils.isBlank(detailEntity.getGender()) ? 0 : Integer.parseInt(detailEntity.getGender()));

                    stayInfo.setZjzl(detailEntity.getIdType());
                    stayInfo.setZjhm(detailEntity.getIdNumber());
                    stayInfo.setGjdq(detailEntity.getGjdm());
                    stayInfo.setRydylb("F");
                    stayInfo.setSjly("5");
                    stayInfo.setLzryywbh(sourceEntity.getId());
                    try {
                        stayInfo.setCsrq(
                            Objects.isNull(detailEntity.getBirthday()) ? "" : format.format(detailEntity.getBirthday()));
                    } catch (Exception e) {
                        log.error("出生日期转换失败！", e);
                    }
                    stayInfo.setLrsj(format.format(new Date()));
                    LoginUser currentUser = AuthHelper.getCurrentUser();
                    if (currentUser != null) {
                        stayInfo.setPcsbh(currentUser.getUnitCode());
                        stayInfo.setPcsmc(currentUser.getUserName());
                    }
                    stayInfo.setZsrq(
                        Objects.isNull(entity.getCheckinTime()) ? "" : dateFormat.format(entity.getCheckinTime()));
                    stayInfo.setLkrq(Objects.isNull(entity.getLeaveTime()) ? "" : dateFormat.format(entity.getLeaveTime()));
                    stayInfo.setLsdwhzdz(sourceEntity.getAddress());
                    stayInfo.setLsdwmc(sourceEntity.getQiyebmc());
                    stayInfo.setQzzl(detailEntity.getVisaType());
                    stayInfo.setQzhm(detailEntity.getVisaNumber());
                    qgCrjWebService.saveStay(new StayInfoArgs(stayInfo));
                }
            }
        }
        crjJwryRepository.saveAll(jwryEntities);
    }

    @Transactional
    @Override
    public void syncSfry() {
        //查出没有同步的信息
        List<String> allId = crjSfryReportRepository.findAllId();
        List<CrjSfryReportSourceEntity> notSync;
        notSync = crjSfryReportSourceRepository.findAll();
//        if (allId.isEmpty()) {
//            notSync = crjSfryReportSourceRepository.findAll();
//        } else {
//            notSync = crjSfryReportSourceRepository.findNotSyncIds(allId);
//        }
        if (notSync.isEmpty()) {
            return;
        }
        Base64.Decoder decoder = Base64.getDecoder();
        //初始化
        List<CrjSfryReportEntity> collect = notSync.stream().filter(item->!allId.contains(item.getUuid())).map(sourceEntity -> {
            CrjSfryReportEntity entity = new CrjSfryReportEntity();
            entity.setDispatchStatus(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode());
            //TODO 根据电话号码自动分派
            String phone = StringUtil.aesDecrypt(sourceEntity.getPhone(), secretKey);
            GridMangerEntity gridMangerEntity = gridMangerRepository.findByGridManagerPhoneNumber(phone);
            if(Objects.nonNull(gridMangerEntity) && StringUtils.isNotBlank(gridMangerEntity.getPoliceIdNumber())){
                UserEntity user = userRepository.findByIdCard(gridMangerEntity.getPoliceIdNumber());
                if(Objects.nonNull(user)){
                    entity.setAcceptor(user.getUnitCode());
                    entity.setDispatchStatus(CrjDispatchStatusEnum.DISPATCHED.getCode());
                    try {
                        String[] phoneNumbers = Collections.singletonList(gridMangerEntity.getPolicePhoneNumber()).toArray(new String[0]);
                        msgGateway.sendMessage("辖区内有一条'三非'举报信息,请及时处理", "出入境", phoneNumbers);
                        log.info("技侦手段预警，短信发送成功！接收电话为：{}", Arrays.toString(phoneNumbers));
                    } catch (Exception e) {
                        log.error("短信网关发送失败！", e);
                    }
                }
            }
            if(Objects.nonNull(sourceEntity.getCreateTime())){
                entity.setReportTime(sourceEntity.getCreateTime());
            }
            entity.setReportId(sourceEntity.getUuid());
            entity.setIsVerified(Boolean.FALSE);
            entity.setVerifyResult("0");
            //图片
            String attachment = sourceEntity.getAttachment();
            if (StringUtils.isNotBlank(attachment)) {
                try {
                    List<String> images = JsonUtil.parseArray(attachment, String.class);
                    List<String> attachmentUrls = new ArrayList<>(images.size());
                    for (String image : images) {
                        byte[] decode = decoder.decode(image.replaceAll("^data:image/jpeg;base64,", ""));
                        String[] imageUrl = remoteStorageService.uploadFile(decode, groupName, "jpg");
                        attachmentUrls.add(CrjConstants.ATTACHMENT_PREFIX + imageUrl[1]);
                    }
                    entity.setAttachment(JsonUtil.toJsonString(attachmentUrls));
                } catch (Exception e) {
                    log.error("同步照片失败！", e);
                }
            }
            return entity;
        }).collect(Collectors.toList());
        crjSfryReportRepository.saveAll(collect);
    }
}
