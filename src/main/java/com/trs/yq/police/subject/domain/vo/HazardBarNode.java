package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/04/14
 */
@Data
public class HazardBarNode implements Serializable {

    private static final long serialVersionUID = 9020683599880981222L;

    private String name;

    private List<NodeData> data;

    /**
     * 节点数据封装
     */
    @Data
    public static class NodeData implements Serializable {

        private static final long serialVersionUID = -3541624029090295935L;

        private LocalDate date;

        private Long value;

        /**
         * 构造器
         *
         * @param date  date
         * @param count count
         */
        public NodeData(LocalDate date, Long count) {
            this.date = date;
            this.value = count;
        }
    }
}
