package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonLabelRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 人员标签关系持久层
 *
 * <AUTHOR>
 * @date 2021/7/30 15:49
 */
@Repository
public interface PersonLabelRelationRepository extends BaseRepository<PersonLabelRelationEntity, String> {

    /**
     * 依据标签id删除所有
     *
     * @param personId 人员id
     * @param labelIds 标签列表
     */
    @Modifying
    void removeAllByPersonIdAndLabelIdIn(String personId, List<String> labelIds);

    /**
     * 移除人员与标签的关联关系
     *
     * @param labelId 标签id
     */
    @Modifying
    void removeAllByLabelId(@Param("labelId") String labelId);


    /**
     * 根据人员id查询标签
     *
     * @param personId 人员id
     * @return 标签名
     */
    @Query(
            "select l.name from LabelEntity l "
                    + "left join PersonLabelRelationEntity plr on plr.labelId = l.id "
                    + "where plr.personId = :personId")
    List<String> findLabelNamesByPersonId(@Param("personId") String personId);

    /**
     * 根据人员Id和标签id查询关联关系
     *
     * @param personId 人员id
     * @param labelId  标签id
     * @return {@link PersonLabelRelationEntity}
     */
    Optional<PersonLabelRelationEntity> findByPersonIdAndLabelId(String personId, String labelId);

    /**
     * 根据标签查询人员信息
     *
     * @param labelId 标签ID
     * @return 结果
     */
    @Query(nativeQuery = true, value = "select plr.person_id from T_PS_PERSON_LABEL_RELATION plr where plr.label_id = :labelId")
    List<String> selectByLabelId(@Param("labelId") String labelId);
}
