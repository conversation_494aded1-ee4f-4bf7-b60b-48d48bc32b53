package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.EducationVO;
import com.trs.yq.police.subject.service.EducationService;
import com.trs.yq.police.subject.service.PersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 教育信息接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/person")
@Slf4j
public class EducationController {
    @Resource
    private EducationService educationService;
    @Resource
    private PersonService personService;

    /**
     * 获取人员教育信息
     *
     * @param personId 人员id
     * @return 教育信息 {@link EducationVO}
     * <AUTHOR>
     */
    @GetMapping("/{personId}/education")
    public List<EducationVO> getAllEducation(@PathVariable String personId) {
        return educationService.findAllEducation(personId);
    }

    /**
     * 获取人员教育信息
     *
     * @param personId    人员主键
     * @param educationVO 教育信息
     * <AUTHOR>
     */
    @PostMapping("/{personId}/education")
    public void addEducation(@PathVariable String personId, @Valid @RequestBody EducationVO educationVO) {
        personService.checkPersonExist(personId);
        educationService.addEducation(personId, educationVO);
    }

    /**
     * 删除人员教育信息
     *
     * @param personId    人员主键
     * @param educationId 教育id
     * <AUTHOR>
     */
    @DeleteMapping("/{personId}/education")
    public void deleteEducation(@PathVariable String personId, @NotBlank(message = "教育信息主键缺失") String educationId) {
        personService.checkPersonExist(personId);
        educationService.deleteEducation(personId, educationId);
    }

    /**
     * 更新人员教育信息
     *
     * @param personId    人员主键
     * @param educationVO 教育信息
     * <AUTHOR>
     */
    @PutMapping("/{personId}/education")
    public void updateEducation(@PathVariable String personId, @Valid @RequestBody EducationVO educationVO) {
        personService.checkPersonExist(personId);
        educationService.updateEducation(personId, educationVO);
    }
}
