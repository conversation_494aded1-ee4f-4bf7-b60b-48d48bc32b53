package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.WorkInformationEntity;
import com.trs.yq.police.subject.domain.vo.WorkInformationVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.WorkInformationRepository;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.WorkInformationService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工作信息类业务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class WorkInformationServiceImpl implements WorkInformationService {

    @Resource
    private WorkInformationRepository workInformationRepository;

    @Resource
    private PersonService personService;

    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public List<WorkInformationVO> getAllByPersonId(String personId) {
        List<WorkInformationVO> workInformationVOList = workInformationRepository.findAllByPersonId(personId)
                .stream()
                .filter(Objects::nonNull)
                .map(WorkInformationVO::new)
                .collect(Collectors.toList());

        //对workInformationVOList进行
        workInformationVOList = workInformationVOList
                .stream()
                .sorted(Comparator.comparing(WorkInformationVO::getWorkBeginTime).reversed())
                .collect(Collectors.toList());
        return workInformationVOList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteOne(String personId, String jobId) {
        personService.checkPersonExist(personId);
        // 验证工作信息是否存在
        WorkInformationEntity work = workInformationRepository.findById(jobId).orElse(null);
        if (Objects.isNull(work)) {
            throw new ParamValidationException("工作信息不存在！");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除工作信息")
                .oldObj(JsonUtil.toJsonString(work))
                .module(OperateModule.WORK_INFO)
                .operator(Operator.DELETE)
                .build();

        workInformationRepository.deleteById(jobId);

        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addOne(String personId, WorkInformationVO workInformationVO) {
        personService.checkPersonExist(personId);
        WorkInformationEntity entity = new WorkInformationEntity();
        BeanUtil.copyPropertiesIgnoreNull(workInformationVO, entity, "id");
        entity.setPersonId(personId);
        entity.setWorkSituation(workInformationVO.getWorkPlace());
        entity.setPost(workInformationVO.getDuty());
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增工作信息")
                .newObj(JsonUtil.toJsonString(entity))
                .module(OperateModule.WORK_INFO)
                .operator(Operator.ADD)
                .build();
        workInformationRepository.save(entity);
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateOne(String personId, WorkInformationVO workInformationVO) {

        // 先验证是否存在
        personService.checkPersonExist(personId);
        // 验证工作信息是否存在
        WorkInformationEntity work = workInformationRepository.findById(workInformationVO.getId()).orElse(null);
        if (Objects.isNull(work)) {
            throw new ParamValidationException("工作信息不存在！");
        }

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑工作信息")
                .oldObj(JsonUtil.toJsonString(work))
                .module(OperateModule.WORK_INFO)
                .operator(Operator.EDIT)
                .build();

        work.setWorkUnit(workInformationVO.getWorkUnit());
        work.setWorkSituation(workInformationVO.getWorkPlace());
        work.setWorkBeginTime(workInformationVO.getWorkBeginTime());
        work.setWorkEndTime(workInformationVO.getWorkEndTime());
        work.setPost(workInformationVO.getDuty());
        work.setPersonId(personId);
        workInformationRepository.save(work);

        logRecord.setNewObj(JsonUtil.toJsonString(work));
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }
}
