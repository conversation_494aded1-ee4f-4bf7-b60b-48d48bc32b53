package com.trs.yq.police.subject.handler;

import com.trs.yq.police.subject.common.ResponseMessage;
import com.trs.yq.police.subject.exception.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.annotation.Priority;
import javax.validation.ValidationException;
import java.util.NoSuchElementException;

/**
 * 该类用于统一异常处理
 *
 * <AUTHOR>
 * @date 2017/5/10
 */
@Slf4j
@RestControllerAdvice(basePackages = {"com.trs"})
@Priority(1)
public class CustomExceptionHandler {

    private static final String SYSTEM_EXCEPTION_MESSAGE = "系统错误";

    private static final String NO_SUCH_ELEMENT_EXCEPTION_MESSAGE = "未找到元素";

    public static final String AUTHORIZATION_ERROR = "权限认证失败";

    public static final String AUTHORIZATION_DEFICIENCY = "暂无操作权限";

    public static final String NOT_FOUND_RESOURCE = "资源未找到";

    public static final String PARAM_VALID_ERROR = "参数校验失败";

    public static final String INTERVAL_SYSTEM_ERROR = "服务器内部异常";

    public static final String SERVICE_UNAVAILABLE = "服务暂不可访问";


    /**
     * 参数校验异常
     *
     * @param e 方法参数校验异常
     * @return 响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        logException(e);
        return ResponseMessage.errorWithStatus(HttpStatus.BAD_REQUEST.value(), e.getBindingResult().getAllErrors().get(0).getDefaultMessage());
    }


    /**
     * 参数校验异常
     *
     * @param e 方法参数校验异常
     * @return 响应
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleMethodArgumentNotValidException(ValidationException e) {
        logException(e);
        return ResponseMessage.errorWithStatus(HttpStatus.BAD_REQUEST.value(), e.getMessage());
    }


    /**
     * 暂停服务
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(ServiceException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.SERVICE_UNAVAILABLE)
    public ResponseMessage handleServiceException(ServiceException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(HttpStatus.SERVICE_UNAVAILABLE.value(), SERVICE_UNAVAILABLE);
    }

    /**
     * 系统内部异常
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(SystemException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseMessage handleSystemException(SystemException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(HttpStatus.INTERNAL_SERVER_ERROR.value(), INTERVAL_SYSTEM_ERROR.concat(":").concat(ex.getMessage()));
    }

    /**
     * 参数校验异常
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(ParamValidationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleParmaValidationException(ParamValidationException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(HttpStatus.BAD_REQUEST.value(), PARAM_VALID_ERROR.concat(":").concat(ex.getMessage()));
    }

    /**
     * 权限拒绝
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(PermissionDeniedException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseMessage handlePermissionDeniedException(PermissionDeniedException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(HttpStatus.UNAUTHORIZED.value(), AUTHORIZATION_DEFICIENCY);
    }

    /**
     * 权限禁止
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(PermissionForbiddenException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseMessage handlePermissionForbiddenException(PermissionForbiddenException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(HttpStatus.FORBIDDEN.value(), AUTHORIZATION_ERROR);
    }

    /**
     * 资源未找到异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseMessage handleResourceNotFoundException(ResourceNotFoundException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(HttpStatus.NOT_FOUND.value(), NOT_FOUND_RESOURCE);
    }

    /**
     * 普通异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(Exception.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseMessage handleCommonException(Exception ex) {
        logException(ex);
        return ResponseMessage.error(StringUtils.isBlank(ex.getMessage()) ? SYSTEM_EXCEPTION_MESSAGE : ex.getMessage());
    }

    /**
     * 无资源异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(NoSuchElementException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseMessage handleNoSuchElementException(NoSuchElementException ex) {
        logException(ex);
        return new ResponseMessage(404, false,
                StringUtils.isBlank(ex.getMessage()) ? NO_SUCH_ELEMENT_EXCEPTION_MESSAGE : ex.getMessage(), null);
    }

    private void logException(Exception ex) {
        log.error("", ex);
    }


    /**
     * 普通异常处理
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(InteractException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseMessage handleInteractException(InteractException ex) {
        logException(ex);
        return ResponseMessage.error(StringUtils.isBlank(ex.getMessage()) ? AUTHORIZATION_DEFICIENCY : ex.getMessage());
    }

    /**
     * 逻辑异常
     *
     * @param ex 具体异常
     * @return 响应结果
     */
    @ExceptionHandler(AppLogicException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseMessage handleTrsLogicException(AppLogicException ex) {
        logException(ex);
        return ResponseMessage.errorWithStatus(200,ex.getMessage());
    }
}
