package com.trs.yq.police.subject.domain.response;

import com.trs.yq.police.subject.domain.entity.HandleEntity;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 处警反馈信息
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/21 14:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HandleFeedbackResponse {

    /**
     * 处警单位
     */
    private String crDept;

    /**
     * 处警时间
     */
    private LocalDateTime flagTime;

    /**
     * 反馈信息
     */
    private String contents;

    /**
     * entity to response
     *
     * @param feedback entity
     * @return vo
     */
    public static HandleFeedbackResponse of(HandleEntity feedback) {
        HandleFeedbackResponse response = new HandleFeedbackResponse();
        UnitRepository unitDao = BeanUtil.getBean(UnitRepository.class);
        Map<String, String> unit = unitDao.findByAreaCode(feedback.getHandleUnit());
        response.setCrDept(unit.getOrDefault("unit_name", ""));
        response.setContents(feedback.getContents());
        response.setFlagTime(feedback.getHandleTime());
        return response;
    }
}
