package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

import java.util.Objects;

import static com.trs.yq.police.subject.constants.WarningConstants.*;

/**
 * 预警类型枚举
 *
 * <AUTHOR>
 * @since 2021/9/15
 */

public enum WarningTypeEnum {


    /**
     * 预警类型-反恐-新流入危安人员
     */
    FK_XLRWARY("01", "fk_xlrwary", "新流入危安人员"),
    /**
     * 预警类型-反恐-关注人员风险预警
     */
    FK_GZRYFXYJ("02", "fk_gzryfxyj", "关注人员风险预警"),
    /**
     * 预警类型-反恐-危安人员聚集
     */
    FK_QTJJ("03", "fk_qtjj", "危安人员聚集"),
    /**
     * 预警类型-禁毒-隐形涉毒人员
     */
    JD_YXSDRY("04", "jd_yxsdry", "隐形涉毒人员"),

    /**
     * 预警类型-禁毒-隐形涉毒窝点
     */
    JD_YXSDWD("05", "jd_yxsdwd", "隐形涉毒窝点"),
    /**
     * 预警类型-禁毒-涉毒人员驾车
     */
    JD_SDRYJC("06", "jd_sdryjc", "涉毒人员驾车"),
    /**
     * 预警类型-禁毒-涉毒人员聚集
     */
    JD_RYJJ("07", "jd_ryjj", "涉毒人员聚集"),
    /**
     * 预警类型-刑侦-人员预警
     */
    XZ_RYYJ("08", "xz_ryyj", "人员预警"),
    /**
     * 预警类型-刑侦-出泸预警
     */
    XZ_ZDRYCJ("09", "xz_zdrycj", "出泸预警"),

    /**
     * 预警类型-政保-人员预警
     */
    ZB_RYYJ("12", "zb_ryyj", "人员预警"),

    /**
     * 预警类型-政保-违安人员聚集预警
     */
    ZB_WARYJJYJ("13", "zb_waryjjyj", "违安人员聚集预警"),

    /**
     * 预警类型-交警-失驾人员驾驶机动车
     */
    JJ_SJRYJC("14", "jj_sjryjc", "失驾人员驾驶机动车"),

    /**
     * 预警类型-交警-赛摩人员聚集
     */
    JJ_SMRYJJ("15", "jj_smryjj", "赛摩人员聚集"),

    /**
     * 预警类型-维稳-重点人员进京
     */
    WW_ZDRYJJ("16", "ww_zdryjj", "重点人员进京"),

    /**
     * 预警类型-维稳-重点人员赴省
     */
    WW_ZDRYFS("17", "ww_zdryfs", "重点人员赴省"),

    /**
     * 预警类型-维稳-涉稳群体聚集
     */
    WW_QTJJ("18", "ww_qtjj", "涉稳群体聚集"),
    /**
     * 预警类型-刑侦-入泸预警
     */
    XZ_ZDRYRJ("19", "xz_zdryrj", "入泸预警"),
    /**
     * 预警类型-交警-关注人员风险预警
     */
    JJ_GZRYFXYJ("20", "jj_gzryfxyj", "关注人员风险预警"),

    /**
     * 预警类型-政保-关注人员风险预警
     */
    ZB_GZRYFXYJ("21", "zb_gzryfxyj", "关注人员风险预警"),

    WW_JZSDYJ_KUASHENG("35", WARNING_TYPE_WW_JZSDYJ_KUASHENG, "技侦预警-跨省"),
    WW_JZSDYJ_LKCS("36", WARNING_TYPE_WW_JZSDYJ_LKCS, "技侦预警-离开城市"),
    WW_JZSDYJ_DDCS("37", WARNING_TYPE_WW_JZSDYJ_DDCS, "技侦预警-到达城市"),
    WW_JZSDYJ_KUASHI("38", WARNING_TYPE_WW_JZSDYJ_KUASHI, "技侦预警-跨市"),
    WW_JZSDYJ_KUAXIAN("39", WARNING_TYPE_WW_JZSDYJ_KUAXIAN, "技侦预警-跨县"),
    WW_JZSDYJ_LKCZD("399", WARNING_TYPE_WW_JZSDYJ_LKCZD, "技侦预警-离开常驻地"),
    FK_JZSDYJ_LKCS("40", WARNING_TYPE_FK_JZSDYJ_LKCS, "技侦预警-离开城市"),
    FK_JZSDYJ_DDCS("41", WARNING_TYPE_FK_JZSDYJ_DDCS, "技侦预警-到达城市"),
    FK_JZSDYJ_QJJJ("42", WARNING_TYPE_FK_JZSDYJ_QJJJ, "技侦预警-聚集"),
    FK_JZSDYJ_HJHK("43", WARNING_TYPE_FK_JZSDYJ_HJHK, "技侦预警-换机换卡"),
    FK_JZSDYJ_KUASHENG("44", WARNING_TYPE_FK_JZSDYJ_KUASHENG, "技侦预警-跨省"),
    FK_JZSDYJ_KUASHI("45", WARNING_TYPE_FK_JZSDYJ_KUASHI, "技侦预警-跨市"),
    FK_JZSDYJ_KUAXIAN("46", WARNING_TYPE_FK_JZSDYJ_KUAXIAN, "技侦预警-跨县"),
    FK_JZSDYJ_LKCZD("47", WARNING_TYPE_FK_JZSDYJ_LKCZD, "技侦预警-离开常驻地"),

    /**
     * 预警类型-反邪-人员预警
     */
    FX_RYYJ("55", "fx_ryyj", "人员预警"),


    /**
     * 预警类型-反邪-违安人员聚集预警
     */
    FX_WARYJJYJ("56", "fx_waryjjyj", "违安人员聚集预警"),

    /**
     * 预警类型-反邪-关注人员风险预警
     */
    FX_GZRYFXYJ("57", "fx_gzryfxyj", "关注人员风险预警");


    @Getter
    private final String code;

    @Getter
    private final String enName;

    @Getter
    private final String cnName;

    WarningTypeEnum(String code, String enName, String cnName) {
        this.code = code;
        this.enName = enName;
        this.cnName = cnName;
    }

    /**
     * 根据编号获取预警类型枚举
     *
     * @param code 编号
     * @return 预警类型枚举
     */
    public static WarningTypeEnum codeOf(String code) {
        if (Objects.nonNull(code)) {
            for (WarningTypeEnum typeEnum : WarningTypeEnum.values()) {
                if (typeEnum.code.equals(code)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据英文名获取预警类型枚举
     *
     * @param enName 英文名
     * @return 预警类型枚举
     */
    public static WarningTypeEnum enNameOf(String enName) {
        if (Objects.nonNull(enName)) {
            for (WarningTypeEnum typeEnum : WarningTypeEnum.values()) {
                if (typeEnum.enName.equals(enName)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
