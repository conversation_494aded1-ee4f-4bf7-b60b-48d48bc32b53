package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ControlEntity;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


/**
 * 管控信息数据层
 *
 * <AUTHOR>
 */
@Repository
public interface ControlRepository extends BaseRepository<ControlEntity, String> {

    /**
     * 根据人员id和专题id查询管控信息
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 管控信息 {@link ControlEntity}
     */
    Optional<ControlEntity> findByPersonIdAndSubjectId(String personId, String subjectId);

    /**
     * 根据专题查询人员管控信息
     *
     * @param subjectId 专题主键
     * @return 管控信息
     */
    @Query("SELECT C FROM ControlEntity C " + "LEFT JOIN PersonSubjectRelationEntity PSR ON PSR.personId = C.personId " + "WHERE PSR.subjectId = ?1 ")
    List<ControlEntity> findAllBySubjectId(String subjectId);

    /**
     * 根据专题查询人员管控信息
     *
     * @param subjectId 专题主键
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 管控信息
     */
    @Query("SELECT C FROM ControlEntity C " + "LEFT JOIN PersonSubjectRelationEntity PSR ON PSR.personId = C.personId " + "WHERE PSR.subjectId = ?1 " + "AND C.crTime >= ?2 " + "AND C.crTime <= ?3")
    List<ControlEntity> findAllBySubjectIdAndCrTime(String subjectId, LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 查询区县管控人员信息
     *
     * @param subjectId 专题id
     * @param areaCode  地区编码
     * @return {@link ControlEntity}
     */
    @Query("select  c from ControlEntity  c  where c.subjectId =:subjectId and c.policeStationCode like concat(:areaCode,'%') ")
    List<ControlEntity> findAllBySubjectIdAndAreaCode(String subjectId, String areaCode);

    /**
     * 根据身份证号码获取管控派出所代码
     *
     * @param idNumber 身份证号
     * @return 派出所代码
     */
    @Query(nativeQuery = true, value = "select police_station_code from T_PS_PERSON_CONTROL where person_id = (select id from t_ps_person where id_number = :idNumber) and rownum < 1")
    String getPoliceStationCodeByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据身份证号码获取管控派出所名称
     *
     * @param idNumber 身份证号
     * @return 派出所名称
     */
    @Query(nativeQuery = true, value = "select police_station_code from T_PS_PERSON_CONTROL where person_id = (select id from t_ps_person where id_number = :idNumber) and rownum < 1")
    String getPoliceStationNameByIdNumber(@Param("idNumber") String idNumber);

    /**
     * 根据身份证号码获取管控民警手机号
     *
     * @param idNumber 身份证号
     * @return 管控民警手机号
     */
    @Query(nativeQuery = true, value = "select RESPONSIBLE_CONTACT from T_PS_PERSON_CONTROL where person_id = (select id from t_ps_person where id_number = :idNumber) and SUBJECT_ID='6'")
    String getPolicePhoneNumberByIdNumber(@Param("idNumber") String idNumber);

}
