package com.trs.yq.police.subject.constants.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/22 17:55
 */
public enum SfrySyncStatusEnum {
    DEFAULT("0", "默认"),
    WAIT_TO_SYNC("1", "待同步"),
    SYNCED("2", "已同步"),
    SYNC_FAILED("3", "同步失败");

    SfrySyncStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @JsonValue
    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * 通过模块码获取枚举
     *
     * @param code 操作符码
     * @return 操作符 {@link Operator}
     * <AUTHOR>
     * @since 2021/7/27 17:55
     */
    public static SfrySyncStatusEnum codeOf(String code) {

        if (StringUtils.isNotBlank(code)) {

            for (SfrySyncStatusEnum syncStatus : SfrySyncStatusEnum.values()) {
                if (StringUtils.equals(code, syncStatus.code)) {
                    return syncStatus;
                }
            }
        }
        return null;
    }
}
