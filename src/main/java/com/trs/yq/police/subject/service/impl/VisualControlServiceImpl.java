package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.MonitorStatusEnum;
import com.trs.yq.police.subject.domain.dto.RiskDetailDto;
import com.trs.yq.police.subject.domain.entity.BattleRecordEntity;
import com.trs.yq.police.subject.domain.entity.BattleReplyEntity;
import com.trs.yq.police.subject.domain.entity.BattleUserEntity;
import com.trs.yq.police.subject.domain.entity.CameraEntity;
import com.trs.yq.police.subject.domain.entity.DictEntity;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.EventPersonRelationEntity;
import com.trs.yq.police.subject.domain.entity.FaceHitEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.HandleEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.response.HandleFeedbackResponse;
import com.trs.yq.police.subject.domain.response.OccurredEventResponse;
import com.trs.yq.police.subject.domain.response.RiskDetailPersonResponse;
import com.trs.yq.police.subject.domain.response.RiskDetailResponse;
import com.trs.yq.police.subject.domain.vo.CameraVO;
import com.trs.yq.police.subject.domain.vo.CompositeDynamicVO;
import com.trs.yq.police.subject.domain.vo.Coordinate;
import com.trs.yq.police.subject.domain.vo.FaceHitVO;
import com.trs.yq.police.subject.domain.vo.GroupListItem;
import com.trs.yq.police.subject.domain.vo.MapInfoVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.ProcessMonitoringVO;
import com.trs.yq.police.subject.domain.vo.RiskJudgeVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.BattleRecordRepository;
import com.trs.yq.police.subject.repository.BattleReplyRepository;
import com.trs.yq.police.subject.repository.BattleUserRepository;
import com.trs.yq.police.subject.repository.EventGroupRelationRepository;
import com.trs.yq.police.subject.repository.EventPersonRelationRepository;
import com.trs.yq.police.subject.repository.EventRepository;
import com.trs.yq.police.subject.repository.GroupClueRelationRepository;
import com.trs.yq.police.subject.repository.HandleRepository;
import com.trs.yq.police.subject.repository.HybaseDao;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.service.HkService;
import com.trs.yq.police.subject.service.VisualControlService;
import com.trs.yq.police.subject.utils.BeanUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * 可视化管控业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/16 15:09
 */
@Slf4j
@Service
public class VisualControlServiceImpl implements VisualControlService {

    @Resource
    private EventRepository eventRepository;

    @Resource
    private EventPersonRelationRepository eventPersonRelationRepository;

    @Resource
    private EventGroupRelationRepository eventGroupRelationRepository;

    @Resource
    private HandleRepository handleRepository;

    @Resource
    private DictService dictService;

    @Resource
    private HybaseDao hybaseDao;

    @Resource
    private BattleReplyRepository battleReplyRepository;

    @Resource
    private BattleUserRepository battleUserRepository;

    @Resource
    private BattleRecordRepository battleRecordDao;

    @Resource
    private GroupClueRelationRepository groupClueRelationRepository;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private LabelRepository labelRepository;


    @Resource
    private HkService hkService;

    @Override
    public RiskDetailResponse getRiskDetail(String eventId, Pageable pageable) {

        // 校验事件是否存在
        EventEntity eventEntity = isEventExist(eventId);

        RiskDetailResponse response = new RiskDetailResponse();

        response.setEventId(eventId);
        response.setAppealPlace(eventEntity.getAppealPlace());
        response.setPersonCount(eventEntity.getEstimatePersonCount());
        response.setRiskLevel(eventEntity.getRiskLevel());

        // 事件群体信息
        response.setGroupType(eventGroupRelationRepository.findAllGroupNameByEvent(eventId));

        // 人员信息
        final Page<RiskDetailDto> detailPage = eventPersonRelationRepository.findAllByEventId(eventId, pageable);

        final List<RiskDetailDto> dtoList = detailPage.getContent();

        final List<RiskDetailPersonResponse> responses = dtoList.stream().map(RiskDetailPersonResponse::new)
            .collect(Collectors.toList());

        response.setParticipants(responses);
        response.setPageNumber(detailPage.getNumber() + 1);
        response.setPageSize(detailPage.getPageable().getPageSize());
        response.setTotal(detailPage.getTotalElements());

        // 查询行为
        final Map<String, String> eventBehavior = dictService.getDictEntitiesByType("event_behavior").stream()
            .collect(Collectors.toMap(DictEntity::getCode, DictEntity::getName));

        final List<String> behaviorCodes = dtoList.stream().map(RiskDetailDto::getBehaviors).reduce((v1, v2) -> {
            v1.addAll(v2);
            return v1;
        }).orElse(new ArrayList<>());

        final Set<String> behaviorValues = eventBehavior.entrySet().stream()
            .filter(entry -> behaviorCodes.contains(entry.getKey())).map(Map.Entry::getValue)
            .collect(Collectors.toSet());
        response.setBehaviors(behaviorValues);

        return response;
    }

    @Override
    public PageResult<RiskDetailPersonResponse> getRisDetailPersonList(String eventId, Pageable pageable) {
        Page<RiskDetailDto> detailPage = eventPersonRelationRepository.findAllByEventId(eventId, pageable);

        final List<RiskDetailDto> dtoList = detailPage.getContent();

        final List<RiskDetailPersonResponse> responses = dtoList.stream().map(item -> {
            RiskDetailPersonResponse riskDetailPersonResponse = new RiskDetailPersonResponse(item);
            riskDetailPersonResponse.setPhoto("/dispatchWarning/getPhoto/" + item.getIdNumber());
            return riskDetailPersonResponse;
        }).collect(Collectors.toList());

        return PageResult.of(responses, pageable.getPageNumber() + 1, (int) detailPage.getTotalElements(),
            pageable.getPageSize());
    }

    @Override
    public RiskJudgeVO getRiskJudgeInfo(String eventId) {
        // 校验事件是否存在
        EventEntity eventEntity = isEventExist(eventId);
        RiskJudgeVO riskJudgeVO = new RiskJudgeVO();
        riskJudgeVO.setEventId(eventId);
        riskJudgeVO.setAppealPlace(
            StringUtils.isEmpty(eventEntity.getAppealPlace()) ? "" : eventEntity.getAppealPlace());
        riskJudgeVO.setRiskLevel(eventEntity.getRiskLevel());
        // 事件群体信息
        List<String> groupType = new ArrayList<>();
        eventGroupRelationRepository.findGroupByEvent(eventId)
            .forEach(item -> groupType.addAll(labelRepository.findNamesByGroupId(item.getId())));
        riskJudgeVO.setGroupType(groupType);

        // 查询行为
        final Map<String, String> eventBehavior = dictService.getDictEntitiesByType("event_behavior").stream()
            .collect(Collectors.toMap(DictEntity::getCode, DictEntity::getName));
        List<EventPersonRelationEntity> personRelationEntities = eventPersonRelationRepository.findAllByEventId(
            eventId);
        riskJudgeVO.setPersonCount(personRelationEntities.size());
        final List<String> behaviorCodes = personRelationEntities.stream().map(EventPersonRelationEntity::getBehaviors)
            .flatMap(List::stream).collect(Collectors.toList());

        final List<String> behaviorValues = eventBehavior.entrySet().stream()
            .filter(entry -> behaviorCodes.contains(entry.getKey())).map(Map.Entry::getValue)
            .collect(Collectors.toList());
        riskJudgeVO.setBehaviors(behaviorValues);
        return riskJudgeVO;
    }

    private EventEntity isEventExist(String eventId) {
        return eventRepository.findById(eventId).orElseThrow(() -> new ParamValidationException("事件不存在，请核实！"));
    }

    @Override
    public OccurredEventResponse getOccurredEvent(String eventId) {
        EventEntity eventEntity = isEventExist(eventId);

        OccurredEventResponse occurredEvent = new OccurredEventResponse();
        occurredEvent.setTitle(eventEntity.getTitle());
        occurredEvent.setTime(eventEntity.getOccurrenceTime());
        occurredEvent.setAddress(eventEntity.getAddress());
        occurredEvent.setContent(eventEntity.getContent());

        return occurredEvent;
    }

    @Override
    public List<HandleFeedbackResponse> getHandleFeedback(String eventId) {
        List<HandleFeedbackResponse> result = new ArrayList<>();
        String receiveNum = eventId.replace("event", "");
        HandleEntity feedback = handleRepository.findByReceiveNum(receiveNum);
        if (Objects.isNull(feedback)) {
            return Collections.emptyList();
        }
        result.add(HandleFeedbackResponse.of(feedback));
        return result;
    }

    @Override
    public String getEventLevel(String eventId) {
        List<Map<String, String>> eventLevel = battleReplyRepository.getEventLevel(eventId);
        if (eventLevel.isEmpty()) {
            throw new ParamValidationException("本事件不存在预案等级！！！");
        }
        return eventLevel.get(0).get("PLANCONFID").substring(eventLevel.get(0).get("PLANCONFID").length() - 1);
    }

    @Override
    public MapInfoVO getMapInfo(String eventId) {
        MapInfoVO result = new MapInfoVO();
        EventEntity event = eventRepository.getById(eventId);
        //取出事发地200米范围内的摄像头
        final List<CameraEntity> allVideoCameras = hybaseDao.getVideoCameras(event.getLng(), event.getLat(), 0.2);
        final List<CameraEntity> allImageCameras = hybaseDao.getImageCameras();
        final List<FaceHitEntity> allFaceHits = hybaseDao.getFaceHits(event);
        final Double lat = Double.valueOf(event.getLat());
        final Double lng = Double.valueOf(event.getLng());
        Coordinate eventCoordinate = new Coordinate(lat, lng);
        result.setEventCoordinate(eventCoordinate);
        //取出在事件经纬度200米范围内的摄像头点位
        final List<CameraVO> videoCameras = allVideoCameras.stream().map(CameraVO::of).collect(Collectors.toList());
        result.setVideoCameras(videoCameras);
        final List<CameraVO> photoCameras = allImageCameras.stream()
            .filter(camera -> camera.getCoordinate().distanceTo(eventCoordinate) <= 1000).map(CameraVO::of)
            .collect(Collectors.toList());
        result.setPhotoCameras(photoCameras);
        final List<FaceHitVO> personTrajectories = allFaceHits.stream()
            .filter(face -> face.getCoordinate().distanceTo(eventCoordinate) <= 1000).map(FaceHitVO::of)
            .collect(Collectors.toList());
        result.setPersonTrajectories(personTrajectories);
        return result;
    }

    @Override
    public List<ProcessMonitoringVO> getProcessMonitoring(String eventId) {
        List<String> hostUnitCode = battleRecordDao.findByEventId(eventId).stream().map(BattleRecordEntity::getUnitkey)
            .collect(Collectors.toList());
        List<BattleUserEntity> users = battleUserRepository.findAllByEventId(eventId);
        List<BattleReplyEntity> replies = battleReplyRepository.getByEventId(eventId);
        log.info("hostUnitCode:=====" + hostUnitCode);
        //签到部门中移除主办单位
        List<String> unitNames = users.stream()
            .filter(battleUserEntity -> !hostUnitCode.contains(battleUserEntity.getUnitCode()))
            .map(BattleUserEntity::getUnitName).distinct().collect(Collectors.toList());
        return unitNames.stream().map(unitName -> {
            ProcessMonitoringVO vo = new ProcessMonitoringVO();
            vo.setUnitName(unitName);
            BattleReplyEntity reply = replies.stream().filter(r -> r.getUnitName().equals(unitName)).findFirst()
                .orElse(null);
            vo.setStatus(Objects.isNull(reply) ? "0" : "1");
            vo.setSignTime(Objects.isNull(reply) ? null : reply.getReplyTime());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CompositeDynamicVO> getCompositeDynamic(String eventId) {
        List<BattleReplyEntity> replies = battleReplyRepository.getByEventId(eventId);
        List<CompositeDynamicVO> result = replies.stream().map(reply -> {
            CompositeDynamicVO vo = new CompositeDynamicVO();
            vo.setActionId(reply.getRecordId());
            vo.setUnitName(reply.getUnitName());
            vo.setUserName(reply.getReplyByName());
            vo.setReplyTime(reply.getReplyTime());
            vo.setDetail(reply.getDetail());
            return vo;
        }).sorted(Comparator.comparing(CompositeDynamicVO::getReplyTime).reversed()).collect(Collectors.toList());
        //添加合成 首条记录
        if (!replies.isEmpty()) {
            BattleRecordEntity battleRecordEntity = battleRecordDao.findById(replies.get(0).getRecordId()).orElse(null);
            if (Objects.nonNull(battleRecordEntity)) {
                CompositeDynamicVO first = new CompositeDynamicVO();
                first.setDetail(battleRecordEntity.getDetail());
                first.setReplyTime(battleRecordEntity.getCrttime());
                first.setUnitName(battleRecordEntity.getUnitname());
                first.setActionId(battleRecordEntity.getId());
                first.setUserName(battleRecordEntity.getHostname());
                result.add(first);
            }
        }

        return result;
    }

    @Override
    public List<GroupListItem> getGroupList(String eventId) {
        List<GroupEntity> groupEntities = eventGroupRelationRepository.findGroupByEvent(eventId);
        return groupEntities.stream().map(group -> {
            GroupListItem vo = new GroupListItem();
            vo.setGroupName(group.getName());
            vo.setGroupId(group.getId());
            vo.setClueNumber(groupClueRelationRepository.countAllByGroupId(group.getId()));
            List<PersonEntity> personEntities = personRepository.findAllByGroupId(group.getId());
            vo.setTotalMembership(personEntities.size());
            vo.setNoControlMembership((int) personEntities.stream()
                .filter(person -> MonitorStatusEnum.NOT_MONITOR.getCode().equals(person.getMonitorStatus())).count());
            vo.setEventNumber(eventGroupRelationRepository.countAllByGroupId(group.getId()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public Integer getEvenDisposalStep(String eventId) {
        List<BattleRecordEntity> battleRecords = battleRecordDao.findByEventId(eventId);
        if (battleRecords.isEmpty()) {
            return 3;
        } else {
            return 5;
        }
    }

    @Override
    public CameraVO getCameraInfo(String cameraId) {
        CameraEntity cameraEntity = hybaseDao.getVideoCamera(cameraId);
        CameraVO vo = new CameraVO();
        if (Objects.nonNull(cameraEntity)) {
            BeanUtil.copyPropertiesIgnoreNull(cameraEntity, vo);
        }
        vo.setUrl(hkService.getCameraPreviewUrl(cameraId));
        return vo;
    }
}
