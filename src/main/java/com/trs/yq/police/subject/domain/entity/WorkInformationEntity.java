package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

/**
 * 工作信息实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_WORK_INFORMATION")
public class WorkInformationEntity extends BaseEntity {

    private static final long serialVersionUID = 7429673587553033304L;

    /**
     * 开始上班时间
     */
    private LocalDate workBeginTime;

    /**
     * 结束上班时间
     */
    private LocalDate workEndTime;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 工作地点
     */
    private String workSituation;

    /**
     * 职务
     */
    private String post;

    /**
     * 人员id
     */
    private String personId;
}
