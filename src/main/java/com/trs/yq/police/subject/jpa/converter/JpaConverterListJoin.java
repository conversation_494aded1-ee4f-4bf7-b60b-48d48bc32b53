package com.trs.yq.police.subject.jpa.converter;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import java.util.List;

/**
 * <AUTHOR>
 */
public class JpaConverterListJoin implements AttributeConverter<List<String>, String> {
    private static final String DELIMITER = ",";

    @Override
    public String convertToDatabaseColumn(List<String> attribute) {
        if (attribute == null || attribute.isEmpty()) {
            return null;
        }
        return StringUtils.join(attribute, DELIMITER);
    }

    @Override
    public List<String> convertToEntityAttribute(String dbData) {
        return Lists.newArrayList(StringUtils.split(StringUtils.trimToEmpty(dbData), DELIMITER));
    }
}
