package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.request.TrajectoryListRequest;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.PersonTrajectoryVO;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.service.HkTrajectoryService;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 出入境服务层实现
 *
 * <AUTHOR>
 * @since 2021/9/17
 */
@Service
@Slf4j
public class HkTrajectoryServiceImpl implements HkTrajectoryService {

    @Resource
    private PersonRepository personRepository;
    private static final String ARTEMIS_PATH = "/artemis";

    @Override
    public Integer countTrajectNum(String idNumber) {
        JsonNode trajectoryNode = getJTrajectory(idNumber, null);
        if (Objects.isNull(trajectoryNode)) {
            return 0;
        }
        JsonNode jsonNode = trajectoryNode.get("data").get("total");
        return Objects.nonNull(jsonNode) ? jsonNode.asInt() : 0;
    }

    private JsonNode getJTrajectory(String idNumber, TrajectoryListRequest request) {
        ArtemisConfig.host = "***********";
        // 合作方Key
        ArtemisConfig.appKey = "25916229";
        // 合作方Secret
        ArtemisConfig.appSecret = "HmRFBMzfbxxxxcAlf1nV";

        String url = ARTEMIS_PATH + "/api/aiapplication/v2/face/queryPersonByAttrWithPage";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("pageNo", "1");
        paramMap.put("pageSize", "20");
        paramMap.put("personLibId", "sqp865");
        paramMap.put("certificateNumber", idNumber);
        String body = JsonUtil.toJsonString(paramMap);
        Map<String, String> path = new HashMap<String, String>(2) {
            {
                put("https://", url);
            }
        };
        String response = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, "application/json");
        log.info(response);
        log.info(path.toString());
        log.info(body);
        JsonNode jsonNode = JsonUtil.parseJsonNode(response);
        if (Objects.nonNull(jsonNode)) {
            log.info(jsonNode.asText());
            JsonNode listNode = jsonNode.get("data");
            if (Objects.isNull(listNode)) {
                return null;
            }
            if (Objects.nonNull(listNode.get("list"))) {
                String humanId = listNode.get("list").get(0).get("humanId").asText();
                List<String> humanIds = new ArrayList<>();
                humanIds.add(humanId);
                Map<String, Object> trajectParam = new HashMap<>();
                trajectParam.put("humanIds", humanIds);
                if (Objects.nonNull(request)) {
                    trajectParam.put("pageNum", request.getPageParams().getPageNumber());
                    trajectParam.put("pageSize", request.getPageParams().getPageSize());
                    trajectParam.put("startTime", request.getTimeParams().getBeginTime()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:s.SSS")).concat("+08:00"));
                    trajectParam.put("endTime", request.getTimeParams().getEndTime()
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")).concat("+08:00"));
                } else {
                    trajectParam.put("pageNum", 1);
                    trajectParam.put("pageSize", 10);
                    trajectParam.put("startTime", "1970-10-01T22:52:27.000+08:00");
                    trajectParam.put("endTime",
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS"))
                            .concat("+08:00"));
                }
                String trajectBody = JsonUtil.toJsonString(trajectParam);
                String trajectUrl = ARTEMIS_PATH + "/api/application/dataresource/v2/personArchive/tracerecord/search";
                Map<String, String> trajectPath = new HashMap<String, String>(2) {
                    {
                        put("https://", trajectUrl);
                    }
                };

                log.info(trajectPath.toString());
                log.info(trajectBody);
                String trajectory = ArtemisHttpUtil.doPostStringArtemis(trajectPath, trajectBody, null, null,
                    "application/json");
                return JsonUtil.parseJsonNode(trajectory);
            }
            return jsonNode;
        }
        return null;
    }

    @Override
    public PageResult<PersonTrajectoryVO> traject(String personId, TrajectoryListRequest request) {
        PersonEntity personEntity = personRepository.findById(personId).orElse(new PersonEntity());
        if (Objects.isNull(personEntity.getIdNumber())) {
            return PageResult.empty(request.getPageParams().getPageNumber(), request.getPageParams().getPageSize());
        } else {
            List<PersonTrajectoryVO> list = new ArrayList<>();
            JsonNode jTrajectory = getJTrajectory(personEntity.getIdNumber(), request);
            if (Objects.nonNull(jTrajectory)) {
                log.info(jTrajectory.asText());
                JsonNode data = jTrajectory.get("data");
                for (JsonNode jsonNode : data.get("list")) {
                    List<String> imgUrls = new ArrayList<>();
                    if (!jsonNode.get("faceBkgUrl").asText().equals("null")) {
                        String faceBkgUrl = jsonNode.get("faceBkgUrl").asText();
                        imgUrls.add(faceBkgUrl);
                    }
                    if (!jsonNode.get("bodyBkgUrl").asText().equals("null")) {
                        String bodyBkgUrl = jsonNode.get("bodyBkgUrl").asText();
                        imgUrls.add(bodyBkgUrl);
                    }
                    if (!jsonNode.get("faceUrl").asText().equals("null")) {
                        String faceUrl = jsonNode.get("faceUrl").asText();
                        imgUrls.add(faceUrl);
                    }
                    if (!jsonNode.get("bodyUrl").asText().equals("null")) {
                        String bodyUrl = jsonNode.get("bodyUrl").asText();
                        imgUrls.add(bodyUrl);
                    }
                    PersonTrajectoryVO personTrajectoryVO = new PersonTrajectoryVO();
                    personTrajectoryVO.setImageUrls(imgUrls);

                    String longitude = jsonNode.get("longitude").asText();
                    personTrajectoryVO.setLng(longitude);

                    String latitude = jsonNode.get("latitude").asText();
                    personTrajectoryVO.setLat(latitude);

                    String deviceName = jsonNode.get("deviceName").asText();
                    personTrajectoryVO.setPlace(deviceName);
                    personTrajectoryVO.setAddress(deviceName);
                    personTrajectoryVO.setDetail("");
                    personTrajectoryVO.setSource("海康轨迹");

                    String faceTraceUuid = jsonNode.get("faceTraceUuid").asText();
                    personTrajectoryVO.setId(faceTraceUuid);

                    long datapoolCreateTime = jsonNode.get("datapoolCreateTime").asLong();
                    LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(datapoolCreateTime),
                        ZoneId.systemDefault());
                    personTrajectoryVO.setTime(localDateTime);
                    list.add(personTrajectoryVO);
                }
                return PageResult.of(list, request.getPageParams().getPageNumber(), data.get("total").asLong(),
                    request.getPageParams().getPageSize());

            }
            return PageResult.empty(request.getPageParams().getPageNumber(), request.getPageParams().getPageSize());
        }

    }

}
