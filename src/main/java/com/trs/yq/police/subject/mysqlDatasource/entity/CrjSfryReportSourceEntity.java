package com.trs.yq.police.subject.mysqlDatasource.entity;

import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 20:17
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "lz_feedback")
public class CrjSfryReportSourceEntity {

    @Id
    private String id;

    private String uuid;

    private String phone;

    private String content;

    private String address;

    private String attachment;

    private String creator;

    private Date createTime;

    private Date updateTime;

    private Integer sync;

    private Date syncTime;
}
