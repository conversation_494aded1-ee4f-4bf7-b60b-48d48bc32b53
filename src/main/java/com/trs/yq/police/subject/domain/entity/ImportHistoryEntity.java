package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 导入历史记录
 *
 * <AUTHOR>
 * @date 2021/8/7 22:11
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_IMPORT_HISTORY")
public class ImportHistoryEntity extends BaseEntity {

    private static final long serialVersionUID = 8945348733168571178L;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件扩展名
     */
    private String extensionName;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件类型 0 - initial excel , 1 - success excel, 2 - fail excel
     */
    private String type;

    /**
     * 警种id
     */
    private String subjectId;

    /**
     * 原始excel主键
     */
    private String initialHistoryId;

}
