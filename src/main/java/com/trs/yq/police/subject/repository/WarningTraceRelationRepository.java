package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.WarningTraceRelationEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 预警轨迹关联表
 *
 * <AUTHOR>
 * @date 2021/9/7 16:00
 */
@SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
@Repository
public interface WarningTraceRelationRepository extends BaseRepository<WarningTraceRelationEntity, String> {

    /**
     * 根据预警来查找人员信息
     *
     * @param warningId 预警主键
     * @return 人员信息
     */
    @Query("select p from PersonEntity p left join WarningTrajectoryEntity wt on wt.idNumber = p.idNumber left join WarningTraceRelationEntity wtr on wtr.trajectoryId = wt.id where wtr.warningId = ?1")
    List<PersonEntity> findAllPersonByWarning(String warningId);

    /**
     * 根据预警id查找预警轨迹关联
     *
     * @param warningId 预警主键
     * @return 预警轨迹关联
     */
    List<WarningTraceRelationEntity> findAllByWarningId(String warningId);

    /**
     * 根据预警来查找人员信息
     *
     * @param warningId 预警主键
     * @return 人员信息
     */
//    @Query("select p from PersonEntity p left join WarningTrajectoryEntity wt on wt.idNumber = p.idNumber left join WarningTraceRelationEntity wtr on wtr.trajectoryId = wt.id where wtr.warningId = ?1 ")
    @Query("select person from PersonEntity person where person.id = (select max(p.id) from PersonEntity p left join WarningTrajectoryEntity wt on wt.idNumber = p.idNumber left join WarningTraceRelationEntity wtr on wtr.trajectoryId = wt.id where wtr.warningId = ?1)")
    PersonEntity findPersonByWarning(String warningId);

    /**
     * 根据预警id查看trajectoryId
     *
     * @param warningId 预警id
     * @return trajectoryId 轨迹id
     * <AUTHOR>
     * @date 2021/09/07
     */
    @Query(nativeQuery = true,
        value = "select t1.TRAJECTORY_ID " +
            "from T_PS_WARNING_TRACE_RELATION t1 inner join T_PS_WARNING t2 " +
            "on t1.WARNING_ID = t2.ID " +
            "where t2.ID = :warningId ")
    List<String> findTrajectoryIdIdByWarningId(@Param("warningId") String warningId);

    /**
     * 根据预警id查看idNumber
     *
     * @param trajectoryId 轨迹id
     * @return idNumber 身份证号
     * <AUTHOR>
     * @date 2021/09/07
     */
    @Query(nativeQuery = true,
        value = "select t1.ID_NUMBER " +
            "from T_PS_WARNING_TRAJECTORY t1 " +
            "where t1.ID = :trajectoryId ")
    String findIdNumberByTrajectoryId(@Param("trajectoryId") String trajectoryId);

    /**
     * 根据人员标签id查询预警id
     *
     * @param personLabel 人员标签id
     * @return 预警id
     */
    @Query("select wtr.warningId from WarningTraceRelationEntity wtr "
        + "where wtr.trajectoryId in "
        + "(select wt.id from WarningTrajectoryEntity wt "
        + "join PersonEntity t1 on t1.idNumber = wt.idNumber "
        + "join PersonLabelRelationEntity t2 on t1.id = t2.personId "
        + "where t2.labelId = :personLabel) ")
    List<String> findWarningIdByPersonLabel(@Param("personLabel") String personLabel);
}
