package com.trs.yq.police.subject.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import lombok.ToString;

/**
 * 信息包装返回类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/10/19 20:28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ResponseMessage implements Serializable {

    private static final long serialVersionUID = 4190691097934625057L;
    private int code;

    private boolean success;

    private String message;

    private Object data;

    /**
     * 构造错误的消息体
     *
     * @param msg 消息文本
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage error(String msg) {
        return new ResponseMessage(500, false, msg, null);
    }

    /**
     * 构造错误信息的应答消息
     *
     * @param status 错误状态码
     * @param msg    错误消息
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage errorWithStatus(int status, String msg) {
        return new ResponseMessage(status, false, msg, null);
    }

    /**
     * 构造成功信息的应答消息
     *
     * @param o 消息体
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage ok(Object o) {
        return new ResponseMessage(200, true, null, o);
    }

    /**
     * 构造成功信息的应答消息
     *
     * @param status 状态码
     * @param o      消息体
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage okWithStatus(int status, Object o) {
        return new ResponseMessage(status, true, "操作成功", o);
    }

    /**
     * 构造未授权信息的应答消息
     *
     * @param o 消息体
     * @return {@link ResponseMessage} 返回给请求方的响应消息
     */
    public static ResponseMessage unauthorized(Object o) {
        return new ResponseMessage(401, false, "角色未授权", o);
    }
}
