package com.trs.yq.police.subject.domain.params;

import com.trs.yq.police.subject.constants.enums.TimeRangeEnum;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/08/29
 */
@Data
@Validated
public class TimeParams implements Serializable {

    private static final long serialVersionUID = 2816916009428095387L;

    /**
     * 时间范围 {@link TimeRangeEnum}
     */
    @NotBlank(message = "时间范围不能为空！")
    private String range;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * @return 按照时间范围返回开始时间
     */
    public LocalDateTime getBeginTime() {
        LocalDate today = LocalDate.now();
        TimeRangeEnum rangeEnum = TimeRangeEnum.codeOf(range);
        if (Objects.isNull(rangeEnum)) {
            return null;
        }
        switch (rangeEnum) {
            case ALL:
                return LocalDateTime.parse("2000-01-01T00:00:00");
            case TODAY:
                return today.atStartOfDay();
            case CURRENT_WEEK:
                return today.with(DayOfWeek.MONDAY).atStartOfDay();
            case CURRENT_MONTH:
                return today.with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case CURRENT_SEASON:
                return today.withMonth(today.getMonth().getValue() / 3 * 3).withDayOfMonth(1).atStartOfDay();
            case CURRENT_YEAR:
                return today.with(ChronoField.DAY_OF_YEAR, 1).atStartOfDay();
            case RECENT_DAY:
                return LocalDateTime.now().minusHours(24);
            case RECENT_WEEK:
                return today.minusDays(6).atStartOfDay();
            case RECENT_MONTH:
                return today.minusMonths(1).atStartOfDay();
            case RECENT_SEASON:
                return today.minusMonths(3).atStartOfDay();
            case RECENT_YEAR:
                return today.minusYears(1).atStartOfDay();
            case YESTERDAY:
                return today.minusDays(1).atStartOfDay();
            case LAST_WEEK:
                return today.minusWeeks(1).with(DayOfWeek.MONDAY).atStartOfDay();
            case LAST_MONTH:
                return today.minusMonths(1).with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case LAST_SEASON:
                return today.withMonth(today.getMonth().getValue() / 3 * 3).minusMonths(3).withDayOfMonth(1).atStartOfDay();
            case LAST_YEAR:
                return today.minusYears(1).withDayOfYear(1).atStartOfDay();
            case CUSTOM:
                return beginTime;
            default:
                return null;
        }
    }

    /**
     * @return 按照时间范围返回结束时间
     */
    public LocalDateTime getEndTime() {
        LocalDate today = LocalDate.now();
        TimeRangeEnum rangeEnum = TimeRangeEnum.codeOf(range);
        if (Objects.isNull(rangeEnum)) {
            return null;
        }
        switch (rangeEnum) {
            case ALL:
                return LocalDate.now().plusYears(10).atStartOfDay();
            case TODAY:
            case CURRENT_WEEK:
            case CURRENT_MONTH:
            case CURRENT_SEASON:
            case CURRENT_YEAR:
            case RECENT_DAY:
            case RECENT_WEEK:
            case RECENT_MONTH:
            case RECENT_SEASON:
            case RECENT_YEAR:
                return LocalDateTime.now();
            case YESTERDAY:
                return today.atStartOfDay();
            case LAST_WEEK:
                return today.with(DayOfWeek.MONDAY).atStartOfDay();
            case LAST_MONTH:
                return today.with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            case LAST_SEASON:
                return today.withMonth(today.getMonth().getValue() / 3 * 3).withDayOfMonth(1).atStartOfDay();
            case LAST_YEAR:
                return today.withDayOfYear(1).atStartOfDay();
            case CUSTOM:
                return endTime;
            default:
                return null;
        }
    }

    /**
     * 是否小于或等于开始时间
     *
     * @param date 待判断时间
     * @return 结果
     */
    public boolean isBeforeOrEqualStart(LocalDate date) {
        return date.isBefore(getBeginTime().toLocalDate()) || date.isEqual(getBeginTime().toLocalDate());
    }

    /**
     * 是否小于或等于结束时间
     *
     * @param date 待判断时间
     * @return 结果
     */
    public boolean isBeforeOrEqualEnd(LocalDate date) {
        return date.isBefore(getEndTime().toLocalDate()) || date.isEqual(getEndTime().toLocalDate());
    }

    /**
     * 是否大于或等于开始时间
     *
     * @param date 待判断时间
     * @return 结果
     */
    public boolean isAfterOrEqualStart(LocalDate date) {
        return date.isAfter(getBeginTime().toLocalDate()) || date.isEqual(getBeginTime().toLocalDate());
    }

    /**
     * 是否大于或等于结束时间
     *
     * @param date 待判断时间
     * @return 结果
     */
    public boolean isAfterOrEqualEnd(LocalDate date) {
        return date.isAfter(getEndTime().toLocalDate()) || date.isEqual(getEndTime().toLocalDate());
    }
}
