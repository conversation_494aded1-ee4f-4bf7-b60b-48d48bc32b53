package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 人员-事件批量关联vo
 *
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
public class PersonEventsRelationVO implements Serializable {

    private static final long serialVersionUID = -7690030520107185071L;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 专题id
     */
    @NotBlank(message = "专题id不能为空!")
    private String subjectId;

    /**
     * 群体id
     */
    @NotNull(message = "事件id不能为空！")
    private List<String> eventIds;
}
