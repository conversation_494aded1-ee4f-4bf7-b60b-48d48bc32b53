package com.trs.yq.police.subject.constants.enums;


import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 预警等级对应热力值枚举类
 *
 * <AUTHOR>
 * @date 2021/9/11 9:44
 */
public enum HeatMapEnum {
    /**
     * enums
     */
    BLUE("1", 20),

    YELLOW("2", 40),

    ORANGE("3", 60),

    RED("4", 80);

    @Getter
    private final String code;

    @Getter
    private final Integer value;

    HeatMapEnum(String code, Integer value) {
        this.code = code;
        this.value = value;
    }


    /**
     * 通过预警等级获取枚举
     *
     * @param code 模块码
     * @return {@link HeatMapEnum}
     */
    public static HeatMapEnum codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            return Arrays.stream(HeatMapEnum.values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
}
