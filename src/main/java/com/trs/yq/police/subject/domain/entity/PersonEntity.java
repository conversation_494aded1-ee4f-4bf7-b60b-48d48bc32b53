package com.trs.yq.police.subject.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Objects;

/**
 * 人员档案实体类
 *
 * <AUTHOR>
 * @since 2021/7/27 14:11
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON")
public class PersonEntity extends BaseEntity {

    private static final long serialVersionUID = 120326850769087675L;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名", order = 0)
    private String name;

    /**
     * 身份证号码
     */
    @ExcelProperty(value = "证件号码", order = 2)
    private String idNumber;
    /**
     * 身份证号码
     */
    @ExcelProperty(value = "证件类型", order = 2)
    private String idType;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别", order = 3)
    private String gender;

    /**
     * 曾用名
     */
    @ExcelProperty(value = "曾用名", order = 4)
    private String formerName;

    /**
     * 绰号
     */
    @ExcelProperty(value = "绰号", order = 5)
    private String nickName;

    /**
     * 民族 码表类型：nation
     */
    @ExcelProperty(value = "民族", order = 6)
    private String nation;

    /**
     * 政治面貌 码表：ps_political_status
     */
    @ExcelProperty(value = "政治面貌", order = 7)
    private String politicalStatus;


    /**
     * 宗教信仰
     */
    @ExcelProperty(value = "宗教信仰", order = 8)
    private String religiousBelief;

    /**
     * 婚姻状态 码表类型：ps_marital_status_group
     */
    @ExcelProperty(value = "婚姻状态", order = 9)
    private String maritalStatus;

    /**
     * 现职业
     */
    @ExcelProperty(value = "现职业", order = 10)
    private String currentJob;

    /**
     * 联系方式
     */
    @ExcelProperty(value = "联系方式", order = 11)
    private String contactInformation;

    /**
     * 户籍地
     */
    @ExcelProperty(value = "户籍地", order = 12)
    private String registeredResidence;

    /**
     * 现住地
     */
    @ExcelProperty(value = "现住址", order = 13)
    private String currentResidence;

    /**
     * 管控状态 码表类型：ps_control_status
     */
    @ExcelProperty(value = "管控状态", order = 14)
    private String controlStatus;

    /**
     * 管控级别 码表类型：ps_zb_control_level_group
     */
    @ExcelProperty(value = "管控级别", order = 15)
    private String controlLevel;

    /**
     * 布控状态 码表类型：ps_monitor_status
     */
    private String monitorStatus = "1";

    /**
     * 基本情况
     */
    @ExcelProperty(value = "基本情况")
    private String basicInfo;

    /**
     * 地区编码
     */
    private String areaCode;

    /**
     * 是否在控
     */
    private String isInControl;

    /**
     * 重点管控依据
     */
    private String keyControlBasis;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PersonEntity that = (PersonEntity) o;
        return name.equals(that.name) && idNumber.equals(that.idNumber) && Objects.equals(gender, that.gender)
            && Objects.equals(formerName, that.formerName) && Objects.equals(nickName, that.nickName) && Objects.equals(
            nation, that.nation) && Objects.equals(politicalStatus, that.politicalStatus) && Objects.equals(
            religiousBelief, that.religiousBelief) && Objects.equals(maritalStatus, that.maritalStatus)
            && Objects.equals(currentJob, that.currentJob) && Objects.equals(contactInformation,
            that.contactInformation) && Objects.equals(registeredResidence, that.registeredResidence) && Objects.equals(
            currentResidence, that.currentResidence) && Objects.equals(controlStatus, that.controlStatus)
            && Objects.equals(controlLevel, that.controlLevel) && Objects.equals(basicInfo, that.basicInfo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, idNumber, gender, formerName, nickName, nation, politicalStatus, religiousBelief,
            maritalStatus, currentJob, contactInformation, registeredResidence, currentResidence, controlStatus,
            controlLevel, basicInfo);
    }
}
