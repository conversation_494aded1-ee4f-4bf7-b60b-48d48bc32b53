package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.MobilePhoneVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 电话信息业务层接口
 *
 * <AUTHOR>
 * @date 2021/7/28 9:47
 */
public interface MobilePhoneService {
    /**
     * 查询该人员电话信息
     *
     * @param personId 人员Id
     * @return 电话信息
     */
    List<MobilePhoneVO> findByPersonId(@NotBlank(message = "人员id缺失") String personId);

    /**
     * 根据id删除电话信息
     *
     * @param personId 人员id
     * @param id       主键
     */
    void deletePhone(@NotBlank(message = "人员id缺失") String personId, @NotBlank(message = "电话号id缺失") String id);

    /**
     * 添加电话信息
     *
     * @param personId      人员主键
     * @param mobilePhoneVO 电话信息实体
     */
    void savePhone(@NotBlank(message = "人员主键缺失") String personId, @NotNull(message = "电话基础信息缺失") MobilePhoneVO mobilePhoneVO);

    /**
     * 更新操作
     *
     * @param personId      人员主键
     * @param mobilePhoneVO 电话实体
     */
    void updatePhone(@NotBlank(message = "人员主键缺失") String personId, @NotNull(message = "电话基础信息缺失") MobilePhoneVO mobilePhoneVO);
}
