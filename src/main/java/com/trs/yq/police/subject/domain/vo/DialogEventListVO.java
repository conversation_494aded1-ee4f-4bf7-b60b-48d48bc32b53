package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * 事件关联对话框查询接口返回vo
 *
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
public class DialogEventListVO implements Serializable {

    private static final long serialVersionUID = -1929718223643935755L;

    /**
     * 线索id
     */
    private String eventId;
    /**
     * 线索名称
     */
    private String eventName;
    /**
     * 线索类别
     */
    private String eventType;
    /**
     * 主管单位
     */
    private String controlDeptName;

    /**
     * 创建vo
     *
     * @param eventEntity {@link ClueEntity}
     * @return {@link DialogClueListVO}
     */
    public static DialogEventListVO of(EventEntity eventEntity) {
        DialogEventListVO vo = new DialogEventListVO();
        vo.setEventId(eventEntity.getId());
        vo.setEventName(eventEntity.getTitle());
        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        LabelEntity eventType = labelRepository.findByEventId(eventEntity.getId());
        if(Objects.nonNull(eventType)) {
            vo.setEventType(eventType.getName());
        }
        vo.setControlDeptName(eventEntity.getControlDeptName());
        return vo;
    }
}
