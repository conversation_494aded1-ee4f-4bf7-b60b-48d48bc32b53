package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/12/14 17:20
 */
@Data
public class EventExportListVO implements Serializable {
    private static final long serialVersionUID = -1421711842036416878L;

    @ExcelProperty(value = "事件名称", order = 0)
    private String title;

    @ExcelProperty(value = "事件来源", order = 1)
    private String source;

    @ExcelProperty(value = "事件级别", order = 2)
    private String emergencyLevel;

    @ExcelProperty(value = "事发时间", order = 3)
    private String occurrenceTime;

    @ExcelProperty(value = "事发地点", order = 4)
    private String address;

    @ExcelProperty(value = "诉求地", order = 5)
    private String appealPlace;

    @ExcelProperty(value = "涉事群体名称", order = 6)
    private String groupNames;

}
