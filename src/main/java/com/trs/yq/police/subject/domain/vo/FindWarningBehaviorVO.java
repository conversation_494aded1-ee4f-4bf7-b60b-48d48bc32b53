package com.trs.yq.police.subject.domain.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 发现预警行为VO
 *
 * <AUTHOR>
 * @date 2021/9/7 16:41
 */
@Data
public class FindWarningBehaviorVO {

    /**
     * 预警id
     */
    private String warningId;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 地点
     */
    private String warningPlace;
    /**
     * 异常行为标签
     */
    private String warningLabel;
    /**
     * 时间
     */
    private LocalDateTime warningTime;
    /**
     * 图片
     */
    private List<ImageVO> images;
    /**
     * 预警等级
     */
    private String warningLevel;

    /**
     * 人员id
     */
    private String personId;
}
