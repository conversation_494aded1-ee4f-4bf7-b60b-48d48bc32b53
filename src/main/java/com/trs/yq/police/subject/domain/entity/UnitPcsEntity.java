package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/06
 **/

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_UNIT_PCS")
public class UnitPcsEntity implements Serializable {

    private static final long serialVersionUID = 2418327796597978149L;

    /**
     * 主键，单位代码
     */
    @Id
    @Column(name = "unit_code")
    private String unitCode;

    /**
     * 单位名称
     */
    @Column(name = "unit_name")
    private String unitName;

    /**
     * 单位简称
     */
    @Column(name = "short_name")
    private String shortName;

    /**
     * 区域代码
     */
    @Column(name = "parent_code")
    private String parentCode;

    /**
     * 树状等级
     */
    @Column(name = "type")
    private Integer type;

}
