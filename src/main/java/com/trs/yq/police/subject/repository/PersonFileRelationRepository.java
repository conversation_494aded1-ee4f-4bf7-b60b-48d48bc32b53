package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonFileRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 人员文件关系表
 *
 * <AUTHOR>
 * @date 2021/7/30 13:32
 */
@Repository
public interface PersonFileRelationRepository extends BaseRepository<PersonFileRelationEntity, String> {

    /**
     * 根据personId,type,module查询
     *
     * @param personId 人员id
     * @param type     文件类型
     * @param module   模块
     * @param recordId 关联记录id
     * @return 所有关联的文件
     */
    @Query("SELECT r FROM PersonFileRelationEntity r"
            + " WHERE r.personId=:personId"
            + " AND r.type = :type"
            + " AND r.module=:module"
            + " AND (:recordId is null or r.recordId=:recordId)")
    List<PersonFileRelationEntity> findAll(@Param("personId") String personId,
                                           @Param("type") String type,
                                           @Param("module") String module,
                                           @Param("recordId") String recordId);

    /**
     * 通过人员主键查询所有
     *
     * @param personId 人物主键
     * @return 关系
     */
    List<PersonFileRelationEntity> findAllByPersonId(@Param("personId") String personId);

    /**
     * 通过人员主键和模块查找图片主键
     *
     * @param personId 人员主键
     * @param module   模块
     * @param recordId 记录主键
     * @return 关系实体
     */
    List<PersonFileRelationEntity> findAllByPersonIdAndModuleAndRecordId(@Param("personId") String personId,
                                                                         @Param("module") String module,
                                                                         @Param("recordId") String recordId);

    /**
     * 通过人员主键移除
     *
     * @param personId 人员id
     */
    @Modifying
    void removeByPersonId(@Param("personId") String personId);
}
