package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 图片上传结果
 *
 * <AUTHOR>
 * @date 2021/8/2 17:35
 */
@Getter
@Setter
@ToString
public class ImageVO implements Serializable {

    private static final long serialVersionUID = 4999327218649432405L;

    /**
     * 图片路径
     */
    private String url;

    /**
     * 图片id
     */
    private String imageId;

    /**
     * 根据FileStorageEntity生成ImageVO
     *
     * @param file entity
     * @return vo
     */
    public static ImageVO of(FileStorageEntity file) {
        ImageVO image = new ImageVO();
        image.setImageId(file.getId());
        image.setUrl(file.getUrl());
        return image;
    }

}
