package com.trs.yq.police.subject.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.yq.police.subject.domain.entity.StqzClueEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StqzClueMapper {

    /**
     * 根据时间范围分页查询线索数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 分页
     * @return 线索数据列表
     */
    IPage<StqzClueEntity> selectByTimeWithPage(@Param("startTime") String startTime,
                                               @Param("endTime") String endTime,
                                               @Param("page") IPage page);
}