package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.entity.DictEntity;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.service.impl.DictServiceImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 码表查询接口
 *
 * <AUTHOR>
 * @date 2021/08/02
 */

@RestController
@RequestMapping("/dict")
@Validated
public class DictController {

    @Resource
    private DictServiceImpl dictService;

    /**
     * 码表查询
     * http://192.168.200.192:3001/project/4897/interface/api/129750
     *
     * @param type 码表类型
     * @return 码表键值对
     */
    @GetMapping("")
    //@Cacheable(value = "yq-dict", key = "#type")
    public List<KeyValueVO> getDict(@NotBlank(message = "type不能为空") String type) {
        List<DictEntity> entities = dictService.getDictEntitiesByType(type);
        return entities.stream().map(entity -> new KeyValueVO(entity.getCode(), entity.getName())).collect(Collectors.toList());
    }
}
