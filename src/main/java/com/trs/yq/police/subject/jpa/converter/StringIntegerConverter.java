package com.trs.yq.police.subject.jpa.converter;

import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * 用于历史建表时选择的数值类型错误
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/21 14:16
 */
@Converter
public class StringIntegerConverter implements AttributeConverter<String, Integer> {

    @Override
    public Integer convertToDatabaseColumn(String s) {
        if (StringUtils.isBlank(s)){
            return null;
        }
        return Integer.parseInt(s);
    }

    @Override
    public String convertToEntityAttribute(Integer integer) {
        return String.valueOf(integer);
    }
}
