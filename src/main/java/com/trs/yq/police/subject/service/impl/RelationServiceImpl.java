package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.RelationEntity;
import com.trs.yq.police.subject.domain.vo.RelationInfoVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.RelationRepository;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.RelationService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.AUTOMATED;

/**
 * 人员关系业务类
 *
 * <AUTHOR>
 * @date 2021/7/27 17:31
 */
@Service
public class RelationServiceImpl implements RelationService {
    @Resource
    private RelationRepository relationRepository;
    @Resource
    private PersonService personService;
    @Resource
    private OperationLogHandler operationLogHandler;
    private static final String RELATE_TYPE_FAMILY = "family";

    @Override
    public List<RelationInfoVO> getRelationInfo(String personId, String type) {
        //检查人员是否存在
        personService.checkPersonExist(personId);
        List<RelationEntity> relationEntity = relationRepository.findByPersonIdAndType(personId, type);
        if (Objects.nonNull(relationEntity)) {
            List<RelationInfoVO> relationInfoVOList = new ArrayList<>();
            relationEntity.forEach(item -> {
                RelationInfoVO relationInfoVo = new RelationInfoVO();
                BeanUtil.copyPropertiesIgnoreNull(item, relationInfoVo);
                relationInfoVOList.add(relationInfoVo);
            });
            return relationInfoVOList;
        }
        return Collections.emptyList();
    }

    @Override
    public void saveRelation(String personId, RelationEntity relationEntity) {
        personService.checkPersonExist(relationEntity.getPersonId());
        final OperateModule module = relationEntity.getType().equals(RELATE_TYPE_FAMILY)
                ? OperateModule.FAMILY_RELATIONSHIP : OperateModule.SOCIAL_RELATIONSHIP;
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(module)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc(Operator.ADD.getName() + module.getName())
                .newObj(JsonUtil.toJsonString(relationEntity))
                .build();
        relationRepository.save(relationEntity);
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void deleteRelation(String personId, String id) {
        personService.checkPersonExist(personId);
        final RelationEntity relation = relationRepository.findById(id).orElse(null);
        if (Objects.isNull(relation)) {
            throw new ParamValidationException("待移除的关系不存在，请刷新后核实");
        }

        if (org.apache.commons.lang3.StringUtils.equals(relation.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可删除");
        }
        final OperateModule module = RELATE_TYPE_FAMILY.equals(relation.getType())
                ? OperateModule.FAMILY_RELATIONSHIP : OperateModule.SOCIAL_RELATIONSHIP;
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(module)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc(Operator.DELETE.getName() + module.getName())
                .oldObj(JsonUtil.toJsonString(relation))
                .build();
        relationRepository.deleteById(id);
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void updateRelation(String personId, RelationEntity relationEntity) {
        // 校验人员是否存在
        personService.checkPersonExist(personId);

        RelationEntity oldRelation = relationRepository.findById(relationEntity.getId()).orElse(null);
        // 校验关系是否存在
        if (Objects.isNull(oldRelation)) {
            throw new ParamValidationException("关联人信息不存在，请核实!");
        }

        if (org.apache.commons.lang3.StringUtils.equals(oldRelation.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可修改");
        }
        // 操作记录
        final OperateModule module = RELATE_TYPE_FAMILY.equals(relationEntity.getType())
                ? OperateModule.FAMILY_RELATIONSHIP : OperateModule.SOCIAL_RELATIONSHIP;
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(module)
                .currentUser(AuthHelper.getCurrentUser())
                .desc(Operator.EDIT.getName() + module.getName())
                .primaryKey(personId)
                .oldObj(JsonUtil.toJsonString(oldRelation))
                .build();

        // 变更数据
        oldRelation.setRelation(relationEntity.getRelation());
        oldRelation.setType(relationEntity.getType());
        oldRelation.setName(relationEntity.getName());
        oldRelation.setIdNumber(relationEntity.getIdNumber());
        oldRelation.setCurrentResidence(relationEntity.getCurrentResidence());
        oldRelation.setContactInformation(relationEntity.getContactInformation());
        relationRepository.save(oldRelation);
        logRecord.setNewObj(JsonUtil.toJsonString(oldRelation));
        // 记录操作
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

}
