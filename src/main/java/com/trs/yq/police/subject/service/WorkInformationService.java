package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.WorkInformationVO;

import java.util.List;

/**
 * 工作信息类业务层接口
 *
 * <AUTHOR>
 */
public interface WorkInformationService {

    /**
     * 获取人员工作信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link WorkInformationVO}
     * <AUTHOR>
     */
    List<WorkInformationVO> getAllByPersonId(String personId);

    /**
     * 删除人员工作信息
     *
     * @param personId 人员id
     * @param jobId    工作id
     * <AUTHOR>
     */
    void deleteOne(String personId, String jobId);

    /**
     * 增加人员的工作信息
     *
     * @param personId          人员id
     * @param workInformationVO 人员工作信息
     * <AUTHOR>
     */
    void addOne(String personId, WorkInformationVO workInformationVO);

    /**
     * 更新人员工作信息
     *
     * @param personId          人员主键
     * @param workInformationVO 人员工作信息
     * <AUTHOR>
     */
    void updateOne(String personId, WorkInformationVO workInformationVO);
}
