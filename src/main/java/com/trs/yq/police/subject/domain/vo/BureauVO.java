package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 指派分局视图层实体类
 *
 * <AUTHOR>
 * @since 2021/8/5
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class BureauVO implements Serializable {

    private static final long serialVersionUID = -3958976892067732071L;

    /**
     * 指派分局code
     */
    @NotBlank(message = "分局代码不能为空")
    private String bureauCode;
    /**
     * 指派分局名称
     */
    @NotBlank(message = "分局名称不能为空")
    private String bureauName;
}
