package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 人员|群体  公用扩展信息
 *
 * <AUTHOR>
 * @date 2021/12/8 14:32
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_COMMON_EXTEND")
public class CommonExtentEntity extends BaseEntity {
    private static final long serialVersionUID = -1611615621087478964L;

    public static  final String PERSON="person";
    public static  final String GROUP="group";
    /**
     * 模块： group|person
     */
    private String module;
    /**
     * 关联的id
     */
    private String recordId;
    /**
     * 主要诉求
     */
    private String mainDemand;

    /**
     * 被依法处理情况
     */
    private String treatment;

    /**
     * 政府主管部门
     */
    private String governmentDepartment;

    /**
     * 政府领导姓名
     */
    private String governmentLeaderName;

    /**
     * 政府领导职务
     */
    private String governmentLeaderJob;

    /**
     * 政府领导联系方式
     */
    private String governmentLeaderTelephone;

    /**
     * 政府责任人姓名
     */
    private String governmentDutyName;

    /**
     * 政府责任人职务
     */
    private String governmentDutyJob;

    /**
     * 政府责任人联系方式
     */
    private String governmentDutyTelephone;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 籍贯
     */
    private String nativePlace;
}
