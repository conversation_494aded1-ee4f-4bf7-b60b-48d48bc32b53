package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.CaseEventVO;

import java.util.List;

/**
 * 案事件信息业务层接口
 *
 * <AUTHOR>
 */
public interface CaseEventService {

    /**
     * 查询人员案事件信息
     *
     * @param personId 人员id
     * @return 人员基本信息 {@link CaseEventVO}
     * <AUTHOR>
     */
    List<CaseEventVO> getAllCaseEvent(String personId);


    /**
     * 增加该人员一条案事件信息
     *
     * @param personId    人员主键
     * @param caseEventVO 案事件信息
     * <AUTHOR>
     */
    void addCaseEvent(String personId, CaseEventVO caseEventVO);

}
