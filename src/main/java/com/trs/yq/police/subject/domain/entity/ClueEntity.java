package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 线索信息类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CLUE")
public class ClueEntity extends BaseEntity {

    private static final long serialVersionUID = -9219986746193871581L;

    /**
     * 线索名称
     */
    private String name;

    /**
     * 线索来源
     */
    private String source;

    /**
     * 紧急程度
     */
    private String emergencyLevel;

    /**
     * 线索详情
     */
    private String detail;

    /**
     * 公开程度
     */
    private String opennessLevel;

    /**
     * 主题id
     */
    private String subjectId;
    /**
     * 线索状态：0,1,2 待处置,预警,已处置,不处置)
     */
    private String disposalStatus;
    /**
     * 上报状态
     */
    private String reportStatus;

    /**
     * 线索编号
     */
    private String code;

    /**
     * 线索id
     */
    @Transient
    private String xsid;
}
