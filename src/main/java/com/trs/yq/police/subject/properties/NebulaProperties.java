package com.trs.yq.police.subject.properties;

import java.util.ArrayList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import lombok.Data;

/**
 * nebula连接相关参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 **/

@Data
@Configuration
@ConfigurationProperties(prefix = "com.trs.person-associate.nebula")
public class NebulaProperties {

    private String host = "***********";

    private Integer port = 9669;

    List<HostAndPort> hostAddressList = new ArrayList<>();

    private String username = "root";

    private String password = "nebula";

    /**
     * 最小连接数
     */
    private int minConnectionsSize = 0;
    /**
     * 最大连接数
     */
    private int maxConnectionsSize = 10;
    /**
     * 超时时间（毫秒，0代表不超时）
     */
    private int timeout = 0;
    /**
     * 最大空闲时间（毫秒）
     */
    private int idleTime = 0;

    /**
     * nebula服务器地址端口信息
     */
    @Data
    public static class HostAndPort {
        private String host;
        private int port;
    }

}
