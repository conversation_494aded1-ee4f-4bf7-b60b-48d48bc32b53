package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.GridMangerEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/7/28 15:25
 */
@Repository
public interface GridMangerRepository extends BaseRepository<GridMangerEntity, String> {

    /**
     * 根据电话号查找网格员信息
     *
     * @param gridPhoneNumber 电话号
     * @return {@link GridMangerEntity}
     */
    GridMangerEntity findByGridManagerPhoneNumber(String gridPhoneNumber);

    /**
     * 更新网格员联系电话
     *
     * @param gridId      网格id
     * @param phoneNumber 手机号
     */
    @Modifying
    @Query(nativeQuery = true, value = "update T_PS_CRJ_GRID_MANGER p set p.GRID_MANAGER_PHONE_NUMBER  = :phoneNumber where p.ID = :gridId")
    void updateGridPhoneNumber(@Param("gridId") String gridId, @Param("phoneNumber") String phoneNumber);
}
