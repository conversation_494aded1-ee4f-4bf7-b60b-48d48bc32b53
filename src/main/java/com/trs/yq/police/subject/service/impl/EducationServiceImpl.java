package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.EducationEntity;
import com.trs.yq.police.subject.domain.vo.EducationVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.EducationRepository;
import com.trs.yq.police.subject.service.EducationService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 教育信息业务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class EducationServiceImpl implements EducationService {
    @Resource
    private EducationRepository educationRepository;
    @Resource
    private OperationLogHandler operationLogHandler;


    @Override
    public List<EducationVO> findAllEducation(String personId) {
        List<EducationEntity> list = educationRepository.findAllByPersonId(personId);
        List<EducationVO> educationVOList = new ArrayList<>();
        for (EducationEntity entity : list) {
            EducationVO educationVO = new EducationVO();
            educationVO.setEducationId(entity.getId());
            educationVO.setSubject(entity.getSubject());
            educationVO.setGraduationSchool(entity.getSchool());
            educationVO.setBeginTime(entity.getBeginTime());
            educationVO.setEndTime(entity.getEndTime());
            educationVOList.add(educationVO);
        }
        //对educationVOList进行结束时间排序
        educationVOList = educationVOList
                .stream()
                .sorted(Comparator.comparing(EducationVO::getBeginTime).reversed())
                .collect(Collectors.toList());
        return educationVOList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteEducation(String personId, String educationId) {

        final EducationEntity education = educationRepository.findById(educationId).orElse(null);
        if (Objects.isNull(education)) {
            throw new ParamValidationException("教育信息不存在，请刷新核实");
        }

        // 操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.EDUCATION_BACKEND)
                .currentUser(AuthHelper.getCurrentUser())
                .desc("删除教育信息")
                .primaryKey(personId)
                .oldObj(JsonUtil.toJsonString(education))
                .build();
        educationRepository.deleteById(educationId);
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addEducation(String personId, EducationVO educationVO) {
        EducationEntity entity = voTransformEntity(new EducationEntity(), educationVO);
        entity.setPersonId(personId);
        final int recordCount = educationRepository.countAllByPersonId(entity.getPersonId());
        final int maxSize = 10;
        if (recordCount >= maxSize) {
            throw new ParamValidationException("不能在添加过多的教育信息!");
        }
        // 操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.EDUCATION_BACKEND)
                .currentUser(AuthHelper.getCurrentUser())
                .desc("新增教育信息")
                .primaryKey(personId)
                .newObj(JsonUtil.toJsonString(entity))
                .build();
        educationRepository.save(entity);
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    /**
     * vo转entity
     *
     * @param educationVO vo教育信息
     * @param entity      entity教育信息
     * <AUTHOR>
     */
    private EducationEntity voTransformEntity(EducationEntity entity, EducationVO educationVO) {
        entity.setBeginTime(educationVO.getBeginTime());
        entity.setEndTime(educationVO.getEndTime());
        entity.setSchool(educationVO.getGraduationSchool());
        entity.setSubject(educationVO.getSubject());
        return entity;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateEducation(String personId, EducationVO educationVO) {

        final String educationId = educationVO.getEducationId();
        final EducationEntity educationEntity = educationRepository.findById(educationId).orElse(null);
        // 校验教育信息是否存在
        if (Objects.isNull(educationEntity)) {
            throw new ParamValidationException("教育信息不存在，请刷新核实");
        }

        EducationEntity entity = voTransformEntity(new EducationEntity(), educationVO);
        entity.setPersonId(personId);
        entity.setId(educationId);
        // 操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.EDUCATION_BACKEND)
                .currentUser(AuthHelper.getCurrentUser())
                .desc("编辑教育信息")
                .primaryKey(personId)
                .newObj(JsonUtil.toJsonString(entity))
                .oldObj(JsonUtil.toJsonString(educationEntity))
                .build();
        // 更新数据
        educationEntity.setBeginTime(entity.getBeginTime());
        educationEntity.setEndTime(entity.getEndTime());
        educationEntity.setSchool(entity.getSchool());
        educationEntity.setSubject(entity.getSubject());
        educationRepository.save(educationEntity);
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }

    }
}
