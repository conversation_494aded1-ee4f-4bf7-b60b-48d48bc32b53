package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BattleCommandEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/13 10:04
 */
@Repository
public interface BattleCommandRepository extends BaseRepository<BattleCommandEntity, String> {
    /**
     * 根据线索id 查询指令信息(已发布、已完成)
     *
     * @param id       线索id
     * @param srcTable 事件资源表
     * @return {@link BattleCommandEntity}
     */
    @Query(nativeQuery = true, value = "SELECT br.* FROM T_BATTLE_COMMAND br WHERE " +
            " EXISTS (" +
            "   SELECT 1 FROM T_BATTLE_EVENT be " +
            "   WHERE be.SRCTABLE= :srcTable " +
            "   AND be.KEYNAME='ID' " +
            "   AND be.KEYVAL=:id " +
            "   AND INSTR(br.eventids,be.ID)> 0)" +
            "   AND br.STATE in (2, 4) ")
    List<BattleCommandEntity> findCommandById(@Param("srcTable") String srcTable, @Param("id") String id);
}
