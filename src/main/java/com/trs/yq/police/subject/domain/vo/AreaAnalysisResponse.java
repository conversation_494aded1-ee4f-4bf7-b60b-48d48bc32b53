package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 治安 区域分析
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/14 11:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaAnalysisResponse {

    /** 区域 */
    private String area;

    /** 警情 */
    private AreaAnalysisInfoVO jq;

    /** 立案 */
    private AreaAnalysisInfoVO la;

    /** 抓获 */
    private AreaAnalysisInfoVO zh;
}
