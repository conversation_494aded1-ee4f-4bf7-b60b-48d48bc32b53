package com.trs.yq.police.subject.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 裁决信息视图层
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class AdjudicationVO implements Serializable {

    private static final long serialVersionUID = -4365976971447831538L;

    /**
     * 裁决信息id
     */
    private String id;

    /**
     * 裁决日期时间戳
     */
    @NotNull(message = "裁决时间不能为空")
    @JsonProperty("adjudgeDate")
    private LocalDate judgementDate;

    /**
     * 截止日期时间戳
     */
    @NotNull(message = "截止时间不能为空")
    @JsonProperty("closingDate")
    private LocalDate endDate;

    /**
     * 限制年限
     */
    @NotNull(message = "限制年限不能为空")
    @JsonProperty("limitDate")
    private Integer limitTime;

    /**
     * 限制年限单位 YEARS/MONTHS
     */
    @NotBlank(message = "限制年限单位不能为空")
    private String limitUnit;

    /**
     * 裁决原因
     */
    private String reason;
}
