package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.vo.VoiceRecognitionVO;
import com.trs.yq.police.subject.service.VoiceRecognitionService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 智能语音识别接口
 *
 * <AUTHOR>
 * @date 2021/10/14 15:16
 */
@RestController()
public class VoiceRecognitionController {
    @Resource
    private VoiceRecognitionService voiceRecognitionService;
    /**
     * 接口接收语音文件 然后调用第三方接口 将语音转换为文本（格式化文本）
     * 2 判断语音文本转换结果是否是预定义的格式化内容
     * 3 不是格式化内容直接返回固定的结果 响应内容为一段文字，一个语音文件
     * 4 是格式化的内容则 根据格式化模板内容查询数据结果并生成格式化结果字符串 调用第三方接口将文本生成语音 最终将 文字和语音文件返回前端
     *
     * @param voice 音频文件
     * @return {@link VoiceRecognitionVO}
     */
    @PostMapping("/voice")
    public VoiceRecognitionVO getVoiceRecognitionVO(MultipartFile voice) throws IOException {
        return voiceRecognitionService.recognizeVoice(voice);
    }

}
