package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.util.Date;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/30 15:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrjJwryDetailVO extends CrjJwryDetailListVO {

    private String cnName;

    private String enFirstName;

    private String enLastName;
    /**
     * 国家代码
     */
    private String gjdm;
    /**
     * 职业
     */
    private String profession;

    private Date birthday;
    /**
     * 签证信息
     */
    private String visaType;

    private String visaNumber;
    /**
     * 在华停留时间
     */
    private Date inChinaTime;

    /**
     * 入境时间
     */
    private Date entryTime;
    /**
     * 入境口岸
     */
    private String entryPort;

    private CrjJwryVisitVO latestRegisterInfo;


    /**
     * 转entity
     *
     * @return {@link CrjJwryEntity}
     */
    public CrjJwryEntity toJwryEntity() {
        CrjJwryEntity entity = new CrjJwryEntity();
        entity.setIdType(this.getIdType());
        entity.setIdNumber(this.getIdNumber());
        entity.setGjdm(this.getGjdm());
        if (Objects.nonNull(this.getAttachment())){
            entity.setAttachment(JsonUtil.toJsonString(this.getAttachment()));
        }
        return entity;
    }

    /**
     * 转entity
     *
     * @return {@link CrjJwryDetailEntity}
     */
    public CrjJwryDetailEntity toJwryDetailEntity() {
        CrjJwryDetailEntity entity = new CrjJwryDetailEntity();
        entity.setCnName(cnName);
        entity.setEnFirstName(enFirstName);
        entity.setEnLastName(enLastName);
        if (StringUtils.isBlank(this.getEnName())) {
            entity.setEnName(enFirstName + " " + enLastName);
        } else {
            entity.setEnName(this.getEnName());
        }
        entity.setGender(getGender());
        entity.setGjdm(getGjdm());
        entity.setIdType(this.getIdType());
        entity.setIdNumber(this.getIdNumber());
        entity.setBirthday(birthday);
        entity.setProfession(profession);
        entity.setVisaType(visaType);
        entity.setVisaNumber(visaNumber);
        entity.setInChinaTime(inChinaTime);
        entity.setEntryTime(entryTime);
        entity.setEntryPort(entryPort);
        return entity;
    }
}
