package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 18:01
 */
@Repository
public interface CrjJwryRepository extends BaseRepository<CrjJwryEntity, String> {

    /**
     * 修改登记状态
     *
     * @param recordId           id
     * @param registrationStatus 登记状态
     * @param reason             不登记原因
     */
    @Modifying(clearAutomatically = true)
    @Query(
        "update CrjJwryEntity t set t.registrationStatus=:registrationStatus,t.reason =:reason,t.upTime = current_timestamp"
            +
            " where t.recordId =:recordId")
    void updateRegistrationStatus(@Param("recordId") String recordId,
        @Param("registrationStatus") String registrationStatus, @Param("reason") String reason);

    /**
     * 分派
     *
     * @param recordIds      uuids
     * @param acceptor       接受部门
     * @param dispatchStatus 分派状态
     */
    @Modifying(clearAutomatically = true)
    @Query(
        "update CrjJwryEntity t set t.acceptor=:acceptor,t.dispatchStatus=:dispatchStatus,t.upTime=current_timestamp " +
            "where t.recordId in (:reportIds) " +
            "and t.registrationStatus = '0'")
    void setAcceptor(@Param("reportIds") List<String> recordIds, @Param("acceptor") String acceptor,
        @Param("dispatchStatus") String dispatchStatus);

    /**
     * 分派
     *
     * @param recordId uuid
     * @param acceptor 接受部门
     */
    @Modifying(clearAutomatically = true)
    @Query("update CrjJwryEntity t set t.acceptor=:acceptor,t.upTime = current_timestamp " +
        "where t.recordId = :reportId ")
    void setAcceptor(@Param("reportId") String recordId, @Param("acceptor") String acceptor);


    /**
     * 通过记录id获取
     *
     * @param recordId 记录id
     * @return {@link CrjJwryEntity}
     */
    CrjJwryEntity findByRecordId(String recordId);

    /**
     * 更新
     *
     * @param recordId           recordId
     * @param idType             idType
     * @param gjdm               gjdm
     * @param idNumber           idNumber
     * @param attachment         attachment
     * @param phone              phone
     * @param address            address
     * @param registrationStatus registrationStatus
     * @return {@link CrjJwryEntity}
     */
    @Modifying(clearAutomatically = true)
    @Query("update CrjJwryEntity t set "
        + "t.idType=:idType, "
        + "t.gjdm=:gjdm, "
        + "t.idNumber =:idNumber, "
        + "t.attachment =:attachment,"
        + "t.phone =:phone,"
        + "t.address=:address,"
        + "t.registrationStatus=:registrationStatus,"
        + "t.upTime=current_timestamp "
        + "where t.recordId=:recordId ")
    int update(@Param("recordId") String recordId, @Param("idType") String idType, @Param("gjdm") String gjdm,
        @Param("idNumber") String idNumber, @Param("attachment") String attachment, @Param("phone") String phone,
        @Param("address") String address, @Param("registrationStatus") String registrationStatus);

    /**
     * 更新
     *
     * @param id id
     * @return {int}
     */
    @Modifying(clearAutomatically = true)
    @Query("update CrjJwryEntity t set "
        + "t.recordId=t.id," +
        "t.upTime = current_timestamp "
        + "where t.id=:id ")
    int update(@Param("id") String id);


    /**
     * 未登记,不登记，都显示 ;确认登记： 同一个人只显示最新的
     *
     * @return {@link String}
     */
    @Query(nativeQuery = true, value = "SELECT t.RECORD_ID from T_PS_CRJ_JWRY t "
        + "JOIN  (SELECT max(UP_TIME) UP_TIME,ID_TYPE,ID_NUMBER FROM T_PS_CRJ_JWRY WHERE (REGISTRATION_STATUS = '1' or REGISTRATION_STATUS = '2') GROUP BY ID_TYPE,ID_NUMBER) b "
        + "on t.UP_TIME = b.UP_TIME AND t.ID_TYPE = B.ID_TYPE AND T.ID_NUMBER = T.ID_NUMBER "
        + "union SELECT t.RECORD_ID from T_PS_CRJ_JWRY t where t.REGISTRATION_STATUS = '0' or t.REGISTRATION_STATUS = '3'")
    List<String> findLatestRecordIds();

    /**
     * 更新登记状态
     *
     * @param recordId           id
     * @param registrationStatus 登记转台
     * @param planLeaveTime      拟离开时间
     */
    @Modifying(clearAutomatically = true)
    @Query("update CrjJwryEntity t set t.registrationStatus =:registrationStatus,t.planLeaveTime = :planLeaveTime, t.upTime=current_timestamp where t.recordId =:recordId")
    void updateRegistrationsStatus(@Param("recordId") String recordId,
        @Param("registrationStatus") String registrationStatus,
        @Param("planLeaveTime") Date planLeaveTime);

    /**
     * 统计未读
     *
     * @param userId             用户id
     * @param unitCode           部门编号
     * @param unitCodePrefix     部门编号前缀
     * @param module             模块
     * @param dispatchStatus     分派状态
     * @param registrationStatus 登记状态
     * @return {@link Long}
     */
    @Query(nativeQuery = true,value = "select count(1) from T_PS_CRJ_JWRY t "
        + "where not exists (select r.id from T_PS_CRJ_READ_RECORD r where r.user_id =:userId and r.module=:module and r.record_id = t.record_id)"
        + "and (:unitCode is null or t.acceptor=:unitCode)"
        + "and (:unitCodePrefix is null or t.acceptor like concat(:unitCodePrefix,'%'))"
        + "and t.dispatch_status in (:dispatchStatus) "
        + "and (:registrationStatus is null or t.registration_status =:registrationStatus) "
        + "and t.record_id in (SELECT t1.RECORD_ID from T_PS_CRJ_JWRY t1  JOIN  (SELECT max(t2.UP_TIME) UP_TIME,t2.ID_TYPE,t2.ID_NUMBER FROM T_PS_CRJ_JWRY t2 WHERE (t2.REGISTRATION_STATUS = '1' or t2.REGISTRATION_STATUS = '2') GROUP BY t2.ID_TYPE,t2.ID_NUMBER) b  on t1.UP_TIME = b.UP_TIME AND t1.ID_TYPE = b.ID_TYPE AND t1.ID_NUMBER = b.ID_NUMBER union SELECT t3.RECORD_ID from T_PS_CRJ_JWRY t3 where t3.REGISTRATION_STATUS = '0' or t3.REGISTRATION_STATUS = '3')")
    Long countUnread(@Param("userId") String userId, @Param("unitCode") String unitCode,
        @Param("unitCodePrefix") String unitCodePrefix, @Param("module") String module,
        @Param("dispatchStatus") List<String> dispatchStatus, @Param("registrationStatus") String registrationStatus);

    /**
     * 统计即将离开
     *
     * @param unitCode       部门编号
     * @param unitCodePrefix 部门编号前缀
     * @param limitDate      拟离开期限
     * @return {@link Long}
     */
    @Query(nativeQuery = true,value = "select count(1) from T_PS_CRJ_JWRY t "
        + "where (:unitCode is null or t.acceptor=:unitCode) "
        + "and (:unitCodePrefix is null or t.acceptor like concat(:unitCodePrefix,'%')) "
        + "and t.registration_status = '1' "
        + "and t.plan_leave_time < :limitDate "
        + "and t.record_id in (SELECT t1.RECORD_ID from T_PS_CRJ_JWRY t1  JOIN  (SELECT max(t2.UP_TIME) UP_TIME,t2.ID_TYPE,t2.ID_NUMBER FROM T_PS_CRJ_JWRY t2 WHERE (t2.REGISTRATION_STATUS = '1' or t2.REGISTRATION_STATUS = '2') GROUP BY t2.ID_TYPE,t2.ID_NUMBER) b  on t1.UP_TIME = b.UP_TIME AND t1.ID_TYPE = b.ID_TYPE AND t1.ID_NUMBER = b.ID_NUMBER union SELECT t3.RECORD_ID from T_PS_CRJ_JWRY t3 where t3.REGISTRATION_STATUS = '0' or t3.REGISTRATION_STATUS = '3')")
    Long countAboutToLeave(@Param("unitCode") String unitCode,
        @Param("unitCodePrefix") String unitCodePrefix,
        @Param("limitDate") Date limitDate);


    /**
     * 地址模糊查询
     *
     * @param address 地址
     * @return {@link String}
     */
    @Query("select t.recordId from CrjJwryEntity t where t.address like concat('%',:address,'%') ")
    List<String> findAllByAddressLike(@Param("address") String address);

    /**
     * 地址模糊查询
     *
     * @param address 地址
     * @param beginTime 起始时间
     * @param endTime 结束时间
     * @return {@link String}
     */
    @Query(nativeQuery = true,value = "select count(distinct ID_NUMBER) from T_PS_CRJ_JWRY where checkin_time between :beginTime and :endTime and ACCEPTOR like :address || '%' ")
    Long findAllByAddressLikeAndTime(@Param("address") String address,@Param("beginTime")LocalDateTime beginTime,@Param("endTime")LocalDateTime endTime);
    /**
     * 找到不能登记的信息身份证id
     *
     * @param recordIds 记录id
     * @return 身份证号
     */
    @Query("select t.idNumber from CrjJwryEntity t " +
        "where t.recordId in (:recordIds) " +
        "and t.registrationStatus <> '0' ")
    List<String> findCantRegister(@Param("recordIds") List<String> recordIds);

    /**
     * 更新走访信息
     *
     * @param id                 recordId
     * @param phone              手机号
     * @param liveAddress        住宿地址
     * @param registrationStatus 登记状态
     * @param planLeaveTime      拟离开时间
     * @param leaveTime          离开时间
     */
    @Modifying(clearAutomatically = true)
    @Query("update CrjJwryEntity t set " +
        "t.phone = :phone," +
        "t.address =:liveAddress," +
        "t.registrationStatus =:registrationStatus," +
        "t.planLeaveTime =:planLeaveTime," +
        "t.leaveTime =:leaveTime," +
        "t.upTime = current_timestamp " +
        "where t.recordId =:recordId ")
    void updateVisit(@Param("recordId") String id, @Param("phone") String phone,
        @Param("liveAddress") String liveAddress,
        @Param("registrationStatus") String registrationStatus, @Param("planLeaveTime") Date planLeaveTime,
        @Param("leaveTime") Date leaveTime);

    /**
     * 找到所有id
     *
     * @return List
     */
    @Query("select t.recordId from CrjJwryEntity t")
    List<String> findAllId();


    /**
     * 查询
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @param createTime 录入时间
     * @param crBy 录入人
     * @return {@link CrjJwryDetailEntity}
     */
    Optional<CrjJwryEntity> findByIdTypeAndIdNumberAndCreateTimeAndCrBy(String idType, String idNumber,Date createTime,String crBy);

    /**
     * 住宿记录
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @return {@link  CrjJwryEntity}
     */
    @Query(nativeQuery = true, value = "select * from T_PS_CRJ_JWRY t where checkin_time between :beginTime and :endTime " +
            "and t.RECORD_ID in (" +
            "SELECT t.RECORD_ID from T_PS_CRJ_JWRY t " +
            "JOIN (SELECT max(UP_TIME) UP_TIME,ID_TYPE,ID_NUMBER FROM T_PS_CRJ_JWRY WHERE (REGISTRATION_STATUS = '1' or REGISTRATION_STATUS = '2') GROUP BY ID_TYPE,ID_NUMBER) b " +
            "on t.UP_TIME = b.UP_TIME AND t.ID_TYPE = B.ID_TYPE AND T.ID_NUMBER = T.ID_NUMBER " +
            "union SELECT t.RECORD_ID from T_PS_CRJ_JWRY t where t.REGISTRATION_STATUS = '0' or t.REGISTRATION_STATUS = '3')")
    List<CrjJwryEntity> findByTime(@Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime);

    /**
     * 查询
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @return {@link CrjJwryEntity}
     */
    List<CrjJwryEntity> findByIdNumberAndIdType(String idNumber, String idType);


    /**
     * 删除
     *
     * @param idNumber 证件号码
     * @param idType   证件类型
     */
    @Modifying
    @Query(nativeQuery = true,value = "delete from T_PS_CRJ_JWRY c where c.id_number=:idNumber and c.id_type=:idType")
    void deleteByIdNumberAndIdType(@Param("idNumber") String idNumber, @Param("idType") String idType);

    /**
     * 根据id查询列表
     *
     * @param recordIds id列表
     * @return 人员列表
     */
    @Query("SELECT crj FROM CrjJwryEntity crj WHERE crj.recordId in (:recordIds)")
    List<CrjJwryEntity> findByIds(@Param("recordIds") List<String> recordIds);

}
