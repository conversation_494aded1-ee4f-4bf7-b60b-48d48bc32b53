package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/30
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ListFilter {
    /**
     * 中文名称
     */
    private String displayName;

    /**
     * 控件类型
     */
    private String type;

    /**
     * 绑定的key
     */
    private String key;

    /**
     * 控件属性
     */
    private String property;

    /**
     * 初始值
     */
    private List<FilterValue> value;

}
