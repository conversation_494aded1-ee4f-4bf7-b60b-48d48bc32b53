package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.LoginUser;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 用户信息vo
 *
 * <AUTHOR>
 * @date 2021/09/22
 */
@Data
public class UserInfoVO implements Serializable {

    private static final long serialVersionUID = -3093286247590654245L;

    /**
     * 身份证号码
     */
    private String idNumber;

    /**
     * 姓名
     */
    private String name;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 头像
     */
    private String photo;

    /**
     * 角色
     */
    private List<String> roles;

    /**
     * 构建vo
     *
     * @param currentUser {@link LoginUser}
     * @return {@link UserInfoVO}
     */
    public static UserInfoVO of(LoginUser currentUser) {
        UserInfoVO vo = new UserInfoVO();
        vo.setIdNumber(currentUser.getIdCard());
        vo.setName(currentUser.getRealName());
        vo.setDeptName(currentUser.getUnitName());
        vo.setPhoto(currentUser.getPhoto());
        vo.setRoles(Collections.emptyList());
        return vo;
    }
}
