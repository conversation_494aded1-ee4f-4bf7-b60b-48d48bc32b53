package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.CluePersonRelationEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.repository.CluePersonRelationRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.SpringContextUtil;
import lombok.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CONTROL_STATUS;

/**
 * 线索中的人员数据
 *
 * <AUTHOR>
 * @date 2021/9/3 14:18
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ClueRelatedPersonVO implements Serializable {
    private static final long serialVersionUID = -9002873222781924582L;
    /**
     * 人员id
     */
    private String personId;

    /**
     * 人员名
     */
    private String personName;

    /**
     * 身份证
     */
    private String idNumber;

    /**
     * 人员类别
     */
    private String personType;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 线索-人员关联id
     */
    private String relationId;

    /**
     * 人员管控状态
     */
    private String controlStatus;

    /**
     * 构建vo
     *
     * @param personEntity {@link PersonEntity}
     * @param clueEntity   {@link ClueEntity}
     * @return {@link ClueRelatedPersonVO}
     */
    public static ClueRelatedPersonVO of(PersonEntity personEntity, ClueEntity clueEntity) {
        ClueRelatedPersonVO vo = new ClueRelatedPersonVO();
        vo.setPersonId(personEntity.getId());
        vo.setPersonName(personEntity.getName());
        vo.setIdNumber(personEntity.getIdNumber());
        //设置人员标签
        LabelRepository personTypeRepository = BeanUtil.getBean(LabelRepository.class);
        String personType = personTypeRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), clueEntity.getSubjectId()).stream()
                .map(LabelEntity::getName)
                .collect(Collectors.joining("、"));
        vo.setPersonType(personType);
        //设置添加时间
        CluePersonRelationRepository cluePersonRelationRepository = BeanUtil.getBean(CluePersonRelationRepository.class);
        CluePersonRelationEntity relation = cluePersonRelationRepository.findByClueIdAndPersonId(clueEntity.getId(), personEntity.getId());
        vo.setCreateTime(relation.getCrTime());
        vo.setRelationId(relation.getId());

        //管控状态
        DictService dictService = SpringContextUtil.getBean(DictService.class);
        vo.setControlStatus(dictService.getDictEntityByTypeAndCode(DICT_TYPE_CONTROL_STATUS, personEntity.getControlStatus()).getName());
        return vo;
    }
}
