package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 人员专题关系表
 *
 * <AUTHOR>
 * @date 2021/07/27
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_SUBJECT_RELATION")
public class PersonSubjectRelationEntity extends BaseEntity {

    private static final long serialVersionUID = -9120063638854044097L;
    /**
     * 人员ID
     */
    private String personId;

    /**
     * 专题id
     */
    private String subjectId;
}
