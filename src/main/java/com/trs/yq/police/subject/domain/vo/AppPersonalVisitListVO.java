package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.CrjVisitRecordEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * app走访记录列表内容
 *
 * <AUTHOR>
 * @since 2021/10/18
 */
@Data
public class AppPersonalVisitListVO implements Serializable {
    private static final long serialVersionUID = 5111055472634146364L;
    private String id;

    private String address;

    private LocalDateTime time;

    /**
     * 根据entity生成vo
     *
     * @param entity {@link CrjVisitRecordEntity}
     * @return {@link AppPersonalVisitListVO}
     */
    public static AppPersonalVisitListVO of(CrjVisitRecordEntity entity) {
        AppPersonalVisitListVO vo = new AppPersonalVisitListVO();
        vo.id = entity.getId();
        vo.address = entity.getLivingInfo();
        vo.time = entity.getVisitTime();
        return vo;
    }
}
