package com.trs.yq.police.subject.domain.entity;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/10/08
 */
@Table(name = "T_ALARM_YJGJ")
@Entity
@Getter
@Setter
public class AlarmYjgjEntity {
    @Id
    private String id;

    //布控主键
    private String bkxxzj;

    //轨迹来源
    private String hdlybcn;

    //轨迹时间
    private LocalDateTime hdfssj;

    //轨迹地点
    private String hdfsdd;

    //经度
    private String hdjd;

    //纬度
    private String hdwd;

    //活动详情
    private String hdxq;

    //关联证件号码
    private String glsfzh;

    //关联姓名
    private String glxm;

    //人员类型
    private String ryxl;

    //图片链接
    private String picurls;
}
