package com.trs.yq.police.subject.jpa.converter;

import com.trs.yq.police.subject.constants.enums.OperateModule;

import javax.persistence.AttributeConverter;
import java.util.Objects;

/**
 * 操作模块枚举转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 17:35
 */
public class EnumOperateModuleConverter implements AttributeConverter<OperateModule, String> {

    @Override
    public String convertToDatabaseColumn(OperateModule attribute) {
        return Objects.isNull(attribute) ? null : attribute.getCode();
    }

    @Override
    public OperateModule convertToEntityAttribute(String dbData) {

        return OperateModule.codeOf(dbData);
    }
}
