package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.constants.PoliceSubjectConstants;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.repository.GroupRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 更新群体信息到redis
 *
 * <AUTHOR>
 * @date 2021/9/28 14:11
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.update.group.task.enable", havingValue = "true")
public class UploadGroupToRedisTask {
    @Value("${com.trs.redis.group.upload.key}")
    private String groupUploadKey;
    /**
     * 人员类型id: 赛摩人员
     */
    @Value("${com.trs.redis.group.motorcycle.person.type.id}")
    private String motorcycleTypeId;
    /**
     * 人员类型id: 吸毒人员
     */
    @Value("${com.trs.redis.group.drugs.person.type.id}")
    private String drugsTypeId;

    @Resource(name = "assignRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private GroupRepository groupRepository;

    /**
     * 定时更新redis中群体信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.update.group.task}")
    public void updateGroupWarningType() {
        HashOperations<String, String, Object> hashOperations = redisTemplate.opsForHash();
        //TODO 更新迁移后的类型id
        //赛摩人员聚集
        hashOperations.put(groupUploadKey, "jj_smryjj", JsonUtil.toJsonString(Collections.singletonList(personRepository.getIdNumbersByPersonType(motorcycleTypeId))));
        //禁毒： 吸毒人员聚集
        hashOperations.put(groupUploadKey, "jd_ryjj", JsonUtil.toJsonString(Collections.singletonList(personRepository.getIdNumbersByPersonType(drugsTypeId))));
        //反恐： 人员聚集
        List<List<String>> fkJsonString = new ArrayList<>();

        groupRepository.findAllBySubjectId(PoliceSubjectConstants.FK_SUBJECT).stream()
                .map(GroupEntity::getId)
                .forEach(groupId -> {
                    List<String> personEntityList = personRepository.findAllByGroupId(groupId).stream().map(PersonEntity::getIdNumber).collect(Collectors.toList());
                    if (!personEntityList.isEmpty()) {
                        fkJsonString.add(personEntityList);
                    }
                });
        hashOperations.put(groupUploadKey, "fk_qtjj", JsonUtil.toJsonString(fkJsonString));
    }
}
