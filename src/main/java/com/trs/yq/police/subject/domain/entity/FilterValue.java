package com.trs.yq.police.subject.domain.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 存放选项的vo
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilterValue {
    private String id;
    private String name;
    @JsonProperty("default")
    private Boolean isDefault = false;
    private List<FilterValue> children;
}
