package com.trs.yq.police.subject.domain.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 线索
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_event")
@Data
public class EventEntity extends BaseEntity {

    private static final long serialVersionUID = -3042821641181335030L;

    private String type;

    /**
     * 事件标题
     */
    private String title;
    /**
     * 事件详细内容
     */
    private String content;

    /**
     * 管控单位
     */
    private String controlDept;

    /**
     * 管控单位名称
     */
    private String controlDeptName;

    /**
     * 相关要求
     */
    private String claim;

    /**
     * 参考码表ps_event_data_source
     */
    private String source;

    /**
     * 事件数据唯一标志
     */
    private String sourceKey;

    /**
     * 事件发生时间
     */
    private LocalDateTime occurrenceTime;

    /**
     * 事件参与人数
     */
    private Integer numberOfParticipants;
    /**
     * 事件发生地
     */
    private String address;

    private String lng;

    private String lat;

    /**
     * 处置状态 参见码表(ps_event_disposal_status)
     */
    private String disposalStatus;

    private boolean deleted;

    /**
     * 诉求地
     */
    private String appealPlace;

    /**
     * 估计人数
     */
    private Integer estimatePersonCount;

    /**
     * 风险等级，码表 risk_level
     */
    private String riskLevel;

    /**
     * 三台合一类别 中文
     */
    private String sthyType;

    /**
     * 上报状态
     * 码表 report_status
     */
    private String reportStatus;
    /**
     * 专题id
     */
    private String subjectId;

}
