package com.trs.yq.police.subject.conf;

import com.trs.yq.police.subject.properties.NebulaProperties;
import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.exception.AuthFailedException;
import com.vesoft.nebula.client.graph.exception.IOErrorException;
import com.vesoft.nebula.client.graph.exception.NotValidConnectionException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import javax.annotation.Resource;
import java.net.UnknownHostException;
import java.util.stream.Collectors;

/**
 * nebula连接池配置
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(prefix = "com.trs.person-associate.nebula", name = "username")
public class NebulaPoolConfiguration {

    @Resource
    NebulaProperties properties;

    /**
     * nebula连接池信息
     *
     *
     * @return 返回nebula连接池
     * @throws UnknownHostException
     *             主机信息错误
     */
    @Bean(destroyMethod = "close")
    public NebulaPool nebulaPool() throws UnknownHostException {

        NebulaPoolConfig poolConfig = new NebulaPoolConfig();
        poolConfig.setIdleTime(properties.getIdleTime());
        poolConfig.setMaxConnSize(properties.getMaxConnectionsSize());
        poolConfig.setMinConnSize(properties.getMinConnectionsSize());
        poolConfig.setTimeout(properties.getTimeout());

        NebulaPool pool = new NebulaPool();
        pool.init(properties.getHostAddressList().stream().map(r -> new HostAddress(r.getHost(), r.getPort()))
            .collect(Collectors.toList()), poolConfig);

        return pool;
    }

    /**
     * nebula session
     *
     * @param pool
     *            nebula连接池
     * @return 返回nebula session
     * @throws IOErrorException
     *             io异常
     * @throws AuthFailedException
     *             权限不正常
     * @throws NotValidConnectionException
     *             无效的连接
     */
    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public Session nebulaSession(@Autowired NebulaPool pool)
        throws IOErrorException, AuthFailedException, NotValidConnectionException {
        try {
            return pool.getSession(properties.getUsername(), properties.getPassword(), false);
        } catch (NotValidConnectionException e) {
            return pool.getSession(properties.getUsername(), properties.getPassword(), true);
        }
    }

}
