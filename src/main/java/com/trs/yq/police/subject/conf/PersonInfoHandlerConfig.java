package com.trs.yq.police.subject.conf;

import javax.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/07/25
 */
@Component
@ConfigurationProperties(prefix = "com.trs.download.person.info")
@Getter
@Setter
public class PersonInfoHandlerConfig {

    @NotBlank
    private String url;

    @NotBlank
    private String token;

    @NotBlank
    private String userId;

    @NotBlank
    private String userName;

    @NotBlank
    private String deptCode;

    @NotBlank
    private String deptName;

}
