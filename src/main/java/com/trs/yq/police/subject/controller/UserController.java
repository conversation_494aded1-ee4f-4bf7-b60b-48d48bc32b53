package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.vo.UserInfoVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 用户Http接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 11:05
 */
@RestController
@RequestMapping("/user")
public class UserController {

    /**
     * 获取当前登录用户信息
     * http://192.168.200.192:3001/project/4897/interface/api/129705
     *
     * @return 角色
     */
    @GetMapping("/info")
    public UserInfoVO getRoles() {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(currentUser)) {
            return null;
        }
        return UserInfoVO.of(currentUser);
    }
}
