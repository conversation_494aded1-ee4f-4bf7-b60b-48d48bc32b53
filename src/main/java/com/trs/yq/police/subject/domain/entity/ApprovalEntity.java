package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 审批信息entity
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_APPROVAL")
public class ApprovalEntity extends BaseEntity {
    private static final long serialVersionUID = -116487164235262471L;
    /**
     * 审批人id
     */
    private String approverId;
    /**
     * 申请操作类型
     */
    private String type;
    /**
     * 申请详情
     */
    private String detail;
    /**
     * 审批状态
     */
    private String status;
    /**
     * 待操作人员id
     */
    private String personId;
    /**
     * 专题id
     */
    private String subjectId;
    /**
     * 其他参数
     */
    private String content;
}
