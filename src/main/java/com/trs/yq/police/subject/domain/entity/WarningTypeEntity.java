package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 预警类型实体
 *
 * <AUTHOR>
 * @date 2021/9/7 9:44
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_TYPE")
public class WarningTypeEntity implements Serializable {

    private static final long serialVersionUID = -2687630120083075022L;

    /**
     * 数据主键
     */
    @Id
    private String id;

    /**
     * 中文名
     */
    private String cnName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 显示类型
     * 1-单人，2-多人，3-话单
     */
    private String displayType;

    /**
     * 文本模板
     */
    private String contentTemplate;

    /**
     * 默认级别
     * 1-红色，2-橙色，3-黄色，4-蓝色
     */
    private String defaultLevel;

    /**
     * 签收时间限制
     */
    private Integer signTimeLimit;

    /**
     * 专题主键
     */
    private String subjectId;

    /**
     * 排序
     */
    private Integer showOrder;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (!(o instanceof WarningTypeEntity)) {
            return false;
        }

        WarningTypeEntity that = (WarningTypeEntity) o;

        return new EqualsBuilder().append(id, that.id).append(enName, that.enName).append(subjectId, that.subjectId).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37).append(id).append(enName).append(subjectId).toHashCode();
    }
}
