package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.PersonCallEntity;
import com.trs.yq.police.subject.domain.entity.PersonFundEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.AttachmentVO;
import com.trs.yq.police.subject.domain.vo.CallVO;
import com.trs.yq.police.subject.domain.vo.FundVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.repository.PersonCallRepository;
import com.trs.yq.police.subject.repository.PersonFundRepository;
import com.trs.yq.police.subject.service.CallService;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * 话单分析、资金分析
 *
 * <AUTHOR>
 */
@Service
public class CallServiceImpl implements CallService {

    @Resource
    private PersonCallRepository personCallRepository;
    @Resource
    private PersonFundRepository personFundRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private OperationLogHandler operationLogHandler;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


    @Override
    public PageResult<CallVO> getCallListByPersonId(String personId, PageParams pageParams) {
        Page<PersonCallEntity> result = personCallRepository.findAllByPersonId(personId, pageParams.toPageable());
        List<CallVO> items = result.get()
            .map(entity -> {
                CallVO callVO = new CallVO();
                callVO.setId(entity.getId());
                callVO.setCallFile(fileIdsToAttachments(entity.getCallFile()));
                callVO.setCallTimeFeature(fileIdsToAttachments(entity.getCallTimeFeature()));
                callVO.setCallPositionTrack(fileIdsToAttachments(entity.getCallPositionTrack()));
                callVO.setAttachments(fileIdsToAttachments(entity.getAttachments()));
                callVO.setTopologyGraph(fileIdsToAttachments(entity.getTopologyGraph()));
                callVO.setRemark(entity.getRemark());
                callVO.setUploadTime(entity.getCrTime().format(FORMATTER));
                callVO.setUploadUser(entity.getCrByName());
                return callVO;
            })
            .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), result.getTotalElements(), pageParams.getPageSize());
    }

    private List<AttachmentVO> fileIdsToAttachments(String ids) {
        List<String> idList = JsonUtil.parseArray(ids, String.class);
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        } else {
            return fileStorageRepository.findAllByFileIds(idList).stream().map(AttachmentVO::of)
                .collect(Collectors.toList());
        }
    }

    private String toIdString(List<AttachmentVO> attachments) {
        List<String> ids = attachments.stream().map(AttachmentVO::getId).collect(Collectors.toList());
        return JsonUtil.toJsonString(ids);
    }

    @Override
    public void addCall(String personId, CallVO callVO) {
        PersonCallEntity entity = new PersonCallEntity();
        callToEntity(personId, callVO, entity);
        personCallRepository.save(entity);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.ADD)
            .module(OperateModule.CALL_ANALYSIS)
            .newObj(JsonUtil.toJsonString(entity))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(personId)
            .desc("新增话单分析")
            .build();

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    private void callToEntity(String personId, CallVO callVO, PersonCallEntity entity) {
        entity.setPersonId(personId);
        entity.setTopologyGraph(toIdString(callVO.getTopologyGraph()));
        entity.setCallFile(toIdString(callVO.getCallFile()));
        entity.setCallTimeFeature(toIdString(callVO.getCallTimeFeature()));
        entity.setCallPositionTrack(toIdString(callVO.getCallPositionTrack()));
        entity.setAttachments(toIdString(callVO.getAttachments()));
        entity.setRemark(callVO.getRemark());
    }

    @Override
    public void modifyCall(String personId, CallVO callVO) {
        PersonCallEntity entity = personCallRepository.findById(callVO.getId()).orElse(null);
        if (entity == null) {
            throw new ParamValidationException("话单分析不存在，请核实");
        }
        String oldObjStr = JsonUtil.toJsonString(entity);
        callToEntity(personId, callVO, entity);
        personCallRepository.save(entity);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.EDIT)
            .module(OperateModule.CALL_ANALYSIS)
            .oldObj(oldObjStr)
            .newObj(JsonUtil.toJsonString(entity))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(personId)
            .desc("修改话单分析")
            .build();

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void deleteCall(String personId, String id) {
        PersonCallEntity entity = personCallRepository.findById(id).orElse(null);
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.DELETE)
            .module(OperateModule.CALL_ANALYSIS)
            .oldObj(JsonUtil.toJsonString(entity))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(personId)
            .desc("删除话单分析")
            .build();

        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }

        personCallRepository.deleteById(id);
    }

    @Override
    public PageResult<FundVO> getFundListByPersonId(String personId, PageParams pageParams) {
        Page<PersonFundEntity> result = personFundRepository.findAllByPersonId(personId, pageParams.toPageable());
        List<FundVO> items = result.get()
            .map(entity -> {
                FundVO fundVO = new FundVO();
                fundVO.setId(entity.getId());
                fundVO.setAttachments(fileIdsToAttachments(entity.getAttachments()));
                fundVO.setTopologyGraph(fileIdsToAttachments(entity.getTopologyGraph()));
                fundVO.setRemark(entity.getRemark());
                fundVO.setUploadTime(entity.getCrTime().format(FORMATTER));
                fundVO.setUploadUser(entity.getCrByName());
                return fundVO;
            })
            .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), result.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public void addFund(String personId, FundVO fundVO) {
        PersonFundEntity entity = new PersonFundEntity();
        fundToEntity(personId, fundVO, entity);
        personFundRepository.save(entity);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.ADD)
            .module(OperateModule.FUND_ANALYSIS)
            .newObj(JsonUtil.toJsonString(entity))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(personId)
            .desc("新增资金分析")
            .build();

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void modifyFund(String personId, FundVO fundVO) {
        PersonFundEntity entity = personFundRepository.findById(fundVO.getId()).orElse(null);
        if (entity == null) {
            throw new ParamValidationException("资金分析不存在，请核实");
        }
        String oldObjStr = JsonUtil.toJsonString(entity);
        fundToEntity(personId, fundVO, entity);
        personFundRepository.save(entity);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.EDIT)
            .module(OperateModule.FUND_ANALYSIS)
            .oldObj(oldObjStr)
            .newObj(JsonUtil.toJsonString(entity))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(personId)
            .desc("修改资金分析")
            .build();

        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    private void fundToEntity(String personId, FundVO fundVO, PersonFundEntity entity) {
        entity.setId(fundVO.getId());
        entity.setPersonId(personId);
        entity.setTopologyGraph(toIdString(fundVO.getTopologyGraph()));
        entity.setAttachments(toIdString(fundVO.getAttachments()));
        entity.setRemark(fundVO.getRemark());
    }

    @Override
    public void deleteFund(String personId, String id) {
        PersonFundEntity entity = personFundRepository.findById(id).orElse(null);
        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.DELETE)
            .module(OperateModule.FUND_ANALYSIS)
            .oldObj(JsonUtil.toJsonString(entity))
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(personId)
            .desc("删除资金分析")
            .build();

        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
        personFundRepository.deleteById(id);
    }

}
