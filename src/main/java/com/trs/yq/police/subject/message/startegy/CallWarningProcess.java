package com.trs.yq.police.subject.message.startegy;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.dto.CallWarningMessageDTO;
import com.trs.yq.police.subject.domain.dto.WarningCallHitDTO;
import com.trs.yq.police.subject.domain.dto.WarningCallHitRecordsDTO;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.message.WarningPushService;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.WarningConstants.WARNING_DETAIL_TEMPLATE_MISSING;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.JD_SUBJECT;

/**
 * 话单预警处理
 *
 * <AUTHOR>
 * @date 2021/9/11 14:40
 */
@Slf4j
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class CallWarningProcess implements WarningProcess {

    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningCallRepository warningCallRepository;
    @Resource
    private WarningCallRelationRepository warningCallRelationRepository;
    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private WarningPushService warningPushService;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void process(String message) {

        // 转换出话单数据
        final CallWarningMessageDTO callMessage = JsonUtil.parseObject(message, CallWarningMessageDTO.class);
        // 验证数据
        if (Objects.isNull(callMessage)) {
            log.error("receive error call list warning message! message = {}", message);
            return;
        }

        // 生成预警信息id
        final String warningId = extractWarningId(callMessage);

        // 存储话单通话数据
        final List<String> callIds = extractCallIds(callMessage);

        // 绑定话单与预警之间的关系
        establishRelationship(warningId, callIds);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.PUSH)
                .module(OperateModule.WARNING)
                .currentUser(AuthHelper.getCurrentUser())
                .targetObjectType(TargetObjectTypeEnum.WARNING.getCode())
                .primaryKey(warningId)
                .desc("推送预警消息")
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    /**
     * 提取预警id
     *
     * @param callMessage 话单预警信息
     * @return 预警id
     */
    private String extractWarningId(CallWarningMessageDTO callMessage) {
        final String type = callMessage.getType();
        final WarningTypeEntity warningType = warningTypeRepository.findByEnName(type);
        WarningEntity warning = new WarningEntity();
        warning.setWarningTime(LocalDateTime.now());
        warning.setWarningLevel(warningType.getDefaultLevel());
        warning.setWarningSource(generateWarningResource(callMessage));
        warning.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
        warning.setWarningDetails(generateWarningDetail(warningType, callMessage));
        warning.setSubjectId(warningType.getSubjectId());
        warning.setWarningType(warningType.getId());
        warningRepository.save(warning);
//        warningPushService.push(warningType, warning);
        return warning.getId();
    }

    /**
     * 构建预警源
     *
     * @param callMessage 预警消息
     * @return 预警源
     */
    private List<String> generateWarningResource(CallWarningMessageDTO callMessage) {

        final List<WarningCallHitDTO> hits = callMessage.getHits();
        final List<String> tableNames = hits
                .stream()
                .map(hit -> hit.getRecords()
                        .stream()
                        .map(WarningCallHitRecordsDTO::getTrsTable)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList())
                .stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        return trajectorySourceRepository.findAllNameByTableNameIn(tableNames);
    }

    /**
     * 生成预警详情
     *
     * @param warningType 预警类型
     * @param callMessage 预警消息
     * @return 预警详情
     */
    private String generateWarningDetail(WarningTypeEntity warningType, CallWarningMessageDTO callMessage) {
        // 根据模板生成详情
        final String contentTemplate = warningType.getContentTemplate();

        if (StringUtils.equals(contentTemplate, WARNING_DETAIL_TEMPLATE_MISSING)) {
            return Strings.EMPTY;
        }

        final String templatePrefix = "'手机号'+#warningNumber";
        final String templateMiddle = "#personType+#name";
        final String templateSuffix = "'通联'+#callCount+'次'";

        SpelExpressionParser parser = new SpelExpressionParser();
        EvaluationContext context = new StandardEvaluationContext();

        //拼接手机号
        context.setVariable("warningNumber", StringUtils.trimToEmpty(callMessage.getWarningNumber()));
        final Expression prefixExpression = parser.parseExpression(templatePrefix);
        final String detailPrefix = prefixExpression.getValue(context, String.class);

        //拼接通联人员列表字符串
        Expression middleExpression = parser.parseExpression(templateMiddle);
        final String detail = callMessage.getHits().stream().map(hit -> {
            PersonEntity person = personRepository.findByIdNumber(hit.getIdNumber());
            String personTypeStr = "";
            String name = "";
            if(Objects.nonNull(person)){
                List<String> personTypes = personRepository.getPersonType(person.getId(), JD_SUBJECT);
                personTypeStr = StringUtils.join(personTypes, "、");
                name = person.getName();
            }
            context.setVariable("personType", personTypeStr);
            context.setVariable("name", name);
            return middleExpression.getValue(context, String.class);
        }).collect(Collectors.joining("，"));

        //拼接通联次数
        long callCount = callMessage.getHits().stream().mapToLong(hit -> hit.getRecords().size()).sum();
        context.setVariable("callCount", callCount);
        final Expression suffixExpression = parser.parseExpression(templateSuffix);
        final String detailSuffix = suffixExpression.getValue(context, String.class);

        return detailPrefix + detail + detailSuffix;
    }


    /**
     * 提取话单记录id列表
     *
     * @param callMessage 话单预警信息
     * @return id列表
     */
    private List<String> extractCallIds(CallWarningMessageDTO callMessage) {
        final List<WarningCallEntity> warningCallEntities = callMessage.getHits()
                .stream()
                .map(hit -> hit.getRecords().stream().map(call -> {
                    WarningCallEntity warningCall = new WarningCallEntity();
                    warningCall.setTrsTable(call.getTrsTable());
                    warningCall.setTrsId(call.getTrsId());
                    warningCall.setPhoneNumber(call.getPhoneNumber());
                    warningCall.setCallTime(LocalDateTime.parse(call.getCallTime(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
                    warningCall.setCallDuration(Long.parseLong(call.getCallDuration()));
                    warningCall.setCallType(call.getCallType());
                    warningCall.setIdNumber(hit.getIdNumber());
                    warningCall.setWarningTime(LocalDateTime.now());
                    warningCall.setWarningNumber(callMessage.getWarningNumber());
                    warningCall.setType(callMessage.getType());
                    return warningCall;
                }).collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        warningCallRepository.saveAll(warningCallEntities);
        return warningCallEntities.stream().map(WarningCallEntity::getId).collect(Collectors.toList());
    }

    /**
     * 建立话单记录与预警信息的联系
     *
     * @param warningId 预警id
     * @param callIds   话单id列表
     */
    private void establishRelationship(String warningId, List<String> callIds) {
        final List<WarningCallRelationEntity> relations = callIds
                .stream()
                .map(callId -> {
                    WarningCallRelationEntity relation = new WarningCallRelationEntity();
                    relation.setCallListId(callId);
                    relation.setWarningId(warningId);
                    relation.setCreateTime(LocalDateTime.now());
                    return relation;
                }).collect(Collectors.toList());
        // 保存关系
        warningCallRelationRepository.saveAll(relations);
    }
}
