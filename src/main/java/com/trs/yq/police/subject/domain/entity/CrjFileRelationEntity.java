package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 出入境文件关联实体类
 *
 * <AUTHOR>
 * @date 2021/9/17 11:50
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_FILE_RELATION")
public class CrjFileRelationEntity extends BaseEntity {
    private static final long serialVersionUID = -8777374470707912940L;
    /**
     * 出入境信息id
     */
    private String crjId;
    /**
     * 文件存储id
     */
    private String fileStorageId;
    /**
     * 文件类型
     */
    private String type;
    /**
     * 模块类型
     */
    private String module;
}
