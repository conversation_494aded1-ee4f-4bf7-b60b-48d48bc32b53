package com.trs.yq.police.subject.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.vo.CrjDispatchVO;
import com.trs.yq.police.subject.domain.vo.CrjRedispatchVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SfryReportVO;
import com.trs.yq.police.subject.domain.vo.SfryReportVerifyVO;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 10:40
 */
public interface CrjSfryReportService {

    /**
     * 获取详情
     *
     * @param id uuid
     * @return {@link SfryReportVO}
     */
    SfryReportVO getById(String id);

    /**
     * 分页查询
     *
     * @param requestVO 请求参数
     * @return {@link SfryReportVO}
     */
    PageResult<SfryReportVO> getPage(ListRequestVO requestVO);

    /**
     * 分派
     *
     * @param dispatchVO 分派信息
     */
    void dispatch(CrjDispatchVO dispatchVO);

    /**
     * 核实
     *
     * @param verifyVO 核实结果
     * @param recordId 记录id
     */
    void verify(String recordId, SfryReportVerifyVO verifyVO);

    /**
     * 重新分派
     *
     * @param dispatchVO 分派信息
     */
    void redispatch(CrjRedispatchVO dispatchVO);

    /**
     * 删除三非人员
     *
     * @param id id
     */
    void deletedById(String id);

    /**
     * 导出excel
     *
     * @param fieldNames 字段
     * @param subjectId subjectId
     * @param response  响应体
     * @param request   导出请求
     */
    void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request, String subjectId)
        throws IOException;

    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    JsonNode getExportPropertyList(String subjectId);
}
