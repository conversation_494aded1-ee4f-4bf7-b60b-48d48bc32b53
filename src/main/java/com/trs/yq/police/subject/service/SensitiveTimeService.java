package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SensitiveListRequestVO;
import com.trs.yq.police.subject.domain.vo.SensitiveTimeListVO;
import com.trs.yq.police.subject.domain.vo.SensitiveTimeVO;

/**
 * 敏感时间节点接口
 *
 * <AUTHOR>
 * @date 2021/12/28 14:15
 */
public interface SensitiveTimeService {
    /**
     * 创建敏感时间节点
     *
     * @param sensitiveTimeVO {@link SensitiveTimeVO}
     * @return 敏感时间节点VO
     */
    String createSensitive(SensitiveTimeVO sensitiveTimeVO);

    /**
     * 更新敏感时间节点
     *
     * @param sensitiveTimeVO 敏感时间节点vo
     */
    void updateSensitive(SensitiveTimeVO sensitiveTimeVO);

    /**
     * 修改敏感时间节点状态
     *
     * @param sensitiveId 敏感时间节点id
     */
    void deleteSensitive(String sensitiveId);

    /**
     * 敏感时间节点列表
     *
     * @param sensitiveListRequestVO 请求参数
     * @return {@link SensitiveTimeListVO}
     */
    PageResult<SensitiveTimeListVO> getSensitiveList(SensitiveListRequestVO sensitiveListRequestVO);
}
