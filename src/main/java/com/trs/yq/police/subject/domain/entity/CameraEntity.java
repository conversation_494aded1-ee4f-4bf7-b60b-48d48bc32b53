package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.domain.vo.Coordinate;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/23
 */
@Data
public class CameraEntity implements Serializable {

    private static final long serialVersionUID = 1801249010483865603L;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 行政区划代码
     */
    private String areaCode;

    /**
     * 构造器
     *
     * @return Coordinate
     */
    public Coordinate getCoordinate() {
        return new Coordinate(lat, lng);
    }
}
