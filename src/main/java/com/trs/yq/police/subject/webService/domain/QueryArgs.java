package com.trs.yq.police.subject.webService.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.trs.yq.police.subject.properties.CrjSystemProperties;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/4/24 14:00
 */
@Data
public class QueryArgs {

    /**
     * 客户端ip
     */
    @JsonProperty("client_ip")
    private String clientIp;

    /**
     * 客户端证书编号
     */
    @JsonProperty("client_zsbh")
    private String clientZsbh;

    /**
     * 客户端单位编号
     */
    @JsonProperty("client_dwbh")
    private String clientDwbh;

    /**
     * 客户端单位名称
     */
    @JsonProperty("client_dwmc")
    private String clientDwmc;

    /**
     * 客户端姓名
     */
    @JsonProperty("client_xm")
    private String clientXm;

    /**
     * 客户端身份证号
     */
    @JsonProperty("client_sfzh")
    private String clientSfzh;

    /**
     * 国家地区
     */
    private String gjdq;

    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 证件种类
     */
    private String zjzl;

    /**
     * 是否补齐相片. 0 否, 1 是. 默认为 1. 即当发 现没有相片时，便尝试用本地库的外国人办 证相片、临住相片来补齐.
     */
    private int usepatchphoto;


    /**
     * 新建查询参数
     *
     * @param gjdm 国家代码
     * @param idNumber 证件号
     * @param idType 证件类型
     */
    public QueryArgs(String gjdm, String idNumber, String idType) {
        CrjSystemProperties bean = BeanUtil.getBean(CrjSystemProperties.class);
        this.clientIp = bean.getClientIp();
        this.clientZsbh = bean.getClientZsbh();
        this.clientDwbh = bean.getClientDwbh();
        this.clientDwmc = bean.getClientDwmc();
        this.clientXm = bean.getClientXm();
        this.clientSfzh = bean.getClientSfzh();
        this.gjdq = gjdm;
        this.zjhm = idNumber;
        this.zjzl = idType;
    }

}
