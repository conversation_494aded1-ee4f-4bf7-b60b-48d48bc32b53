package com.trs.yq.police.subject.operation.event;

import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.LoginUser;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @date 2021/8/18 16:10
 */
@Getter
@Setter
@ToString
@Builder
public class OperationLogRecord {

    /**
     * 操作
     */
    private Operator operator;

    /**
     * 操作模块
     */
    private OperateModule module;

    /**
     * 主键
     */
    private String primaryKey;

    /**
     * 旧数据
     */
    private String oldObj;

    /**
     * 新数据
     */
    private String newObj;

    /**
     * 描述
     */
    private String desc;

    /**
     * 当前用户
     */
    private LoginUser currentUser;

    /**
     * 对象类型
     * 0-person, 1-group, 2-clue
     */
    @Builder.Default
    private Integer targetObjectType = 0;
}
