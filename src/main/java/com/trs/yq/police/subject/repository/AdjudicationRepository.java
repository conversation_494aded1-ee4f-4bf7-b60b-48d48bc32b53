package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.AdjudicationEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * T_PS_PERSON_JUDGEMENT表查询接口
 *
 * <AUTHOR>
 * @since 2021/7/27
 */
@Repository
public interface AdjudicationRepository extends BaseRepository<AdjudicationEntity, String> {
    /**
     * 根据人员id查询所有裁决信息, 按裁决日期倒序排列
     *
     * @param personId 人员id
     * @return 裁决信息列表 {@link AdjudicationEntity}
     */
    List<AdjudicationEntity> findAllByPersonIdOrderByJudgementDateDesc(String personId);
}
