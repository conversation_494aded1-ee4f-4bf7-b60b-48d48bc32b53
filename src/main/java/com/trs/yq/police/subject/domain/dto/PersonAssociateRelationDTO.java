package com.trs.yq.police.subject.domain.dto;

import com.trs.yq.police.subject.constants.enums.PersonAssociateAttributesEnum;
import com.trs.yq.police.subject.constants.enums.PersonRelationEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 人员档案关联关系
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 */

public class PersonAssociateRelationDTO
        extends HashMap<
        PersonAssociateAttributesEnum, Set<PersonAssociateRelationDTO.RelationPair>> {

    private static final long serialVersionUID = 7212336498991079512L;

    /**
     * 私有化构造方法，只能使用newInstance作为实例新建入口
     */
    private PersonAssociateRelationDTO() {
    }

    /**
     * 新建实例
     *
     * @return 新实例
     */
    public static PersonAssociateRelationDTO newInstance() {
        PersonAssociateRelationDTO returnDTO = new PersonAssociateRelationDTO();
        for (PersonAssociateAttributesEnum attributesEnum :
                PersonAssociateAttributesEnum.values()) {
            returnDTO.put(attributesEnum, new HashSet<RelationPair>());
        }
        return returnDTO;
    }

    /**
     * 添加关系
     *
     * @param attributesEnum 属性类型
     * @param relation       关系
     * @param value          关联值
     */
    public void setRelation(
            PersonAssociateAttributesEnum attributesEnum, PersonRelationEnum relation, String value) {
        this.get(attributesEnum).add(new RelationPair(relation, value));
    }

    /**
     * 提供将两个相同类型DTO的值合并成一个DTO的能力。合并后的对象为被调用方
     *
     * @param otherDTO 另一个待合并的DTO
     * @return 被调用此方法的对象
     */
    public PersonAssociateRelationDTO join(PersonAssociateRelationDTO otherDTO) {
        if (Objects.isNull(otherDTO)) {
            return this;
        }
        for (Entry<PersonAssociateAttributesEnum, Set<RelationPair>> entry : otherDTO.entrySet()) {
            this.get(entry.getKey()).addAll(entry.getValue());
        }
        return this;
    }

    /**
     * 表示关系的对象
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RelationPair {

        private PersonRelationEnum relation;

        private String value;
    }
}
