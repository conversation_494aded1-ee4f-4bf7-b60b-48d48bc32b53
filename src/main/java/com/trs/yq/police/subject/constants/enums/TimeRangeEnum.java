package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 时间范围枚举
 *
 * <AUTHOR>
 * @date 2021/08/30
 */
public enum TimeRangeEnum {
    /**
     * enums
     */
    ALL("0", "全部"),
    TODAY("1", "今天"),
    CURRENT_WEEK("2", "本周"),
    CURRENT_MONTH("3", "本月"),
    CURRENT_SEASON("4", "本季度"),
    CURRENT_YEAR("5", "本年"),
    RECENT_DAY("6", "近24小时"),
    RECENT_WEEK("7", "近一周"),
    RECENT_MONTH("8", "近一月"),
    RECENT_SEASON("9", "近一季度"),
    RECENT_YEAR("10", "近一年"),
    YESTERDAY("11", "昨天"),
    LAST_WEEK("12", "上一周"),
    LAST_MONTH("13", "上一月"),
    LAST_SEASON("14", "上一季度"),
    LAST_YEAR("15", "上一年"),
    CUSTOM("99", "自定义");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    TimeRangeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据枚举值的code解析枚举值
     *
     * @param range code
     * @return {@link TimeRangeEnum}
     */
    public static TimeRangeEnum codeOf(String range) {
        if (StringUtils.isNotBlank(range)) {
            for (TimeRangeEnum typeEnum : TimeRangeEnum.values()) {
                if (StringUtils.equals(range, typeEnum.code)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }
}
