package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.OperationLogEntity;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 一条预警操作记录vo
 *
 * <AUTHOR>
 * @since 2021/9/11
 */
@Data
@NoArgsConstructor
public class WarningLogRecordVO implements Serializable {

    private static final long serialVersionUID = 7829275106175268791L;
    /**
     * 记录信息
     */
    private String logMessage;
    /**
     * 记录日期
     */
    private LocalDateTime logTime;
    /**
     * 记录详情
     */
    private String logDetail;
    /**
     * 记录创建者
     */
    private String creator;
    /**
     * 记录创建者部门
     */
    private String createDept;
    /**
     * 合成=1 指令=2 协作=3
     */
    private String type;
    /**
     * 合成/指令/协作id
     */
    private String id;
    /**
     * 合成/指令/协作标题
     */
    private String title;
    /**
     * 相关附件
     */
    private List<FileInfoVO> files;
    /**
     * 操作记录entity生成预警操作记录vo
     *
     * @param entity 操作记录
     */
    public WarningLogRecordVO(OperationLogEntity entity) {
        this.logMessage = entity.getOverview();
        this.logTime = entity.getCrTime();
        this.logDetail = entity.getDetail();
        this.creator = entity.getCrByName();
        this.createDept = entity.getCrDept();
    }

}
