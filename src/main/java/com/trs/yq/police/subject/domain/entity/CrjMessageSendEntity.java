package com.trs.yq.police.subject.domain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * (TPsCrjMessage)数据访问类
 *
 * <AUTHOR>
 * @since 2022-07-28 17:26:36
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_MESSAGE_SEND")
public class CrjMessageSendEntity extends BaseEntity implements Serializable {
    
    private static final long serialVersionUID = 339881088874541015L;

    /**
     * 接收人 
     */ 
    private String receiver;
                                                                                            
    /**
     * 接收人电话 
     */ 
    private String phone;
                                                                                            
    /**
     * 发送内容 
     */ 
    private String content;
                                                                                            
    /**
     * 发送时间 
     */ 
    private LocalDateTime sendTime;
                                                                                            
    /**
     * 三非人员id 
     */ 
    private String sfryId;
}
