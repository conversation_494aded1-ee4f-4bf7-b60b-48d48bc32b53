package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.JwzhDictEntity;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2022/12/22 16:15
 */
@Repository
public interface JwzhDictRepository extends BaseRepository<JwzhDictEntity,String>{
    /**
     * 获取警情类别
     *
     * @param type 类别(刑事，治安)
     * @return 子类别
     */
    @Query(nativeQuery = true, value = "select * from JWZH_DICT2 where zdbh='BD_D_JQLBDM' and LENGTH(DM)=4 and dm like concat(:type,'%')")
    List<JwzhDictEntity> getJwzhDict(@Param("type") String type);

    /**
     * 获取刑事案件类别
     *
     * @return 子类别
     */
    @Query(nativeQuery = true, value = "select * from JWZH_DICT2 where zdbh='GA_D_XSAJLBDM'  and dm like '%000000'")
    List<JwzhDictEntity> getXsajlbDict();
}
