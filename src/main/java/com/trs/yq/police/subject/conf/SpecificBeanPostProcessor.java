package com.trs.yq.police.subject.conf;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.NonNull;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;
import org.springframework.stereotype.Component;

/**
 * hqc 对RemoteTokenServices注入自定义的UserAuthenticationConverter
 *
 * <AUTHOR>
 */
@Component
public class SpecificBeanPostProcessor implements BeanPostProcessor {


    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {

        if (bean instanceof RemoteTokenServices) {

            DefaultAccessTokenConverter tokenConverter = new DefaultAccessTokenConverter();
            CustomResourceUserAuthenticationConverter converter = new CustomResourceUserAuthenticationConverter();
            tokenConverter.setUserTokenConverter(converter);
            ((RemoteTokenServices) bean).setAccessTokenConverter(tokenConverter);
        }

        return bean;
    }

}
