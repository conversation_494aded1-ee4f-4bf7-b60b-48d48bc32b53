package com.trs.yq.police.subject.operation.event;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 操作日志事件
 *
 * <AUTHOR>
 * @date 2021/8/18 11:03
 */
@Getter
@Setter
@ToString
public class OperationLogEvent implements BaseEvent<OperationLogRecord> {

    private OperationLogRecord operationLogRecord;

    @Override
    public OperationLogRecord getValue() {
        return operationLogRecord;
    }

    @Override
    public void setValue(OperationLogRecord value) {
        this.operationLogRecord = value;
    }
}
