package com.trs.yq.police.subject.mysqlDatasource.entity;

import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import com.trs.yq.police.subject.webService.QgCrjWebService;
import com.trs.yq.police.subject.webService.domain.QueryArgs;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/28 18:00
 */
@Entity
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Table(name = "lz_yq_info")
@Slf4j
public class CrjJwrySourceEntity {

    @Id
    private String id;
    private String hotelApplyId;
    /**
     * 证件种类
     */
    private String zjzl;
    /**
     * 证件号码
     */
    private String zjhm;
    /**
     * 国家地区
     */
    private String gjdq;
    /**
     * 地址
     */
    private String address;
    /**
     * 管辖单位编码
     */
    private String gxdwbm;
    /**
     * 管辖单位名称
     */
    private String gxdwmc;
    /**
     * 云墙处理状态
     */
    private String yqclState;
    /**
     * 云墙处理时间
     */
    private Date yqclTime;
    /**
     * 创建时间
     */
    private Date createTime;

    private String ywx;

    private String ywm;

    private String ywxm;
    /**
     * 中文姓名
     */
    private String zwxm;
    /**
     * 性别
     */
    private String xb;
    /**
     * 出生日期
     */
    private String csrq;
    /**
     * 签证种类
     */
    private String qzzl;
    /**
     * 签证号码
     */
    private String qzhm;
    /**
     * 在华停留至
     */
    private String tingliuqixian;
    /**
     * 入境日期
     */
    private String rjrq;
    /**
     * 入境口岸
     */
    private String rjka;
    /**
     * 数据类型
     */
    private Integer sjlx;
    /**
     * 入住时间
     */
    private String rzsj;
    /**
     * 退房时间
     */
    private String tfsj;
    /**
     * 旅店名称
     */
    private String qiyebmc;


    /**
     * 转CrjJwryEntity
     *
     * @param secretKey aes密钥
     * @return {@link CrjJwryEntity}
     */
    public CrjJwryEntity toCrjJwryEntity(String secretKey) {
        CrjJwryEntity crjJwryEntity = new CrjJwryEntity();
        crjJwryEntity.setRecordId(id);
        crjJwryEntity.setIdType(Objects.isNull(zjzl) ? "14" : zjzl);
        crjJwryEntity.setIdNumber(StringUtil.aesDecrypt(zjhm, secretKey));
        crjJwryEntity.setGjdm(gjdq);
        crjJwryEntity.setAddress(address);
        crjJwryEntity.setDispatchStatus(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode());
        crjJwryEntity.setRegistrationStatus(CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode());
        crjJwryEntity.setCreateTime(createTime);
        crjJwryEntity.setSourceType(sjlx);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            crjJwryEntity.setCheckinTime(StringUtils.isBlank(rzsj) ? null : format.parse(rzsj.substring(0, 19)));
            crjJwryEntity.setLeaveTime(StringUtils.isBlank(tfsj) ? null : format.parse(tfsj.substring(0, 19)));
        } catch (ParseException e) {
            log.error("时间转换出错！", e);
        }
        return crjJwryEntity;
    }

    /**
     * 转CrjJwryDetailEntity
     *
     * @param secretKey aes密钥
     * @return {@link CrjJwryDetailEntity}
     */
    public CrjJwryDetailEntity toJwryDetailEntity(String secretKey) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        CrjJwryDetailEntity detailEntity = new CrjJwryDetailEntity();
        detailEntity.setCnName(zwxm);
        detailEntity.setEnName(ywxm);
        detailEntity.setEnFirstName(ywm);
        detailEntity.setEnLastName(ywx);
        detailEntity.setGender(xb);
        detailEntity.setGjdm(gjdq);
        detailEntity.setIdType(Objects.isNull(zjzl) ? "14" : zjzl);
        detailEntity.setIdNumber(StringUtil.aesDecrypt(zjhm, secretKey));
        if (StringUtils.isNotBlank(qzhm) || StringUtils.isNotBlank(qzzl)) {
            try {
                QgCrjWebService qgCrjWebService = BeanUtil.getBean(QgCrjWebService.class);
                QueryArgs queryArgs = new QueryArgs(gjdq, StringUtil.aesDecrypt(zjhm, secretKey),
                    Objects.isNull(zjzl) ? "14" : zjzl);
                List<CrjJwryVisitVO> crjJwryVisitVOS = qgCrjWebService.stayInfo(queryArgs);
                if (!crjJwryVisitVOS.isEmpty()) {
                    detailEntity.setVisaType(crjJwryVisitVOS.get(0).getVisaType());
                    detailEntity.setVisaNumber(crjJwryVisitVOS.get(0).getVisaNumber());
                    detailEntity.setInChinaTime(crjJwryVisitVOS.get(0).getInChinaTime());
                }
            } catch (Exception e) {
                log.info("全国境外人员综合查询失败！");
            }
        }
        detailEntity.setVisaType(qzzl);
        detailEntity.setVisaNumber(qzhm);
        detailEntity.setEntryPort(rjka);
        if (StringUtils.isBlank(detailEntity.getEnName())) {
            detailEntity.setEnName(ywm + " " + ywx);
        }
        try {
            detailEntity.setBirthday(dateFormat.parse(csrq));
        } catch (Exception e) {
            log.error(String.format("出生日期:%s转换出错！", csrq));
        }
        try {
            detailEntity.setInChinaTime(dateFormat.parse(tingliuqixian));
        } catch (Exception e) {
            log.error(String.format("在华停留时间:%s转换出错！", tingliuqixian));
        }
        try {
            detailEntity.setEntryTime(dateFormat.parse(rjrq));
        } catch (Exception e) {
            log.error(String.format("入境时间:%s转换出错！", rjrq));
        }
        return detailEntity;
    }
}
