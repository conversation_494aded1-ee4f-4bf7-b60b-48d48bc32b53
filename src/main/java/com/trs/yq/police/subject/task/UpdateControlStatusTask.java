package com.trs.yq.police.subject.task;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.constants.enums.MonitorStatusEnum;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;


/**
 * 同步布控状态的定时任务
 *
 * <AUTHOR>
 * @date 2021/8/25 10:23
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.update.control.status.task.enable",havingValue = "true")
public class UpdateControlStatusTask {
    @Value("${com.trs.redis.control.status.key}")
    private String controlStatusKey;
    @Resource
    private PersonRepository personRepository;
    @Resource(name = "assignRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 定时从redis同步人员布控信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.update.controlStatus.task}")
    public void updatePersonControlStatus() {
        List<PersonEntity> personEntities = personRepository.findAll();
        personEntities.forEach(personEntity -> {
            HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
            Object hashVal = hashOperations.get(controlStatusKey, personEntity.getIdNumber());
            String controlInfo = Objects.isNull(hashVal) ? null : hashVal.toString();
            String currentControlStatus = personEntity.getControlStatus();
            if (StringUtils.isNotBlank(controlInfo)) {
                List<JsonNode> objects = JsonUtil.parseArray(controlInfo, JsonNode.class);
                //是否处于布控时间段内
                boolean betweenControlDuration = objects.stream().anyMatch(jsonObject -> {
                    long currentTime = DateUtil.dateTimeToUtc(LocalDateTime.now());
                    long beginTime = jsonObject.get("begintime").asLong();
                    long endTime = jsonObject.get("endtime").asLong();
                    return currentTime > beginTime && currentTime < endTime;
                });
                //如果处于布控时间内并且当前状态为未布控就更新状态为已布控
                if (betweenControlDuration && currentControlStatus.equals(MonitorStatusEnum.NOT_MONITOR.getCode())) {
                    personRepository.updateControlStatusWithId(personEntity.getId(), MonitorStatusEnum.IN_MONITOR.getCode());
                }
                //如果不在布控时间段内并且当前状态为已布控就更新状态为未布控
                if (!betweenControlDuration && currentControlStatus.equals(MonitorStatusEnum.IN_MONITOR.getCode())) {
                    personRepository.updateControlStatusWithId(personEntity.getId(), MonitorStatusEnum.NOT_MONITOR.getCode());

                }
                //如果redis中无数据并且当前状态为已布控就更新状态为未布控
            } else if (currentControlStatus.equals(MonitorStatusEnum.IN_MONITOR.getCode())) {
                personRepository.updateControlStatusWithId(personEntity.getId(), MonitorStatusEnum.NOT_MONITOR.getCode());
            }
        });
    }

}
