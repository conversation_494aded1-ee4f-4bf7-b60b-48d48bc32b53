package com.trs.yq.police.subject.domain.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/1 16:12
 */

@Getter
@Setter
@ToString
public class GroupVO implements Serializable {

    private static final long serialVersionUID = -278226388558424692L;
    private String groupId;
    /**
     * 群体名
     */
    @NotBlank(message = "群体名称缺失")
    private String groupName;
    /**
     * 群体基本情况
     */
    @NotBlank(message = "群体基本情况缺失")
    private String basicInfo;

    /**
     * 群体类别
     */
    private List<IdNameVO> groupTypes;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;
    /**
     * 录入单位
     */
    private String createDept;
    /**
     * 专题id
     */
    private String subjectId;
    /**
     * 主要诉求
     */
    private String mainDemand;
}
