package com.trs.yq.police.subject.constants.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 10:40
 */
public enum ForeignerStatusEnum {
    UNREGISTERED("1", "未登记"),
    LIVING("2", "在住"),
    NOT_REGISTERED("3", "不登记"),
    LEAVE("4", "离开");

    @JsonValue
    @Getter
    private final String code;

    @Getter
    private final String name;

    ForeignerStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
