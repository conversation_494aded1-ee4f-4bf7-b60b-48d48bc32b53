package com.trs.yq.police.subject.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trs.yq.police.subject.domain.entity.StqzClueEntity;
import com.trs.yq.police.subject.domain.entity.StqzCluePersonEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 省厅情指-线索人员mapper
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@DS("stqz")
@Mapper
public interface StqzCluePersonMapper extends BaseMapper<StqzCluePersonEntity> {

    /**
     * 按时间查询线索
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return {@link List}<{@link StqzClueEntity}>
     */
    @Select("select * from zhzh_qzx_xsryxx where cr_time >= #{startTime} and cr_time <= #{endTime}")
    List<StqzCluePersonEntity> selectByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 按线索id查询线索人员
     *
     * @param clueId 线索id
     * @return {@link List}<{@link StqzCluePersonEntity}>
     */
    @Select("select * from zhzh_qzx_xsryxx where xsid = #{clueId}")
    List<StqzCluePersonEntity> selectByClueId(@Param("clueId") String clueId);
}
