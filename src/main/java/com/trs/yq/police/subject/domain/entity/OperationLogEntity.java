package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.jpa.converter.EnumOperateModuleConverter;
import com.trs.yq.police.subject.jpa.converter.EnumOperatorConverter;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 14:11
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_OPERATION_LOG")
public class OperationLogEntity implements Serializable {

    private static final long serialVersionUID = 329604759721148223L;

    /**
     * 主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 数据创建用户id
     */
    private String crBy;

    /**
     * 数据创建用户姓名
     */
    private String crByName;

    /**
     * 数据创建用户名
     */
    private String crByUsername;

    /**
     * 数据创建时间
     */
    @CreatedDate
    private LocalDateTime crTime;

    /**
     * 创建部门名称
     */
    private String crDept;

    /**
     * 创建部门单位代码
     */
    private String crDeptCode;

    /**
     * 操作模块
     */
    @Convert(converter = EnumOperateModuleConverter.class)
    private OperateModule operateModule;

    /**
     * 操作类型
     */
    @Convert(converter = EnumOperatorConverter.class)
    private Operator operator;

    /**
     * 操作概述
     */
    private String overview;

    /**
     * 操作详情
     */
    private String detail;

    /**
     * ip地址
     */
    private String ipAddr;

    /**
     * 目标对象id
     */
    private String targetObjectId;

    /**
     * 目标对象类型
     * 0-person, 1-group, 2-clue
     */
    private Integer targetObjectType;
}

