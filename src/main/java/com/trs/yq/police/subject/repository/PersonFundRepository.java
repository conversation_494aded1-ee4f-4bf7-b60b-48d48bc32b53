package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonFundEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * 资金分析
 *
 * <AUTHOR>
 */
@Repository
public interface PersonFundRepository extends BaseRepository<PersonFundEntity, String> {

    /**
     * 查询资金列表
     *
     * @param personId 人员id
     * @param pageable 分页参数
     * @return 结果
     */
    @Query("select fund from PersonFundEntity fund where fund.personId = :personId order by fund.upTime desc ")
    Page<PersonFundEntity> findAllByPersonId(String personId, Pageable pageable);
}
