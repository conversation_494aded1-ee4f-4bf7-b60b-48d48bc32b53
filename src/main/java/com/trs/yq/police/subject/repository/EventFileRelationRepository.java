package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.EventFileRelationEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/14 11:02
 */
@Repository
public interface EventFileRelationRepository extends BaseRepository<EventFileRelationEntity, String> {
    /**
     * 删除事件-文件关联
     *
     * @param eventId 事件id
     */
    void removeAllByEventId(String eventId);

    /**
     * 根据事件id和文件id删除关联关系
     *
     * @param eventId 事件id
     * @param fileId  文件id
     */
    void deleteByEventIdAndFileStorageId(String eventId, String fileId);

    /**
     * 根据事件id、module、recordId检索文件
     *
     * @param eventId  事件id
     * @param module   模块
     * @param recordId 记录主键
     * @return {@link EventFileRelationEntity}
     */
    List<EventFileRelationEntity> findAllByEventIdAndModuleAndRecordId(String eventId, String module, String recordId);

    /**
     * 删除事件相关的文件
     *
     * @param eventId 事件id
     * @param module  模块
     * @param recordId 记录id
     */
    void deleteByEventIdAndModuleAndRecordId(String eventId, String module, String recordId);
}
