package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 虚拟身份类实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_VIRTUAL_IDENTITY")
public class VirtualIdentityEntity extends BaseEntity {

    private static final long serialVersionUID = -2283993031395923885L;

    /**
     * 人员ID
     */
    private String personId;

    /**
     * 身份类型
     */
    private String virtualType;

    /**
     * 虚拟号码
     */
    private String virtualNumber;

    /**
     * 自定义虚拟身份名称
     */
    private String virtualTypeName;

    /**
     * 是否为自动更新的
     */
    private String isAutomated;
}
