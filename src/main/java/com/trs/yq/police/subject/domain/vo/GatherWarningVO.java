package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 群体聚集预警列表信息vo
 *
 * <AUTHOR>
 * @since 2021/9/1
 */
@Data
public class GatherWarningVO implements Serializable {

    private static final long serialVersionUID = 6269388016104740383L;
    /**
     * id
     */
    private String warningId;
    /**
     * 预警级别
     */
    private String warningLevel;
    /**
     * 时间
     */
    private LocalDateTime warningTime;
    /**
     * 场所
     */
    private String warningPlace;
    /**
     * 人数
     */
    private Integer warningPersonCount;
    /**
     * 照片列表
     */
    private List<ImageVO> images;

}
