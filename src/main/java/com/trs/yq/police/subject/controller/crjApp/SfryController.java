package com.trs.yq.police.subject.controller.crjApp;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.vo.CrjDispatchVO;
import com.trs.yq.police.subject.domain.vo.CrjRedispatchVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SfryReportVO;
import com.trs.yq.police.subject.domain.vo.SfryReportVerifyVO;
import com.trs.yq.police.subject.service.CrjSfryReportService;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 出入境-三非人员
 *
 * <AUTHOR> yanghy
 * @date : 2022/11/16 10:09
 */
@RestController
@RequestMapping("/entry-exit/sfry-report")
public class SfryController {

    @Resource
    CrjSfryReportService crjSfryReportService;

    /**
     * 分页查询
     *
     * @param requestVO 请求参数
     * @return {@link SfryReportVO}
     */
    @PostMapping("/page")
    PageResult<SfryReportVO> getPage(@RequestBody ListRequestVO requestVO) {
        return crjSfryReportService.getPage(requestVO);
    }

    /**
     * 获取详情
     *
     * @param id uuid
     * @return {@link SfryReportVO}
     */
    @GetMapping("/{id}")
    SfryReportVO getById(@PathVariable("id") String id) {
        return crjSfryReportService.getById(id);
    }

    /**
     * 删除三非人员
     *
     * @param ids id
     */
    @PostMapping("/delete")
    void deletedById(@RequestBody List<String> ids) {
        ids.parallelStream().forEach(id->{
            crjSfryReportService.deletedById(id);
        });
    }

    /**
     * 分派
     *
     * @param dispatchVO 分派信息
     */
    @PutMapping("/dispatch")
    void dispatch(@RequestBody @Valid CrjDispatchVO dispatchVO) {
        crjSfryReportService.dispatch(dispatchVO);
    }


    /**
     * 重新分派
     *
     * @param dispatchVO 分派信息
     */
    @PutMapping("/redispatch")
    void redispatch(@RequestBody @Valid CrjRedispatchVO dispatchVO) {
        crjSfryReportService.redispatch(dispatchVO);
    }

    /**
     * 核实
     *
     * @param verifyVO 核实结果
     * @param recordId 记录id
     */
    @PostMapping("/{recordId}/verify")
    void verify(@RequestBody @Valid SfryReportVerifyVO verifyVO, @PathVariable("recordId") String recordId) {
        crjSfryReportService.verify(recordId, verifyVO);
    }
    /**
     * 导出excel
     *
     * @param response  响应体
     * @param request   导出请求
     * @throws IOException IO异常
     */
    @PostMapping("/list/export")
    public void exportSfry(HttpServletResponse response, @RequestBody ExportParams request) throws IOException {
        crjSfryReportService.downLoadExcel(response, request.getFieldNames(), request, request.getSubjectId());
    }
    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getSfryProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return crjSfryReportService.getExportPropertyList(subjectId);
    }
}
