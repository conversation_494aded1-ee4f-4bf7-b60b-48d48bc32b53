package com.trs.yq.police.subject.service.impl;

import com.trs.dubbo.provide.MsgGateway;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.CrjJwrySourceTypeEnum;
import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileTypeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.CrjService;
import com.trs.yq.police.subject.service.DataViewCountService;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.task.UpdateVoiceDataTask;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.CRJ_SUBJECT;

/**
 * 出入境服务层实现
 *
 * <AUTHOR>
 * @since 2021/9/17
 */
@Slf4j
@Service
public class CrjServiceImpl implements CrjService {

    @Resource
    private CrjVisitRecordRepository crjVisitRecordRepository;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private CrjAccommodationRepository crjAccommodationRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private DictService dictService;
    @Resource
    private DataViewCountService dataViewCountService;
    @Resource
    private UnitRepository unitRepository;
    @Resource
    private CrjSfryRepository crjSfryRepository;
    @Resource
    private GridMangerRepository gridMangerRepository;
    @Resource
    private CrjMessageSendRepository crjMessageSendRepository;
    @Resource
    private CrjJwryDetailRepository crjJwryDetailRepository;
    @Resource
    private CrjCzryRepository crjCzryRepository;
    @Resource
    private CrjJwryRepository crjJwryRepository;
    @Resource
    private AlarmYjgjRepository yjgjRepository;

    @DubboReference(check = false)
    private MsgGateway msgGateway;
    Random random = new Random(System.currentTimeMillis());

    @Override
    public WarningScrollListVO getWarningEntryExitPerson(String status) {
        Page<WarningEntity> entities = warningRepository.findAllBySubjects(status,
                DataViewServiceImpl.getDefaultPage());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(entities.getTotalElements());
        List<WarningEntryExitPersonVO> result = entities.stream()
                .map(warningEntity -> {
                    List<CrjJwryDetailEntity> jwryList = crjJwryDetailRepository.findByIdNumber(warningEntity.getWarningKey());
                    WarningEntryExitPersonVO warningEntryExitPersonVO = new WarningEntryExitPersonVO();
                    warningEntryExitPersonVO.setWarningId(warningEntity.getId());
                    warningEntryExitPersonVO.setWarningLevel(warningEntity.getWarningLevel());
                    if (jwryList.isEmpty()) {
                        return null;
                    }
                    warningEntryExitPersonVO.setPersonName(jwryList.get(0).getEnName());
                    switch (warningEntity.getWarningType()) {
                        case "22":
                            warningEntryExitPersonVO.setTypeName("境外人员");
                            break;
                        case "23":
                            warningEntryExitPersonVO.setTypeName("回泸涉疑人员");
                            break;
                        case "24":
                            warningEntryExitPersonVO.setTypeName("非法出入境");
                            break;
                        default:
                            break;
                    }
                    warningEntryExitPersonVO.setWarningTime(warningEntity.getWarningTime());
                    return warningEntryExitPersonVO;
                }).filter(warningEntryExitPersonVO -> Objects.nonNull(warningEntryExitPersonVO) && Objects.nonNull(
                        warningEntryExitPersonVO.getTypeName()))
                .collect(Collectors.toList());
        warningScrollListVO.setItem(result);
        return warningScrollListVO;
    }

    @Override
    public WarningScrollListVO getAccommodationWarning(String status) {
        return getRegisterOverdue(status, "49");
    }

    @Override
    public WarningScrollListVO getOverdue(String status) {
        return getRegisterOverdue(status, "48");
    }

    @Override
    public HarmfulBorderVO getHarmful(TimeParams timeParams) {
        return new HarmfulBorderVO(random.nextInt(100), random.nextInt(100), random.nextInt(100));
    }

    private WarningScrollListVO getRegisterOverdue(String status, String warningType) {
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(20);
        Page<WarningEntity> warningEntities = warningRepository.findAllByWarningTypeAndStatusPage(warningType, status, pageParams.toPageable());
        WarningScrollListVO warningScrollListVO = new WarningScrollListVO();
        warningScrollListVO.setTotal(warningEntities.getTotalElements());
        List<RegisterOverdueVO> list = warningEntities.stream().map(warning -> {
            RegisterOverdueVO vo = new RegisterOverdueVO();
            vo.setWarningId(warning.getId());
            vo.setWarningLevel(warning.getWarningLevel());
            vo.setWarningTime(warning.getWarningTime());
            if ("48".equals(warningType)) {
                vo.setWarningPassportNo(warning.getWarningKey());
            } else if ("49".equals(warningType)) {
                vo.setWarningCountry("境外人员");
                WarningTrajectoryEntity track = warningTrajectoryRepository.findTraceByWarningId(warning.getId());
                if (Objects.nonNull(track)) {
                    AlarmYjgjEntity yjgj = yjgjRepository.getById(track.getSourceId());
                    vo.setWarningPassportNo(yjgj.getGlxm());
                }
            }
            if (warning.getWarningSource().get(0).equals("临住人员")) {
                List<CrjJwryDetailEntity> jwryDetail =
                        crjJwryDetailRepository.findByIdNumber(warning.getWarningKey());
                if (!jwryDetail.isEmpty()) {
                    vo.setWarningCountry(jwryDetail.get(0).getGjdm());
                }
            } else if (warning.getWarningSource().get(0).equals("常住人员")) {
                CrjCzryEntity czry = crjCzryRepository.findByCertificateNumber(warning.getWarningKey());
                vo.setWarningCountry(czry.getCountry());
            }
            return vo;
        }).collect(Collectors.toList());
        warningScrollListVO.setItem(list);
        return warningScrollListVO;
    }

    //TODO 真实数据接入

    @Override
    public List<CountTypeResponseVO> getRegistrationCount(TimeParams timeParams) {
        List<CountTypeResponseVO> results = new ArrayList<>();
        List<CrjJwryEntity> time = crjJwryRepository.findByTime(timeParams.getBeginTime(), timeParams.getEndTime());
        for (CrjJwrySourceTypeEnum value : CrjJwrySourceTypeEnum.values()) {
            results.add(new CountTypeResponseVO(value.getName(), time.stream().filter(item -> value.getCode().equals(item.getSourceType())).count()));
        }
        return results;
    }

    //TODO 真实数据接入

    @Override
    public List<CountTypeResponseVO> getNationalityCount(TimeParams timeParams) {
        List<CountTypeResponseVO> results = new ArrayList<>();
        List<CrjJwryEntity> time = crjJwryRepository.findByTime(timeParams.getBeginTime(), timeParams.getEndTime()).stream().filter(item -> StringUtils.isNotBlank(item.getGjdm())).collect(
                Collectors.toList());
        results.add(new CountTypeResponseVO("美国", time.stream().filter(item -> item.getGjdm().equals("USA")).count()));
        results.add(new CountTypeResponseVO("加拿大", time.stream().filter(item -> item.getGjdm().equals("CAN")).count()));
        results.add(new CountTypeResponseVO("法国", time.stream().filter(item -> item.getGjdm().equals("FRA")).count()));
        results.add(new CountTypeResponseVO("英国", time.stream().filter(item -> item.getGjdm().equals("GBR")).count()));
        results.add(new CountTypeResponseVO("澳大利亚", time.stream().filter(item -> item.getGjdm().equals("AUS")).count()));
        results.add(new CountTypeResponseVO("意大利", time.stream().filter(item -> item.getGjdm().equals("ITA")).count()));
        results.add(new CountTypeResponseVO("德国", time.stream().filter(item -> item.getGjdm().equals("DEU")).count()));
        results.add(new CountTypeResponseVO("日本", time.stream().filter(item -> item.getGjdm().equals("JPN")).count()));
        results.add(new CountTypeResponseVO("其他", (long) time.size() - results.stream().mapToLong(CountTypeResponseVO::getPersonCount).sum()));
        return results;
    }

    //TODO 真实数据接入

    @Override
    public List<VisaWaiverCountVO> getVisaWaiverCount(TimeParams timeParams) {
        List<VisaWaiverCountVO> results = new ArrayList<>();
        results.add(new VisaWaiverCountVO("成都", 82, 25));
        results.add(new VisaWaiverCountVO("重庆", 72, 3));
        return results;
    }

    @Override
    public PageResult<CrjVisitListVO> getCrjVisitListVOList(CrjVisitListRequestVO crjVisitListRequestVO) {
        PageParams pageParams = crjVisitListRequestVO.getPageParams();
        TimeParams timeParams = crjVisitListRequestVO.getTimeParams();
        CrjVisitListRequestVO.OtherParams otherParams =
                Objects.isNull(crjVisitListRequestVO.getOtherParams()) ? new CrjVisitListRequestVO.OtherParams()
                        : crjVisitListRequestVO.getOtherParams();
        Page<CrjVisitRecordEntity> crjVisitRecordList = crjVisitRecordRepository.getCrjVisitRecordList(
                otherParams.getVisitType(),
                otherParams.getDeptName(),
                otherParams.getSearchValue(),
                timeParams.getBeginTime(),
                timeParams.getEndTime(),
                pageParams.toPageable());
        List<CrjVisitListVO> result = crjVisitRecordList.stream().map(crjVisitRecordEntity -> {
            CrjVisitListVO crjVisitListVO = new CrjVisitListVO();
            BeanUtil.copyPropertiesIgnoreNull(crjVisitRecordEntity, crjVisitListVO);
            crjVisitListVO.setName(crjVisitRecordEntity.getFirstName() + " " + crjVisitRecordEntity.getLastName());
            crjVisitListVO.setDeptName(crjVisitRecordEntity.getCrDept());
            crjVisitListVO.setVisitType(
                    dictService.getDictEntityByTypeAndCode("ps_person_visit_method", crjVisitRecordEntity.getVisitType())
                            .getName());
            crjVisitListVO.setCertificateType(dictService.getDictEntityByTypeAndCode("crj_certificate_type",
                    crjVisitRecordEntity.getCertificateType()).getName());
            return crjVisitListVO;
        }).collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), crjVisitRecordList.getTotalElements(),
                pageParams.getPageSize());
    }

    @Override
    public CrjWarningCountVO getWarningCount(TimeParams timeParams) {
        CrjWarningCountVO count = new CrjWarningCountVO();
        List<WarningEntity> warningList = warningRepository.findAllBySubjectId(CRJ_SUBJECT, timeParams.getBeginTime(),
                timeParams.getEndTime());
        //count.setTotal(warningList.size());

        //TODO 数据源名称
        count.setShiMing(random.nextInt(100));
        count.setQiaKou(random.nextInt(100));
        count.setRenXiang(random.nextInt(100));
        count.setDianWei(random.nextInt(100));
        count.setMac(random.nextInt(100));
        count.setTotal(
                count.getShiMing() + count.getQiaKou() + count.getRenXiang() + count.getDianWei() + count.getMac());
        return count;
    }

    /**
     * 统计所有预警中预警来源包含该来源的预警数量
     *
     * @param list   全部预警
     * @param source 预警来源
     * @return 统计数量
     */
    private Integer countByWarningSource(List<WarningEntity> list, String source) {
        return (int) list.stream().filter(e -> e.getWarningSource().contains(source)).count();
    }

    @Override
    public CrjVisitVO getCrjVisitVOList(String visitId) {
        CrjVisitRecordEntity crjVisitRecordEntity = crjVisitRecordRepository.findById(visitId).orElse(null);
        if (Objects.isNull(crjVisitRecordEntity)) {
            throw new ParamValidationException("走访信息不存在，请刷新核实");
        }
        CrjVisitVO crjVisitVO = new CrjVisitVO();
        BeanUtils.copyProperties(crjVisitRecordEntity, crjVisitVO);
        crjVisitVO.setName(crjVisitRecordEntity.getFirstName() + " " + crjVisitRecordEntity.getLastName());
        crjVisitVO.setDeptName(crjVisitRecordEntity.getCrDept());
        crjVisitVO.setVisitType(
                dictService.getDictEntityByTypeAndCode("ps_person_visit_method", crjVisitRecordEntity.getVisitType())
                        .getName());
        crjVisitVO.setControlStatus(
                dictService.getDictEntityByTypeAndCode("crj_control_status", crjVisitRecordEntity.getControlStatus())
                        .getName());
        crjVisitVO.setCertificateType(
                dictService.getDictEntityByTypeAndCode("crj_certificate_type", crjVisitRecordEntity.getCertificateType())
                        .getName());
        crjVisitVO.setImages(fileStorageRepository.findAllByCrjIdAndModule(visitId,
                        FileTypeEnum.IMAGE.getCode(),
                        FileModuleEnum.CRJ_VISIT_RECORD_PHOTO.getCode()).stream()
                .filter(Objects::nonNull)
                .map(ImageVO::of)
                .collect(Collectors.toList()));
        return crjVisitVO;
    }

    @Override
    public CrjAccommodationVO getAccommodationDetail(String id) {
        CrjAccommodationVO accommodation = new CrjAccommodationVO();
        CrjAccommodationEntity entity = crjAccommodationRepository.getById(id);
        accommodation.setName(entity.getLastName() + "" + entity.getFirstName());
        accommodation.setNationality(entity.getNationality());
        //accommodation.setNationality(dictService.getDictEntityByTypeAndCode("", entity.getNationality()).getName());
        accommodation.setCertificateType(
                dictService.getDictEntityByTypeAndCode("crj_certificate_type", entity.getCertificateType()).getName());
        accommodation.setCertificateNumber(entity.getCertificateNumber());
        accommodation.setPhoneNumber(entity.getPhoneNumber());
        accommodation.setLivingInfo(entity.getLivingInfo());
        accommodation.setDepartureDate(entity.getDepartureDate());
        List<ImageVO> images = fileStorageRepository.findAllByCrjIdAndModule(id,
                        FileTypeEnum.IMAGE.getCode(),
                        FileModuleEnum.CRJ_ACCOMMODATION_PHOTO.getCode())
                .stream()
                .filter(Objects::nonNull)
                .map(ImageVO::of)
                .collect(Collectors.toList());
        if (!images.isEmpty()) {
            accommodation.setImage(images.get(0));
        }
        return accommodation;
    }

    @Override
    public PageResult<CrjAccommodationListVO> getCrjAccommodationListVOList(
            CrjAccommodationListRequestVO crjAccommodationListRequestVO) {
        PageParams pageParams = crjAccommodationListRequestVO.getPageParams();
        TimeParams timeParams = crjAccommodationListRequestVO.getTimeParams();
        CrjAccommodationListRequestVO.OtherParams otherParams =
                Objects.isNull(crjAccommodationListRequestVO.getOtherParams())
                        ? new CrjAccommodationListRequestVO.OtherParams() : crjAccommodationListRequestVO.getOtherParams();
        Page<CrjAccommodationEntity> crjAccommodationRecordList = crjAccommodationRepository.getCrjAccommodationRecordPageList(
                timeParams.getBeginTime(),
                timeParams.getEndTime(),
                otherParams.getSearchValue(),
                pageParams.toPageable());
        List<CrjAccommodationListVO> result = crjAccommodationRecordList.stream().map(item -> {
            CrjAccommodationListVO crjAccommodationListVO = new CrjAccommodationListVO();
            BeanUtils.copyProperties(item, crjAccommodationListVO);
            crjAccommodationListVO.setName(item.getFirstName() + " " + item.getLastName());
            crjAccommodationListVO.setRegistryTime(item.getCrTime());
            crjAccommodationListVO.setCertificateType(
                    dictService.getDictEntityByTypeAndCode("crj_certificate_type", item.getCertificateType()).getName());
            return crjAccommodationListVO;
        }).collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), crjAccommodationRecordList.getTotalElements(),
                pageParams.getPageSize());
    }

    @Override
    public List<UnitVO> getListUnitTree() {
        List<UnitEntity> areaUnits = unitRepository.findByType("2");
        List<UnitEntity> countyUnits = unitRepository.findByType("3");
        List<UnitEntity> stationUnits = unitRepository.findByType("4");
        List<UnitVO> results = new ArrayList<>();
        areaUnits.forEach(areaUnit -> {
            //机场分局不需要
            if ("510500430000".equals(areaUnit.getUnitCode())) {
                UnitVO unitVO = new UnitVO(areaUnit.getUnitName(), areaUnit.getUnitCode(), null);
                results.add(unitVO);
            } else {
                List<UnitVO> children = Stream.concat(
                        countyUnits.stream()
                                .filter(
                                        countyUnit -> countyUnit.getUnitCode().startsWith(areaUnit.getAreaCode().substring(0, 6)))
                                .map(child -> new UnitVO(child.getUnitCode(), child.getUnitName())),
                        stationUnits.stream()
                                .filter(
                                        countyUnit -> countyUnit.getUnitCode().startsWith(areaUnit.getAreaCode().substring(0, 6)))
                                .map(child -> new UnitVO(child.getUnitCode(), child.getUnitName()))
                ).collect(Collectors.toList());
                UnitVO areaValue = new UnitVO(areaUnit.getUnitName(), areaUnit.getUnitCode(), children);
                results.add(areaValue);
            }
        });
        return results;
    }

    @Override
    public List<CountTypeResponseVO> getDiseaseInflow(TimeParams timeParams) {
        List<CountTypeResponseVO> results = new ArrayList<>();
        results.add(new CountTypeResponseVO("美国", (long) random.nextInt(100)));
        results.add(new CountTypeResponseVO("澳大利亚", (long) random.nextInt(100)));
        results.add(new CountTypeResponseVO("伊朗", (long) random.nextInt(100)));
        results.add(new CountTypeResponseVO("马来西亚", (long) random.nextInt(100)));
        results.add(new CountTypeResponseVO("俄罗斯", (long) random.nextInt(100)));
        results.add(new CountTypeResponseVO("日本", (long) random.nextInt(100)));
        return results;
    }

    @Override
    public List<CrjDiseaseAreaCountVO> getDiseaseAreaCount(TimeParams timeParams) {
        List<AreaVO> areas = dataViewCountService.getAreaInfo();
        AreaVO luZhouCity = new AreaVO();
        luZhouCity.setAreaCode("5105");
        luZhouCity.setAreaName("泸州市");
        areas.remove(luZhouCity);
        return areas.stream().map(area -> {
            CrjDiseaseAreaCountVO vo = new CrjDiseaseAreaCountVO();
            vo.setAreaCode(area.getAreaCode());
            vo.setAreaName(area.getAreaName());
            vo.setPersonCount(crjJwryRepository.findAllByAddressLikeAndTime(area.getAreaCode(), timeParams.getBeginTime(), timeParams.getEndTime()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<CrjSfryListVO> getSwryList(CrySfryRequestVO requestVO) {
        final LocalDateTime beginTime = requestVO.getTimeParams().getBeginTime();
        final LocalDateTime endTime = requestVO.getTimeParams().getEndTime();
        final PageParams pageParams = requestVO.getPageParams();
        final Page<CrjSfryEntity> page = crjSfryRepository.findSfryPageList(requestVO.getSearchValue(),
                StringUtil.getPoliceStationPrefix(requestVO.getDeptCode()), beginTime, endTime, requestVO.getStatus(),
                pageParams.toPageable());
        final List<CrjSfryListVO> result = page.getContent().stream().map(entity -> {
            final CrjSfryListVO vo = new CrjSfryListVO();
            BeanUtil.copyPropertiesIgnoreNull(entity, vo);
            vo.setGxdwmc(StringUtils.isNotBlank(entity.getGxdwmc()) ? entity.getGxdwmc() : entity.getGxdwbm());
            return vo;
        }).collect(Collectors.toList());
        return PageResult.of(result, pageParams.getPageNumber(), page.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public CrjSfryBaseVO getSfryInfo(String sfryId) {
        final CrjSfryEntity entity = crjSfryRepository.findById(sfryId)
                .orElseThrow(() -> new ParamValidationException("三非人员不存在，请刷新重试"));
        final CrjSfryBaseVO vo = new CrjSfryBaseVO();
        BeanUtil.copyPropertiesIgnoreNull(entity, vo);
        final List<CrjMessageSendVO> messageSendList = crjMessageSendRepository.findBySfryIdOrderBySendTimeDesc(entity.getId())
                .stream()
                .map(CrjMessageSendVO::of)
                .collect(Collectors.toList());
        vo.setMsgList(messageSendList);
        vo.setGxdwmc(StringUtils.isNotBlank(entity.getGxdwmc()) ? entity.getGxdwmc() : entity.getGxdwbm());
        return vo;

    }

    @Override
    public List<GridTreeVO> getGridTree() {
        final List<UnitEntity> area = unitRepository.findByTypeAndCodeLike("1", "5105").stream()
                .filter(unit -> !"5105".equals(unit.getAreaCode())).collect(Collectors.toList());
        final List<GridMangerEntity> gridMangerEntities = gridMangerRepository.findAll();
        final List<GridTreeVO> result = new ArrayList<>();
        area.forEach(unitEntity -> {
            final GridTreeVO vo = new GridTreeVO();
            vo.setId(UUID.randomUUID().toString());
            vo.setName(unitEntity.getShortname());
            vo.setCode(unitEntity.getAreaCode());
            final List<GridTreeVO> first = gridMangerEntities.stream()
                    .filter(entity -> unitEntity.getAreaCode().equals(entity.getAreaCode()) && StringUtils.isNotBlank(
                            entity.getCommunity()))
                    .map(GridMangerEntity::getCommunity)
                    .distinct()
                    .map(community -> {
                        final GridTreeVO child = new GridTreeVO();
                        child.setId(UUID.randomUUID().toString());
                        child.setName(community);
                        child.setCode(UUID.randomUUID().toString());
                        child.setChildren(
                                gridMangerEntities
                                        .stream()
                                        .filter(gridMangerEntity -> community.equals(gridMangerEntity.getCommunity()))
                                        .map(grid -> {
                                            final GridTreeVO unitVO = new GridTreeVO();
                                            unitVO.setId(grid.getId());
                                            unitVO.setName(grid.getGridName());
                                            unitVO.setCode(grid.getGridManagerPhoneNumber());
                                            return unitVO;
                                        }).collect(Collectors.toList())
                        );
                        return child;
                    }).collect(Collectors.toList());
            vo.setChildren(first);
            result.add(vo);
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void sendMsg(String sfryId, String gridPhoneNumber) throws Exception {
        final LoginUser currentUser = AuthHelper.getCurrentUser();
        final CrjSfryEntity crjSfryEntity = crjSfryRepository.findById(sfryId)
                .orElseThrow(() -> new ParamValidationException("当前人员不存在，请刷新重试"));
        final GridMangerEntity grid = gridMangerRepository.findByGridManagerPhoneNumber(gridPhoneNumber);
        String msgTemplate = crjMessageSendRepository.getMessageTemplate();
        Map<String, String> data = new HashMap<>();
        data.put("gridName", StringUtils.isBlank(grid.getGridName()) ? " " : grid.getGridName());
        data.put("name", StringUtils.isNotBlank(crjSfryEntity.getZwxm()) ? crjSfryEntity.getZwxm() : crjSfryEntity.getYwxm());
        data.put("zjhm", StringUtils.isBlank(crjSfryEntity.getZjhm()) ? " " : crjSfryEntity.getZjhm());
        data.put("rzsj", Objects.isNull(crjSfryEntity.getRzsj()) ? " " : crjSfryEntity.getRzsj().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")));
        data.put("address", StringUtils.isBlank(crjSfryEntity.getAddress()) ? " " : crjSfryEntity.getAddress());
        final String content = UpdateVoiceDataTask.renderString(msgTemplate, data);
        final LocalDateTime now = LocalDateTime.now();
        msgGateway.sendMessage(content, currentUser.getRealName(), gridPhoneNumber);
        crjSfryEntity.setIsSendMessage(true);
        crjSfryEntity.setUpdateTime(now);
        crjSfryRepository.save(crjSfryEntity);

        //存储短信记录
        final CrjMessageSendEntity msgEntity = new CrjMessageSendEntity();
        msgEntity.setContent(content);
        msgEntity.setPhone(gridPhoneNumber);
        msgEntity.setReceiver(grid.getGridName());
        msgEntity.setSendTime(now);
        msgEntity.setSfryId(sfryId);
        crjMessageSendRepository.save(msgEntity);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGridPhoneNumber(String gridId, String phoneNumber) {
        gridMangerRepository.updateGridPhoneNumber(gridId, phoneNumber);
    }
}
