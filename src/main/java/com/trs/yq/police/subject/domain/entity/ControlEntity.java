package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 管控信息实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_CONTROL")
public class ControlEntity extends BaseEntity {

    private static final long serialVersionUID = 6471884855653701132L;
    /**
     * 人员id
     */
    private String personId;

    /**
     * 派出所code
     */
    private String policeStationCode;

    /**
     * 派出所名称
     */
    private String policeStationName;

    /**
     * 派出所责任领导
     */
    private String leaderName;

    /**
     * 责任领导职务
     */
    private String leaderJob;

    /**
     * 责任领导联系方式
     */
    private String leaderContact;

    /**
     * 责任人id
     */
    private String responsibleId;

    /**
     * 责任人姓名
     */
    private String responsibleName;

    /**
     * 责任人职务
     */
    private String responsibleJob;

    /**
     * 责任人联系方式
     */
    private String responsibleContact;

    /**
     * 专题id
     */
    private String subjectId;

    /**
     * 管控级别
     */
    private String controlLevel;
}
