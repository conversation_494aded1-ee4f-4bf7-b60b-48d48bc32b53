package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.io.FileUtils;

/**
 * 上传附件vo
 *
 * <AUTHOR>
 * @date 2021/09/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttachmentVO {

    /**
     * 附件id
     */
    private String id;

    /**
     * 文件名
     */
    private String name;

    /**
     * 附件url
     */
    private String url;

    /**
     * 文件大小
     */
    private String size;

    /**
     * 视频文件预览图片url
     */
    private String previewImage;

    /**
     * 转VO
     *
     * @param fileStorageEntity 文件
     * @return {@link FileInfoVO}
     */
    public static AttachmentVO of(FileStorageEntity fileStorageEntity) {
        AttachmentVO vo = new FileInfoVO();
        vo.setSize(FileUtils.byteCountToDisplaySize(fileStorageEntity.getFileSize()));
        vo.setId(fileStorageEntity.getId());
        vo.setUrl(fileStorageEntity.getUrl());
        vo.setName(fileStorageEntity.getName());
        vo.setPreviewImage(fileStorageEntity.getPreviewImage());
        return vo;
    }
}
