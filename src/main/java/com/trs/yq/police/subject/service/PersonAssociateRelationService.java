package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.controller.TestController;
import com.trs.yq.police.subject.domain.dto.PersonAssociateRelationDTO;
import org.apache.commons.csv.CSVRecord;

import java.io.IOException;
import java.util.List;

/**
 * 查询关联&关系服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/13
 */
public interface PersonAssociateRelationService {

    /**
     * 根据身份证号查询人员关联以及关系
     *
     * @param idNumber 身份证号
     * @return 人员属性关联关系列表
     */
    PersonAssociateRelationDTO queryAssociateAndRelation(String idNumber);

    /**
     * 从禁毒卷宗人员基本信息csv文本导入人员信息
     *
     * @param csvRecords csv文本
     */
    void importPersonFromAds(List<CSVRecord> csvRecords);

    /**
     * 从禁毒卷宗手机号码基本信息csv文件导入手机号码
     *
     * @param csvRecords csv文本
     */
    void importPhoneFromAds(List<CSVRecord> csvRecords);

    /**
     * 从禁毒卷宗人物关系信息csv文件导入人物关系
     *
     * @param csvRecords csv文本
     */
    void importRelationFromAds(List<CSVRecord> csvRecords) throws IOException;

    /**
     * 查询图
     *
     * @param id   id
     * @param type 对象id
     * @return 一度关联节点
     */
    TestController.RelationGraphVo graph(String id, String type);
}
