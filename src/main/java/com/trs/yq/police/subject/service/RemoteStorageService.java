package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.ClueModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.domain.vo.AttachmentVO;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import javax.annotation.Nullable;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

/**
 * 远端存储接口
 *
 * <AUTHOR>
 * @date 2021/7/30 9:49
 */
@Validated
public interface RemoteStorageService {

    /**
     * 上传图片
     *
     * @param buff          字节数组
     * @param groupName     存储组名
     * @param fileExtension 文件拓展名
     * @return 图片路径
     */
    String[] uploadFile(byte[] buff, String groupName, String fileExtension);

    /**
     * 删除文件
     *
     * @param groupName 组名
     * @param fileId    文件ID
     */
    void deleteFile(String groupName, String fileId);

    /**
     * 获取文件
     *
     * @param groupName 分组
     * @param fileId    文件id
     * @return 文件内容
     */
    byte[] downloadFile(String groupName, String fileId);

    /**
     * 上传图片
     *
     * @param image 图片
     * @return 图片存储地址 / 图片id
     * @throws IOException 文件异常
     */
    ImageVO uploadImage(MultipartFile image) throws IOException;

    /**
     * 替换现有图片
     *
     * @param bytes    字节流
     * @param fileName 文件名
     * @return {@link ImageVO}
     * @throws IOException io异常
     */
    ImageVO uploadImage(byte[] bytes, String fileName) throws IOException;

    /**
     * 批量上传图片
     *
     * @param images 图片
     * @return 路径
     * @throws IOException 文件处理异常
     */
    List<ImageVO> uploadImages(List<MultipartFile> images) throws IOException;

    /**
     * 上传图片与人员关联
     *
     * @param personId 人员id
     * @param images   关联的图片
     * @param module   模块
     * @param recordId 记录主键
     */
    void savePersonImageRelations(String personId, List<ImageVO> images, FileModuleEnum module, @Nullable String recordId);

    /**
     * 更新图片与人员关联
     *
     * @param personId 人员id
     * @param images   图片
     * @param module   模块
     * @param recordId 记录主键
     */
    void updatePersonImageRelations(String personId, List<ImageVO> images, FileModuleEnum module, @Nullable String recordId);

    /**
     * 删除图片与人员的关联
     *
     * @param personId 人员id
     * @param module   模块
     * @param visitId  记录主键
     */
    void deletePersonImageRelations(String personId, FileModuleEnum module, @Nullable String visitId);

    /**
     * 删除线索照片关系
     *
     * @param clueId           人员id
     * @param clueMessagePhoto 模块
     * @param recordId         记录主键
     */
    void deleteClueImageRelation(String clueId, ClueModuleEnum clueMessagePhoto, @Nullable String recordId);

    /**
     * 上传线索附件
     *
     * @param file 文件
     * @return {@link AttachmentVO}
     */
    List<AttachmentVO> uploadAttachments(List<MultipartFile> file);

    /**
     * 下载指定id文件
     *
     * @param fileId 文件
     * @return {@link ResponseEntity}
     * @throws UnsupportedEncodingException url编码异常
     */
    ResponseEntity<ByteArrayResource> downLoadFile(String fileId) throws UnsupportedEncodingException;

    /**
     * 获取大头照
     *
     * @param idNumber 身份证号
     * @return 图片
     */
    byte[] downloadPhoto(String idNumber);
}
