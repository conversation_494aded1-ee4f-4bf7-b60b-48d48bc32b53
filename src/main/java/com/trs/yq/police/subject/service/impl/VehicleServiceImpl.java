package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.VehicleEntity;
import com.trs.yq.police.subject.domain.vo.VehicleVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.VehicleRepository;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.service.VehicleService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.AUTOMATED;

/**
 * 人员车辆信息业务层
 *
 * <AUTHOR>
 * @date 2021/7/28 9:47
 */
@Service
public class VehicleServiceImpl implements VehicleService {
    @Resource
    private VehicleRepository vehicleRepository;
    @Resource
    private PersonService personService;
    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public List<VehicleVO> getVehicleInfo(String personId) {
        //检查人员是否存在
        personService.checkPersonExist(personId);
        List<VehicleEntity> vehicleEntityList = vehicleRepository.findByPersonId(personId);
        if (!Objects.isNull(vehicleEntityList)) {
            List<VehicleVO> vehicleVOList = new ArrayList<>();
            vehicleEntityList.forEach(item -> {
                VehicleVO vehicleVO = new VehicleVO();
                BeanUtil.copyPropertiesIgnoreNull(item, vehicleVO);
                vehicleVO.setType(item.getType());
                vehicleVOList.add(vehicleVO);
            });
            return vehicleVOList;
        }
        return Collections.emptyList();
    }

    @Override
    public void saveVehicle(String personId, VehicleVO vehicleVO) {
        //检查人员是否存在
        personService.checkPersonExist(vehicleVO.getPersonId());
        VehicleEntity vehicleEntity = new VehicleEntity();
        BeanUtil.copyPropertiesIgnoreNull(vehicleVO, vehicleEntity);

        // 记录此操作
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.VEHICLE)
                .newObj(JsonUtil.toJsonString(vehicleEntity))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增车辆信息")
                .build();
        vehicleRepository.save(vehicleEntity);

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void deleteVehicle(String personId, String id) {
        //检查人员是否存在
        personService.checkPersonExist(personId);

        VehicleEntity oldVehicleEntity = vehicleRepository.findById(id).orElse(null);

        if (Objects.isNull(oldVehicleEntity)) {
            throw new ParamValidationException("车辆信息不存在，请刷新核实!");
        }

        if(StringUtils.equals(oldVehicleEntity.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可删除");
        }
        // 记录此操作
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.VEHICLE)
                .oldObj(JsonUtil.toJsonString(oldVehicleEntity))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除车辆信息")
                .build();

        vehicleRepository.deleteById(id);

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public void updateVehicle(String personId, VehicleVO vehicleVO) {

        //检查当前人员是否存在
        personService.checkPersonExist(personId);

        VehicleEntity oldVehicleEntity = vehicleRepository.findById(vehicleVO.getId()).orElse(null);

        if (Objects.isNull(oldVehicleEntity)) {
            throw new ParamValidationException("车辆信息不存在，请刷新核实!");
        }

        if(StringUtils.equals(oldVehicleEntity.getIsAutomated(), AUTOMATED)) {
            throw new ParamValidationException("自动更新插入的数据不可修改");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.VEHICLE)
                .oldObj(JsonUtil.toJsonString(oldVehicleEntity))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑车辆信息")
                .build();
        oldVehicleEntity.setOwner(vehicleVO.getOwner());
        oldVehicleEntity.setVehicleNumber(vehicleVO.getVehicleNumber());
        oldVehicleEntity.setType(vehicleVO.getType());
        vehicleRepository.save(oldVehicleEntity);
        logRecord.setNewObj(JsonUtil.toJsonString(oldVehicleEntity));
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

}
