package com.trs.yq.police.subject.domain.entity;

import java.util.Date;
import java.util.Objects;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

/**
 * 境外人员走访记录
 *
 * <AUTHOR> yanghy
 * @date : 2022/11/16 15:17
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_JWRY_VISIT")
public class CrjJwryVisitEntity extends BaseEntity{


    private String idType;
    private String idNumber;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 居住地址
     */
    private String liveAddress;
    /**
     * 工作地址
     */
    private String workAddress;
    /**
     * 拟定离开时间
     */
    private Date planLeaveTime;
    /**
     * 走访时间
     */
    private Date visitTime;
    /**
     * 即时定位
     */
    private String instantAddress;
    /**
     * 走访情况
     */
    private String detail;

    private String registrationStatus;

    private String attachment;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        CrjJwryVisitEntity that = (CrjJwryVisitEntity) o;
        return getId() != null && Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
