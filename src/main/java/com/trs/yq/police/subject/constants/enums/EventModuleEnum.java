package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 * 事件的模块枚举
 *
 * <AUTHOR>
 * @date 2021/09/03
 */

public enum EventModuleEnum {
    /**
     * enums
     */
    Event_MESSAGE_PHOTO("3", "事件信息照片"),
    Event_MESSAGE_WORD("4", "事件信息附件"),
    Event_PERSON_PHOTO("5", "人员涉事照片");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    EventModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
