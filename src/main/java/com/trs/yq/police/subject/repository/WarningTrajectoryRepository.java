package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.WarningTrajectoryEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预警轨迹持久层接口类
 *
 * <AUTHOR>
 * @date 2021/9/6 12:07
 */
@SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
@Repository
public interface WarningTrajectoryRepository extends BaseRepository<WarningTrajectoryEntity, String> {

    /**
     * 根据预警id查询轨迹列表
     *
     * @param warningId 预警id
     * @return 轨迹列表 {@link WarningTrajectoryEntity}
     */
    @Query("SELECT wt FROM WarningTrajectoryEntity wt JOIN WarningTraceRelationEntity wtr ON wt.id = wtr.trajectoryId WHERE wtr.warningId = :warningId")
    List<WarningTrajectoryEntity> findTraceListByWarningId(@Param("warningId") String warningId);

    /**
     * 根据预警id查询轨迹列表
     *
     * @param warningId 预警id
     * @return 轨迹列表 {@link WarningTrajectoryEntity}
     */
//    @Query("SELECT wt FROM WarningTrajectoryEntity wt JOIN WarningTraceRelationEntity wtr ON wt.id = wtr.trajectoryId WHERE wtr.warningId = :warningId")
    @Query("select t from WarningTrajectoryEntity  t where t.id = (SELECT max(wt.id) FROM WarningTrajectoryEntity wt JOIN WarningTraceRelationEntity wtr ON wt.id = wtr.trajectoryId WHERE wtr.warningId = :warningId)")
    WarningTrajectoryEntity findTraceByWarningId(@Param("warningId") String warningId);

    /**
     * 根据轨迹id查询轨迹来源类型名称
     *
     * @param id 轨迹id
     * @return 活动来源类型名称
     */
    @Query(value = "select name from t_ps_warning_trajectory wt join t_ps_trajectory_source ts on wt.source_id = ts.id where wt.id = :trajectoryId", nativeQuery = true)
    String findSourceNameByTrajectoryId(@Param("trajectoryId") String id);

    /**
     * 根据轨迹id查询人员entity
     *
     * @param trajectoryId 轨迹id
     * @return {@link PersonEntity}
     */
    @Query("select p from PersonEntity p join WarningTrajectoryEntity wtr on p.idNumber = wtr.idNumber where wtr.id = :trajectoryId")
    PersonEntity findPersonByTrajectoryId(@Param("trajectoryId") String trajectoryId);

    /**
     * 根据来源id，身份证号，时间查询轨迹
     *
     * @param sourceId 来源id
     * @param idNumber 身份证号
     * @param dateTime 时间
     * @param lng      经度
     * @param lat      纬度
     * @return 查询结果
     */
    List<WarningTrajectoryEntity> findBySourceIdAndIdNumberAndDateTimeAndLngAndLat(String sourceId, String idNumber, LocalDateTime dateTime, String lng, String lat);

    /**
     * 根据条件查询热力数据（带人员类型）
     *
     * @param beginTime  起始时间
     * @param endTime    结束时间
     * @param subjectId  专题id
     * @param areaCode   地区code
     * @param personType 人员类被
     * @return 热力数据
     */
    @Query(nativeQuery = true, value = "select substr(t.LNG, 0, 7) as lng, substr(t.LAT, 0, 6) as lat, count(1) as count" +
            " from T_PS_WARNING_TRAJECTORY t" +
            " where t.ID in" +
            "      (select t2.TRAJECTORY_ID" +
            "       from T_PS_WARNING t1" +
            "                join T_PS_WARNING_TRACE_RELATION t2 on t1.ID = t2.WARNING_ID" +
            "  where t1.WARNING_TIME>:beginTime" +
            "  and t1.WARNING_TIME<:endTime" +
            "  and t1.SUBJECT_ID=:subjectId" +
            "      )" +
            "  and t.AREA_CODE like concat(:areaCode,'%')" +
            "  and t.ID_NUMBER in" +
            "      (select t5.ID_NUMBER" +
            "       from T_PS_PERSON t5" +
            "       where t5.ID in" +
            "             (select t3.PERSON_ID" +
            "              from T_PS_PERSON_LABEL_RELATION t3" +
            "                       join T_PS_LABEL t4 on t3.LABEL_ID = t4.ID" +
            "              where t4.ID = :personType))" +
            " group by substr(t.LNG, 0, 7), substr(t.LAT, 0, 6)")
    List<Map<String, Object>> getHeatMapData(@Param("beginTime") LocalDateTime beginTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("subjectId") String subjectId,
                                             @Param("areaCode") String areaCode,
                                             @Param("personType") String personType);

    /**
     * 根据条件查询热力数据（不带人员类型）
     *
     * @param beginTime 起始时间
     * @param endTime   结束时间
     * @param subjectId 专题id
     * @param areaCode  地区code
     * @return 热力数据
     */
    @Query(nativeQuery = true, value = "select substr(t.LNG, 0, 7) as lng, substr(t.LAT, 0, 6) as lat, count(1) as count" +
            " from T_PS_WARNING_TRAJECTORY t" +
            " where t.ID in" +
            "      (select t2.TRAJECTORY_ID" +
            "       from T_PS_WARNING t1" +
            "                join T_PS_WARNING_TRACE_RELATION t2 on t1.ID = t2.WARNING_ID" +
            "  where t1.WARNING_TIME>:beginTime" +
            "  and t1.WARNING_TIME<:endTime" +
            "  and t1.SUBJECT_ID=:subjectId" +
            "      )" +
            "  and t.AREA_CODE like concat(:areaCode,'%')" +
            "  group by substr(t.LNG, 0, 7), substr(t.LAT, 0, 6)")
    List<Map<String, Object>> getHeatMapData(@Param("beginTime") LocalDateTime beginTime,
                                             @Param("endTime") LocalDateTime endTime,
                                             @Param("subjectId") String subjectId,
                                             @Param("areaCode") String areaCode);

    /**
     * 查询预警下面所有轨迹
     *
     * @param warningId 预警id
     * @return {@link WarningTrajectoryEntity}
     */
    @Query("SELECT wt FROM WarningTraceRelationEntity r JOIN WarningTrajectoryEntity wt ON r.trajectoryId=wt.id WHERE r.warningId=:warningId")
    List<WarningTrajectoryEntity> findAllByWarningId(@Param("warningId") String warningId);

    /**
     * 统计预警轨迹数量
     *
     * @param idNumber 身份证号
     * @param subjectId 专题id
     * @param warningType 预警类型
     * @return 结果
     */
    @Query(value = "select count(wt.id) from T_PS_WARNING_TRAJECTORY wt join T_PS_WARNING_TRACE_RELATION wtr on wt.ID = wtr.TRAJECTORY_ID join T_PS_WARNING w on wtr.WARNING_ID = w.ID "
        + "where w.SUBJECT_ID = :subjectId and w.WARNING_TYPE = :warningType and wt.ID_NUMBER = :idNumber", nativeQuery = true)
    int countByPersonIdAndSubjectIdAndWarningType(@Param("idNumber") String idNumber, @Param("subjectId") String subjectId, @Param("warningType") String warningType);
}
