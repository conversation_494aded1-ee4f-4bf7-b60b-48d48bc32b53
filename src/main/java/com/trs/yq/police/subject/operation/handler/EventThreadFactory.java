package com.trs.yq.police.subject.operation.handler;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 操作日志线程工厂
 *
 * <AUTHOR>
 * @date 2021/8/18 11:11
 */
public class EventThreadFactory implements ThreadFactory {

    /**
     * 线程数
     */
    private final AtomicLong count = new AtomicLong(0);

    /**
     * 线程前缀
     */
    private final String namePrefix;

    /**
     * 构造器
     *
     * @param namePrefix 名称前缀
     */
    public EventThreadFactory(String namePrefix) {
        this.namePrefix = StringUtils.isNotBlank(namePrefix)
                ? namePrefix + "-thread-" : "EventThreadFactory" + "-thread-";
    }

    @Override
    public Thread newThread(@NotNull Runnable r) {
        return new Thread(r, this.namePrefix + "-" + this.count.incrementAndGet());
    }
}
