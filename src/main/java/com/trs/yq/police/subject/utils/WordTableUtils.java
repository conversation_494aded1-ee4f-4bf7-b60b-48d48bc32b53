package com.trs.yq.police.subject.utils;

import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

/**
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/8 15:29
 **/
@Component
public class WordTableUtils {

    /**
     * 对表格进行列宽自动分割
     *
     * @param table 表格对象
     */
    public static void divisionColumnWidth(XWPFTable table) {
        //列宽自动分割
        CTTblWidth comTableWidth = table.getCTTbl().addNewTblPr().addNewTblW();
        comTableWidth.setType(STTblWidth.DXA);
        comTableWidth.setW(BigInteger.valueOf(9072));
    }
    /**
     * 创建表格的标题
     *
     * @param document 文档对象
     * @param wordName 文档名称
     */
    public static void createTitle(XWPFDocument document,String wordName) {
        XWPFParagraph titleParagraph = document.createParagraph();
        //设置段落居中
        titleParagraph.setAlignment(ParagraphAlignment.CENTER);
        XWPFRun titleParagraphRun = titleParagraph.createRun();
        titleParagraphRun.setText(wordName);
        titleParagraphRun.setColor("000000");
        titleParagraphRun.setFontSize(20);
    }

    /**
     * 表格行合并
     *
     * @param infoTable 表格对象
     * @param row       第几行
     * @param startCell 开始列
     * @param endCell   结束列
     */
    public static void mergeCellsHorizontal(XWPFTable infoTable, int row, int startCell, int endCell) {
        for (int i = startCell; i <= endCell; i++) {
            XWPFTableCell cell = infoTable.getRow(row).getCell(i);
            if (i == startCell) {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 表格列合并
     *
     * @param table    字节输出流
     * @param col      第级列
     * @param startRow 开始行
     * @param endRow   结束行
     */
    public static void mergeCellsVertically(XWPFTable table, int col, int startRow, int endRow) {
        for (int i = startRow; i <= endRow; i++) {
            XWPFTableCell cell = table.getRow(i).getCell(col);
            if (i == startRow) {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 设置表格内容居中
     *
     * @param table 表格对象
     */
    public static void setCenter(XWPFTable table) {
        int rowNumber = table.getRows().size();
        int cellNumber = table.getRows().get(0).getTableCells().size();
        for (int i = 0; i < rowNumber; i++) {
            for (int j = 0; j < cellNumber; j++) {
                CTTc cttc = table.getRow(i).getCell(j).getCTTc();
                CTTcPr ctPr = cttc.addNewTcPr();
                ctPr.addNewVAlign().setVal(STVerticalJc.CENTER);
                cttc.getPList().get(0).addNewPPr().addNewJc().setVal(STJc.CENTER);
            }
        }
    }
    /**
     * 设置换行
     *
     * @param document 文档对象
     */
    public static void lineFeed(XWPFDocument document) {
        XWPFParagraph paragraph1 = document.createParagraph();
        XWPFRun paragraphRun1 = paragraph1.createRun();
        paragraphRun1.setText("\r");
    }
}
