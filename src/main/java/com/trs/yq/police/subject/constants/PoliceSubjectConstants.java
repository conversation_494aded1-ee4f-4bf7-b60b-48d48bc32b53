package com.trs.yq.police.subject.constants;

/**
 * 警种专题字符串常量
 *
 * <AUTHOR>
 * @date 2021/8/2 9:21
 */
public class PoliceSubjectConstants {

    private PoliceSubjectConstants() {
    }

    /**
     * 反恐专题
     */
    public static final String FK_SUBJECT = "1";

    /**
     * 禁毒专题
     */
    public static final String JD_SUBJECT = "2";

    /**
     * 政保专题
     */
    public static final String ZB_SUBJECT = "3";

    /**
     * 交警专题
     */
    public static final String JJ_SUBJECT = "4";

    /**
     * 刑侦专题
     */
    public static final String XZ_SUBJECT = "5";

    /**
     * 维稳专题
     */
    public static final String WW_SUBJECT = "6";

    /**
     * 机场专题
     */
    public static final String JC_SUBJECT = "7";

    /**
     * 出入境专题
     */
    public static final String CRJ_SUBJECT = "8";

    /**
     * 反邪教专题
     */
    public static final String FX_SUBJECT = "9";

    /**
     * 不导入
     */
    public static final String REPEAT_STRATEGY_STOP = "1";

    /**
     * 覆盖导入
     */
    public static final String REPEAT_STRATEGY_REPLACE = "2";

    /**
     * 错误信息
     */
    public static class ErrorMessage {

        private ErrorMessage() {
        }

        /**
         * 人员主键缺失
         */
        public static final String PERSON_ID_MISSING = "人员主键缺失!";

        /**
         * 专题主键缺失
         */
        public static final String SUBJECT_ID_MISSING = "专题主键缺失!";

        /**
         * 请求参数缺失
         */
        public static final String REQUEST_PARAMS_MISSING = "请求参数缺失!";

        /**
         * 身份证号码缺失
         */
        public static final String ID_NUMBER_MISSING = "身份证缺失!";

        /**
         * 文件缺失
         */
        public static final String FILE_MISSING = "文件缺失!";


    }

    /**
     * 导入类型-原始
     */
    public static final String IMPORT_TYPE_INITIAL = "0";

    /**
     * 导入类型-成功
     */
    public static final String IMPORT_TYPE_SUCCESS = "1";

    /**
     * 导入类型-失败
     */
    public static final String IMPORT_TYPE_FAILURE = "2";

    /**
     * 裁决时限单位 - 年
     */
    public static final String TIME_UNIT_YEAR = "年";

    /**
     * 裁决时限单位 - 月
     */
    public static final String TIME_UNIT_MONTH = "月";

    /**
     * 裁决时限单位 - 月
     */
    public static final String TIME_UNIT_MONTH_ALIAS = "个月";

    /**
     * 裁决时限单位 - 终身
     */
    public static final String TIME_UNIT_FOREVER = "终身禁驾";

    /**
     * 标准日期格式
     */
    public static final String STANDARD_DATE_PATTERN = "yyyyMMdd";

    /**
     * 日志-图片已更新提示语
     */
    public static final String OPERATION_LOG_UPDATE_MESSAGE = "已更新";

    /**
     * 日志-时间编辑提示语
     */
    public static final String OPERATION_LOG_UNTIL_NOW = "至今";

    /**
     * 日志-时间单位提示语
     */
    public static final String OPERATION_LOG_TIME_UNIT_YEAR = "年";

    /**
     * 日志-时间单位提示语
     */
    public static final String OPERATION_LOG_TIME_UNIT_MONTH = "月";

    /**
     * 日志-空标识符
     */
    public static final String OPERATION_LOG_NULL_MARK = "--";

    /**
     * 是否为自动更新添加的
     */
    public static final String AUTOMATED = "1";

    /**
     * 警种专题模块名称-person
     */
    public static final String MODULE_PERSON = "person";
    /**
     * 警种专题模块名称-group
     */
    public static final String MODULE_GROUP = "group";
    /**
     * 警种专题模块名称-clue
     */
    public static final String MODULE_CLUE = "clue";
    /**
     * 警种专题模块名称-event
     */
    public static final String MODULE_EVENT = "event";

    /**
     * 事件主键
     */
    public static final String EVENT_PK = "eventId";

    /**
     * 人员主键
     */
    public static final String PERSON_PK = "personId";

    /**
     * 线索主键
     */
    public static final String CLUE_PK = "clueId";

    /**
     * 群体主键
     */
    public static final String GROUP_PK = "groupId";
}
