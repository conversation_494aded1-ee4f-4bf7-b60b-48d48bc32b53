package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.entity.ListFilter;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/28
 */
public interface SubjectService {
    /**
     * 查询专题人员列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题人员列表检索条件
     */
    List<ListFilter> getPersonListQueryFilters(@NotBlank(message = "专题主键不能为空！") String subjectId);

    /**
     * 查询专题群体列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题群体列表检索条件
     */
    List<ListFilter> getGroupListQueryFilters(@NotBlank(message = "专题主键不能为空！") String subjectId);

    /**
     * 查询专题线索列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题线索列表检索条件
     */
    List<ListFilter> getClueListQueryFilters(@NotBlank(message = "专题主键不能为空！") String subjectId);

    /**
     * 查询专题预警列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题预警列表的检索条件
     */
    List<ListFilter> getWarningListQueryFilters(@NotBlank(message = "专题主键不能为空！") String subjectId);

    /**
     * 查询事件列表的检索条件
     *
     * @param subjectId 专题id
     * @return 事件列表检索条件
     */
    List<ListFilter> getEventListQueryFilters(String subjectId);
}
