package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.FootholdStatusEnum;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.FootholdEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.FootholdListRequestVO;
import com.trs.yq.police.subject.domain.vo.FootholdVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.FootholdRepository;
import com.trs.yq.police.subject.service.FootholdService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 落脚点地址业务层接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class FootholdServiceImpl implements FootholdService {

    @Resource
    private final FootholdRepository footholdRepository;

    @Resource
    private final PersonService personService;

    @Resource
    private final OperationLogHandler operationLogHandler;

    @Override
    public PageResult<FootholdVO> getFootholdPageable(String personId, FootholdListRequestVO footholdParams) {
        TimeParams params = footholdParams.getTimeParams();
        List<FootholdEntity> footholdAll = getFootholdAll(personId, params);
        List<FootholdVO> result = getFootholdList(footholdAll, footholdParams.getPageParams());
        int total = footholdAll.size();
        return PageResult.of(result, footholdParams.getPageParams().getPageNumber(), total, footholdParams.getPageParams().getPageSize());
    }

    /**
     * 查询所有符合时间和人员筛选条件的落脚点信息
     *
     * @param personId 人员id
     * @param params   时间筛选参数
     * @return 落脚点信息列表 {@link FootholdEntity}
     */
    private List<FootholdEntity> getFootholdAll(String personId, TimeParams params) {
        List<FootholdEntity> result = new ArrayList<>();
        List<FootholdEntity> footholdEntities = footholdRepository.findAllByPersonIdOrderByStartTimeDesc(personId);
        //自定义筛选时间均不存在，返回全部结果
        if (Objects.isNull(params.getBeginTime()) && Objects.isNull(params.getEndTime())) {
            return footholdEntities;
        }
        for (FootholdEntity f : footholdEntities) {
            if (Objects.isNull(f.getEndTime())) {
                f.setEndTime(LocalDate.now());
            }
            if (timeStrategyFilter(f, params)) {
                result.add(f);
            }
        }
        return result;
    }

    /**
     * 判断落脚点实体是否满足筛选条件
     *
     * @param f      落脚点实体
     * @param params 时间筛选条件
     * @return 是否满足条件
     */
    private boolean timeStrategyFilter(FootholdEntity f, TimeParams params) {
        //只有筛选结束时间，筛选居住开始时间大于等于筛选结束时间的
        if (Objects.isNull(params.getBeginTime()) && Objects.nonNull(params.getEndTime())) {
            return params.isBeforeOrEqualEnd(f.getStartTime());
        }
        //只有筛选开始时间，筛选居住结束时间小于等于筛选开始时间的
        else if (Objects.nonNull(params.getBeginTime()) && Objects.isNull(params.getEndTime())) {
            return params.isAfterOrEqualStart(f.getEndTime());
        } else {
            //筛选开始、结束时间均存在，筛选居住时间内有至少一天在时间范围内的
            for (LocalDate date = f.getStartTime(); date.isBefore(f.getEndTime()) || date.isEqual(f.getEndTime()); date = date.plusDays(1)) {
                if (params.isAfterOrEqualStart(date) && params.isBeforeOrEqualEnd(date)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 对落脚点列表进行分页
     *
     * @param footholdAll 全部落脚点信息列表
     * @param pageParams  分页参数
     * @return 分页完成后的列表 {@link FootholdVO}
     */
    private List<FootholdVO> getFootholdList(List<FootholdEntity> footholdAll, PageParams pageParams) {
        List<FootholdEntity> footholdEntityList = new ArrayList<>();
        int beginIndex = (pageParams.getPageNumber() - 1) * pageParams.getPageSize();
        int endIndex = pageParams.getPageNumber() * pageParams.getPageSize();
        for (int i = beginIndex; i < endIndex && i < footholdAll.size(); i++) {
            footholdEntityList.add(footholdAll.get(i));
        }
        return footholdEntityList.stream().map(FootholdVO::of).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addStay(String personId, FootholdVO footholdVO) {
        personService.checkPersonExist(personId);
        FootholdEntity footholdEntity = new FootholdEntity();
        footholdEntity.setStartTime(DateUtil.utcToLocalDate(footholdVO.getStartTime()));
        footholdEntity.setPersonId(personId);
        if (FootholdStatusEnum.NOT_LIVING.getCode().equals(footholdVO.getLivingStatus())) {
            if (footholdVO.getStartTime() > footholdVO.getEndTime()) {
                throw new ParamValidationException("开始时间需小于结束时间！");
            }
            footholdEntity.setEndTime(DateUtil.utcToLocalDate(footholdVO.getEndTime()));
        }
        footholdEntity.setState(footholdVO.getLivingStatus());
        footholdEntity.setAddress(footholdVO.getAddress());
        footholdEntity.setNote(footholdVO.getNote());
        footholdRepository.save(footholdEntity);

        // 记录新增操作
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.TRANSIENT_PLACE)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增落脚地信息")
                .newObj(JsonUtil.toJsonString(footholdEntity))
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateStay(String personId, FootholdVO footholdVO) {
        personService.checkPersonExist(personId);
        FootholdEntity original = footholdRepository.findById(footholdVO.getId()).orElse(null);

        if (Objects.isNull(original)) {
            throw new ParamValidationException("落脚点信息不存在，请刷新核实");
        }

        // 记录操作
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.TRANSIENT_PLACE)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑落脚地信息")
                .oldObj(JsonUtil.toJsonString(original))
                .build();

        FootholdEntity footholdEntity = new FootholdEntity();
        footholdEntity.setPersonId(personId);
        footholdEntity.setStartTime(DateUtil.utcToLocalDate(footholdVO.getStartTime()));
        if (FootholdStatusEnum.NOT_LIVING.getCode().equals(footholdVO.getLivingStatus())) {
            if (footholdVO.getStartTime() > footholdVO.getEndTime()) {
                throw new ParamValidationException("开始时间需小于结束时间！");
            }
            footholdEntity.setEndTime(DateUtil.utcToLocalDate(footholdVO.getEndTime()));
        }
        footholdEntity.setAddress(footholdVO.getAddress());
        footholdEntity.setNote(footholdVO.getNote());
        footholdEntity.setState(footholdVO.getLivingStatus());
        BeanUtil.copyPropertiesIgnoreNull(footholdEntity, original);
        if (footholdVO.getLivingStatus().equals(FootholdStatusEnum.IS_LIVING.getCode())) {
            original.setEndTime(null);
        }
        footholdRepository.save(original);

        logRecord.setNewObj(JsonUtil.toJsonString(original));
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteStay(String personId, String id) {
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.TRANSIENT_PLACE)
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除落脚地信息")
                .oldObj(JsonUtil.toJsonString(footholdRepository.findById(id).orElse(null)))
                .build();
        footholdRepository.deleteById(id);
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }
}
