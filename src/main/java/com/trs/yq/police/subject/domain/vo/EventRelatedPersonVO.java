package com.trs.yq.police.subject.domain.vo;


import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.repository.EventFileRelationRepository;
import com.trs.yq.police.subject.repository.EventPersonRelationRepository;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.enums.EventModuleEnum.Event_PERSON_PHOTO;

/**
 * <AUTHOR>
 * @date 2021/12/13 17:43
 */
@Data
public class EventRelatedPersonVO implements Serializable {
    private static final long serialVersionUID = -8007340239858006587L;
    /**
     * 人员id
     */
    private String personId;

    /**
     * 人员名
     */
    private String personName;

    /**
     * 身份证
     */
    private String idNumber;

    /**
     * 人员类别
     */
    private String personType;

    /**
     * 添加时间
     */
    private LocalDateTime createTime;

    /**
     * 线索-人员关联id
     */
    private String relationId;

    /**
     * 涉事行为
     */
    private List<String> behaviours;

    /**
     * 人员来源
     */
    private String source;
    /**
     * 创建人
     */
    private String addPerson;
    /**
     * 创建人单位
     */
    private String addDepartment;

    /**
     * 人员涉事图片
     */
    private List<ImageVO> images;

    /**
     * 构建vo
     *
     * @param personEntity {@link PersonEntity}
     * @param eventEntity  {@link EventEntity}
     * @return {@link ClueRelatedPersonVO}
     */
    public static EventRelatedPersonVO of(PersonEntity personEntity, EventEntity eventEntity) {
        EventRelatedPersonVO vo = new EventRelatedPersonVO();
        vo.setPersonId(personEntity.getId());
        vo.setPersonName(personEntity.getName());
        vo.setIdNumber(personEntity.getIdNumber());
        vo.setAddPerson(personEntity.getCrByName());
        vo.setAddDepartment(personEntity.getCrDept());
        //设置人员类别
        LabelRepository personTypeRepository = BeanUtil.getBean(LabelRepository.class);
        String personType = personTypeRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), eventEntity.getSubjectId()).stream()
                .map(LabelEntity::getName)
                .collect(Collectors.joining("、"));
        vo.setPersonType(personType);
        //设置添加时间
        EventPersonRelationRepository cluePersonRelationRepository = BeanUtil.getBean(EventPersonRelationRepository.class);
        EventPersonRelationEntity relation = cluePersonRelationRepository.findByEventIdAndPersonId(eventEntity.getId(), personEntity.getId());
        vo.setCreateTime(relation.getCrTime());
        vo.setRelationId(relation.getId());
        //设置涉事行为
        EventPersonRelationRepository eventPersonRelationRepository = BeanUtil.getBean(EventPersonRelationRepository.class);
        EventPersonRelationEntity relationEntity = eventPersonRelationRepository.findByEventIdAndPersonId(eventEntity.getId(), personEntity.getId());
        vo.setBehaviours(relationEntity.getBehaviors());

        //设置人员来源
        vo.setSource(relationEntity.getSource());

        //设置涉事图片
        EventFileRelationRepository eventFileRelationRepository = BeanUtil.getBean(EventFileRelationRepository.class);
        FileStorageRepository fileStorageRepository = BeanUtil.getBean(FileStorageRepository.class);
        List<ImageVO> images = eventFileRelationRepository.findAllByEventIdAndModuleAndRecordId(eventEntity.getId(), Event_PERSON_PHOTO.getCode(), relation.getId()).stream().map(r -> {
            FileStorageEntity file = fileStorageRepository.getById(r.getFileStorageId());
            ImageVO image = new ImageVO();
            image.setImageId(file.getId());
            image.setUrl(file.getUrl());
            return image;
        }).collect(Collectors.toList());
        vo.setImages(images);

        return vo;
    }
}
