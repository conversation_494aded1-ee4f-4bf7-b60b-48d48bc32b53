package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.validation.RuleGroup;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/08/04
 */
@Data
public class VisitRecordVO {

    @NotBlank(message = "走访记录主键不可为空", groups = {RuleGroup.Update.class})
    private String id;

    /**
     * 走访时间
     */
    @NotNull(message = "走访时间不可为空", groups = {RuleGroup.Create.class})
    private LocalDateTime time;

    /**
     * 走访方式
     */
    @NotBlank(message = "走访方式不可为空", groups = {RuleGroup.Create.class})
    private String method;

    /**
     * 是否在控
     */
    @NotBlank(message = "是否在控不可为空", groups = {RuleGroup.Create.class})
    private String inControl;

    /**
     * 失控时间
     */
    private LocalDateTime outOfControlTime;

    /**
     * 当前去向
     */
    private String destination;

    /**
     * 走访情况
     */
    private String info;

    /**
     * 走访工作人员
     */
    private String visitBy;

    /**
     * 图片
     */
    private List<ImageVO> images;

    private Double lng;

    private Double lat;
}
