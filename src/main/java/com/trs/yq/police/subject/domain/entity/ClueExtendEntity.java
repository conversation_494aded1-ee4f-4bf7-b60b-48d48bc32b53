package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/12/8 14:19
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CLUE_EXTEND")
public class ClueExtendEntity extends BaseEntity {
    private static final long serialVersionUID = -6267483558310829290L;
    private String clueId;
    /**
     * 涉及市州
     */
    private String relatedPlace;
    /**
     * 维权方式：0-上访，1-集权，2-维权，3-极端方式维权
     */
    private String method;
    /**
     * 维权行为：0-网上煽动，1-电话串联，2-线下串联
     */
    private String behaviour;
    /**
     * 维权时间
     */
    private LocalDateTime occurrenceTime;
    private String lat;
    private String lng;
    private String type;
}
