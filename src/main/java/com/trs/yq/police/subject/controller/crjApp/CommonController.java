package com.trs.yq.police.subject.controller.crjApp;

import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.vo.UnitVO;
import com.trs.yq.police.subject.domain.vo.UnreadRecordVO;
import com.trs.yq.police.subject.service.CrjCommonService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/24 14:02
 */
@RestController
@RequestMapping("/entry-exit")
public class CommonController {

    @Resource
    private CrjCommonService crjCommonService;

    /**
     * 获取接受部门
     *
     * @return {@link UnitVO}
     */
    @GetMapping("/acceptor")
    public List<UnitVO> dispatchTree() {
        return crjCommonService.getAcceptor();
    }

    /**
     * 列表未读统计
     *
     * @return {@link UnreadRecordVO}
     */
    @GetMapping("/unread")
    public UnreadRecordVO statisticUnread() {
        return crjCommonService.statisticUnread();
    }

    /**
     * 获取列表筛选条件
     *
     * @param listName 列表名称
     * @return {@link ListFilter}
     */
    @GetMapping("/list-filter/{listName}")
    public List<ListFilter> getListFilter(@PathVariable("listName") String listName) {
        return crjCommonService.getListFilter(listName);
    }

    /**
     * 获取模块
     *
     * @return {@link String}
     */
    @GetMapping("/module")
    public List<String> getModule(){
       return crjCommonService.getModule();
    }
}
