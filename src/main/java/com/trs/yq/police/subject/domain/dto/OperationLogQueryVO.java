package com.trs.yq.police.subject.domain.dto;

import com.trs.yq.police.subject.domain.params.PageParams;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 操作日志 列表查询
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 18:45
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OperationLogQueryVO {

    /**
     * 警种id
     */
    @NotEmpty(message = "专题id缺失")
    private String subjectId;

    /**
     * 操作模块
     */
    private String operateModule;

    /**
     * 目标对象类型
     */
    private Integer targetObjectType;

    /**
     * 分页参数
     */
    @NotNull(message = "分页参数缺失")
    private PageParams pageParams;

}
