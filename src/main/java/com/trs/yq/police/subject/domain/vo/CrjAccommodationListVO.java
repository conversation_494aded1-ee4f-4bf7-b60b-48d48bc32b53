package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * [专题首页-出入境] 住宿列表VO
 *
 * <AUTHOR>
 * @date 2021/9/22 16:37
 */
@Data
public class CrjAccommodationListVO implements Serializable {
    private static final long serialVersionUID = -1646757805908182381L;
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 登记时间
     */
    private LocalDateTime registryTime;
    /**
     * 居住信息
     */
    private String livingInfo;

}
