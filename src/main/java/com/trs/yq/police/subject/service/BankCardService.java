package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.BankCardVO;

import java.util.List;

/**
 * 银行卡信息业务层接口
 *
 * <AUTHOR>
 */
public interface BankCardService {
    /**
     * 获取人员银行卡信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link BankCardVO}
     * <AUTHOR>
     */
    List<BankCardVO> getAllByPersonId(String personId);

    /**
     * 根据id删除人员的银行卡信息
     *
     * @param personId 人员主键
     * @param id       银行卡id
     * <AUTHOR>
     */
    void deleteOneById(String personId, String id);

    /**
     * 增加一条银行卡信息
     *
     * @param personId   人员id
     * @param bankCardVO 银行卡信息
     * <AUTHOR>
     */
    void addOne(String personId, BankCardVO bankCardVO);

    /**
     * 更新一条银行卡信息
     *
     * @param bankCardVO 银行卡信息
     * @param personId   人员主键
     * <AUTHOR>
     */
    void updateOne(BankCardVO bankCardVO, String personId);
}
