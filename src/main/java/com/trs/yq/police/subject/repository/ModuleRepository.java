package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ModuleEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 档案目录查询接口
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@Repository
public interface ModuleRepository extends BaseRepository<ModuleEntity, String> {

    /**
     * 根据subjectId和type字段查询档案目录项
     *
     * @param subjectId 专题id
     * @param type      类型
     * @return 档案目录
     */
    @Query("SELECT t2 FROM ModuleSubjectRelateEntity t1 JOIN ModuleEntity t2 ON t1.moduleId=t2.id WHERE t1.subjectId=?1 and t2.type=?2 ORDER BY t1.showOrder")
    List<ModuleEntity> findAllBySubjectIdAndType(String subjectId, String type);

    /**
     * 根据subjectId和type字段查询档案目录项, 不含档案管理
     *
     * @param subjectId 专题id
     * @param type      类型
     * @return 档案目录
     */
    @Query("SELECT t2 FROM ModuleSubjectRelateEntity t1 JOIN ModuleEntity t2 ON t1.moduleId=t2.id WHERE t1.subjectId=?1 and t2.type=?2 and t1.showOrder != 0 ORDER BY t1.showOrder")
    List<ModuleEntity> findAllBySubjectIdAndTypeWithoutArchive(String subjectId, String type);
}
