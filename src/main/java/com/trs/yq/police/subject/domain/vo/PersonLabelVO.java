package com.trs.yq.police.subject.domain.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

/**
 * 人员标签响应
 *
 * <AUTHOR>
 * @date 2021/7/31 18:06
 */
@Getter
@Setter
@ToString
public class PersonLabelVO extends IdNameVO {

    private static final long serialVersionUID = 1972419225534244909L;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建方式 0-手动创建，1-大数据平台推送
     */
    private String createType;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (!(o instanceof PersonLabelVO)) {
            return false;
        }

        PersonLabelVO labelVO = (PersonLabelVO) o;

        return new EqualsBuilder().appendSuper(super.equals(o))
                .append(getId(), labelVO.getId())
                .append(getName(), labelVO.getName()).isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .appendSuper(super.hashCode())
                .append(getId())
                .append(getName())
                .toHashCode();
    }
}
