package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.RacingWarningVO;
import com.trs.yq.police.subject.mppDatasource.repository.MppRepository;
import com.trs.yq.police.subject.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/29 10:58
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.update.warning.racing.task.enable", havingValue = "true")
public class UpdateRacingWarningTask {
    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private MppRepository mppRepository;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    private static final List<String> STHY = Collections.singletonList("三台合一");
    private static final List<String> MPP = Collections.singletonList("12345");


    private static final String TRAJECTORY_SOURCE_STHY = "26";
    private static final String TRAJECTORY_SOURCE_12345 = "27";


    /**
     * 更新飙车预警信息
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.update.warning.racing.task}")
    public void synchronizeRacingWarningInfo() {
        //筛选出未同步的高风险警情
        log.info("开始从v_jjdb同步飙车预警");
        LocalDateTime beginTime = LocalDateTime.now().minusMonths(1).toLocalDate().atStartOfDay();
        LocalDateTime endTime = LocalDateTime.now().plusDays(1).toLocalDate().atStartOfDay();
        List<Map<String, Object>> racing = warningRepository.selectJjdbRacing(beginTime, endTime);
        for (Map<String, Object> g : racing) {
            final String contents = (String) g.get("CONTENTS");
            final Timestamp receiveTime = (Timestamp) g.get("RECEIVETIME");
            final String receiveNum = (String) g.get("RECEIVENUM");
            final String address = (String) g.get("ADDRESS");
            final String districtCode = (String) g.get("DISTRICTCODE");

            if (Objects.nonNull(warningRepository.findByWarningKey(receiveNum))) {
                log.info("该事件已预警！" + receiveNum);
                continue;
            }
            RacingWarningVO vo = new RacingWarningVO(receiveNum, receiveTime.toLocalDateTime(), contents, address, districtCode);
            saveWarning(vo, STHY, "system.V_JJDB", "RECEIVENUM", TRAJECTORY_SOURCE_STHY);
        }
        log.info("从v_jjdb飙车预警同步结束");

        log.info("开始从12345同步飙车预警");
        List<Map<String, String>> mpp = mppRepository.selectRacingWarning(beginTime.format(formatter), endTime.format(formatter));
        for (Map<String, String> data : mpp) {
            final String contents = data.get("content");
            final LocalDateTime addDate = LocalDateTime.parse(data.get("adddate"), formatter);
            final String uuid = data.get("uuid");
            final String address = data.get("address");
            final String districtCode = data.get("areaname");
            if (Objects.nonNull(warningRepository.findByWarningKey(uuid))) {
                log.info("该12345已预警！" + uuid);
                continue;
            }
            RacingWarningVO vo = new RacingWarningVO(uuid, addDate, contents, address, districtCode);
            saveWarning(vo, MPP, "shzy_12345rx_gdjcxx", "uuid", TRAJECTORY_SOURCE_12345);
        }
        log.info("从12345飙车预警同步结束");
    }

    private void saveWarning(RacingWarningVO vo, List<String> warningSource,
                             String tableName, String tableIdName, String sourceId) {
        WarningTypeEntity warningType = warningTypeRepository.getById("34");
        //设置预警
        WarningEntity warningEntity = new WarningEntity();
        warningEntity.setAddress(vo.getAddress());
        warningEntity.setWarningTime(vo.getTime());
        warningEntity.setWarningKey(vo.getId());
        warningEntity.setWarningType(warningType.getId());
        warningEntity.setSubjectId(warningType.getSubjectId());
        warningEntity.setWarningLevel("4");
        warningEntity.setWarningStatus("1");
        warningEntity.setWarningDetails(String.format("触发飙车炸街预警，预警详情：%s", vo.getContent()));
        warningEntity.setWarningSource(warningSource);
        warningEntity.setAreaCode(vo.getDistrictCode());
        final String warningId = warningRepository.save(warningEntity).getId();
        //设置轨迹表
        WarningTrajectoryEntity warningTrajectoryEntity = new WarningTrajectoryEntity();
        warningTrajectoryEntity.setAddress(vo.getAddress());
        warningTrajectoryEntity.setDateTime(vo.getTime());
        warningTrajectoryEntity.setHybaseTable(tableName);
        warningTrajectoryEntity.setHybaseId(tableIdName);
        warningTrajectoryEntity.setSourceId(sourceId);

        warningTrajectoryEntity.setLat("28.877139");
        warningTrajectoryEntity.setLng("105.448391");

        final String warningTrajectoryId = warningTrajectoryRepository.save(warningTrajectoryEntity).getId();
        //设置预警-轨迹关系表
        WarningTraceRelationEntity warningTraceRelationEntity = new WarningTraceRelationEntity();
        warningTraceRelationEntity.setWarningId(warningId);
        warningTraceRelationEntity.setTrajectoryId(warningTrajectoryId);
        warningTraceRelationEntity.setCreateTime(vo.getTime());
        warningTraceRelationRepository.save(warningTraceRelationEntity);
    }
}

