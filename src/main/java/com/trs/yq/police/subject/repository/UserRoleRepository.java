package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BaseEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/07/23
 */
@Repository
@Validated
public interface UserRoleRepository extends BaseRepository<BaseEntity, String> {

    /**
     * 根据用户id查询角色名
     *
     * @param userId   用户id
     * @param roleName 角色名
     * @return 角色名
     */
    @Query(nativeQuery = true, value = "SELECT TR.ID FROM T_ROLE TR LEFT JOIN T_ROLEUSER TRU ON TR.ID = TRU.ROLEID WHERE TRU.USERID = ?1 AND TR.ROLENAME = ?2")
    List<String> findRolesByUserId(@Param("userId") String userId, @Param("roleName") String roleName);

}
