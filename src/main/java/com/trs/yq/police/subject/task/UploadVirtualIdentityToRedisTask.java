package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 同步虚拟身份信息到redis的定时任务
 *
 * <AUTHOR>
 * @date 2021/09/02
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.upload.virtual.identity.task.enable",havingValue = "true")
public class UploadVirtualIdentityToRedisTask {

    @Value("${com.trs.redis.virtual.identity.key}")
    private String virtualIdentityKey;

    @Resource(name = "assignRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private PersonRepository personRepository;

    /**
     * 定时更新人员的虚拟身份到redis
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.upload.virtual.identity.task}")
    public void updatePersonVirtualIdentity() {
        List<Map<String, Object>> entities = personRepository.findVirtualIdentities();
        Map<String, List<Map<String, Object>>> collect = entities.stream().collect(Collectors.groupingBy(entity -> entity.get("VALUE").toString()));
        collect.forEach((key, maps) -> {
            HashOperations<String, Object, Object> hashOperations = redisTemplate.opsForHash();
            List<String> values = maps.stream().map(m->m.get("ID_NUMBER").toString()).collect(Collectors.toList());
            hashOperations.put(virtualIdentityKey, key, JsonUtil.toJsonString(values));
        });
    }
}
