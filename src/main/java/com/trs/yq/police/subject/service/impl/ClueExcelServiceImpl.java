package com.trs.yq.police.subject.service.impl;

import com.alibaba.excel.EasyExcel;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.ClueListPredicatesBuilder;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.ClueExtendEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.SubjectEntity;
import com.trs.yq.police.subject.domain.vo.ClueExportListVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.ClueExcelService;
import com.trs.yq.police.subject.utils.JsonUtil;
import javax.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CLUE_EMERGENCY_LEVEL;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 线索批量导出服务实现类
 *
 * <AUTHOR>
 * @date 2021/09/09
 */
@Service
public class ClueExcelServiceImpl implements ClueExcelService {

    @Resource
    private SubjectRepository subjectRepository;

    @Resource
    private ClueRepository clueRepository;

    @Resource
    private DictRepository dictRepository;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private ClueExtendRepository extendRepository;

    @Resource
    private OperationLogHandler operationLogHandler;

    /**
     * 根据传来的属性导出excel
     *
     * @param response     响应体
     * @param exportParams {@link ExportParams}
     * @param subjectId    主题id
     * @throws IOException IO异常
     */
    @Override
    public void downLoadExcel(HttpServletResponse response, ExportParams exportParams, String subjectId)
        throws IOException {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElseThrow(IOException::new);
        String fileName = String.format("%s-线索档案-%s.xlsx", subjectEntity.getName(),
            LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<ClueEntity> clueList =
            exportParams.getIsAll()
                ? clueRepository.findAll((root, query, criteriaBuilder) -> {
                List<Predicate> predicates = ClueListPredicatesBuilder.buildListFilterPredicates(subjectId,
                        exportParams.getListParams().getFilterParams(), root, criteriaBuilder).stream()
                    .filter(Objects::nonNull).collect(Collectors.toList());
                if (Objects.nonNull(exportParams.getListParams().getSearchParams())) {
                    predicates.addAll(
                        ClueListPredicatesBuilder.buildSearchPredicates(exportParams.getListParams().getSearchParams(),
                            root, criteriaBuilder));
                }
                return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
            })
                : clueRepository.findAllByIdIn(exportParams.getIds());

        List<ClueExportListVO> vos = new ArrayList<>();
        clueList.forEach(clue -> {
            ClueExportListVO vo = new ClueExportListVO();
            vo.setClueName(clue.getName());
            vo.setEmergencyLevel(
                dictRepository.findByTypeAndCode(DICT_TYPE_CLUE_EMERGENCY_LEVEL, clue.getEmergencyLevel()).getName());
            vo.setCreateDeptName(clue.getCrDept());
            vo.setCreateTime(clue.getCrTime().format(DATE_TIME_FORMATTER));
            vo.setUpdateTime(clue.getUpTime().format(DATE_TIME_FORMATTER));

            List<LabelEntity> clueTypes = labelRepository.findByClueId(clue.getId());
            vo.setClueType(clueTypes.stream().map(LabelEntity::getName).collect(Collectors.joining("")));
            if (subjectId.equals(WW_SUBJECT)) {
                vo.setClueSource(dictRepository.findByTypeAndCode("ps_ww_clue_source", clue.getSource()).getName());
            } else {
                vo.setClueSource(dictRepository.findByTypeAndCode("ps_clue_source", clue.getSource()).getName());
            }
            ClueExtendEntity extendEntity = extendRepository.findByClueId(clue.getId()).orElse(new ClueExtendEntity());
            vo.setMethod(StringUtils.isBlank(extendEntity.getMethod()) ? null
                : dictRepository.findByTypeAndCode("clue_action_method", extendEntity.getMethod()).getName());
            vo.setBehaviour(
                dictRepository.findByTypeAndCode("clue_action_behaviour", extendEntity.getBehaviour()).getName());
            vo.setOccurrenceTime(extendEntity.getOccurrenceTime().format(DATE_TIME_FORMATTER));
            vo.setCreateDept(clue.getCrDept());
            vos.add(vo);

            // 日志记录
            OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DOWNLOAD)
                .module(OperateModule.CLUE_ARCHIVE_MANAGE)
                .desc("批量导出线索档案")
                .primaryKey(clue.getId())
                .currentUser(AuthHelper.getCurrentUser())
                .targetObjectType(TargetObjectTypeEnum.CLUE.getCode())
                .build();
            if (Objects.nonNull(operationLogHandler)) {
                operationLogHandler.publishEvent(logRecord);
            }
        });
        EasyExcel.write(response.getOutputStream(), ClueExportListVO.class)
            .registerWriteHandler(new CustomCellWriteHandler())
            .includeColumnFiledNames(exportParams.getFieldNames())
            .sheet()
            .doWrite(vos);
    }

    /**
     * 获得不同专题下批量导出需要的属性
     *
     * @param subjectId 专题Id
     * @return 属性json
     */
    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getClueListProperty());
    }
}
