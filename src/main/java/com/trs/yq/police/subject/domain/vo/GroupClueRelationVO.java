package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 在群体中添加关联线索的vo
 *
 * <AUTHOR>
 * @date 2021/09/07
 */
@Data
public class GroupClueRelationVO implements Serializable {

    private static final long serialVersionUID = 4536972377197695961L;

    /**
     * 群体Id
     */
    private String groupId;

    /**
     * 线索id
     */
    @NotNull(message = "线索id缺失")
    private List<String> clueIds;
}
