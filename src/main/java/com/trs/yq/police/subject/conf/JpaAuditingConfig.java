package com.trs.yq.police.subject.conf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.Optional;

/**
 * 开启jpa审计功能并配置获取用户的bean
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
@EnableJpaAuditing
public class JpaAuditingConfig {

    /**
     * 创建AuditorAware实例
     *
     * @return {@link AuditorAware}
     */
    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            String userId = null;
            try {
                final OAuth2Authentication authentication = (OAuth2Authentication) SecurityContextHolder.getContext().getAuthentication();
                if (Objects.nonNull(authentication)) {
                    userId = (String) ((LinkedHashMap<String, Object>) authentication.getUserAuthentication().getDetails()).get("id");
                }
            } catch (Exception e) {
                log.info("获取用户信息失败", e);
            }
            return Optional.ofNullable(userId);
        };
    }

}
