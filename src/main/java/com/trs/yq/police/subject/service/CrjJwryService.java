package com.trs.yq.police.subject.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.vo.CrjDispatchVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryAddVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryBaseVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryDetailVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryRegisteredVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import com.trs.yq.police.subject.domain.vo.CrjOcrResultVO;
import com.trs.yq.police.subject.domain.vo.CrjRedispatchVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.ImportResultVO;
import com.trs.yq.police.subject.domain.vo.ImportVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SfryReportVO;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 15:35
 */
public interface CrjJwryService {

    /**
     * 分页查询
     *
     * @param requestVO      请求参数
     * @param isDispatchList 是否是分派列表
     * @return {@link CrjJwryBaseVO}
     */
    PageResult<CrjJwryBaseVO> findPage(ListRequestVO requestVO, boolean isDispatchList);

    /**
     * 增加走访记录
     *
     * @param visitVO 走访记录
     * @param id      人员id
     * @param visitType 走访类型
     */
    void addVisitRecord(CrjJwryVisitVO visitVO, String id,String visitType);

    /**
     * 查询走访记录
     *
     * @param id        id
     * @param requestVO 分页参数
     * @return {@link CrjJwryVisitVO}
     */
    PageResult<CrjJwryVisitVO> getVisitRecord(String id, ListRequestVO requestVO);

    /**
     * 登记
     *
     * @param registeredVO 登记
     * @param id           id
     */
    void register(CrjJwryRegisteredVO registeredVO, String id);

    /**
     * 分派
     *
     * @param dispatchVO 分派信息
     */
    void dispatch(CrjDispatchVO dispatchVO);

    /**
     * 获取详情
     *
     * @param id uuid
     * @return {@link SfryReportVO}
     */
    CrjJwryBaseVO getById(String id);

    /**
     * 获取境外人员信息
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @return {@link CrjJwryDetailVO}
     */
    CrjJwryDetailVO getByIdTypeAndIdNumber(String idType, String idNumber);

    /**
     * 新增境外人员
     *
     * @param addVO 境外人员信息
     */
    void addJwry(CrjJwryAddVO addVO);

    /**
     * ocr识别证件
     *
     * @param image 证件照片
     * @return {@link CrjOcrResultVO}
     */
    CrjOcrResultVO ocrIdCard(MultipartFile image);

    /**
     * 重新分派
     *
     * @param dispatchVO 分派vo
     */
    void redispatch(CrjRedispatchVO dispatchVO);

    /**
     * 获取全国走访记录
     *
     * @param id 人员id
     * @return {@link CrjJwryVisitVO}
     */
    List<CrjJwryVisitVO> getCountryVisitRecord(String id);

    /**
     * @param importVO 导入
     * @return 导入结果
     */
    ImportResultVO importJwry(ImportVO importVO);

    /**
     * 根据id删除
     *
     * @param id id
     */
    void deletedById(String id);

    /**
     * 导出excel
     *
     * @param response   response
     * @param fieldNames fieldNames
     * @param request    request
     * @param subjectId  subjectId
     */
    void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request, String subjectId)
        throws IOException;

    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    JsonNode getExportPropertyList(String subjectId);
}
