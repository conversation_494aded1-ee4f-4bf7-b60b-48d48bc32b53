package com.trs.yq.police.subject.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.trs.yq.police.subject.json.deserializer.UtcToDateDeserializer;
import com.trs.yq.police.subject.json.deserializer.UtcToDateTimeDeserializer;
import com.trs.yq.police.subject.json.serializer.DateTimeToUtcSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Json工具类基于jackson实现
 *
 * <AUTHOR>
 * @date 2020/11/9
 **/
@Slf4j
public class JsonUtil {

    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private JsonUtil() {
    }

    static {
        SimpleModule module = new SimpleModule();
        module.addSerializer(LocalDateTime.class, new DateTimeToUtcSerializer());
        module.addSerializer(LocalDate.class, new DateTimeToUtcSerializer());
        module.addDeserializer(LocalDateTime.class, new UtcToDateTimeDeserializer());
        module.addDeserializer(LocalDate.class, new UtcToDateDeserializer());

        OBJECT_MAPPER.registerModule(module)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL)
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * json字符串转实体类
     *
     * @param jsonText json文本
     * @param clazz    实体类型
     * @param <T>      泛型参数
     * @return 转出来的实体类实例，转换失败返回null
     */
    public static <T> T parseObject(String jsonText, Class<T> clazz) {
        if (StringUtils.isBlank(jsonText)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonText, clazz);
        } catch (Exception e) {
            log.error("json string to object error: ", e);
            return null;
        }
    }

    /**
     * json字符串转json对象
     *
     * @param jsonText json文本
     * @return json对象，转换失败返回null
     */
    public static JsonNode parseJsonNode(String jsonText) {
        if (StringUtils.isBlank(jsonText)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(jsonText);
        } catch (Exception e) {
            log.error("json string to json node error: ", e);
            return null;
        }
    }

    /**
     * json字符串转实体类列表
     *
     * @param jsonText json文本
     * @param clazz    实体类型
     * @param <T>      泛型参数
     * @return 转换出来的列表，转换失败返回emptyList
     */
    public static <T> List<T> parseArray(String jsonText, Class<T> clazz) {
        if (StringUtils.isBlank(jsonText)) {
            return Collections.emptyList();
        }
        CollectionType collectionType = TypeFactory.defaultInstance().constructCollectionType(List.class, clazz);
        try {
            return OBJECT_MAPPER.readValue(jsonText, collectionType);
        } catch (Exception e) {
            log.error("json string to array error: ", e);
            return Collections.emptyList();
        }
    }

    /**
     * json字符串转map
     *
     * @param jsonText json文本
     * @param clazz    值对象的类型
     * @param <T>      泛型参数
     * @return 转换出来的map，转换失败返回emptyMap
     */
    public static <T> Map<String, T> parseMap(String jsonText, Class<T> clazz) {
        if (StringUtils.isEmpty(jsonText)) {
            return Collections.emptyMap();
        }
        MapType mapType = TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, clazz);
        try {
            return OBJECT_MAPPER.readValue(jsonText, mapType);
        } catch (Exception e) {
            log.error("json string to map error: ", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 实体类转json字符串
     *
     * @param object 实体类对象
     * @return 转换出来的json字符串，转换失败返回emptyString
     */
    public static String toJsonString(Object object) {
        try {
            return OBJECT_MAPPER.writer().writeValueAsString(object);
        } catch (Exception e) {
            log.error("object to json string error: ", e);
            return "";
        }
    }

    /**
     * 实体类转json字符数组
     *
     * @param object 实体类对象
     * @return 转换出来的json字符数组，转换失败返回emptyArray
     */
    public static byte[] toJsonBytes(Object object) {
        try {
            return OBJECT_MAPPER.writer().writeValueAsBytes(object);
        } catch (IOException e) {
            log.error("object to json bytes error: ", e);
            return new byte[]{};
        }
    }

    /**
     * object类型转实体类
     *
     * @param o        object类型
     * @param clazz    实体类型
     * @param <T>      泛型参数
     * @return 转出来的实体类实例，转换失败返回null
     */
    public static <T> T parseSpecificObject(Object o, Class<T> clazz) {
        if (o == null) {
            return null;
        }

        String jsonText;
        if (o instanceof String) {
            jsonText = o.toString();
        } else {
            jsonText = toJsonString(o);
        }

        if (org.apache.commons.lang3.StringUtils.isBlank(jsonText)) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonText, clazz);
        } catch (Exception e) {
            log.error("json string to object error: ", e);
            return null;
        }
    }
}
