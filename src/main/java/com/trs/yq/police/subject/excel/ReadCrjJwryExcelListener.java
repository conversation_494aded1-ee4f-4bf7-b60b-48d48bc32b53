package com.trs.yq.police.subject.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryFieldEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwrySourceTypeEnum;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.domain.entity.DictEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.vo.CrjJwryImportDataVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import com.trs.yq.police.subject.domain.vo.ImportResultListVO;
import com.trs.yq.police.subject.repository.CrjJwryDetailRepository;
import com.trs.yq.police.subject.repository.CrjJwryRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.webService.QgCrjWebService;
import com.trs.yq.police.subject.webService.domain.QueryArgs;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;


/**
 * Excel 读取类 配合 {@link com.alibaba.excel.EasyExcelFactory} 的 read() 使用
 *
 * <AUTHOR>
 * @date 2021/3/26 10:58
 */
@Slf4j
@Builder
public class ReadCrjJwryExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    private CrjJwryRepository crjJwryRepository;

    private CrjJwryDetailRepository crjJwryDetailRepository;

    private DictRepository dictRepository;

    private UnitRepository unitRepository;
    private QgCrjWebService qgCrjWebService;
    /**
     * 专题主键
     */
    private final String subjectId;

    /**
     * 重复策略 1-不导入, 2-覆盖导入，3-继续导入
     */
    private final String repeatStrategy;
    /**
     * 当前用户
     */
    private final LoginUser currentUser;
    /**
     * 字段映射
     */
    @Getter
    private final Map<Integer, CrjJwryFieldEnum> columnMap = new ConcurrentHashMap<>();

    /**
     * 处理总量
     */
    @Getter
    private final AtomicInteger analysisCount = new AtomicInteger();

    /**
     * 表头行数
     */
    @Getter
    private final AtomicInteger headCount = new AtomicInteger(0);

    /**
     * 失败记录
     */
    @Getter
    private final List<ImportResultListVO> failResult = new LinkedList<>();

    /**
     * 成功记录 key - 行数 value - key : 属性， value : 值
     */
    @Getter
    private final Map<Integer, Map<Integer, String>> success = new HashMap<>();

    /**
     * 失败记录
     */
    @Getter
    private final List<Map<Integer, String>> failRows = new LinkedList<>();

    private List<DictEntity> crjZjzlDict;
    private List<DictEntity> visaTypeDict;
    private List<DictEntity> crjEntryPortDict;
    private List<DictEntity> gjdmDict;
    SimpleDateFormat importDateFormat;
    SimpleDateFormat importDateFormat2;
    SimpleDateFormat format;
    SimpleDateFormat dateFormat;

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 检验是否为正常的header
        if (checkHeader(headMap)) {
            // 通过header 转换字段结构
            headMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    CrjJwryFieldEnum crjJwryFieldEnum = CrjJwryFieldEnum.cnNameOf(entry.getValue());
                    if (Objects.nonNull(crjJwryFieldEnum)) {
                        columnMap.put(entry.getKey(), crjJwryFieldEnum);
                    }
                });
        }
        headCount.incrementAndGet();
        crjZjzlDict = dictRepository.findAllByType("crjZjzl");
        visaTypeDict = dictRepository.findAllByType("crjVisaType");
        crjEntryPortDict=dictRepository.findAllByType("crjEntryPort");
        gjdmDict=dictRepository.findAllByType("crjGjdm");
    }

    private boolean checkHeader(Map<Integer, String> headMap) {
        return headMap.values().stream().anyMatch(value -> Objects.nonNull(PersonFieldEnum.cnNameOf(value)));
    }

    @Override
    public void invoke(Map<Integer, String> rows, AnalysisContext analysisContext) {
        // 1.先找到当前行的身份证信息
        if (checkAssignColumn(rows)) {
            success.put(analysisCount.get() + headCount.get(), rows);
        } else {
            failRows.add(rows);
        }
        // 计数
        analysisCount.incrementAndGet();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 存储
        success.forEach((key, value) -> {
            Map<String, String> personMap = new LinkedHashMap<>();
            value.forEach((k, v) -> {
                CrjJwryFieldEnum fieldEnum = columnMap.get(k);
                if (Objects.nonNull(fieldEnum)) {
                    personMap.put(columnMap.get(k).getField(), v);
                }
            });
            CrjJwryImportDataVO dataVO = JsonUtil.parseObject(JsonUtil.toJsonString(personMap),
                CrjJwryImportDataVO.class);
            String idType = crjZjzlDict.stream().filter(name -> name.getName().equals(dataVO.getIdType())).findFirst()
                .map(DictEntity::getCode).orElse("99");
            Date createTime=new Date();
            try {
                 createTime = importDateFormat2.parse(dataVO.getCreateTime());
            } catch (Exception e) {
                log.info("录入时间格式有误！");
            }
            CrjJwryEntity crjJwryEntity = crjJwryRepository.findByIdTypeAndIdNumberAndCreateTimeAndCrBy(idType, dataVO.getIdNumber(),createTime,currentUser.getId())
                .orElse(new CrjJwryEntity());
            crjJwryEntity.setCreateTime(createTime);
            crjJwryEntity.setIdType(idType);
            crjJwryEntity.setIdNumber(dataVO.getIdNumber());
            String gjdm = gjdmDict.stream().filter(name -> name.getName().equals(dataVO.getGjdm())).findFirst()
                .map(DictEntity::getCode).orElse("ZZZ");
            crjJwryEntity.setGjdm(gjdm);
            crjJwryEntity.setAddress(StringUtils.isNotBlank(dataVO.getLiveAddress())?dataVO.getLiveAddress():dataVO.getLsdw());
            UnitEntity unit = unitRepository.findByUnitName(dataVO.getPoliceStation());
            crjJwryEntity.setAcceptor(Objects.nonNull(unit) ? unit.getUnitCode() : currentUser.getUnitCode());
            crjJwryEntity.setDispatchStatus(CrjDispatchStatusEnum.DISPATCHED.getCode());
            crjJwryEntity.setRegistrationStatus("在住".equals(dataVO.getLiveStatus())?CrjJwryRegistrationStatusEnum.LIVING.getCode():CrjJwryRegistrationStatusEnum.LEAVE.getCode());

            crjJwryEntity.setCheckinTime(new Date());
            crjJwryEntity.setSourceType(CrjJwrySourceTypeEnum.IMPORT.getCode());

            try {
                Date date = importDateFormat.parse(dataVO.getPlanLeaveTime());
                crjJwryEntity.setPlanLeaveTime(date);
            } catch (Exception e) {
                log.info("拟离开日期 格式有误！");
            }
            try {
                Date date = importDateFormat.parse(dataVO.getZsrq());
                crjJwryEntity.setCheckinTime(date);
            } catch (Exception e) {
                log.info("住宿日期格式有误！");
            }

           try {
               QueryArgs queryArgs = new QueryArgs(gjdm, dataVO.getIdNumber(), idType);
               List<CrjJwryVisitVO> crjJwryVisitVOS = qgCrjWebService.stayInfo(queryArgs);
                crjJwryEntity.setAttachment(JsonUtil.toJsonString(crjJwryVisitVOS.get(0).getAttachment()));
           }catch (Exception e){
               log.info("获取临住人员照片失败，{},{},{}",gjdm, dataVO.getIdNumber(), idType);
           }
            CrjJwryEntity save = crjJwryRepository.save(crjJwryEntity);
            //将recordId设置成id
            crjJwryRepository.update(save.getId());

            CrjJwryDetailEntity crjJwryDetailEntity = crjJwryDetailRepository.findByIdTypeAndIdNumber(idType,
                dataVO.getIdNumber()).orElse(new CrjJwryDetailEntity());
            crjJwryDetailEntity.setCnName(dataVO.getCnName());
            crjJwryDetailEntity.setEnFirstName(dataVO.getEnFirstName());
            crjJwryDetailEntity.setEnLastName(dataVO.getEnLastName());
            crjJwryDetailEntity.setEnName(dataVO.getEnName());
            crjJwryDetailEntity.setGender(dataVO.getGender().equals("男") ? "1" : "2");
            crjJwryDetailEntity.setGjdm(gjdm);
            crjJwryDetailEntity.setIdType(idType);
            crjJwryDetailEntity.setIdNumber(dataVO.getIdNumber());
            String visaType = visaTypeDict.stream().filter(name -> name.getName().equals(dataVO.getVisaType())).findFirst()
                .map(DictEntity::getCode).orElse("Q");
            crjJwryDetailEntity.setVisaType(visaType);
            crjJwryDetailEntity.setVisaNumber(dataVO.getVisaNumber());
            try {
                Date date = importDateFormat.parse(dataVO.getInChinaTime());
                crjJwryDetailEntity.setInChinaTime(date);
            } catch (Exception e) {
                log.info("在华时间格式有误！");
            }
            try {
                Date date = importDateFormat.parse(dataVO.getEntryTime());
                crjJwryDetailEntity.setEntryTime(date);
            } catch (Exception e) {
                log.info("入境时间格式有误！");
            }
            try {
                Date date = importDateFormat.parse(dataVO.getBirthday());
                crjJwryDetailEntity.setBirthday(date);
            } catch (Exception e) {
                log.info("出生日期格式有误！");
            }
            String entryPort = crjEntryPortDict.stream().filter(name -> name.getName().equals(dataVO.getEntryPort())).findFirst()
                .map(DictEntity::getCode).orElse("");
            crjJwryDetailEntity.setEntryPort(entryPort);
            crjJwryDetailRepository.save(crjJwryDetailEntity);
        });
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {

        // 记录异常
        if (exception instanceof ExcelDataConvertException) {
            ExcelDataConvertException convertException = (ExcelDataConvertException) exception;
            final Integer rowIndex = convertException.getRowIndex();
            final Integer columnIndex = convertException.getColumnIndex();
            final String cellData = convertException.getCellData().toString();
            log.error("read excel fail! line : [ "
                .concat(String.valueOf(rowIndex))
                .concat(" ] row, [ ")
                .concat(String.valueOf(columnIndex))
                .concat(" ] cell, data : [ ")
                .concat(cellData).concat(" ]"), exception);
        } else {
            log.error("analysis excel error !", exception);
        }

    }

    private boolean checkAssignColumn(Map<Integer, String> rows) {

        AtomicBoolean validate = new AtomicBoolean(true);

        checkName(rows, validate);
        checkIdNumber(rows, validate);
        checkGjdm(rows, validate);
        checkBirthday(rows, validate);
        checkLiveStatus(rows, validate);
        checkZsrq(rows, validate);
        checkDataSource(rows, validate);
        checkEntryTime(rows, validate);
        checkGender(rows, validate);
        checkPoliceStation(rows, validate);
        checkLrsj(rows, validate);
        return validate.get();
    }

    private void checkLrsj(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.CREATE_TIME.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkLrsjValidate(rows.get(entry.getKey()), validate));
    }

    private void checkLrsjValidate(String date, AtomicBoolean validate) {
        if (StringUtils.isBlank(date)) {
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("录入日期不能为空"))
                .build();
            failResult.add(validFailResult);
        }else {
            try {
                importDateFormat.parse(date);
            } catch (Exception e) {
                validate.set(false);
                final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("录入日期格式错误，有效日期格式例:2023-01-01"))
                    .build();
                failResult.add(validFailResult);
            }
        }
    }
    private void checkPoliceStation(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.POLICE_STATION.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkPoliceStationIsBlank(rows.get(entry.getKey()), validate));
    }
    private void checkPoliceStationIsBlank(String name, AtomicBoolean validate) {
        if (StringUtils.isBlank(name)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("派出所名称不能为空"))
                .build();
            failResult.add(validFailResult);
        }
    }
    private void checkGender(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.GENDER.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkGenderIsBlank(rows.get(entry.getKey()), validate));
    }
    private void checkGenderIsBlank(String name, AtomicBoolean validate) {
        if (StringUtils.isBlank(name)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("性别不能为空"))
                .build();
            failResult.add(validFailResult);
        }
    }
    private void checkEntryTime(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.ENTRY_TIME.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkEntryTimeValidate(rows.get(entry.getKey()), validate));
    }

    private void checkEntryTimeValidate(String date, AtomicBoolean validate) {
        if (StringUtils.isNotBlank(date)) {
            try {
                importDateFormat.parse(date);
            } catch (Exception e) {
                validate.set(false);
                final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("入境日期格式错误，有效日期格式例:2023-01-01"))
                    .build();
                failResult.add(validFailResult);
            }
        }
    }
    private void checkDataSource(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.DATA_SOURCE.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkDataSourceBlank(rows.get(entry.getKey()), validate));
    }

    private void checkDataSourceBlank(String leveStatus, AtomicBoolean validate) {
        if (StringUtils.isBlank(leveStatus)) {
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("数据来源不能为空"))
                .build();
            failResult.add(validFailResult);
        }
    }
    private void checkZsrq(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.ZSRQ.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkZsrqValidate(rows.get(entry.getKey()), validate));
    }

    private void checkZsrqValidate(String date, AtomicBoolean validate) {
        if (StringUtils.isBlank(date)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("住宿日期不能为空"))
                .build();
            failResult.add(validFailResult);
        }
        try {
            importDateFormat.parse(date);
        } catch (Exception e) {
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("住宿日期格式错误，有效日期格式例:2023-01-01"))
                .build();
            failResult.add(validFailResult);
        }
    }
    private void checkLiveStatus(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.LIVE_STATUS.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkLiveStatusBlank(rows.get(entry.getKey()), validate));
    }

    private void checkLiveStatusBlank(String leveStatus, AtomicBoolean validate) {
        if (StringUtils.isBlank(leveStatus)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("居住状态不能为空"))
                .build();
            failResult.add(validFailResult);
        }else {
            if(!"在住".equals(leveStatus)){
                validate.set(false);
                final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("非在住状态的人不导入"))
                    .build();
                failResult.add(validFailResult);
            }
        }
    }
    private void checkBirthday(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.BIRTHDAY.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkBirthdayValidate(rows.get(entry.getKey()), validate));
    }

    private void checkBirthdayValidate(String date, AtomicBoolean validate) {
        if (StringUtils.isBlank(date)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("出生日期不能为空"))
                .build();
            failResult.add(validFailResult);
        }else {
            try {
                importDateFormat.parse(date);
            } catch (Exception e) {
                validate.set(false);
                final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("出生日期格式错误，有效日期格式例:2023-01-01"))
                    .build();
                failResult.add(validFailResult);
            }
        }

    }

    private void checkGjdm(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.GJDM.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkGjdmIsBlank(rows.get(entry.getKey()), validate));
    }

    private void checkGjdmIsBlank(String gjdm, AtomicBoolean validate) {
        if (StringUtils.isBlank(gjdm)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("国家地区不能为空"))
                .build();
            failResult.add(validFailResult);
        }
    }

    private void checkName(Map<Integer, String> rows, AtomicBoolean validate) {
        // 找到姓名
        columnMap.entrySet()
            .stream()
            .filter(entry -> CrjJwryFieldEnum.EN_NAME.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkNameIsBlank(rows.get(entry.getKey()), validate));
    }

    private void checkIdNumber(Map<Integer, String> rows, AtomicBoolean validate) {
        Integer idNumberRowIndex = columnMap.entrySet().stream()
            .filter(entry -> CrjJwryFieldEnum.IDNUMBER.equals(entry.getValue())).findFirst().map(Entry::getKey)
            .orElse(3);
        Integer idTypeRowIndex = columnMap.entrySet().stream()
            .filter(entry -> CrjJwryFieldEnum.ID_TYPE.equals(entry.getValue())).findFirst().map(Entry::getKey)
            .orElse(2);
        checkIdNumberIsRepeat(rows.get(idNumberRowIndex), rows.get(idTypeRowIndex), validate);
    }

    private void checkIdNumberIsRepeat(String idNumber, String type, AtomicBoolean validate) {
        if (StringUtils.isBlank(idNumber)) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("证件号码为空"))
                .build();
            failResult.add(validFailResult);
            validate.set(false);
        }
        if (StringUtils.isBlank(type)) {
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("证件类型为空"))
                .build();
            failResult.add(validFailResult);
            validate.set(false);
        }
        String idType = crjZjzlDict.stream().filter(name -> name.getName().equals(type)).findFirst()
            .map(DictEntity::getCode).orElse("99");
        if (repeatStrategy.equals("1") && !crjJwryRepository.findByIdNumberAndIdType(idNumber,idType).isEmpty()) {
            // 如果选择为不导入，则当前用户为不能导入，则为失败
            final ImportResultListVO repeatFailResult = ImportResultListVO.builder().status(1)
                .status(1)
                .desc(generateFailMsg("该人员已存在不导入")).build();
            failResult.add(repeatFailResult);
            validate.set(false);
        }

    }

    private void checkNameIsBlank(String name, AtomicBoolean validate) {
        if (StringUtils.isBlank(name)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("英文姓名不能为空"))
                .build();
            failResult.add(validFailResult);
        }
    }


    private String generateFailMsg(String errorMsg) {
        return "第".concat(String.valueOf(analysisCount.get() + headCount.get() + 1))
            .concat("行 ").concat(errorMsg);
    }
}
