package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2022/1/12 15:33
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_ATTRIBUTE_VALUE")
public class LabelAttributeValueEntity extends BaseEntity {
    /**
     * 档案记录id
     */
    private String recordId;
    /**
     * 模块
     */
    private String module;
    /**
     * 属性id
     */
    private String attributeId;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性值
     */
    private String value;

}
