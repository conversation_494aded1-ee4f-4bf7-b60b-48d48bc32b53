package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ClueExtendEntity;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 线索-扩展信息书记库接口
 *
 * <AUTHOR>
 * @date 2021/12/8 15:25
 */
@Repository
public interface ClueExtendRepository extends BaseRepository<ClueExtendEntity, String> {

    /**
     * 查询线索扩展信息
     *
     * @param clueId 线索id
     * @return {@link ClueExtendEntity}
     */
    Optional<ClueExtendEntity> findByClueId(String clueId);
}
