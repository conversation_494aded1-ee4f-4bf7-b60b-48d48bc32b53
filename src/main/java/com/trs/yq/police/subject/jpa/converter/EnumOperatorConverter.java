package com.trs.yq.police.subject.jpa.converter;

import com.trs.yq.police.subject.constants.enums.Operator;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Objects;

/**
 * 操作枚举转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 17:52
 */
@Converter
public class EnumOperatorConverter implements AttributeConverter<Operator, String> {
    @Override
    public String convertToDatabaseColumn(Operator attribute) {
        return Objects.isNull(attribute) ? null : attribute.getCode();
    }

    @Override
    public Operator convertToEntityAttribute(String dbData) {
        return Operator.codeOf(dbData);
    }
}
