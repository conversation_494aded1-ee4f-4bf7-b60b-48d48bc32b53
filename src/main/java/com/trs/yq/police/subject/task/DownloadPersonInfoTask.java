package com.trs.yq.police.subject.task;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.PersonFileRelationEntity;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.repository.PersonFileRelationRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * 通过身份证号从外部接口同步人员信息的请求客户端
 *
 * <AUTHOR>
 * @date 2021/09/15
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "com.trs.download.person.info.task.enable", havingValue = "true")
public class DownloadPersonInfoTask {

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private RemoteStorageService remoteStorageService;

    @Resource
    private PersonFileRelationRepository personFileRelationRepository;

    private static class Constants {
        public static String GENDER_MALE = "1";
        public static String GENDER_FEMALE = "2";
    }

    private static final Map<String, String> BELIEFS_MAP = new HashMap<>();

    static {
        BELIEFS_MAP.put("00", "无宗教信仰");
        BELIEFS_MAP.put("10", "佛教");
        BELIEFS_MAP.put("20", "喇嘛教");
        BELIEFS_MAP.put("30", "道教");
        BELIEFS_MAP.put("40", "天主教");
        BELIEFS_MAP.put("50", "基督教");
        BELIEFS_MAP.put("60", "东正教");
        BELIEFS_MAP.put("70", "伊斯兰教");
        BELIEFS_MAP.put("99", "其他");
    }

    @Value("${com.trs.download.person.info.url}")
    private String url;

    @Value("${com.trs.download.person.info.apikey}")
    private String apikey;

    /**
     * 定时同步人员基本信息到本地
     */
    @Scheduled(cron = "${com.trs.download.person.info.task}")
    public void syncPersonInfo() {
        List<PersonEntity> personEntities = personRepository.findAll();
        //请求基本信息
        for (PersonEntity personEntity : personEntities) {
            String idNumber = personEntity.getIdNumber();
            log.info("begin downloading person info : " + idNumber);
            UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("gmsfhm", idNumber)
                    .queryParam("apikey", apikey)
                    .build();
            String jsonText = restTemplate.getForObject(uriComponents.toUriString(), String.class);
            if (StringUtils.isBlank(jsonText)) {
                log.error("response empty!");
                continue;
            }
            log.info("finish downloading person info : \n");
            JsonNode jsonNode = JsonUtil.parseJsonNode(jsonText);
            if (Objects.isNull(jsonNode)) {
                log.error("parse json failed!\n" + jsonText);
                continue;
            }

            if (!jsonNode.get("data").elements().hasNext()) {
                log.error("data is empty!");
                continue;
            }

            JsonNode vo = jsonNode.get("data").elements().next();
            //更新基本信息
            personEntity.setIdNumber(vo.get("GMSFHM").asText());
            personEntity.setName(vo.get("XM").asText());
            personEntity.setNation(vo.get("MZ").asText());
            personEntity.setFormerName(vo.get("CYM").asText().replace("null", ""));
            String xb = vo.get("XB").asText();
            if (Constants.GENDER_FEMALE.equals(xb)) {
                personEntity.setGender("1");
            } else if (Constants.GENDER_MALE.equals(xb)) {
                personEntity.setGender("0");
            }
            personEntity.setReligiousBelief(BELIEFS_MAP.get(vo.get("ZJXY").asText()));
            String ssxq = vo.get("SSXQ").asText();
            if (ssxq.matches("\\d{6}")) {
                personEntity.setAreaCode(ssxq);
            }
            personEntity.setRegisteredResidence(vo.get("ZZ").asText());
            personRepository.save(personEntity);

            //存储照片
            String xp = vo.get("XP").asText();
            if (!StringUtils.isNotBlank(xp)) {
                log.info("no photo data to update");
                continue;
            }

            List<PersonFileRelationEntity> relations = personFileRelationRepository.findAllByPersonId(personEntity.getId());
            if (relations.stream().anyMatch(relation -> relation.getModule().equals(FileModuleEnum.BASIC_INFO_PHOTO.getCode()))) {
                log.info("photo existed, no need to update");
                continue;
            }

            try {
                log.info("begin to update photo");
                byte[] imageData = Base64.getDecoder().decode(xp);
                ImageVO imageVO = remoteStorageService.uploadImage(imageData, idNumber + ".jpg");
                remoteStorageService.savePersonImageRelations(personEntity.getId(),
                        Collections.singletonList(imageVO),
                        FileModuleEnum.BASIC_INFO_PHOTO,
                        null);
                log.info("photo update finished");
            } catch (IOException exception) {
                log.error("upload image failed! ", exception);
            }
        }
    }
}
