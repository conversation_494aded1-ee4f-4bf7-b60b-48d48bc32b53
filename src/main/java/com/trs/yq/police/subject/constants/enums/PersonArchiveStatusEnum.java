package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 人员归档激活状态枚举类
 *
 * <AUTHOR>
 * @date 2021/08/10
 */
public enum PersonArchiveStatusEnum {

    /**
     * enums
     */
    ARCHIVE("0", "归档"),
    ACTIVE("1", "激活");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    PersonArchiveStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过模块码获取枚举
     *
     * @param code 模块码
     * @return 操作模块 {@link PersonArchiveStatusEnum}
     */
    public static PersonArchiveStatusEnum codeOf(String code) {
        if (StringUtils.isBlank(code)) {
            return ACTIVE;
        }
        return Arrays.stream(PersonArchiveStatusEnum.values())
                .filter(module -> module.getCode().equals(code))
                .findFirst()
                .orElse(ACTIVE);
    }
}
