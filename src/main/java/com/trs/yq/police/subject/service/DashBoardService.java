package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/12/20 11:17
 */
public interface DashBoardService {

    /**
     * 通过人员类型统计数量
     *
     * @param statisticRequestVO 参数
     * @return 统计vo
     */
    List<BigScreenStatisticVO> statisticByRylb(StatisticRequestVO statisticRequestVO);

    /**
     * 重点人员-人员预警
     *
     * @param statisticRequestVO 请求参数
     * @return 统计vo
     */
    List<BigScreenStatisticVO> statisticByYjjb(StatisticRequestVO statisticRequestVO);

    /**
     * 获取警情top5
     *
     * @param requestVO 请求vo
     * @return 数据
     */
    List<BigScreenStatisticVO> getJqfxJjtop(StatisticRequestVO requestVO);

    /**
     * 获取区县接警情况
     *
     * @param requestVO 请求vo
     * @return 数据
     */
    List<BigScreenStatisticVO> getQxjjqk(StatisticRequestVO requestVO);

    /**
     * 警情类别
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getJqlb(StatisticRequestVO requestVO);

    /**
     * 警情趋势
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getJqqs(StatisticRequestVO requestVO);

    /**
     * 机制赋能-警务协作
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getJwxz(StatisticRequestVO requestVO);

    /**
     * 机制赋能-警务合作
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getJwhz(StatisticRequestVO requestVO);

    /**
     * 日常工作-警务合成-协作手段
     *
     * @param requestVO 请求参数
     * @param type      类型
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getJwhz(StatisticRequestVO requestVO, String type);

    /**
     * 获取刑事案件
     *
     * @param requestVO 请求参数
     * @return 数据
     */
    List<BigScreenStatisticVO> getXsaj(StatisticRequestVO requestVO);

    /**
     * 获取刑事案件
     *
     * @param requestVO 请求参数
     * @return 数据
     */
    List<BigScreenStatisticVO> getZaaj(StatisticRequestVO requestVO);

    /**
     * 街面六类
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getJmll(StatisticRequestVO requestVO);

    /**
     * 打击质量
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getDjzl(StatisticRequestVO requestVO);

    /**
     * 机制赋能-协作单位
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getXzdw(StatisticRequestVO requestVO);


    /**
     * 案件合成率
     *
     * @param requestVO 请求参数
     * @return 数据
     */
    BigScreenStatisticVO getAjhcl(StatisticRequestVO requestVO);

    /**
     * 风险管控-治安风险
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getZafx(StatisticRequestVO requestVO);

    /**
     * 风险管控-风险上报
     *
     * @param requestVO 请求参数
     * @return {@link FxsbVO}
     */
    List<FxsbVO> getFxsb(StatisticRequestVO requestVO);

    /**
     * 风险管控-热点分析
     *
     * @param requestVO 请求参数
     * @return {@link FxsbVO}
     */
    List<BigScreenStatisticVO> getRdfx(StatisticRequestVO requestVO);

    /**
     * 日志检测-在线人数日志量
     *
     * @param requestVO 请求参数
     * @return {@link SystemLogVO}
     */
    SystemLogVO getZxrsrzl(StatisticRequestVO requestVO);

    /**
     * 日志检测-使用功能
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getSygn(StatisticRequestVO requestVO);

    /**
     * 日志检测-访问分布
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getFwfb(StatisticRequestVO requestVO);

    /**
     * 日志检测-平台活跃趋势
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getPthyqs(StatisticRequestVO requestVO);

    /**
     * 日常工作-指令中心
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<BigScreenStatisticVO> getZlzx(StatisticRequestVO requestVO);

    /**
     * 预警信息
     *
     * @param requestVO 请求参数
     * @return {@link BigScreenStatisticVO}
     */
    List<YjxxVO> getYjxx(StatisticRequestVO requestVO);

}
