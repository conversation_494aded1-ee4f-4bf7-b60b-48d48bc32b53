package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/4 16:59
 */
@Data
public class EventPersonRelationRequestVO implements Serializable {

    private static final long serialVersionUID = -5602833195689928326L;
    /**
     * 线索id
     */
    private String eventId;

    /**
     * 人员Id
     */
    @NotNull(message = "人员id不能为空！")
    private List<String> personIds;

    /**
     * 涉事行为
     */
    private List<String> behaviours;

    /**
     * 来源 1: 网安 2: 视侦
     */
    private String source;
}
