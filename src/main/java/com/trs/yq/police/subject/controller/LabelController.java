package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.domain.vo.IdNameVO;
import com.trs.yq.police.subject.domain.vo.LabelQueryVO;
import com.trs.yq.police.subject.domain.vo.LabelVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.service.LabelService;
import com.trs.yq.police.subject.validation.RuleGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 标签接口
 *
 * <AUTHOR>
 * @date 2021/7/31 19:00
 */
@RestController
@RequestMapping("/label")
@Validated
@Slf4j
public class LabelController {

    @Resource
    private LabelService labelService;

    /**
     * 创建标签
     * http://192.168.200.192:3001/project/4897/interface/api/129680
     *
     * @param label 创建标签所需数据
     * @return 标签id
     */
    @PostMapping("")
    public String createLabel(@Validated(RuleGroup.Create.class) @RequestBody @NotNull(message = "创建标签所需数据不可缺失") LabelVO label) {

        return labelService.createLabel(label);
    }

    /**
     * 查询标签
     * http://192.168.200.192:3001/project/4897/interface/api/129695
     *
     * @param labelId 标签id
     * @return 标签数据
     */
    @GetMapping("")
    public LabelVO getLabel(@NotBlank(message = "标签主键不可为空") String labelId) {

        return labelService.getLabel(labelId);
    }

    /**
     * 移除标签
     * http://192.168.200.192:3001/project/4897/interface/api/129690
     *
     * @param labelId 标签id
     */
    @DeleteMapping("")
    public void deleteLabel(@NotBlank(message = "标签主键不可为空") String labelId) {

        labelService.deleteLabel(labelId);
    }

    /**
     * 更新标签
     * http://192.168.200.192:3001/project/4897/interface/api/129690
     *
     * @param label 更新标签数据
     */
    @PutMapping("")
    public void updateLabel(@Validated(RuleGroup.Update.class) @RequestBody @NotNull(message = "更新标签所需数据不可缺失") LabelVO label) {

        labelService.updateLabel(label);
    }

    /**
     * 列表查询
     * http://192.168.200.192:3001/project/4897/interface/api/129700
     *
     * @param query    查询参数
     * @param pageable 分页参数
     * @return 列表查询标签
     */
    @GetMapping("/list")
    public PageResult<LabelVO> listLabels(LabelQueryVO query, Pageable pageable) {

        return labelService.listLabels(query, pageable);
    }

    /**
     * 获取专题所有标签
     * http://192.168.200.192:3001/project/4897/interface/api/130124
     *
     * @param subjectId 专题id
     * @param module 模块
     * @return 标签列表
     */
    @GetMapping("/all")
    public List<IdNameVO> getLabels(@NotBlank(message = "模块缺失") String module,
                                   @NotBlank(message = "专题主键缺失") String subjectId) {

        return labelService.getLabels(subjectId, module);
    }


}
