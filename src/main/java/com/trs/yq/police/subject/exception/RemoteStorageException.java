package com.trs.yq.police.subject.exception;

import lombok.Getter;

/**
 * 对象存储异常
 *
 * <AUTHOR>
 * @date 2020/7/14
 **/
public class RemoteStorageException extends RuntimeException {

    private static final long serialVersionUID = 7424708841477016223L;

    @Getter
    private final String message;

    /**
     * 构造器
     *
     * @param message   消息内容
     * @param groupName groupName
     * @param fileId    fieldId
     */
    public RemoteStorageException(String message, String groupName, String fileId) {
        this.message = String.format("%s,[groupName=%s,fileId=%s]", message, groupName, fileId);
    }
}
