package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.WarningJudgeVO;

/**
 * 预警详情service层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/10 9:55
 **/
public interface WarningDetailsService {


    /**
     * 根据预警id研判
     *
     * @param warningId       预警id
     * @param warningJudgeVO 研判VO
     */
    void doJudge(String warningId, WarningJudgeVO warningJudgeVO);

    /**
     * 根据预警id签收
     *
     * @param warningId 预警id
     */
    void doSign(String warningId);
}
