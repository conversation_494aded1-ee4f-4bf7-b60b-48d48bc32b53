package com.trs.yq.police.subject.domain.dto;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警信息子级数据传输类
 *
 * <AUTHOR>
 * @date 2021/9/6 14:26
 */
@Getter
@Setter
@ToString
public class WarningTrajectoryListDTO implements Serializable {

    private static final long serialVersionUID = 3606318450714583776L;

    /**
     * 轨迹类型对应海贝表名
     */
    private String trajectoryType;

    /**
     * 海贝表主键 uuid
     */
    private String trsId;

    /**
     * id的类型
     * idNumber - 身份证、
     * phoneNumber - 手机号、
     * imei、imsi、mac - 设备码、
     * plate - 车牌号
     */
    private String idType;

    /**
     * id的值 格式与idType对应
     */
    private String idValue;

    /**
     * 映射涉及的身份证号码
     */
    private String idNumber;

    /**
     * 时间，时间戳
     */
    private LocalDateTime dateTime;

    /**
     * 全景图url，可以为空
     */
    private String imageUrl;

    /**
     * 裁剪图url，可以为空
     */
    private String cropUrl;

    /**
     * 图片相似度，可以为空
     */
    private String similarity;

    /**
     * 场所
     */
    private String place;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 原始信息
     */
    private JsonNode raw;
}
