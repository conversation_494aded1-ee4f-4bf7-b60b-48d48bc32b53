package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 * 线索的模块枚举
 *
 * <AUTHOR>
 * @date 2021/09/03
 */

public enum ClueModuleEnum {
    /**
     * enums
     */
    CLUE_MESSAGE_PHOTO("3", "线索信息照片"),
    CLUE_MESSAGE_WORD("4","线索信息附件");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    ClueModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
