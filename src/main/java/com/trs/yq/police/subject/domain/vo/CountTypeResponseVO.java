package com.trs.yq.police.subject.domain.vo;

import lombok.*;

import java.io.Serializable;

/**
 * 类型分布
 *
 * <AUTHOR>
 * @date 2021/9/10 13:14
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class CountTypeResponseVO implements Serializable {

    private static final long serialVersionUID = 8497448294824242289L;

    /**
     * 人员类型id
     */
    private String personTypeId;
    /**
     * 人员类型
     */
    private String personType;

    /**
     * 人员数量
     */
    private Long personCount;

    /**
     * @param personType  人员类型
     * @param personCount 数量
     */
    public CountTypeResponseVO(String personType, long personCount) {
        this.personType = personType;
        this.personCount = personCount;
    }
}
