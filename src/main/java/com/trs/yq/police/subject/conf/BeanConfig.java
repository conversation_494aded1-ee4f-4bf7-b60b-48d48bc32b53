package com.trs.yq.police.subject.conf;

import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * bean 配置类
 *
 * <AUTHOR>
 * @date 2021/8/19 10:44
 */
@Configuration
public class BeanConfig {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 创建 OperationLogHandler
     *
     * @return {@link OperationLogHandler}
     */
    @Bean
    public OperationLogHandler operationLogHandler() {
        OperationLogHandler handler = new OperationLogHandler(applicationContext);
        handler.start();
        return handler;
    }

}
