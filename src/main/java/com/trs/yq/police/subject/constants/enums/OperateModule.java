package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 操作模块枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 15:33
 */
public enum OperateModule {

    /**
     * enums
     */
    ARCHIVES_MANAGE("0", "档案管理"),
    BASIC_INFO("1", "基本信息"),
    EDUCATION_BACKEND("2", "教育信息"),
    WORK_INFO("3", "工作信息"),
    FAMILY_RELATIONSHIP("4", "家庭关系"),
    SOCIAL_RELATIONSHIP("5", "社会关系"),
    VEHICLE("6", "车辆信息"),
    CONCAT_METHOD("7", "手机号"),
    VIRTUAL_IDENTITY("8", "虚拟身份"),
    VISIT_RECORD("9", "走访记录"),
    CASE_EVENT("10", "案事件记录"),
    PERSON_TRACE("11", "人员轨迹"),
    TRANSIENT_PLACE("12", "落脚地地址"),
    MOBILITY("13", "流动信息"),
    CONTROL_INFO("14", "公安管控信息"),
    RELATE_TO_GROUP("15", "涉事相关群体"),
    PERSON_RELATED_CLUE("16", "相关线索"),
    BANK_CARD("17", "银行卡信息"),
    ADJUDICATION("18", "裁决信息"),
    OPERATION_LOG("19", "操作日志"),
    GOV_CONTROL_INFO("100", "政府管控信息"),
    PERSON_RELATED_EVENT("101", "相关事件"),
    CALL_ANALYSIS("104", "话单分析"),
    FUND_ANALYSIS("105", "资金分析"),

    GROUP_ARCHIVE_MANAGE("20", "档案管理"),
    GROUP_BASIC_INFO("21", "基本信息"),
    GROUP_MEMBER("22", "群体成员"),
    GROUP_RELATED_CLUE("23", "相关线索"),
    GROUP_PARENT("24", "上级群体"),
    GROUP_GOV_CONTROL_INFO("25", "政府管控信息"),
    GROUP_RELATED_EVENT("26", "相关事件"),
    GROUP_SENSITIVE_TIME("27", "敏感时间节点"),

    CLUE_ARCHIVE_MANAGE("30", "档案管理"),
    CLUE_BASIC_INFO("31", "基本信息"),
    CLUE_RELATED_PERSON("32", "涉及人员"),
    CLUE_RELATED_GROUP("33", "涉及群体"),
    CLUE_FILE("36", "线索材料"),

    WARNING("40", "预警"),

    EVENT_ARCHIVE_MANAGE("50", "档案管理"),
    EVENT_BASIC_INFO("51", "基本信息"),
    EVENT_RELATED_PERSON("52", "涉及人员"),
    EVENT_RELATED_GROUP("53", "涉及群体"),
    EVENT_FILE("54", "事件材料"),
    EVENT_COMMAND("55", "工作指令"),
    EVENT_RECORD("56", "合成作战");


    /**
     * fields
     */
    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * constructor
     *
     * @param code 模块码
     * @param name 模块名
     */
    OperateModule(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过模块码获取枚举
     *
     * @param code 模块码
     * @return 操作模块 {@link OperateModule}
     * <AUTHOR>
     * @since 2021/7/27 17:55
     */
    public static OperateModule codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            return Arrays.stream(OperateModule.values())
                    .filter(module -> module.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
}
