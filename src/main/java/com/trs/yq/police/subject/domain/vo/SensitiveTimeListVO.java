package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.SensitiveTimeEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/1/13 17:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SensitiveTimeListVO extends SensitiveTimeVO implements Serializable {
    private static final long serialVersionUID = -9199279152475871576L;
    /**
     * 总天数
     */
    private Long days;
    /**
     * 创建单位
     */
    private String createDepartment;

    /**
     * 转vo
     *
     * @param entity 实体类
     * @return vo
     */
    public static SensitiveTimeListVO of(SensitiveTimeEntity entity) {
        SensitiveTimeListVO vo = new SensitiveTimeListVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setCreateDepartment(entity.getCrDept());
        long days = entity.getEndTime().toEpochDay() - entity.getStartTime().toEpochDay() + 1;
        vo.setDays(days);
        vo.setNodeId(entity.getId());
        return vo;
    }
}
