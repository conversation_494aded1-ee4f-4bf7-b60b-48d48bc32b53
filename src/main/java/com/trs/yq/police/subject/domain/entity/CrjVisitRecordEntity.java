package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 出入境走访记录
 *
 * <AUTHOR>
 * @since 2021/9/16
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_VISIT_RECORD")
public class CrjVisitRecordEntity extends BaseEntity{
    private static final long serialVersionUID = -3037102721140130078L;

    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 走访时间
     */
    private LocalDateTime visitTime;
    /**
     * 走访类型
     */
    private String visitType;
    /**
     * 是否在控
     */
    private String controlStatus;
    /**
     * 居住信息
     */
    private String livingInfo;
    /**
     * 旅店信息
     */
    private String livingInn;
    /**
     * 走访详情
     */
    private String visitDetail;
}
