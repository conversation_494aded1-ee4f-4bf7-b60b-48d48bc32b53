package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.MobilityEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.MobilityPageWithTotal;
import com.trs.yq.police.subject.domain.vo.MobilityVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.MobilityRepository;
import com.trs.yq.police.subject.service.MobilityService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 流动信息业务层接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class MobilityServiceImpl implements MobilityService {

    @Resource
    MobilityRepository mobilityRepository;

    @Resource
    PersonService personService;

    @Resource
    private OperationLogHandler operationLogHandler;

    private static final String MOVE_IN = "1";

    private static final String MOVE_OUT = "2";

    @Override
    public MobilityPageWithTotal getMovement(String personId, PageParams pageParams) {
        Integer totalIn = mobilityRepository.countByMoveTypeAndPersonId(MOVE_IN, personId);
        return MobilityPageWithTotal.of(mobilityRepository.findByPersonId(personId, pageParams.toPageable(Sort.by(Sort.Direction.DESC, "moveTime"))), totalIn);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addMovement(String personId, MobilityVO mobilityVO) {
        personService.checkPersonExist(personId);
        MobilityEntity mobilityEntity = new MobilityEntity(
                personId,
                DateUtil.utcToLocalDateTime(mobilityVO.getMoveTime()),
                mobilityVO.getMoveType(),
                mobilityVO.getLocation(),
                mobilityVO.getNote());
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.MOBILITY)
                .desc("新增流动信息")
                .newObj(JsonUtil.toJsonString(mobilityEntity))
                .primaryKey(personId)
                .currentUser(AuthHelper.getCurrentUser())
                .build();
        mobilityRepository.save(mobilityEntity);

        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateMovement(String personId, MobilityVO mobilityVO) {
        personService.checkPersonExist(personId);
        MobilityEntity original = mobilityRepository.findById(mobilityVO.getId()).orElse(null);

        if (Objects.isNull(original)) {
            throw new ParamValidationException("流动信息不存在，请刷新核实");
        }

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.MOBILITY)
                .desc("编辑流动信息")
                .oldObj(JsonUtil.toJsonString(original))
                .primaryKey(personId)
                .currentUser(AuthHelper.getCurrentUser())
                .build();

        MobilityEntity mobilityEntity = new MobilityEntity(
                personId,
                DateUtil.utcToLocalDateTime(mobilityVO.getMoveTime()),
                mobilityVO.getMoveType(),
                mobilityVO.getLocation(),
                mobilityVO.getNote());
        BeanUtil.copyPropertiesIgnoreNull(mobilityEntity, original);
        mobilityRepository.save(original);

        logRecord.setNewObj(JsonUtil.toJsonString(original));
        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteMovement(String personId, String id) {

        // 校验人员
        personService.checkPersonExist(personId);

        // 校验流动
        final MobilityEntity mobility = mobilityRepository.findById(id).orElse(null);

        if (Objects.isNull(mobility)) {
            throw new ParamValidationException("流动信息不存在，请刷新核实");
        }

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.MOBILITY)
                .desc("删除流动信息")
                .oldObj(JsonUtil.toJsonString(mobility))
                .primaryKey(personId)
                .currentUser(AuthHelper.getCurrentUser())
                .build();

        mobilityRepository.deleteById(id);

        if (Objects.nonNull(operationLogHandler)) {
            // 操作记录
            operationLogHandler.publishEvent(logRecord);
        }
    }


}
