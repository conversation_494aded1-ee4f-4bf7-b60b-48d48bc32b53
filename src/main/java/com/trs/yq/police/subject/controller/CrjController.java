package com.trs.yq.police.subject.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.request.ListParamsRequest;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.service.CrjCzryService;
import com.trs.yq.police.subject.service.CrjService;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

/**
 * 出入境专题接口类
 *
 * <AUTHOR>
 * @since 2021/9/17
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/data-view/entry-exit")
public class CrjController {

    @Resource
    private CrjService crjService;
    @Resource
    private CrjCzryService crjCzryService;


    /**
     * [专题首页-出入境] 在泸境外人员登记情况
     * http://192.168.200.192:3001/project/4897/interface/api/131695
     *
     * @param timeParams 时间参数
     * @return 统计结果
     */
    @PostMapping("/statistics/registration")
    public List<CountTypeResponseVO> getRegistrationCount(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getRegistrationCount(timeParams);
    }

    /**
     * [专题首页-出入境] 境外人员国籍情况
     * http://192.168.200.192:3001/project/4897/interface/api/131731
     *
     * @param timeParams 时间参数
     * @return 统计结果
     */
    @PostMapping("/statistics/nationality")
    public List<CountTypeResponseVO> getNationalityCount(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getNationalityCount(timeParams);
    }

    /**
     * [专题首页-出入境] 成渝过境免签人员活动情况
     * http://192.168.200.192:3001/project/4897/interface/api/131713
     *
     * @param timeParams 时间参数
     * @return 统计结果
     */
    @PostMapping("/statistics/visa-waiver")
    public List<VisaWaiverCountVO> getVisaWaiverCount(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getVisaWaiverCount(timeParams);
    }

    /**
     * [专题首页-出入境] 人员预警
     * http://192.168.200.192:3001/project/4897/interface/api/131371
     *
     * @param status 预警状态
     * @return 出入境人  人员预警列表
     */
    @GetMapping("/warning/person/new")
    public WarningScrollListVO getWarningEntryExitPerson(@NotEmpty(message = "预警状态不能为空") String status) {
        return crjService.getWarningEntryExitPerson(status);
    }

    /**
     * [专题首页-出入境] 未办理住宿登记预警
     * http://192.168.200.192:3001/project/4897/interface/api/131419
     *
     * @param status 预警状态
     * @return 出入境人  未办理住宿登记列表
     */
    @GetMapping("/warning/accommodation")
    public WarningScrollListVO getRegisterOverdue(@NotEmpty(message = "预警状态不能为空") String status) {
        return crjService.getAccommodationWarning(status);
    }

    /**
     * [专题首页-出入境] 免签超期预警
     * http://192.168.200.192:3001/project/4897/interface/api/131413
     *
     * @param status 预警状态
     * @return 出入境人  免签超期预警
     */
    @GetMapping("/warning/visa")
    public WarningScrollListVO getOverdue(@NotEmpty(message = "预警状态不能为空") String status) {
        return crjService.getOverdue(status);
    }

    /**
     * [专题首页-出入境] 妨害国（边）境管理人员(数量统计)
     * http://192.168.200.192:3001/project/4897/interface/api/131749
     *
     * @param timeParams 时间范围
     * @return 统计结果
     */
    @PostMapping("/harmful/border/total")
    public HarmfulBorderVO getHarmful(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getHarmful(timeParams);
    }

    /**
     * [专题首页-出入境] 走访记录列表查询
     *
     * @param crjVisitListRequestVO {@link CrjVisitListRequestVO}
     * @return {@link CrjVisitListVO}
     */
    @PostMapping("/visit/list")
    public PageResult<CrjVisitListVO> getCrjVisitListVOList(@RequestBody CrjVisitListRequestVO crjVisitListRequestVO) {
        return crjService.getCrjVisitListVOList(crjVisitListRequestVO);
    }

    /**
     * [专题首页-出入境] 基本数据统计
     * http://192.168.200.192:3001/project/4897/interface/api/131773
     *
     * @param timeParams 时间范围
     * @return 统计结果
     */
    @PostMapping("/count")
    public CrjWarningCountVO getWarningCount(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getWarningCount(timeParams);
    }

    /**
     * [专题首页-出入境] 走访记录详情
     * http://192.168.200.192:3001/project/4897/interface/api/131671
     *
     * @param visitId 走访记录id
     * @return {@link CrjVisitVO}
     */
    @GetMapping("/{visitId}/visit")
    public CrjVisitVO getCrjVisitVO(@PathVariable String visitId) {
        return crjService.getCrjVisitVOList(visitId);
    }

    /**
     * [专题首页-出入境] 住宿登记详情
     * http://192.168.200.192:3001/project/4897/interface/api/131677
     *
     * @param accommodationId 住宿id
     * @return 住宿详情 {@link CrjAccommodationVO}
     */
    @GetMapping("/{accommodationId}/accommodation")
    public CrjAccommodationVO getAccommodationDetail(@PathVariable String accommodationId) {
        return crjService.getAccommodationDetail(accommodationId);
    }

    /**
     * [专题首页-出入境] 住宿登记列表查询
     *
     * @param crjVisitListRequestVO {@link CrjAccommodationListRequestVO}
     * @return {@link CrjAccommodationListVO}
     */
    @PostMapping("/accommodation/list")
    public PageResult<CrjAccommodationListVO> getCrjAccommodationListVOList(@RequestBody CrjAccommodationListRequestVO crjVisitListRequestVO) {
        return crjService.getCrjAccommodationListVOList(crjVisitListRequestVO);
    }

    /**
     * [专题首页-出入境] 走访部门筛选条件查询
     *
     * @return 所有走访单位
     */
    @GetMapping("/visit/deptNames")
    public List<UnitVO> getDeptNames() {
        return crjService.getListUnitTree();
    }

    /**
     * [专题首页-出入境] 境外回泸涉疫人员-流入地分析
     * http://192.168.200.192:3001/project/4897/interface/api/131821
     *
     * @param timeParams 时间筛选
     * @return 统计结果
     */
    @PostMapping("/statistics/disease/inflow")
    public List<CountTypeResponseVO> getDiseaseInflow(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getDiseaseInflow(timeParams);
    }

    /**
     * [专题首页-出入境] 境外回泸涉疫人员-人员分布情况
     * http://192.168.200.192:3001/project/4897/interface/api/131827
     *
     * @param timeParams 时间筛选
     * @return 统计结果
     */
    @PostMapping("/statistics/disease/area")
    public List<CrjDiseaseAreaCountVO> getDiseaseAreaCount(@Validated @RequestBody TimeParams timeParams) {
        return crjService.getDiseaseAreaCount(timeParams);
    }

    /**
     * [专题首页-出入境] 出入境三非人员列表
     *
     * @param requestVO {@link CrySfryRequestVO}
     * @return {@link CrjSfryListVO}
     */
    @PostMapping("/sfry/list")
    public PageResult<CrjSfryListVO> getSwryList(@RequestBody CrySfryRequestVO requestVO) {
        return crjService.getSwryList(requestVO);
    }
    /**
     * [专题首页-出入境] 出入境三非人员列表
     *
     * @param sfryId 三非人员id
     * @return {@link CrjSfryBaseVO}
     */
    @GetMapping("/{sfryId}/sfry")
    public CrjSfryBaseVO getSwryList(@PathVariable String sfryId) {
        return crjService.getSfryInfo(sfryId);
    }

    /**
     * 全部门的树，带层级
     *
     * @return 树
     */
    @GetMapping("/grid/tree")
    public List<GridTreeVO> getGridTree() {
        return crjService.getGridTree();
    }


    /**
     * [专题首页-出入境] 出入境三非人员列表
     *
     * @param sfryId 三非人员id
     * @param gridPhoneNumber 网格员手机号
     */
    @GetMapping("/{sfryId}/sfry/send-msg")
    public void sendMsg(@PathVariable  String sfryId,String gridPhoneNumber) throws Exception {
         crjService.sendMsg(sfryId,gridPhoneNumber);
    }

    /**
     * [专题首页-出入境] 出入境三非人员列表
     *
     * @param gridId 三非人员id
     * @param phoneNumber 手机号
     */
    @GetMapping("/grid/phone")
    public void updateGridPhoneNumber(String gridId,String phoneNumber) {
        crjService.updateGridPhoneNumber(gridId,phoneNumber);
    }

    /**
     * 常住人员列表查询
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/residence/list")
    public PageResult<CrjResidenceListVO> getResidenceList(@RequestBody ListParamsRequest request) {
        return crjCzryService.getResidenceList(request);
    }

    /**
     * 常住人员详情查询
     *
     * @param id 人员id
     * @return 结果
     */
    @GetMapping("/residence/detail")
    public CrjResidenceDetailVO getResidenceDetail(@RequestParam("id") String id) {
        return crjCzryService.getResidenceDetail(id);
    }

    /**
     * 批量导入
     *
     * @param importVO 导入参数
     * @return 批量导入结果
     */
    @PostMapping("/residence/import")
    public ImportResultVO importPerson(ImportVO importVO) {
        return crjCzryService.importCzry(importVO);
    }

    /**
     * 批量删除
     *
     * @param ids id
     */
    @PostMapping("/residence/delete")
    public void batchDeleteCzry(@RequestBody List<String> ids) {
        crjCzryService.deleteCzry(ids);
    }

    /**
     * 导出excel
     *
     * @param response  响应体
     * @param request   导出请求
     * @throws IOException IO异常
     */
    @PostMapping("/residence/list/export")
    public void exportPerson(HttpServletResponse response,
                             @Validated @RequestBody ExportParams request) throws IOException {
        crjCzryService.downLoadExcel(response, request.getFieldNames(), request, request.getSubjectId());
    }

    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/residence/list/export/checklist")
    public JsonNode getJwryProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return crjCzryService.getExportPropertyList(subjectId);
    }
}
