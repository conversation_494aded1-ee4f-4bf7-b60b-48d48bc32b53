package com.trs.yq.police.subject.conf;

import com.trs.yq.police.subject.properties.MinioProperties;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * minio 客户端
 *
 * <AUTHOR>
 * @date 2020/7/7
 **/
@Slf4j
@Configuration
public class MinioStorageConfiguration {

    @Resource(name = "minioProperties")
    private MinioProperties minioProperties;

    /**
     * 在容器中创建MinioClient用于访问Minio
     *
     * @return {@link MinioClient}
     */
    @Bean
    public MinioClient minio() {

        return MinioClient.builder()
                .endpoint(minioProperties.getEndpoint())
                .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                .build();
    }
}
