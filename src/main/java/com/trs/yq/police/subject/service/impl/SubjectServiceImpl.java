package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.TimeRangeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.AreaVO;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DataViewCountService;
import com.trs.yq.police.subject.service.DepartmentService;
import com.trs.yq.police.subject.service.SubjectService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 主题的服务层
 *
 * <AUTHOR>
 * @date 2021/07/28
 */
@Service
@Slf4j
public class SubjectServiceImpl implements SubjectService {

    @Resource
    private SubjectRepository subjectRepository;

    @Resource
    private DictRepository dictRepository;

    @Resource
    private UnitRepository unitRepository;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private WarningTypeRepository warningTypeRepository;

    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;

    @Resource
    private DataViewCountService dataViewCountService;

    @Resource
    private RoleDataRepository roleDataRepository;


    /**
     * 查询专题人员列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题人员列表检索条件
     */
    @Override
    public List<ListFilter> getPersonListQueryFilters(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        JsonNode filters = JsonUtil.parseJsonNode(subjectEntity.getPersonListFilters());
        return getListFilters(subjectId, filters);
    }

    /**
     * 查询专题群体列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题人员列表检索条件
     */
    @Override
    public List<ListFilter> getGroupListQueryFilters(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        JsonNode filters = JsonUtil.parseJsonNode(subjectEntity.getGroupListFilters());
        return getListFilters(subjectId, filters);
    }

    /**
     * 查询专题线索列表的检索条件
     *
     * @param subjectId 专题id
     * @return 专题线索列表检索条件
     */
    @Override
    public List<ListFilter> getClueListQueryFilters(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        JsonNode filters = JsonUtil.parseJsonNode(subjectEntity.getClueListFilters());
        return getListFilters(subjectId, filters);
    }

    @Override
    public List<ListFilter> getWarningListQueryFilters(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        JsonNode filters = JsonUtil.parseJsonNode(subjectEntity.getWarningListFilters());
        return getListFilters(subjectId, filters);
    }

    @NotNull
    private List<ListFilter> getListFilters(String subjectId, JsonNode filters) {
        if (Objects.isNull(filters) || !filters.isArray()) {
            return Collections.emptyList();
        }

        List<ListFilter> listFilters = new ArrayList<>();

        for (JsonNode filter : filters) {
            ListFilter listFilter = new ListFilter();
            if (filter.has("type")) {
                listFilter.setType(filter.get("type").asText());
            }
            if (filter.has("displayName")) {
                listFilter.setDisplayName(filter.get("displayName").asText());
            }
            if (filter.has("key")) {
                listFilter.setKey(filter.get("key").asText());
            }
            if (filter.has("property")) {
                listFilter.setProperty(filter.get("property").asText());
            }
            if (filter.has("value")) {
                List<FilterValue> values = parseValues(filter.get("value"), subjectId);
                listFilter.setValue(values);
            }
            listFilters.add(listFilter);
        }
        return listFilters;
    }

    /**
     * 解析value字段
     *
     * @param item      节点
     * @param subjectId 专题id
     * @return 解析出来的value字段
     */
    private List<FilterValue> parseValues(JsonNode item, String subjectId) {
        List<FilterValue> values = new ArrayList<>();
        for (JsonNode value : item) {
            String valueText = value.asText();
            // 如果是$$ps_xxx$$表示码表中的type
            if (valueText.matches("^\\$\\$ps_.*?\\$\\$$")) {
                values.addAll(getDictValues(valueText));
            }
            // 如果是&&xxx&&表示从对应的表中查询所有选项
            else if (valueText.matches("^&&.*?&&$")) {
                values.addAll(getTableValues(valueText, subjectId));
            }
            // 默认按照json解析
            else {
                values.add(JsonUtil.parseObject(value.toString(), FilterValue.class));
            }
        }
        return values;
    }

    /**
     * 数据表中的选项
     *
     * @param expression 表达式
     * @param subjectId  专题id
     * @return 解析后的值
     */
    private List<FilterValue> getTableValues(String expression, String subjectId) {
        String type = expression.replace("&", "");
        switch (type) {
            case "department": {
                return departmentService.getAllUnitList();
            }
            case "policeStation": {
                return departmentService.getPoliceStationList();
            }
            case "departmentTree": {
                return departmentService.getUnitTree();
            }
            case "personType": {
                return getPersonTypeValues(subjectId);
            }
            case "personLabel": {
                return getLabelValues(subjectId);
            }
            case "groupType": {
                return getGroupTypeValues(subjectId);
            }
            case "clueType": {
                return getClueTypeValues(subjectId);
            }
            case "warningType": {
                return getWarningTypeValues(subjectId);
            }
            case "eventType": {
                return getEventTypeValues(subjectId);
            }
            case "time": {
                return getTimeValues();
            }
            case "areaCode": {
                return getAreaCodeValues();
            }
            default: {
                return Collections.emptyList();
            }
        }
    }

    private List<FilterValue> getTimeValues() {
        return Arrays.stream(TimeRangeEnum.values())
                .map(e -> new FilterValue(e.getCode(), e.getName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 码表中配置的选项
     *
     * @param expression 表达式
     * @return 解析的值
     */
    private List<FilterValue> getDictValues(String expression) {
        String type = expression.replaceAll("\\$", "");
        List<DictEntity> dictEntities = dictRepository.findAllByType(type);
        return dictEntities.stream()
                .map(de -> {
                    //预警状态-待签收 为默认状态
                    boolean isDefault = "ps_warning_status".equals(type) && de.getCode().equals("1");
                    //zb人员列表默认选中已列管
//                    if("ps_control_status".equals(type) && de.getCode().equals("1")) {
//                        isDefault = true;
//                    }
                    return new FilterValue(de.getCode(), de.getName(), isDefault, null);
                })
                .collect(Collectors.toList());
    }

    /**
     * 组合群体类别选项
     *
     * @param subjectId 专题id
     * @return 群体类别
     */
    private List<FilterValue> getGroupTypeValues(String subjectId) {
        //维稳群体和人员公用一套label
        String module = "group";
        if (subjectId.equals(WW_SUBJECT)) {
            module = "person";
        }
        List<LabelEntity> types = labelRepository.findAllBySubjectId(subjectId, module);
        return types.stream()
                .map(type -> new FilterValue(type.getId(), type.getName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 组合线索类别选项
     *
     * @param subjectId 专题id
     * @return 线索类别
     */
    private List<FilterValue> getClueTypeValues(String subjectId) {
        List<LabelEntity> types = labelRepository.findAllBySubjectId(subjectId, "clue");
        return types.stream()
                .map(type -> new FilterValue(type.getId(), type.getName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 组合线索类别选项
     *
     * @param subjectId 专题id
     * @return 线索类别
     */
    private List<FilterValue> getEventTypeValues(String subjectId) {
        List<LabelEntity> types = labelRepository.findAllBySubjectId(subjectId, "event");
        return types.stream()
                .map(type -> new FilterValue(type.getId(), type.getName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 组合personType选项
     *
     * @param subjectId 专题id
     * @return 选项
     */
    private List<FilterValue> getPersonTypeValues(String subjectId) {
        List<LabelEntity> types = labelRepository.findAllBySubjectId(subjectId, "person");
        return types.stream()
                .map(type -> new FilterValue(type.getId(), type.getName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 组合personLabel选项
     *
     * @param subjectId 专题id
     * @return 选项
     */
    private List<FilterValue> getLabelValues(String subjectId) {
        List<LabelEntity> labels = labelRepository.findAllBySubjectId(subjectId, "person");
        return labels.stream()
                .map(label -> new FilterValue(label.getId(), label.getName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 组合warningType选项
     *
     * @param subjectId 专题id
     * @return 选项
     */
    private List<FilterValue> getWarningTypeValues(String subjectId) {
        List<WarningTypeEntity> types = warningTypeRepository.findAllBySubjectId(subjectId);
        return types.stream()
                .map(type -> new FilterValue(type.getId(), type.getCnName(), false, null))
                .collect(Collectors.toList());
    }

    /**
     * 组合areaCode选项
     *
     * @return 选项
     */
    private List<FilterValue> getAreaCodeValues() {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        List<RoleDataEntity> roleDataEntityList = roleDataRepository.findViewRangesByUserId(currentUser.getId());
        String viewRange = roleDataEntityList.isEmpty()
                ? StringUtils.EMPTY
                : roleDataEntityList.get(0).getOperationRange();
        List<AreaVO> areas = dataViewCountService.getAreaInfo();
        List<FilterValue> list = new ArrayList<>();
        for (AreaVO area : areas) {
            if (!StringUtils.equals(area.getAreaCode(), "5105")) {
                boolean isDefault = StringUtils.isNotBlank(viewRange) && area.getAreaCode().equals(viewRange);
                list.add(new FilterValue(area.getAreaCode(), area.getAreaName(), isDefault, null));
            }
        }
        return list;
    }

    @Override
    public List<ListFilter> getEventListQueryFilters(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        JsonNode filters = JsonUtil.parseJsonNode(subjectEntity.getEventListFilters());
        return getListFilters(subjectId, filters);
    }
}
