package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.entity.PersonEntity;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;

/**
 * 维稳专题人员、群体、事件档案导出
 *
 * <AUTHOR>
 * @date 2022/1/11 19:39
 */
public interface ExportExcelService {
    /**
     * 导出excel
     *
     * @param response 响应体
     * @param name     excel名称
     * @param workbook {@link Workbook}
     */
    void export(HttpServletResponse response, String name, Workbook workbook);

    /**
     * 获取excel
     *
     * @param personEntity  人员
     * @param subjectId 专题id
     * @return {@link XSSFWorkbook}
     */
    XSSFWorkbook getPersonExcel(PersonEntity personEntity, String subjectId);

    /**
     * 获取excel
     *
     * @param groupId   群体id
     * @param subjectId 专题id
     * @return {@link XSSFWorkbook}
     */
    XSSFWorkbook getGroupExcel(String groupId, String subjectId);

    /**
     * 获取excel
     *
     * @param eventId   事件id
     * @param subjectId 专题id
     * @return {@link XSSFWorkbook}
     */
    XSSFWorkbook getEventExcel(String eventId, String subjectId);
}
