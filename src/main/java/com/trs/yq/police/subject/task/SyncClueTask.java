package com.trs.yq.police.subject.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.mapper.StqzClueMapper;
import com.trs.yq.police.subject.mapper.StqzCluePersonMapper;
import com.trs.yq.police.subject.repository.CluePersonRelationRepository;
import com.trs.yq.police.subject.repository.ClueRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 同步线索定时任务
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "com.trs.sync.clue.task.enable", havingValue = "true")
public class SyncClueTask {
    @Autowired
    private StqzClueMapper stqzClueMapper;
    @Autowired
    private StqzCluePersonMapper stqzCluePersonMapper;
    @Autowired
    private ClueRepository clueRepository;
    @Autowired
    private CluePersonRelationRepository cluePersonRelationRepository;
    @Autowired
    private PersonRepository personRepository;

    @Value("${com.trs.sync.clue.task.sync.day:3}")
    private Long syncDay;

    @Scheduled(cron = "${com.trs.sync.clue.task}")
    public void syncClue(String startTime, String endTime){
        log.info("开始同步线索");
        if(StringUtils.isEmpty(startTime)){
            startTime = LocalDateTime.now().minusDays(syncDay).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        if(StringUtils.isEmpty(endTime)){
            endTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        log.info("同步线索时间段：{} - {}", startTime, endTime);
        
        int pageNum = 1;
        int pageSize = 1000;
        List<StqzClueEntity> stqzClues;
        do {
            IPage page = new Page<>(pageNum, pageSize);
            stqzClues = stqzClueMapper.selectByTimeWithPage(startTime, endTime, page).getRecords();
            stqzClues.stream().map(entity -> entity.of()).forEach(clueEntity -> {
                log.info("同步线索：{}", clueEntity.getName());
                // 无则新增
                ClueEntity clue = clueRepository.findByCode(clueEntity.getCode());
                if (clue==null){
                    clueRepository.save(clueEntity);
                }
                syncCluePerson(clueEntity);
            });
            pageNum++;
        } while (stqzClues.size() == pageSize);
    }

    /**
     * 同步线索人员
     *
     * @param clue
     */
    public void syncCluePerson(ClueEntity clue){
        log.info("开始同步线索人员：{}", clue.getCode());
        List<StqzCluePersonEntity> stqzCluePersonEntities = stqzCluePersonMapper.selectByClueId(clue.getXsid());
        stqzCluePersonEntities.stream().map(entity -> entity.of()).forEach(personEntity -> {
            log.info("同步线索人员：{}", personEntity.getName());
            // 只处理在我们系统中已存在的人员
            PersonEntity existingPerson = personRepository.findByIdNumber(personEntity.getIdNumber());
            if(existingPerson != null){
                // 检查线索是否已存在，不存在则新增
                ClueEntity existingClue = clueRepository.findByCode(clue.getCode());
                if (existingClue == null) {
                    clueRepository.save(clue);
                }
                
                // 插入关联关系
                CluePersonRelationEntity relation = cluePersonRelationRepository.findByClueIdAndPersonId(existingPerson.getId(), clue.getId());
                if(relation==null){
                    cluePersonRelationRepository.save(new CluePersonRelationEntity(clue.getId(), existingPerson.getId()));
                }
            }
        });
    }
}
