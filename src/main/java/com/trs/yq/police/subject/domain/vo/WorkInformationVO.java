package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.WorkInformationEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;


/**
 * 工作信息vo
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class WorkInformationVO implements Serializable {

    private static final long serialVersionUID = 8002930697885834283L;

    /**
     * 工作开始时间
     */
    @NotNull(message = "工作开始时间缺失")
    private LocalDate workBeginTime;

    /**
     * 工作结束时间
     */
    private LocalDate workEndTime;

    /**
     * 工作单位
     */
    @NotBlank(message = "工作单位缺失")
    private String workUnit;

    /**
     * 工作地点
     */
    private String workPlace;

    /**
     * 职务
     */
    private String duty;

    /**
     * 工作信息id
     */
    private String id;

    /**
     * 转换构造方法
     *
     * @param entity 实体类
     */
    public WorkInformationVO(WorkInformationEntity entity) {
        this.workBeginTime = entity.getWorkBeginTime();
        this.workEndTime = entity.getWorkEndTime();
        this.workUnit = entity.getWorkUnit();
        this.duty = entity.getPost();
        this.id = entity.getId();
        this.workPlace = entity.getWorkSituation();
    }
}
