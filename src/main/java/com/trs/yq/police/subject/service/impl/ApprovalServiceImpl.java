package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.ApprovalEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.exception.PermissionForbiddenException;
import com.trs.yq.police.subject.repository.ApprovalRepository;
import com.trs.yq.police.subject.service.ApprovalService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 人员档案审批接口实现
 *
 * <AUTHOR>
 * @since 2021/11/29
 */
@Service
public class ApprovalServiceImpl implements ApprovalService {

    @Resource
    private ApprovalRepository approvalRepository;

    @Resource
    private PersonService personService;
    /**
     * 待审批
     */
    private static final String WAITING_APPROVAL = "0";
    /**
     * 已同意
     */
    private static final String AGREED = "1";
    /**
     * 已驳回
     */
    private static final String REJECTED = "2";

    @Override
    public PageResult<ApprovalListVO> getApprovalList(ApprovalListRequest request) {
        PageParams pageParams = request.getPageParams();
        LoginUser loginUser = AuthHelper.getCurrentUser();
        if (Objects.isNull(loginUser)) {
            throw new PermissionForbiddenException("用户未登录，请核实！");
        }

        Page<ApprovalEntity> result = approvalRepository.findAllByApproverIdAndStatus(loginUser.getId(), WAITING_APPROVAL, pageParams.toPageable());
        List<ApprovalListVO> voList = result.getContent().stream().map(ApprovalListVO::new).collect(Collectors.toList());
        return PageResult.of(voList, pageParams.getPageNumber(), result.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void submitDeletePersonApproval(String personId, String subjectId, ApprovalVO deleteVO) {
        ApprovalEntity approval = approvalRepository
                .findByPersonIdAndSubjectIdAndTypeAndStatus(personId, subjectId, Operator.DELETE.getCode(), WAITING_APPROVAL);
        if (Objects.nonNull(approval)) {
            throw new ParamValidationException("该人员删除操作已处于待审批状态，无法再次申请删除！");
        }
        ApprovalEntity newApproval = new ApprovalEntity();
        newApproval.setApproverId(deleteVO.getApproverId());
        newApproval.setDetail(deleteVO.getReason());
        newApproval.setType(Operator.DELETE.getCode());
        newApproval.setPersonId(personId);
        newApproval.setSubjectId(subjectId);
        newApproval.setStatus(WAITING_APPROVAL);
        approvalRepository.save(newApproval);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void submitCreatePersonApproval(String idNumber, String subjectId, PersonBasicVO personBasicVO) {
        ApprovalEntity approval = approvalRepository
                .findByPersonIdAndSubjectIdAndTypeAndStatus(idNumber, subjectId, Operator.ADD.getCode(), WAITING_APPROVAL);
        if (Objects.nonNull(approval)) {
            throw new ParamValidationException("该人员新建操作已处于待审批状态，无法再次申请新建！");
        }
        ApprovalEntity newApproval = new ApprovalEntity();
        newApproval.setApproverId("");//TODO
        newApproval.setDetail("");
        newApproval.setType(Operator.ADD.getCode());
        newApproval.setPersonId(idNumber);
        newApproval.setSubjectId(subjectId);
        newApproval.setContent(JsonUtil.toJsonString(personBasicVO));
        newApproval.setStatus(WAITING_APPROVAL);
        approvalRepository.save(newApproval);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void submitArchivePersonApproval(String personId, String subjectId, PersonArchiveVO archiveVO) {
        ApprovalEntity approval = approvalRepository
                .findByPersonIdAndSubjectIdAndTypeAndStatus(personId, subjectId, Operator.ARCHIVE.getCode(), WAITING_APPROVAL);
        if (Objects.nonNull(approval)) {
            throw new ParamValidationException("该人员归档操作已处于待审批状态，无法再次申请归档！");
        }
        ApprovalEntity newApproval = new ApprovalEntity();
        //newApproval.setApproverId(archiveVO.getApproverId());
        //newApproval.setDetail(archiveVO.getReason());
        newApproval.setType(Operator.ARCHIVE.getCode());
        newApproval.setPersonId(personId);
        newApproval.setSubjectId(subjectId);
        newApproval.setContent(JsonUtil.toJsonString(archiveVO));
        newApproval.setStatus(WAITING_APPROVAL);
        approvalRepository.save(newApproval);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void submitActivatePersonApproval(String personId, String subjectId, ApprovalVO deleteVO) {
        ApprovalEntity approval = approvalRepository
                .findByPersonIdAndSubjectIdAndTypeAndStatus(personId, subjectId, Operator.ACTIVATE.getCode(), WAITING_APPROVAL);
        if (Objects.nonNull(approval)) {
            throw new ParamValidationException("该人员激活操作已处于待审批状态，无法再次申请激活！");
        }
        ApprovalEntity newApproval = new ApprovalEntity();
        newApproval.setApproverId(deleteVO.getApproverId());
        newApproval.setDetail(deleteVO.getReason());
        newApproval.setType(Operator.ACTIVATE.getCode());
        newApproval.setPersonId(personId);
        newApproval.setSubjectId(subjectId);
        newApproval.setStatus(WAITING_APPROVAL);
        approvalRepository.save(newApproval);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void agreeApproval(String id) {
        ApprovalEntity entity = approvalRepository.findById(id).orElse(null);
        if (Objects.isNull(entity)) {
            throw new ParamValidationException("审批信息不存在，请核实！");
        }
        entity.setStatus(AGREED);
        approvalRepository.save(entity);
        Operator type = Operator.codeOf(entity.getType());
        if (Objects.isNull(type)) {
            throw new ParamValidationException("审批类型不存在，请核实！");
        }
        final String personId = entity.getPersonId();
        final String subjectId = entity.getSubjectId();
        switch (type) {
            case ADD:
                PersonBasicVO personBasicVO = JsonUtil.parseObject(entity.getContent(), PersonBasicVO.class);
                personService.createPerson(personBasicVO);
                break;
            case DELETE:
                personService.deletePerson(personId, subjectId);
                break;
            case ARCHIVE:
                PersonArchiveVO personArchiveVO = JsonUtil.parseObject(entity.getContent(), PersonArchiveVO.class);
                personService.archivePerson(personId, subjectId, personArchiveVO);
                break;
            case ACTIVATE:
                personService.activePerson(personId, subjectId);
                break;
            default:
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void rejectApproval(String id) {
        ApprovalEntity entity = approvalRepository.findById(id).orElse(null);
        if (Objects.nonNull(entity)) {
            entity.setStatus(REJECTED);
            approvalRepository.save(entity);
        }
    }
}
