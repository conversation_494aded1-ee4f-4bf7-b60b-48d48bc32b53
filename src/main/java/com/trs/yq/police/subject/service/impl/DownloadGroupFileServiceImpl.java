package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.vo.GroupRelatedClueVO;
import com.trs.yq.police.subject.domain.vo.GroupRelatedPersonVO;
import com.trs.yq.police.subject.domain.vo.GroupVO;
import com.trs.yq.police.subject.domain.vo.IdNameVO;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.service.DownloadGroupFileService;
import com.trs.yq.police.subject.service.ExportExcelService;
import com.trs.yq.police.subject.service.GroupService;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_ACTIVITY_LEVEL;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;
import static com.trs.yq.police.subject.utils.WordTableUtils.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/8 11:54
 **/
@Service
public class DownloadGroupFileServiceImpl implements DownloadGroupFileService {


    @Resource
    private DictRepository dictRepository;
    @Resource
    private GroupService groupService;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private ExportExcelService excelService;

    @Override
    public void getFile(HttpServletResponse response, String groupId, String subjectId) {
        //检查该groupId和subjectId对应的群体是否存在
        //获取word文档字节输出流
        ByteArrayOutputStream out = createWord(new ByteArrayOutputStream(), groupId);
        //把word文档的字节输出流封装到response中
        if (WW_SUBJECT.equals(subjectId)) {
            excelService.export(response, "群体档案-"+groupService.getGroup(groupId).getGroupName(), excelService.getGroupExcel(groupId, subjectId));
        } else {
            export(response, out);
        }

        // 日志记录
        OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DOWNLOAD)
                .module(OperateModule.GROUP_ARCHIVE_MANAGE)
                .desc("导出群体档案")
                .primaryKey(groupId)
                .currentUser(AuthHelper.getCurrentUser())
                .targetObjectType(TargetObjectTypeEnum.GROUP.getCode())
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }

    private void export(HttpServletResponse response, ByteArrayOutputStream out) {
        String fileName = "群体档案.docx";
        try {
            response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            outputStream.write(out.toByteArray());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private ByteArrayOutputStream createWord(ByteArrayOutputStream out, String groupId) {
        XWPFDocument document = new XWPFDocument();
        createTitle(document, "群体档案");
        List<String> list = getWord();
        for (String s : list) {
            switch (s) {
                case "Basic":
                    createBasicTable(groupId, document);
                    break;
                case "Group":
                    createPersonTable(groupId, document);
                    break;
                case "Clue":
                    createClueTable(groupId, document);
                    break;
                default:
                    break;
            }
        }
        try {
            //导出文档
            document.write(out);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return out;
    }

    private void createClueTable(String groupId, XWPFDocument document) {
        List<GroupRelatedClueVO> list = getClueList(groupId);
        if (list.size() == 0) {
            return;
        }
        XWPFTable xwpfTable = document.createTable(list.size() + 1, 6);
        divisionColumnWidth(xwpfTable);
        XWPFTableRow xwpfTableRowOne = xwpfTable.getRow(0);
        xwpfTableRowOne.getCell(0).setText("相关线索");
        xwpfTableRowOne.getCell(1).setText("序号");
        xwpfTableRowOne.getCell(2).setText("线索名称");
        xwpfTableRowOne.getCell(3).setText("线索来源");
        xwpfTableRowOne.getCell(4).setText("线索级别");
        xwpfTableRowOne.getCell(5).setText("添加时间");
        for (int i = 0; i < list.size(); i++) {
            GroupRelatedClueVO groupAssociatedClueVO = list.get(i);
            XWPFTableRow xwpfTableRow = xwpfTable.getRow(i + 1);
            xwpfTableRow.getCell(1).setText((i + 1) + "");
            xwpfTableRow.getCell(2).setText(groupAssociatedClueVO.getClueName());
            xwpfTableRow.getCell(3).setText(groupAssociatedClueVO.getClueSource());
            xwpfTableRow.getCell(4).setText(groupAssociatedClueVO.getClueLevel());
            xwpfTableRow.getCell(5).setText(groupAssociatedClueVO.getCreateTime().toString().replace("T", " ").substring(0, 16));
        }
        divisionColumnWidth(xwpfTable);
        //设置表格内容居中
        setCenter(xwpfTable);
        //设置表格换行
        lineFeed(document);
        mergeCellsVertically(xwpfTable, 0, 0, list.size());
    }

    private List<GroupRelatedClueVO> getClueList(String groupId) {
        return groupService.getGroupRelatedClue(groupId);
    }

    private void createPersonTable(String groupId, XWPFDocument document) {
        List<GroupRelatedPersonVO> list = getPersonList(groupId);
        if (list.size() == 0) {
            return;
        }
        XWPFTable xwpfTable = document.createTable(list.size() + 1, 7);

        XWPFTableRow xwpfTableRowOne = xwpfTable.getRow(0);
        xwpfTableRowOne.getCell(0).setText("群体成员");
        xwpfTableRowOne.getCell(1).setText("序号");
        xwpfTableRowOne.getCell(2).setText("姓名");
        xwpfTableRowOne.getCell(3).setText("身份证号");
        xwpfTableRowOne.getCell(4).setText("活跃程度");
        xwpfTableRowOne.getCell(5).setText("重点人员类别");
        xwpfTableRowOne.getCell(6).setText("添加时间");

        for (int i = 0; i < list.size(); i++) {
            GroupRelatedPersonVO groupAssociatedPersonVO = list.get(i);
            String activityLevel = dictRepository.findByTypeAndCode(DICT_TYPE_ACTIVITY_LEVEL, groupAssociatedPersonVO.getActivityLevel()).getName();
            XWPFTableRow xwpfTableRow = xwpfTable.getRow(i + 1);
            xwpfTableRow.getCell(1).setText((i + 1) + "");
            xwpfTableRow.getCell(2).setText(groupAssociatedPersonVO.getPersonName());
            xwpfTableRow.getCell(3).setText(groupAssociatedPersonVO.getIdNumber());
            xwpfTableRow.getCell(4).setText(activityLevel);
            xwpfTableRow.getCell(5).setText(groupAssociatedPersonVO.getPersonType());
            xwpfTableRow.getCell(6).setText(groupAssociatedPersonVO.getCreateTime().toString().replace("T", " ").substring(0, 16));
        }
        divisionColumnWidth(xwpfTable);
        //设置表格内容居中
        setCenter(xwpfTable);
        //设置表格换行
        lineFeed(document);
        mergeCellsVertically(xwpfTable, 0, 0, list.size());
    }


    private List<GroupRelatedPersonVO> getPersonList(String groupId) {
        return groupService.getGroupRelatedPerson(groupId);
    }

    private void createBasicTable(String groupId, XWPFDocument document) {
        final GroupVO groupVO = groupService.getGroup(groupId);
        StringBuilder stringBuffer = new StringBuilder();
        boolean b = true;
        for (IdNameVO idNameVO : groupVO.getGroupTypes()) {
            if (b) {
                stringBuffer.append(idNameVO.getName());
                b = false;
                continue;
            }
            stringBuffer.append(",");
            stringBuffer.append(idNameVO.getName());
        }
        XWPFTable xwpfTable = document.createTable(3, 5);
        divisionColumnWidth(xwpfTable);
        XWPFTableRow xwpfTableRowOne = xwpfTable.getRow(0);
        xwpfTableRowOne.getCell(0).setText("基本信息");
        xwpfTableRowOne.getCell(1).setText("群体名称");
        xwpfTableRowOne.getCell(2).setText(groupVO.getGroupName());
        xwpfTableRowOne.getCell(3).setText("群体类别");
        xwpfTableRowOne.getCell(4).setText(stringBuffer.toString());

        XWPFTableRow xwpfTableRowTwo = xwpfTable.getRow(1);
        xwpfTableRowTwo.getCell(1).setText("录入时间");
        xwpfTableRowTwo.getCell(2).setText(groupVO.getCreateTime().toString().replace("T", " ").substring(0, 16));
        xwpfTableRowTwo.getCell(3).setText("录入单位");
        xwpfTableRowTwo.getCell(4).setText(groupVO.getCreateDept());

        XWPFTableRow xwpfTableRowThree = xwpfTable.getRow(2);
        xwpfTableRowThree.getCell(1).setText("基本情况");
        xwpfTableRowThree.getCell(2).setText(groupVO.getBasicInfo());

        //设置表格内容居中
        setCenter(xwpfTable);
        //设置表格换行
        lineFeed(document);
        mergeCellsVertically(xwpfTable, 0, 0, 2);
//        mergeCellsHorizontal(xwpfTable,2,1,4);

    }

    private List<String> getWord() {
        List<String> list = new ArrayList<>();
        list.add("Basic");
        list.add("Group");
        list.add("Clue");
        return list;
    }

}
