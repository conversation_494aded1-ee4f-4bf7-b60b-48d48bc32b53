package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.AssignEntity;
import org.springframework.stereotype.Repository;

/**
 * 分局分配表数据层接口
 *
 * <AUTHOR>
 * @since 2021/8/6
 */
@Repository
public interface AssignRepository extends BaseRepository<AssignEntity, String> {
    /**
     * 按人员id查询指派情况
     *
     * @param personId 人员id
     * @param subjectId 专题id
     * @return 指派信息 {@link AssignEntity}
     */
    AssignEntity getByPersonIdAndSubjectId(String personId, String subjectId);
}
