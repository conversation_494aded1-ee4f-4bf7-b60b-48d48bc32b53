package com.trs.yq.police.subject.operation.handler;

import com.lmax.disruptor.ExceptionHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志异常处理器
 *
 * <AUTHOR>
 * @date 2021/8/18 11:10
 */
@Slf4j
public class DefaultExceptionHandler implements ExceptionHandler<Object> {

    @Override
    public void handleEventException(Throwable ex, long sequence, Object event) {
        log.error("record operation log error! event : {}", event, ex);
    }

    @Override
    public void handleOnStartException(Throwable ex) {
        log.error("start disruptor error!", ex);
    }

    @Override
    public void handleOnShutdownException(Throwable ex) {
        log.error("shutdown disruptor error!", ex);
    }
}
