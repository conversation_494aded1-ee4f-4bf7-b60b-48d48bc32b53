package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.AdjudicationEntity;
import com.trs.yq.police.subject.domain.vo.AdjudicationVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.AdjudicationRepository;
import com.trs.yq.police.subject.service.AdjudicationService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 裁决信息业务层接口实现
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@AllArgsConstructor
public class AdjudicationServiceImpl implements AdjudicationService {

    @Resource
    private final AdjudicationRepository adjudicationRepository;

    @Resource
    private final PersonService personService;

    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public List<AdjudicationVO> getJudgement(String personId) {
        return adjudicationRepository.findAllByPersonIdOrderByJudgementDateDesc(personId).stream().map(
                adjudicationEntity -> {
                    AdjudicationVO vo = new AdjudicationVO();
                    vo.setId(adjudicationEntity.getId());
                    vo.setJudgementDate(adjudicationEntity.getJudgementDate());
                    vo.setEndDate(adjudicationEntity.getEndDate());
                    vo.setLimitTime(adjudicationEntity.getLimitTime());
                    vo.setLimitUnit(adjudicationEntity.getLimitUnit());
                    vo.setReason(adjudicationEntity.getReason());
                    return vo;
                }
        ).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveJudgement(String personId, AdjudicationVO adjudicationVO) {
        personService.checkPersonExist(personId);

        LocalDate adjudgeDate = adjudicationVO.getJudgementDate();
        LocalDate closingDate = adjudicationVO.getEndDate();
        LocalDate expectClosingDate = adjudgeDate
                .plus(adjudicationVO.getLimitTime(), ChronoUnit.valueOf(adjudicationVO.getLimitUnit()))
                .plusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        if (!expectClosingDate.isEqual(closingDate)) {
            throw new ParamValidationException("截止日期与限制年限不匹配，请核实！");
        }

        AdjudicationEntity adjudicationEntity = new AdjudicationEntity(
                personId,
                adjudgeDate,
                closingDate,
                adjudicationVO.getLimitTime(),
                adjudicationVO.getLimitUnit(),
                adjudicationVO.getReason()
        );

        adjudicationRepository.save(adjudicationEntity);
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.ADJUDICATION)
                .newObj(JsonUtil.toJsonString(adjudicationEntity))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("新增裁决信息")
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateJudgement(String personId, AdjudicationVO adjudicationVO) {
        personService.checkPersonExist(personId);

        LocalDate closingDate = adjudicationVO.getEndDate();
        LocalDate adjudgeDate = adjudicationVO.getJudgementDate();
        LocalDate expectClosingDate = adjudgeDate
                .plus(adjudicationVO.getLimitTime(), ChronoUnit.valueOf(adjudicationVO.getLimitUnit()))
                .plusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
        if (!expectClosingDate.isEqual(closingDate)) {
            throw new ParamValidationException("截止日期与限制年限不匹配，请核实！");
        }

        AdjudicationEntity original = adjudicationRepository.findById(adjudicationVO.getId()).orElse(null);

        if (Objects.isNull(original)) {
            throw new ParamValidationException("裁决信息不存在，请刷新核实");
        }
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.ADJUDICATION)
                .oldObj(JsonUtil.toJsonString(original))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("编辑裁决信息")
                .build();

        AdjudicationEntity adjudicationEntity = new AdjudicationEntity(
                personId,
                adjudgeDate,
                closingDate,
                adjudicationVO.getLimitTime(),
                adjudicationVO.getLimitUnit(),
                adjudicationVO.getReason()
        );
        BeanUtil.copyPropertiesIgnoreNull(adjudicationEntity, original);
        adjudicationRepository.save(original);
        logRecord.setNewObj(JsonUtil.toJsonString(original));
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteJudgement(String personId, String id) {

        final AdjudicationEntity adjudication = adjudicationRepository.findById(id).orElse(null);

        if (Objects.isNull(adjudication)) {
            throw new ParamValidationException("裁决信息不存在，请刷新核实");
        }

        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DELETE)
                .module(OperateModule.ADJUDICATION)
                .oldObj(JsonUtil.toJsonString(adjudication))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("删除裁决信息")
                .build();
        adjudicationRepository.deleteById(id);
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }


}
