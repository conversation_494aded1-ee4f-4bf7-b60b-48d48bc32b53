package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/10/18 11:55
 */
@Data
public class CrjOcrVO implements Serializable {
    private static final long serialVersionUID = 1644386381760244874L;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 名
     */
    private String firstName;

    /**
     * 假数据
     *
     * @return {@link CrjOcrVO}
     */
    public static CrjOcrVO getVirtualDate() {
        CrjOcrVO crjOcrVO = new CrjOcrVO();
        crjOcrVO.setNationality("美利坚合众国");
        crjOcrVO.setCertificateNumber("************");
        crjOcrVO.setFirstName("Trump");
        crjOcrVO.setLastName("<PERSON>");
        return crjOcrVO;
    }
}
