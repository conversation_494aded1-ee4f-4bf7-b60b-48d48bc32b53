package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 目标对象类型枚举
 *
 * <AUTHOR>
 * @date 2021/9/2 9:37
 */
public enum TargetObjectTypeEnum {

    /**
     * enums
     */
    PERSON(0, "person", "人员"),
    GROUP(1, "group", "群体"),
    CLUE(2, "clue", "线索"),
    WARNING(3, "warning","预警"),
    EVENT(4, "event", "事件");

    /**
     * fields
     */
    @Getter
    private final Integer code;

    @Getter
    private final String type;

    @Getter
    private final String name;

    TargetObjectTypeEnum(Integer code, String type, String name) {
        this.code = code;
        this.type = type;
        this.name = name;
    }

    /**
     * 根据编码匹配目标对象类型枚举
     *
     * @param code 编码
     * @return 目标对象类型枚举
     */
    public static TargetObjectTypeEnum codeOf(Integer code) {

        if (Objects.nonNull(code)) {
            for (TargetObjectTypeEnum typeEnum : TargetObjectTypeEnum.values()) {
                if (typeEnum.code.equals(code)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据类型匹配目标对象类型枚举
     *
     * @param type 类型
     * @return 目标对象类型枚举
     */
    public static TargetObjectTypeEnum codeOf(String type) {

        if (StringUtils.isNotBlank(type)) {
            for (TargetObjectTypeEnum typeEnum : TargetObjectTypeEnum.values()) {
                if (StringUtils.equals(type, typeEnum.type)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 由code转换为type
     *
     * @param code {@link Integer}
     * @return type {@link String}
     */
    public static String getTypeByCode(Integer code) {
        if(Objects.nonNull(code)) {
            for (TargetObjectTypeEnum typeEnum : TargetObjectTypeEnum.values()) {
                if (code.equals(typeEnum.code)) {
                    return typeEnum.getType();
                }
            }
        }
        return null;
    }

}
