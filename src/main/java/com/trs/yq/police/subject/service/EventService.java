package com.trs.yq.police.subject.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 事件档案业务层接口
 *
 * <AUTHOR>
 * @date 2021/12/13 16:45
 */
public interface EventService {
    /**
     * 获取事件基本信息
     *
     * @param eventId 事件id
     * @return {@link EventVO}
     */
    EventVO getEventBasicInfo(String eventId);

    /**
     * 分页获取事件关联人员
     *
     * @param eventId    事件id
     * @param pageParams 分页参数
     * @return {@link EventRelatedPersonVO}
     */
    PageResult<EventRelatedPersonVO> getRelatedPersonList(String eventId, PageParams pageParams);

    /**
     * 分页获取事件关联群体
     *
     * @param eventId    事件id
     * @param pageParams 分页参数
     * @return {@link EventRelateGroupVO}
     */
    PageResult<EventRelateGroupVO> getRelatedGroupList(String eventId, PageParams pageParams);

    /**
     * 获取事件关联的材料信息
     *
     * @param eventId 事件id
     * @return {@link FileInfoVO}
     */
    List<FileInfoVO> getEventFileInfo(String eventId);

    /**
     * 获取事件关联的合成作战信息
     *
     * @param eventId 事件id
     * @return {@link BattleRecordCommandListVO}
     */
    List<BattleRecordCommandListVO> getBattleRecordList(String eventId);

    /**
     * 获取事件关联的指令信息
     *
     * @param eventId 事件id
     * @return {@link BattleRecordCommandListVO}
     */
    List<BattleRecordCommandListVO> getBattleCommandList(String eventId);

    /**
     * 新增事件
     *
     * @param eventVO 事件信息
     * @return 新增后的事件id
     */
    String addEvent(EventVO eventVO);

    /**
     * 修改事件信息
     *
     * @param eventVO 事件信息
     */
    void updateEvent(EventVO eventVO);

    /**
     * 批量删除线索
     *
     * @param eventIds 线索id
     */
    void deleteEvents(List<String> eventIds);

    /**
     * 获取事件已关联的人员列表
     *
     * @param eventId 事件id
     * @return {@link EventRelatedPersonVO}
     */
    List<EventRelatedPersonVO> getPersonsFromEvent(String eventId);

    /**
     * 获取事件已关联的群体列表
     *
     * @param eventId 事件id
     * @return {@link EventRelateGroupVO}
     */
    List<EventRelateGroupVO> getGroupsFromEvent(String eventId);

    /**
     * 事件批量关联群体
     *
     * @param vo 关联的群体id
     */
    void updateGroupEventRelation(EventGroupRelationRequestVO vo);

    /**
     * 事件批量关联人员
     *
     * @param vo 关联的人员id
     */
    void updateEventPersonRelations(EventPersonRelationRequestVO vo);

    /**
     * 移除事件-人员关联
     *
     * @param module 模块
     * @param relationId 关联id
     */
    void removePersonFromEvent(ModuleEnum module, String relationId);

    /**
     * 移除事件-群体关联
     *
     * @param relationId 关联id
     */
    void removeGroupFromEvent(String relationId);

    /**
     * 添加事件-材料关联
     *
     * @param eventId          事件id
     * @param attachmentVOList 材料信息
     */
    void addEventFileRelation(String eventId, List<AttachmentVO> attachmentVOList);

    /**
     * 移除事件-材料关联
     *
     * @param eventId 事件id
     * @param fileId  文件id
     */
    void deleteClueFileRelation(String eventId, String fileId);

    /**
     * 分页查询事件列表
     *
     * @param subjectId 专题id
     * @param request   参数
     * @return 分页查询结果
     */
    PageResult<EventListVO> getEventList(String subjectId, ListRequestVO request);

    /**
     * 批量导出事件信息
     *
     * @param response  响应体
     * @param request   {@link EventListExportRequest}
     * @param subjectId 专题id
     * @throws IOException io异常
     */
    void downLoadExcel(HttpServletResponse response, ExportParams request, String subjectId) throws IOException;

    /**
     * 获取事件能导出的字段信息
     *
     * @param subjectId 专题id
     * @return 字段信息
     */
    JsonNode getExportPropertyList(String subjectId);

    /**
     * 获取事件按钮
     *
     * @param eventId 事件id
     * @return {@link ButtonVO}
     */
    ButtonVO getButton(String eventId);

    /**
     * 事件上报
     *
     * @param eventId     事件id
     * @param currentUser 当前用户
     * @return {@link ReportInfoVO}
     */
    ReportInfoVO reportToSuperior(String eventId, LoginUser currentUser);

    /**
     * 事件处置
     *
     * @param eventId 事件id
     * @param status  状态
     */
    void changeDisposeStatus(String eventId, String status);

    /**
     * 获取关联对话框中事件分页查询的结果
     *
     * @param request 请求
     * @return {@link DialogEventListVO}
     */
    PageResult<DialogEventListVO> getDialogEventList(DialogEventListRequestVO request);

    /**
     * 修改已关联人员的涉事行为
     *
     * @param personEventBehaviourVO {@link PersonEventBehaviourVO}
     * @param eventId 事件id
     */
    void updateBehaviour(String eventId, PersonEventBehaviourVO personEventBehaviourVO);

    /**
     * [事件详情]修改事件关联人员涉事照片
     *
     * @param eventId        事件id
     * @param personImagesVO 人员涉事图片vo
     */
    void updatePersonImages(String eventId, EventPersonImagesVO personImagesVO);

    /**
     * [事件详情]修改事件关联人员来源
     *
     * @param eventId        事件id
     * @param personSourceVO 人员来源vo
     */
    void updatePersonSource(String eventId, EventPersonSourceVO personSourceVO);

    /**
     * 活动相关人员列表
     *
     * @param personId 人员id
     * @param pageParams 分页参数
     * @return 活动相关人员
     */
    PageResult<ActivityRelatedPersonVO> getEventRelatedPerson(String personId, PageParams pageParams);
}
