package com.trs.yq.police.subject.jpa.config;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 18:59
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef="oracleEntityManagerFactory",
    transactionManagerRef="oracleTransactionManager",
    basePackages= {"com.trs.yq.police.subject.repository"})
public class OracleRepositoryConfig {

    @Autowired
    @Qualifier("oracleDatasource")
    private DataSource systemDataSource;

    @Autowired
    private JpaProperties jpaProperties;

    @Autowired
    private HibernateProperties hibernateProperties;

    @Value("${spring.jpa.properties.hibernate.oracle-dialect}")
    private String dialect;

    /**
     *
     * @param builder builder
     * @return {@link LocalContainerEntityManagerFactoryBean}
     */
    @Primary
    @Bean(name = "oracleEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactorySystem(
        EntityManagerFactoryBuilder builder) {
        HashMap<String, String> map = new HashMap<>();
        map.put("hibernate.dialect",dialect);
        jpaProperties.setProperties(map);
        Map<String, Object> properties = hibernateProperties.determineHibernateProperties(
            jpaProperties.getProperties(), new HibernateSettings());
        return builder.dataSource(systemDataSource).properties(properties)
            .packages("com.trs.yq.police.subject.domain.entity").build();
    }

    /**
     *
     * @param builder builder
     * @return {@link PlatformTransactionManager}
     */
    @Primary
    @Bean(name = "oracleTransactionManager")
    public PlatformTransactionManager transactionManagerSystem(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactorySystem(builder).getObject()));
    }

    /**
     * 创建 EntityManager
     *
     * @param builder builder
     * @return {@link EntityManager}
     */
    @Primary
    @Bean(name = "oracleEntityManagerOracle")
    public EntityManager entityManager(EntityManagerFactoryBuilder builder) {
        return Objects.requireNonNull(entityManagerFactorySystem(builder).getObject()).createEntityManager();
    }


}
