package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.ClueExtendEntity;
import com.trs.yq.police.subject.domain.entity.GroupClueRelationEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.ClueExtendRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.GroupClueRelationRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DictTypeConstants.*;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 与群体关联的线索列表vo
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class GroupRelatedClueVO implements Serializable {

    private static final long serialVersionUID = -3202991127398302165L;

    /**
     * 线索id
     */
    private String clueId;
    /**
     * 线索名称
     */
    private String clueName;
    /**
     * 线索来源
     */
    private String clueSource;
    /**
     * 线索等级
     */
    private String clueLevel;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;
    /**
     * 关系id
     */
    private String relationId;

    /**
     * 维权方式
     */
    private String rightProtectionMethod;

    /**
     * 维权行为
     */
    private String rightProtectionBehavior;

    /**
     * 维权时间
     */
    private LocalDateTime rightProtectionTime;


    /**
     * 构建vo
     *
     * @param clueEntity {@link ClueEntity}
     * @param groupId    群体id
     * @return {@link GroupRelatedClueVO}
     */
    public static GroupRelatedClueVO of(ClueEntity clueEntity, String groupId) {
        GroupRelatedClueVO vo = new GroupRelatedClueVO();
        vo.setClueId(clueEntity.getId());
        vo.setClueName(clueEntity.getName());
        GroupClueRelationRepository groupClueRelationRepository = BeanUtil.getBean(GroupClueRelationRepository.class);
        GroupClueRelationEntity relation = groupClueRelationRepository.findByClueIdAndGroupId(clueEntity.getId(), groupId);
        vo.setRelationId(relation.getId());
        vo.setCreateTime(relation.getCrTime());
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);

        /*
         * 扩展维稳
         */
        if (WW_SUBJECT.equals(clueEntity.getSubjectId())) {
            LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
            String clueType = labelRepository.findByClueId(clueEntity.getId()).stream()
                    .map(LabelEntity::getName)
                    .collect(Collectors.joining("、"));
            vo.setClueLevel(clueType);
            vo.setClueSource(dictRepository.findByTypeAndCode(DICT_TYPE_WW_CLUE_SOURCE, clueEntity.getSource()).getName());
            ClueExtendRepository clueExtendRepository = BeanUtil.getBean(ClueExtendRepository.class);
            Optional<ClueExtendEntity> extend = clueExtendRepository.findByClueId(clueEntity.getId());
            extend.ifPresent(e -> {
                vo.setRightProtectionMethod(StringUtils.isNotBlank(e.getMethod()) ? dictRepository.findByTypeAndCode("ps_clue_action_method", e.getMethod()).getName() : "--");
                vo.setRightProtectionBehavior(StringUtils.isNotBlank(e.getBehaviour()) ? dictRepository.findByTypeAndCode("ps_clue_action_behaviour", e.getBehaviour()).getName() : "--");
                vo.setRightProtectionTime(e.getOccurrenceTime());
            });
        } else {
            vo.setClueLevel(dictRepository.findByTypeAndCode(DICT_TYPE_CLUE_EMERGENCY_LEVEL, clueEntity.getEmergencyLevel()).getName());
            vo.setClueSource(dictRepository.findByTypeAndCode(DICT_TYPE_CLUE_SOURCE, clueEntity.getSource()).getName());
        }
        return vo;
    }
}
