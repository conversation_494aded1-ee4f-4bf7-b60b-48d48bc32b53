package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.constants.enums.WeekEnums;
import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/12/22 15:38
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimePeriodVO {
    private String name;

    private LocalDateTime beginTime;

    private LocalDateTime enTime;

    /**
     * 根据timeparams中的range生成时间段
     *
     * @param timeParams 时间参数
     * @return 时间段
     */
    public static List<TimePeriodVO> listOf(TimeParams timeParams) {
        switch (timeParams.getRange()) {
            case "2":
                return ofWeek();
            case "3":
                return ofMonth();
            case "4":
                return ofYear();
            case "1":
            default:
                return ofDay();
        }
    }

    @NotNull
    private static ArrayList<TimePeriodVO> ofYear() {
        ArrayList<TimePeriodVO> result = new ArrayList<>();
        LocalDateTime month = LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear()).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        for (int i = 0; i < 12; i++) {
            String name = "第" + (i + 1) + "个月";
            result.add(new TimePeriodVO(name, month.plusMonths(i), month.plusMonths(i + 1)));
        }
        return result;
    }

    @NotNull
    private static ArrayList<TimePeriodVO> ofMonth() {
        ArrayList<TimePeriodVO> result = new ArrayList<>();
        LocalDateTime month = LocalDateTime.now().with(TemporalAdjusters.firstDayOfMonth()).withHour(0).withMinute(0)
                .withSecond(0).withNano(0);
        for (int i = 0; i < month.getMonth().maxLength(); i++) {
            String name = "第" + (i + 1) + "天";
            result.add(new TimePeriodVO(name, month.plusDays(i), month.plusDays(i + 1)));
        }
        return result;
    }

    @NotNull
    private static ArrayList<TimePeriodVO> ofWeek() {
        ArrayList<TimePeriodVO> result = new ArrayList<>();
        LocalDateTime monday = LocalDateTime.now().with(DayOfWeek.MONDAY).withHour(0).withMinute(0).withSecond(0)
                .withNano(0);
        for (int i = 0; i < 7; i++) {
            WeekEnums weekEnums = WeekEnums.codeOf(String.valueOf(i + 1));
            String name = Objects.nonNull(weekEnums) ? weekEnums.getName() : " ";
            result.add(new TimePeriodVO(name, monday.plusDays(i), monday.plusDays(i + 1)));
        }
        return result;
    }

    @NotNull
    private static ArrayList<TimePeriodVO> ofDay() {
        ArrayList<TimePeriodVO> result = new ArrayList<>();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("HH:mm");
        LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        for (int i = 0; i < 24; i++) {
            String name = today.plusHours(i).format(dateTimeFormatter);
            result.add(new TimePeriodVO(name, today.plusHours(i), today.plusHours(i)));
        }
        return result;
    }

}

