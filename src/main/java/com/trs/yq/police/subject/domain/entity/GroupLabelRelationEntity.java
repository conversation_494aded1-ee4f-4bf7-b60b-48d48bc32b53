package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/9/2 14:49
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_GROUP_LABEL_RELATION")
public class GroupLabelRelationEntity extends BaseEntity{
    private static final long serialVersionUID = 5351588223193952050L;
    /**
     * 群体Id
     */
    private String groupId;

    /**
     * 类型id
     */
    private String labelId;
}
