package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.domain.vo.Coordinate;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/24
 */
@Data
public class FaceHitEntity implements Serializable {

    private static final long serialVersionUID = 8816798235579022913L;

    private String idNumber;

    private Double lat;

    private Double lng;

    private String personId;

    private String personName;

    private LocalDateTime dateTime;

    /**
     * getCoordinate
     *
     * @return Coordinate
     */
    public Coordinate getCoordinate() {
        return new Coordinate(lat, lng);
    }
}
