package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Data;

import java.io.Serializable;

/**
 * 用来封装一个PageParams
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class RequestParams implements Serializable {

    private static final long serialVersionUID = -860191081835189368L;

    PageParams pageParams;

    TimeParams timeParams;

    SortParams sortParams;
}
