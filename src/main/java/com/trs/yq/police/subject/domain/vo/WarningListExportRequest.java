package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 预警列表批量导出请求
 *
 * <AUTHOR>
 * @date 2021/09/15
 */
@Data
public class WarningListExportRequest implements Serializable {

    private static final long serialVersionUID = -5067223817174514734L;

    @NotEmpty(message = "字段名称不能为空！")
    private List<String> fieldNames;

    @NotEmpty(message = "线索id不能为空！")
    private List<String> warningIds;
    
}
