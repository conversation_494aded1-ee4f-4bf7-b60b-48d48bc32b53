package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwrySourceTypeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjJwryDetailEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryEntity;
import com.trs.yq.police.subject.domain.entity.CrjJwryVisitEntity;
import com.trs.yq.police.subject.domain.entity.CrjReadRecordEntity;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/30 10:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrjJwryBaseVO {

    private String id;

    private String idType;
    private String idTypeName;

    /**
     * 姓名
     */
    private String name;
    private String idNumber;

    /**
     * 类型
     */
    private String type;
    /**
     * 登记状态
     */
    private String registrationStatus;
    /**
     * 国家代码
     */
    private String gjdm;
    /**
     * 国家名称
     */
    private String gjmc;

    private List<String> attachment;

    private String phone;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 拟离开时间
     */
    private Date planLeaveTime;

    /**
     * 录入时间
     */
    private Date createTime;

    private Boolean isRead;

    private Boolean isDispatched;

    private Boolean isRVisa;

    private String acceptor;

    private String reason;

    /**
     * 类型
     */
    private String sourceType;
    /**
     * 管辖单位
     */
    private String controlDeptName;

    private String visaType;
    private String visaNumber;
    private Date birthday;
    private String enName;
    private String gender;
    /**
     * 转vo
     *
     * @param jwryEntity  entity
     * @param currentUser 当前用户
     * @return {@link CrjJwryBaseVO}
     */
    public static CrjJwryBaseVO of(CrjJwryEntity jwryEntity, LoginUser currentUser) {
        CrjJwryDetailRepository crjJwryDetailRepository = BeanUtil.getBean(CrjJwryDetailRepository.class);
        Optional<CrjJwryDetailEntity> crjJwryDetailEntity = crjJwryDetailRepository.findByIdTypeAndIdNumber(jwryEntity.getIdType(), jwryEntity.getIdNumber());

        //是否是已经登记状态
        if (CrjJwryRegistrationStatusEnum.isRegistered(jwryEntity.getRegistrationStatus())) {
           if (crjJwryDetailEntity.isPresent()) {
                CrjJwryDetailVO detailVO = new CrjJwryDetailVO();
                generateBaseVO(jwryEntity, currentUser, detailVO);
                generateDetailVO(jwryEntity, detailVO, crjJwryDetailEntity.get());
                return detailVO;
            }
        }

        CrjJwryBaseVO crjJwryBaseVO = new CrjJwryBaseVO();
        generateBaseVO(jwryEntity, currentUser, crjJwryBaseVO);
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        crjJwryDetailEntity.ifPresent(detail->{
            crjJwryBaseVO.setBirthday(detail.getBirthday());
            crjJwryBaseVO.setEnName(detail.getEnName());
            crjJwryBaseVO.setGender(dictRepository.findNameByTypeAndCode(CrjConstants.MALE, detail.getGender()));
        });
        return crjJwryBaseVO;
    }


    private static void generateDetailVO(CrjJwryEntity jwryEntity, CrjJwryDetailVO detailVO,
                                         CrjJwryDetailEntity detailEntity) {

        generateDetailListVO(jwryEntity, detailVO, detailEntity);

        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        detailVO.setCnName(detailEntity.getCnName());
        detailVO.setBirthday(detailEntity.getBirthday());
        //证件类型
        detailVO.setIdType(detailEntity.getIdType());
        //签证种类
        detailVO.setVisaType(dictRepository.findNameByTypeAndCode(CrjConstants.VISA_TYPE, detailEntity.getVisaType()));
        detailVO.setVisaNumber(detailEntity.getVisaNumber());
        detailVO.setInChinaTime(detailEntity.getInChinaTime());
        detailVO.setEntryTime(detailEntity.getEntryTime());
        //入境口岸
        detailVO.setEntryPort(
                dictRepository.findNameByTypeAndCode(CrjConstants.ENTRY_PORT, detailEntity.getEntryPort()));

        //最新住宿信息
        CrjJwryVisitRepository visitRepository = BeanUtil.getBean(CrjJwryVisitRepository.class);

        Optional<CrjJwryVisitEntity> latestVisit = visitRepository.findLatestByIdTypeAndIdNumber(
                jwryEntity.getIdType(), jwryEntity.getIdNumber());

        CrjJwryVisitVO latestVo = new CrjJwryVisitVO();
        if (latestVisit.isPresent()) {
            boolean isRVisa = CrjConstants.R_VISA_CODE.equals(detailEntity.getVisaType());
            latestVo = CrjJwryVisitVO.of(latestVisit.get(), isRVisa);
            latestVo.setWorkAddress(detailEntity.getWorkAddress());
            latestVo.setLiveAddress(detailEntity.getLiveAddress());
            latestVo.setPhone(detailEntity.getPhone());
            latestVo.setPlanLeaveTime(jwryEntity.getPlanLeaveTime());
            if (!isRVisa) {
                latestVo.setWorkAddress(null);
            }
        }
        detailVO.setLatestRegisterInfo(latestVo);
        detailVO.setName(detailEntity.getEnName());
    }

    private static void generateDetailListVO(CrjJwryEntity jwryEntity, CrjJwryDetailListVO listVO,
                                             CrjJwryDetailEntity detailEntity) {
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        listVO.setEnName(StringUtils.isNotBlank(detailEntity.getEnName())?detailEntity.getEnName():detailEntity.getCnName());
        listVO.setPhone(jwryEntity.getPhone());
        //性别
        listVO.setGender(dictRepository.findNameByTypeAndCode(CrjConstants.MALE, detailEntity.getGender()));
        //设置拟离开时间
        listVO.setPlanLeaveTime(jwryEntity.getPlanLeaveTime());
        listVO.setIsRVisa(CrjConstants.R_VISA_CODE.equals(detailEntity.getVisaType()));
    }

    /**
     * 转vo
     *
     * @param jwryEntity     entity
     * @param currentUser    当前用户
     * @param isDispatchList 是否是分派列表
     * @return {@link CrjJwryBaseVO}
     */
    public static CrjJwryBaseVO listVoOf(CrjJwryEntity jwryEntity, LoginUser currentUser, boolean isDispatchList) {
        CrjJwryDetailRepository crjJwryDetailRepository = BeanUtil.getBean(CrjJwryDetailRepository.class);
        Optional<CrjJwryDetailEntity> crjJwryDetailEntityOptional = crjJwryDetailRepository.findByIdTypeAndIdNumber(jwryEntity.getIdType(), jwryEntity.getIdNumber());

        //是否是确认登记状态
        if (CrjJwryRegistrationStatusEnum.isRegistered(jwryEntity.getRegistrationStatus()) && !isDispatchList) {
            if (crjJwryDetailEntityOptional.isPresent()) {
                CrjJwryDetailVO crjVo = new CrjJwryDetailVO();
                generateBaseVO(jwryEntity, currentUser, crjVo);
                generateDetailListVO(jwryEntity, crjVo, crjJwryDetailEntityOptional.get());
                return crjVo;
            }
        }
        //不是已登记状态
        CrjJwryBaseVO crjVo = new CrjJwryBaseVO();
        generateBaseVO(jwryEntity, currentUser, crjVo);
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        crjJwryDetailEntityOptional.ifPresent(detail->{
            crjVo.setBirthday(detail.getBirthday());
            crjVo.setEnName(detail.getEnName());
            crjVo.setGender(dictRepository.findNameByTypeAndCode(CrjConstants.MALE, detail.getGender()));

        });
        return crjVo;
    }


    private static void generateBaseVO(CrjJwryEntity jwryEntity, LoginUser currentUser, CrjJwryBaseVO crjVo) {
        crjVo.setId(jwryEntity.getRecordId());
        crjVo.setIdType(jwryEntity.getIdType());
        crjVo.setIdNumber(jwryEntity.getIdNumber());
        crjVo.setAddress(jwryEntity.getAddress());
        crjVo.setCreateTime(jwryEntity.getCreateTime());
        crjVo.setPhone(StringUtils.isBlank(jwryEntity.getPhone()) ? "" : jwryEntity.getPhone());
        crjVo.setGjdm(jwryEntity.getGjdm());
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        crjVo.setIdTypeName(dictRepository.findNameByTypeAndCode("crjZjzl", jwryEntity.getIdType()));
        crjVo.setGjmc(dictRepository.findNameByTypeAndCode("crjGjdm", jwryEntity.getGjdm()));
        crjVo.setAttachment(JsonUtil.parseArray(jwryEntity.getAttachment(), String.class));
        crjVo.setReason(jwryEntity.getReason());
        UnitRepository unitRepository = BeanUtil.getBean(UnitRepository.class);
        if(StringUtils.isNotBlank(jwryEntity.getAcceptor())){
            crjVo.setControlDeptName(unitRepository.findByUnitCode(jwryEntity.getAcceptor()).getShortname());
        }
        crjVo.setSourceType(Objects.isNull(jwryEntity.getSourceType())?"":CrjJwrySourceTypeEnum.codeOf(jwryEntity.getSourceType()).getName());
        //接受部门
        if (StringUtils.isNotBlank(jwryEntity.getAcceptor())) {
            crjVo.setAcceptor(unitRepository.findByUnitCode(jwryEntity.getAcceptor()).getUnitName());
        }
        //设置登记状态
        CrjJwryRegistrationStatusEnum registrationStatus = CrjJwryRegistrationStatusEnum.codeOf(
                jwryEntity.getRegistrationStatus());
        crjVo.setRegistrationStatus(
                registrationStatus == null ? CrjJwryRegistrationStatusEnum.NOT_REGISTER.getName()
                        : registrationStatus.getName());
        //未登记状态，设置未读消息
        if (registrationStatus == CrjJwryRegistrationStatusEnum.NOT_REGISTER) {
            CrjReadRecordRepository readRecordRepository = BeanUtil.getBean(CrjReadRecordRepository.class);
            Optional<CrjReadRecordEntity> readRecord = readRecordRepository.findByRecordIdAndModuleAndUserId(
                    jwryEntity.getRecordId(), CrjConstants.JWRY_RECORD_MODULE, currentUser.getId());
            crjVo.setIsRead(readRecord.isPresent());
        }

        //是否分配
        CrjDispatchStatusEnum dispatchStatus = CrjDispatchStatusEnum.codeOf(jwryEntity.getDispatchStatus());
        String unitCode = currentUser.getUnitCode();
        if (CrjConstants.SJZD.equals(unitCode)) {
            //分到区县就算已分派
            crjVo.setIsDispatched(dispatchStatus == CrjDispatchStatusEnum.DISPATCHED
                    || dispatchStatus == CrjDispatchStatusEnum.DISPATCHED_TO_QX);
        } else {
            //分到派出所才算已分派
            crjVo.setIsDispatched(dispatchStatus == CrjDispatchStatusEnum.DISPATCHED);
        }
    }

}


