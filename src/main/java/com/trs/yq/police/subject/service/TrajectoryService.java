package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.request.TrajectoryListRequest;
import com.trs.yq.police.subject.domain.vo.DateCountVO;
import com.trs.yq.police.subject.domain.vo.IdNameCountVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.PersonTrajectoryVO;

import java.util.List;

/**
 * 轨迹相关查询服务层
 *
 * <AUTHOR>
 * @date 2021/08/27
 */
public interface TrajectoryService {

    /**
     * 查询所有轨迹来源
     *
     * @param personId 人员id
     * @param trajectoryType 轨迹类型
     * @return 轨迹来源
     */
    List<IdNameCountVO> getTrajectorySourceList(String personId, String trajectoryType);

    /**
     * 查询轨迹
     *
     * @param personId 人员id
     * @param request  请求
     * @return 轨迹
     */
    PageResult<PersonTrajectoryVO> getPersonTrajectoryList(String personId, TrajectoryListRequest request);

    /**
     * 轨迹计数
     *
     * @param personId 人员id
     * @param sourceId 预警来源id
     * @return 计数
     */
    List<DateCountVO> getTrajectoryCountList(String personId, String sourceId);
}
