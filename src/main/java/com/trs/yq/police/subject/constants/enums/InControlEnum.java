package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2021/08/17
 */
public enum InControlEnum {
    /**
     * enums
     */
    IN_CONTROL("1", "在控"),
    LOSS_CONTROL("0", "失控");

    /**
     * fields
     */
    @Getter
    private final String code;

    @Getter
    private final String name;

    /**
     * constructor
     *
     * @param code 模块码
     * @param name 模块名
     */
    InControlEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 通过模块码获取枚举
     *
     * @param code 模块码
     * @return 操作模块 {@link InControlEnum}
     */
    public static InControlEnum codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            return Arrays.stream(InControlEnum.values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
}
