package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 更新群体-事件关联vo
 *
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
public class GroupEventsRelationVO implements Serializable {

    private static final long serialVersionUID = 7595310170718459279L;

    /**
     * 群体id
     */
    private String groupId;

    /**
     * 事件id
     */
    @NotNull(message = "eventIds不能为空")
    private List<String> eventIds;
}
