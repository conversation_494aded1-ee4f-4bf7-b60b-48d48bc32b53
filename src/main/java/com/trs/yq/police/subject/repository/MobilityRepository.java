package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.MobilityEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 流动信息数据层
 *
 * <AUTHOR>
 */
@Repository
public interface MobilityRepository extends BaseRepository<MobilityEntity, String> {

    /**
     * 按人员id分页查询
     *
     * @param personId 人员id
     * @param pageable 分页参数
     * @return 流动信息分页列表
     */
    Page<MobilityEntity> findByPersonId(String personId, Pageable pageable);

    /**
     * 按流动类型统计数量
     *
     * @param personId 人员id
     * @param moveType 流动类型
     * @return 统计总数
     */
    Integer countByMoveTypeAndPersonId(String moveType, String personId);

    /**
     * 查询流动时间在时间范围内的流动信息
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 所有流动信息
     */
    List<MobilityEntity> findAllByMoveTimeBetween(LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 根据人员id和时间查找
     *
     * @param ids       人员id列表
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 所有流动信息
     */
    List<MobilityEntity> findAllByPersonIdInAndMoveTimeBetween(List<String> ids, LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 根据专题查询轨迹
     *
     * @param subjectId 专题
     * @param beginTime 开始时间
     * @param endTime 借宿时间
     * @return 轨迹
     */
    @Query("select t from MobilityEntity t " +
            "where t.personId in (select p.id from PersonEntity p left join PersonSubjectRelationEntity r on p.id = r.personId where r.subjectId = :subjectId and p.crTime < :endTime) " +
            "and (t.moveTime between :beginTime and :endTime)")
    List<MobilityEntity> findAllBySubjectIdAndMoveTimeBetween(@Param("subjectId") String subjectId, @Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);
}
