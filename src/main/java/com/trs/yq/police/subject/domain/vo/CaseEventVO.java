package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 案事件信息vo
 *
 * <AUTHOR>
 */
@Data
public class CaseEventVO implements Serializable {

    private static final long serialVersionUID = 7946263521143503628L;
    
    /**
     * 案事件名称
     */
    private String name;

    /**
     * 案事件类型
     */
    private String caseType;

    /**
     * 发生时间
     */
    private LocalDateTime happenTime;

    /**
     * 发生地点
     */
    private String happenSituation;

    /**
     * 派出所
     */
    private String policeSituation;
}
