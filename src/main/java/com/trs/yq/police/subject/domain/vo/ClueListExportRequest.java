package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * 线索批量导出请求
 *
 * <AUTHOR>
 * @date 2021/09/09
 */
@Data
public class ClueListExportRequest implements Serializable {
    private static final long serialVersionUID = 4095826597646388394L;

    @NotEmpty(message = "字段名称不能为空！")
    private List<String> fieldNames;

    @NotEmpty(message = "线索id不能为空！")
    private List<String> clueIds;
}
