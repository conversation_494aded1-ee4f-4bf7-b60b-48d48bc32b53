package com.trs.yq.police.subject.controller.crjApp;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.domain.vo.CrjDispatchVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryAddVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryBaseVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryDetailVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryRegisteredVO;
import com.trs.yq.police.subject.domain.vo.CrjJwryVisitVO;
import com.trs.yq.police.subject.domain.vo.CrjOcrResultVO;
import com.trs.yq.police.subject.domain.vo.CrjRedispatchVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.domain.vo.ImportResultVO;
import com.trs.yq.police.subject.domain.vo.ImportVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.SfryReportVO;
import com.trs.yq.police.subject.repository.CrjReadRecordRepository;
import com.trs.yq.police.subject.service.CrjJwryService;
import java.io.IOException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 出入境-境外人员
 *
 * <AUTHOR> yanghy
 * @date : 2022/11/16 10:10
 */
@RestController
@RequestMapping("/entry-exit/jwry")
public class JwryController {

    @Resource
    CrjJwryService crjJwryService;
    @Resource
    private CrjReadRecordRepository crjReadRecordRepository;
    /**
     * 分页查询
     *
     * @param requestVO 请求参数
     * @return {@link SfryReportVO}
     */
    @PostMapping("/page")
    PageResult<CrjJwryBaseVO> getPage(@RequestBody ListRequestVO requestVO) {
        return crjJwryService.findPage(requestVO, false);
    }

    /**
     * 分页查询
     *
     * @param requestVO 请求参数
     * @return {@link SfryReportVO}
     */
    @PostMapping("/page/dispatch")
    PageResult<CrjJwryBaseVO> getDispatchPage(@RequestBody ListRequestVO requestVO) {
        return crjJwryService.findPage(requestVO, true);
    }

    /**
     * 获取详情
     *
     * @param id uuid
     * @return {@link SfryReportVO}
     */
    @GetMapping("/{id}")
    CrjJwryBaseVO getById(@PathVariable("id") String id) {
        return crjJwryService.getById(id);
    }
    /**
     * 批量删除
     *
     * @param ids uuid
     */
    @PostMapping("/delete")
    @Transactional(rollbackFor = RuntimeException.class)
    public void deletedById(@RequestBody List<String> ids) {
        ids.parallelStream().forEach(id->{
            crjJwryService.deletedById(id);
        });
        crjReadRecordRepository.deleteJwryRead();
    }
    /**
     * 分派
     *
     * @param dispatchVO 分派信息
     */
    @PutMapping("/dispatch")
    void dispatch(@RequestBody @Valid CrjDispatchVO dispatchVO) {
        crjJwryService.dispatch(dispatchVO);
    }

    /**
     * 登记
     *
     * @param registeredVO 登记
     * @param id           id
     */
    @PutMapping("/{id}/register")
    void register(@RequestBody CrjJwryRegisteredVO registeredVO, @PathVariable("id") String id) {
        crjJwryService.register(registeredVO, id);
    }

    /**
     * 走访
     *
     * @param crjJwryVisitVO 登记
     */
    @PostMapping("/{id}/visit")
    void visit(@RequestBody CrjJwryVisitVO crjJwryVisitVO, @PathVariable("id") String id) {
        crjJwryService.addVisitRecord(crjJwryVisitVO, id, CrjConstants.VISIT);
    }

    /**
     * 获取走访记录
     *
     * @param id 人员id
     * @return {@link CrjJwryVisitVO}
     */
    @PostMapping("/{id}/visit/list")
    PageResult<CrjJwryVisitVO> visit(@PathVariable("id") String id, @RequestBody ListRequestVO requestVO) {
        return crjJwryService.getVisitRecord(id, requestVO);
    }


    /**
     * 获取全国走访记录
     *
     * @param id 人员id
     * @return {@link CrjJwryVisitVO}
     */
    @GetMapping("/{id}/visit/country")
    List<CrjJwryVisitVO> countryVisit(@PathVariable("id") String id) {
        return crjJwryService.getCountryVisitRecord(id);
    }

    /**
     * 新增境外人员
     *
     * @param vo 境外人员信息
     */
    @PostMapping("/add")
    void add(@RequestBody CrjJwryAddVO vo) {
        crjJwryService.addJwry(vo);
    }

    /**
     * 通过证件类型和号码获取人员信息
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @return {@link  CrjJwryDetailVO}
     */
    @GetMapping("/get/{idType}/{idNumber}")
    CrjJwryDetailVO getByIdTypeAndIdNumber(@PathVariable("idType") String idType,
        @PathVariable("idNumber") String idNumber) {
        return crjJwryService.getByIdTypeAndIdNumber(idType, idNumber);
    }

    /**
     * ocr识别证件
     *
     * @param image 证件照片
     * @return {@link CrjOcrResultVO}
     */
    @PostMapping("/ocr/id-card")
    CrjOcrResultVO ocr(@RequestBody(required = false) MultipartFile image) {
        return crjJwryService.ocrIdCard(image);
    }


    /**
     * 重新分派
     *
     * @param dispatchVO 分派信息
     */
    @PutMapping("/redispatch")
    void redispatch(@RequestBody CrjRedispatchVO dispatchVO) {
        crjJwryService.redispatch(dispatchVO);
    }

    /**
     * 批量导入
     *
     * @param importVO 导入参数
     * @return 批量导入结果
     */
    @PostMapping("/import")
    public ImportResultVO importPerson(ImportVO importVO) {
        return crjJwryService.importJwry(importVO);
    }

    /**
     * 导出excel
     *
     * @param response  响应体
     * @param request   导出请求
     * @throws IOException IO异常
     */
    @PostMapping("/list/export")
    public void exportJwry(HttpServletResponse response, @RequestBody ExportParams request) throws IOException {
        crjJwryService.downLoadExcel(response, request.getFieldNames(), request, request.getSubjectId());
    }
    /**
     * 根据专题id出查询可导出的人员信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getJwryProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return crjJwryService.getExportPropertyList(subjectId);
    }
}
