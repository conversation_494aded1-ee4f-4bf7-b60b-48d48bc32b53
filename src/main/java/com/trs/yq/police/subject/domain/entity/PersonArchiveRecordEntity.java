package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 人员归档激活记录
 *
 * <AUTHOR>
 * @date 2021/08/10
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_ARCHIVE")
public class PersonArchiveRecordEntity extends BaseEntity {

    private static final long serialVersionUID = -7253265411004736321L;
    /**
     * 人员ID
     */
    private String personId;

    /**
     * 归档激活状态
     */
    private String archiveStatus;

    /**
     * 流出时间
     */
    private LocalDateTime outTime;

    /**
     * 流出方向
     */
    private String outDestination;

    /**
     * 专题ID
     */
    private String subjectId;
}


