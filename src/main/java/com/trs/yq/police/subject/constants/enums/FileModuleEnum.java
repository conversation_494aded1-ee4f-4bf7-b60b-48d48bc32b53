package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 * 文件模块枚举
 *
 * <AUTHOR>
 * @date 2021/7/30 16:41
 */
public enum FileModuleEnum {

    /**
     * enums
     */
    BASIC_INFO_PHOTO("0", "基本信息照片"),
    VISIT_RECORD_PHOTO("1","走访记录照片"),

    CRJ_VISIT_RECORD_PHOTO("3", "出入境走访记录头像"),
    CRJ_ACCOMMODATION_PHOTO("4", "出入境住宿登记头像"),
    CRJ_FOREIGNER_VISIT_PHOTO("5", "出入境境外人员走访照片");

    /**
     * 状态码
     */
    @Getter
    private final String code;

    /**
     * 中文名
     */
    @Getter
    private final String name;

    FileModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
}
