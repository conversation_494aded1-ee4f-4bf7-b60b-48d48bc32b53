package com.trs.yq.police.subject.domain.entity;

import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

/**
 * <AUTHOR>
 * @date 2023/3/15 15:33
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_CAR_JUDGE")
public class WarningCarJudgeEntity {

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    private String plateNumber;

    private String judgeResult;

    private LocalDateTime judgeTime;

    /**
     * 构造函数
     *
     * @param plateNumber 车牌号
     * @param judgeResult 结果
     * @param judgeTime   时间
     */
    public WarningCarJudgeEntity(String plateNumber, String judgeResult, LocalDateTime judgeTime) {
        this.plateNumber = plateNumber;
        this.judgeResult = judgeResult;
        this.judgeTime = judgeTime;
    }
}
