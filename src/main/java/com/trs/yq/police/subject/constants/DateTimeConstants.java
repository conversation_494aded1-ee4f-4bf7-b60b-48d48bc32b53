package com.trs.yq.police.subject.constants;

import java.time.format.DateTimeFormatter;

/**
 * 存放时间日期常量
 *
 * <AUTHOR>
 * @date 2021/07/30
 */
public class DateTimeConstants {

    private DateTimeConstants() {
    }

    /**
     * 时间格式
     */
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 日期格式
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
}
