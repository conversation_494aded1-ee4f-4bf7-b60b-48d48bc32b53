package com.trs.yq.police.subject.jpa.config;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateSettings;
import org.springframework.boot.autoconfigure.orm.jpa.JpaProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/21 19:05
 */
@Configuration
@EnableTransactionManagement
@EnableJpaRepositories(
    entityManagerFactoryRef = "mppEntityManagerFactory",
    transactionManagerRef = "mppTransactionManager",
    basePackages = {"com.trs.yq.police.subject.mppDatasource.repository"})
public class MppRepositoryConfig {

    @Autowired
    @Qualifier("mppDatasource")
    private DataSource mppDataSource;

    @Autowired
    private JpaProperties jpaProperties;

    @Autowired
    private HibernateProperties hibernateProperties;

    @Value("${spring.jpa.properties.hibernate.mpp-dialect}")
    private String dialect;

    /**
     * @param builder builder
     * @return {@link LocalContainerEntityManagerFactoryBean}
     */
    @Bean(name = "mppEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean entityManagerFactoryUser(
        EntityManagerFactoryBuilder builder) {
        HashMap<String, String> map = new HashMap<>();
        map.put("hibernate.dialect", dialect);
        jpaProperties.setProperties(map);
        Map<String, Object> properties = hibernateProperties.determineHibernateProperties(
            jpaProperties.getProperties(), new HibernateSettings());
        return builder.dataSource(mppDataSource).properties(properties)
            .packages("com.trs.yq.police.subject.mppDatasource.entity").build();
    }

    /**
     * @param builder builder
     * @return {@link PlatformTransactionManager}
     */
    @Bean(name = "mppTransactionManager")
    public PlatformTransactionManager transactionManagerUser(EntityManagerFactoryBuilder builder) {
        return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactoryUser(builder).getObject()));
    }

}
