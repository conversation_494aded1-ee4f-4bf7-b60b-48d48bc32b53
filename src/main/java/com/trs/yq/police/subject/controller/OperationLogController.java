package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.dto.OperationLogQueryVO;
import com.trs.yq.police.subject.domain.vo.ContentVO;
import com.trs.yq.police.subject.domain.vo.OperationLogVo;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.WarningLogsVO;
import com.trs.yq.police.subject.operation.LogListService;
import com.trs.yq.police.subject.service.ModuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 操作日志接口
 *
 * <AUTHOR>
 * @since 2021/7/27 18:17
 */
@Validated
@RestController
@RequestMapping("")
@Slf4j
public class OperationLogController {

    @Resource
    private LogListService logListService;
    @Resource
    private ModuleService moduleService;

    /**
     * 分页查询操作日志
     * http://192.168.200.192:3001/project/4897/interface/api/129615
     *
     * @param personId 人员id
     * @param query    请求参数
     * @return 分页查询操作日志
     */
    @PostMapping("/person/{personId}/operation-log")
    public PageResult<OperationLogVo> getOperationLogList(@PathVariable @NotBlank(message = "人员主键缺失") String personId,
                                                          @RequestBody @NotNull(message = "操作日志查询参数缺失") @Valid OperationLogQueryVO query) {
        query.setTargetObjectType(TargetObjectTypeEnum.PERSON.getCode());
        return logListService.getOperationLogList(personId, query, query.getPageParams().toPageable());
    }

    /**
     * 获取操作日志目录
     * http://192.168.200.192:3001/project/4897/interface/api/130061
     *
     * @param personId  人员主键
     * @param subjectId 专题主键
     * @return 目录
     */
    @GetMapping("/person/{personId}/operation-log/content")
    public List<ContentVO> getPersonOperationLogContent(@PathVariable @NotBlank(message = "人员主键缺失") String personId,
                                                        @NotBlank(message = "专题主键缺失") String subjectId) {
        return moduleService.getPersonOperationLogContent(personId, subjectId);
    }

    /**
     * 分页查询群体操作日志
     * http://192.168.200.192:3001/project/4897/interface/api/130465
     *
     * @param groupId 群体id
     * @param query   请求参数
     * @return 分页查询操作日志
     */
    @PostMapping("/group/{groupId}/operation-log")
    public PageResult<OperationLogVo> getGroupOperationLogList(@PathVariable @NotBlank(message = "群体主键缺失") String groupId,
                                                               @RequestBody @NotNull(message = "操作日志查询参数缺失") @Valid OperationLogQueryVO query) {
        query.setTargetObjectType(TargetObjectTypeEnum.GROUP.getCode());
        return logListService.getOperationLogList(groupId, query, query.getPageParams().toPageable());
    }

    /**
     * 获取群体操作日志目录
     * http://192.168.200.192:3001/project/4897/interface/api/130471
     *
     * @param groupId   群体主键
     * @param subjectId 专题主键
     * @return 目录
     */
    @GetMapping("/group/{groupId}/operation-log/content")
    public List<ContentVO> getGroupOperationLogContent(@PathVariable @NotBlank(message = "群体主键缺失") String groupId,
                                                       @NotBlank(message = "专题主键缺失") String subjectId) {
        return moduleService.getOperationLogContent(groupId, subjectId, TargetObjectTypeEnum.GROUP);
    }

    /**
     * 分页查询线索操作日志
     * http://192.168.200.192:3001/project/4897/interface/api/130591
     *
     * @param clueId 线索id
     * @param query  请求参数
     * @return 分页查询操作日志
     */
    @PostMapping("/clue/{clueId}/operation-log")
    public PageResult<OperationLogVo> getClueOperationLogList(@PathVariable @NotBlank(message = "群体主键缺失") String clueId,
                                                              @RequestBody @NotNull(message = "操作日志查询参数缺失") @Valid OperationLogQueryVO query) {
        query.setTargetObjectType(TargetObjectTypeEnum.CLUE.getCode());
        String subjectId = query.getSubjectId();
        if (subjectId.equals("6")) {
            //维稳专题线索返回合成/指令
            return logListService.getStabilityOperationLogList(clueId, query, query.getPageParams());
        } else {
            //其他专题正常返回
            return logListService.getOperationLogList(clueId, query, query.getPageParams().toPageable());
        }
    }

    /**
     * 获取线索操作日志目录
     * http://192.168.200.192:3001/project/4897/interface/api/130567
     *
     * @param clueId    线索主键
     * @param subjectId 专题主键
     * @return 目录
     */
    @GetMapping("/clue/{clueId}/operation-log/content")
    public List<ContentVO> getClueOperationLogContent(@PathVariable @NotBlank(message = "群体主键缺失") String clueId,
                                                      @NotBlank(message = "专题主键缺失") String subjectId) {
        return moduleService.getOperationLogContent(clueId, subjectId, TargetObjectTypeEnum.CLUE);
    }

    /**
     * 查询预警操作日志
     *
     * @param warningId 预警id
     * @param order 顺序
     * @return 操作日志
     */
    @GetMapping("/warning/{warningId}/logs")
    public WarningLogsVO getWarningOperationLogList(@PathVariable @NotBlank(message = "预警主键缺失") String warningId, @NotBlank(message = "排序顺序缺失") String order) {
        return logListService.getWarningOperationLogList(warningId, order);
    }

    /**
     * 分页查询事件操作日志
     * http://192.168.200.192:3001/project/4897/interface/api/134412
     *
     * @param eventId 事件id
     * @param query  请求参数
     * @return 分页查询操作日志
     */
    @PostMapping("/event/{eventId}/operation-log")
    public PageResult<OperationLogVo> getEventOperationLogList(@PathVariable @NotBlank(message = "群体主键缺失") String eventId,
                                                              @RequestBody @NotNull(message = "操作日志查询参数缺失") @Valid OperationLogQueryVO query) {
        query.setTargetObjectType(TargetObjectTypeEnum.EVENT.getCode());
        return logListService.getStabilityOperationLogList(eventId, query, query.getPageParams());
    }

    /**
     * 获取事件操作日志目录
     * http://192.168.200.192:3001/project/4897/interface/api/134411
     *
     * @param eventId    事件主键
     * @param subjectId 专题主键
     * @return 目录
     */
    @GetMapping("/event/{eventId}/operation-log/content")
    public List<ContentVO> getEventOperationLogContent(@PathVariable @NotBlank(message = "群体主键缺失") String eventId,
                                                      @NotBlank(message = "专题主键缺失") String subjectId) {
        return moduleService.getOperationLogContent(eventId, subjectId, TargetObjectTypeEnum.EVENT);
    }
}
