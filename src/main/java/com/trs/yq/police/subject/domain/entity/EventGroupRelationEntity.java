package com.trs.yq.police.subject.domain.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;


/**
 * 事件与群体的关联
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "t_event_group_relation")
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventGroupRelationEntity implements Serializable {

    private static final long serialVersionUID = -3576738817465670476L;

    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;
    /**
     * 事件id
     */
    private String eventId;
    /**
     * 群体id
     */
    private String groupId;

    /**
     * 构造器
     *
     * @param groupId 群体id
     * @param eventId 事件id
     */
    public EventGroupRelationEntity(String eventId, String groupId) {
        this.groupId = groupId;
        this.eventId = eventId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) return false;
        EventGroupRelationEntity that = (EventGroupRelationEntity) o;
        return id != null && Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
