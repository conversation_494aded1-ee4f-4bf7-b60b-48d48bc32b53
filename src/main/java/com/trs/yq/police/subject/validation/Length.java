package com.trs.yq.police.subject.validation;

import org.apache.commons.lang3.StringUtils;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 字符串长度验证
 *
 * <AUTHOR>
 * @date 2021/8/4 18:51
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = Length.LengthValidator.class)
public @interface Length {

    /**
     * message
     *
     * @return {length.invalid}
     */
    String message() default "{length.invalid}";

    /**
     * groups
     *
     * @return {}
     */
    Class<?>[] groups() default {};

    /**
     * payload
     *
     * @return {}
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * length
     *
     * @return 0
     */
    int length() default 0;


    /**
     * LengthValidator
     */
    class LengthValidator implements ConstraintValidator<Length, String> {

        private int length;

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {

            return !StringUtils.isBlank(value) && value.length() > length;
        }

        @Override
        public void initialize(Length constraintAnnotation) {
            length = constraintAnnotation.length();
        }
    }
}
