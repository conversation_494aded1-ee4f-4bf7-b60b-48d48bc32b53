package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * [专题首页-出入境] 走访记录详情VO
 *
 * <AUTHOR>
 * @date 2021/9/18 15:05
 */
@Data
public class CrjVisitVO implements Serializable {

    private static final long serialVersionUID = -2208109879747621718L;
    /**
     * 名
     */
    private String firstName;
    /**
     * 姓
     */
    private String lastName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号
     */
    private String certificateNumber;
    /**
     * 走访时间
     */
    private LocalDateTime visitTime;
    /**
     * 走访单位
     */
    private String deptName;
    /**
     * 走访类型
     */
    private String visitType;
    /**
     * 管控状态
     */
    private String controlStatus;
    /**
     * 住宿信息
     */
    private String livingInfo;
    /**
     * 旅店名称
     */
    private String livingInn;
    /**
     * 走访详情
     */
    private String visitDetail;
    /**
     * 走访照片
     */
    private List<ImageVO> images;

}
