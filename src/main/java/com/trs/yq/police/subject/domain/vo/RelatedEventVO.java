package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_EVENT_DISPOSAL_STATUS;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@Data
@NoArgsConstructor
public class RelatedEventVO implements Serializable {
    private static final long serialVersionUID = 4295768326359754518L;

    /**
     * 事件id
     */
    protected String eventId;

    /**
     * 事件标题
     */
    protected String eventName;

    /**
     * 事件类别
     */
    protected String eventType;

    /**
     * 事发事件
     */
    protected LocalDateTime eventTime;

    /**
     * 事发地点
     */
    protected String eventAddress;

    /**
     * 主管单位
     */
    protected String controlDeptName;

    /**
     * 处置状态
     */
    protected String disposalStatus;

    /**
     * 关系id
     */
    protected String relationId;

    /**
     * 构造器
     *
     * @param eventEntity {@link EventEntity}
     */
    protected RelatedEventVO(EventEntity eventEntity) {
        this.eventId = eventEntity.getId();
        this.eventName = eventEntity.getTitle();
        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        LabelEntity label = labelRepository.findByEventId(eventEntity.getId());
        if(Objects.nonNull(label)) {
            this.eventType = label.getName();
        }
        this.eventTime = eventEntity.getOccurrenceTime();
        this.eventAddress = eventEntity.getAddress();
        this.controlDeptName = eventEntity.getControlDeptName();
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        this.disposalStatus = dictRepository.findByTypeAndCode(DICT_TYPE_EVENT_DISPOSAL_STATUS, eventEntity.getDisposalStatus()).getName();
    }
}
