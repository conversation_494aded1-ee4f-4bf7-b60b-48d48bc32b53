package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjJwryVisitEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 15:23
 */
@Repository
public interface CrjJwryVisitRepository extends BaseRepository<CrjJwryVisitEntity, String> {

    /**
     * 获取走访记录
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @param pageable  分页参数
     * @return {@link CrjJwryVisitEntity}
     */
    Page<CrjJwryVisitEntity> findByIdTypeAndIdNumberOrderByCrTimeDesc(String idType, String idNumber, Pageable pageable);


    /**
     * 获取最新住宿信息
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @return {@link CrjJwryVisitEntity}
     */
    @Query(nativeQuery = true,
        value = "SELECT  b.* from (select t.* from T_PS_CRJ_JWRY_VISIT t where t.ID_TYPE=:idType and t.ID_NUMBER=:idNumber order by t.CR_TIME) b where ROWNUM = 1")
    Optional<CrjJwryVisitEntity> findLatestByIdTypeAndIdNumber(@Param("idType") String idType,
        @Param("idNumber") String idNumber);


}
