package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/09/07
 */
@Data
public class PersonGroupRelationVO implements Serializable {

    private static final long serialVersionUID = 8481839800421326466L;
    /**
     * 人员id
     */
    private String personId;

    /**
     * 专题id
     */
    @NotBlank(message = "专题id不能为空!")
    private String subjectId;

    /**
     * 群体id
     */
    @NotNull(message = "群体id不能为空！")
    private List<String> groupIds;
}
