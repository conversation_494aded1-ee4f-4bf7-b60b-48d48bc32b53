package com.trs.yq.police.subject.domain.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/28 9:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrjSfryBaseVO extends CrjSfryListVO {

    /**
     * 退房时间
     */
    private LocalDateTime tfsj;
    /**
     * 企业名称
     */
    private String qiyebmc;
    /**
     * 消息
     */
    private List<CrjMessageSendVO> msgList;

}
