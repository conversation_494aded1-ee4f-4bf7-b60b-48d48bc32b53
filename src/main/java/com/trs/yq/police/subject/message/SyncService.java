package com.trs.yq.police.subject.message;

/**
 * 同步业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/22 17:54
 */
public interface SyncService {

    /**
     * 同步处置状态
     *
     * @param msg 消息
     */
    void syncDisposalStatus(String msg);

    /**
     * 同步布控状态
     *
     * @param msg kafka消息
     */
    void syncBkStatus(String msg);

    /**
     * 同步预警信息
     *
     * @param message 预警信息
     */
    void syncWarning(String message);

    /**
     * 同步技侦手段预警信息
     *
     * @param message   预警信息
     * @param subjectId 专题id
     */
    void syncJzsdWarning(String message, String subjectId);
}
