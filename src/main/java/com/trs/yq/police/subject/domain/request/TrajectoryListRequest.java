package com.trs.yq.police.subject.domain.request;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 人员轨迹列表查询请求
 *
 * <AUTHOR>
 * @date 2021/08/30
 */
@Getter
@Setter
@ToString
public class TrajectoryListRequest implements Serializable {

    private static final long serialVersionUID = 7921072336296737015L;

    @NotBlank(message = "轨迹来源不能为空")
    private String sourceId;

    @NotNull(message = "时间参数不能为空")
    private TimeParams timeParams;

    @NotNull(message = "分页参数不能为空")
    private PageParams pageParams;
}
