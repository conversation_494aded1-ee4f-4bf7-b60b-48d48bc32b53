package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileTypeEnum;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.exception.SystemException;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.ExportExcelService;
import com.trs.yq.police.subject.service.PhotoService;
import com.trs.yq.police.subject.service.RemoteStorageService;
import com.trs.yq.police.subject.utils.DateUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;

import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;

import java.net.URLEncoder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CONTROL_STATUS;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * <AUTHOR>
 * @date 2022/1/11 19:39
 */
@Service
public class ExportExcelServiceImpl implements ExportExcelService {
    @Resource
    private PersonRepository personRepository;
    @Resource
    private DictRepository dictRepository;
    @Resource
    private VirtualIdentityRepository virtualIdentityRepository;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private CommonExtendRepository commonExtendRepository;
    @Resource
    private PersonGroupRelationRepository personGroupRelationRepository;
    @Resource
    private GroupRepository groupRepository;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private LabelAttributesRepository labelAttributesRepository;
    @Resource
    private LabelAttributeValueRepository labelAttributeValueRepository;
    @Resource
    private FileStorageRepository fileStorageRepository;
    @Resource
    private RemoteStorageService remoteStorageService;
    @Resource
    private RelationRepository relationRepository;
    @Resource
    private VehicleRepository vehicleRepository;
    @Resource
    private EventRepository eventRepository;
    @Resource
    private EventPersonRelationRepository eventPersonRelationRepository;
    @Resource
    private ClueRepository clueRepository;
    @Resource
    private VisitRecordRepository visitRecordRepository;
    @Resource
    private SensitiveTimeRepository sensitiveTimeRepository;
    @Resource
    private BattleCommandRepository battleCommandRepository;
    @Resource
    private BattleRecordRepository battleRecordRepository;
    @Resource
    private PhotoService photoService;
    private XSSFCellStyle defaultCellStyle;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public void export(HttpServletResponse response, String name, Workbook workbook) {
        try (OutputStream outputStream = response.getOutputStream()) {
            // 设置强制下载不打开
            response.setContentType("application/force-download;charset=utf-8");
            // 设置文件名
            response.addHeader(
                    "Content-Disposition",
                    "attachment;fileName=" + URLEncoder.encode(name + ".xlsx", "UTF-8"));
            workbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            throw new SystemException("export excel failed: ", e);
        }
    }

    @Override
    public XSSFWorkbook getPersonExcel(PersonEntity personEntity, String subjectId) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        if (Objects.isNull(personEntity)) {
            return workbook;
        }
        // 加载默认样式
        defaultCellStyle = getDefaultCellStyle(workbook);
        XSSFSheet sheet = workbook.createSheet("重点人员信息");
        // 绘制的行索引
        int drawRowIndex = 1;
        // 详细属性的开始列
        int startColumnIndex = 2;

        drawCell(sheet.createRow(0).createCell(0), "涉稳重点人员档案");
        mergeCell(sheet, 0, 0, 0, startColumnIndex + 5 * 2 - 1);
        // 基本信息
        drawRowIndex = drawPersonBasic(sheet, startColumnIndex, drawRowIndex, personEntity, subjectId);
        // 管控专班
        drawRowIndex = drawControlDepartment(sheet, startColumnIndex, drawRowIndex, personEntity, subjectId);
        // 家庭主要成员
        int familyStartRowIndex = drawRowIndex;
        List<RelationEntity> families =
                relationRepository.findByPersonIdAndType(personEntity.getId(), "family");
        drawRowIndex =
                drawPersonRelation(
                        families,
                        familyStartRowIndex,
                        startColumnIndex,
                        sheet,
                        drawRowIndex,
                        "家\n庭\n主\n要\n成\n员");
        // 主要社会关系
        int societyStartRowIndex = drawRowIndex;
        List<RelationEntity> societies =
                relationRepository.findByPersonIdAndType(personEntity.getId(), "society");
        drawRowIndex =
                drawPersonRelation(
                        societies,
                        societyStartRowIndex,
                        startColumnIndex,
                        sheet,
                        drawRowIndex,
                        "主\n要\n社\n会\n关\n系");

        drawCell(sheet.getRow(1).createCell(0), "人");
        mergeCell(sheet, 1, drawRowIndex - 1, 0, 0);

        // 车
        drawRowIndex = drawPersonCar(sheet, startColumnIndex, drawRowIndex, personEntity.getId());

        // 活动情况
        drawRowIndex = drawActivity(sheet, startColumnIndex, drawRowIndex, personEntity.getId(), subjectId);
        // 情报线索
        drawRowIndex = drawClue(sheet, startColumnIndex, drawRowIndex, personEntity.getId(), subjectId);
        // 人员管控情况
        drawPersonControl(sheet, startColumnIndex, drawRowIndex, personEntity.getId());

        return workbook;
    }

    @Override
    public XSSFWorkbook getGroupExcel(String groupId, String subjectId) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        GroupEntity groupEntity = groupRepository.findById(groupId).orElse(null);
        if (Objects.isNull(groupEntity)) {
            return workbook;
        }
        // 加载默认样式
        defaultCellStyle = getDefaultCellStyle(workbook);
        XSSFSheet sheet = workbook.createSheet("重点群体档案");
        // 绘制的行索引
        int drawRowIndex = 1;
        // 详细属性的开始列
        int startColumnIndex = 1;

        drawCell(sheet.createRow(0).createCell(0), groupEntity.getName() + "重点群体档案");
        mergeCell(sheet, 0, 0, 0, startColumnIndex + 9);

        // 基本情况
        drawRowIndex = drawGroupBasic(sheet, startColumnIndex, drawRowIndex, groupEntity);
        // 活动情况
        drawRowIndex = drawGroupActivity(sheet, startColumnIndex, drawRowIndex, groupEntity);
        // 成员情况
        drawRowIndex = drawGroupMembership(sheet, startColumnIndex, drawRowIndex, groupEntity.getId());

        // 备注
        XSSFRow remarkRow = sheet.createRow(drawRowIndex);
        drawCell(remarkRow.createCell(0), "备注");
        drawCell(remarkRow.createCell(1), "");
        mergeCell(sheet, remarkRow.getRowNum(), remarkRow.getRowNum(), 1, startColumnIndex + 9);

        return workbook;
    }

    /**
     * 绘制单元格
     *
     * @param cell      单元格
     * @param cellValue 单元格值
     * @param cellStyle 单元格样式，传则使用第一个样式，不传则使用默认样式
     */
    private void drawCell(XSSFCell cell, String cellValue, XSSFCellStyle... cellStyle) {
        cell.setCellValue(cellValue);
        cell.setCellStyle(cellStyle.length == 0 ? defaultCellStyle : cellStyle[0]);
    }

    private void mergeCell(XSSFSheet sheet, int firstRow, int lastRow, int firstCol, int lastCol) {
        CellRangeAddress region = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        RegionUtil.setBorderTop(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderBottom(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderLeft(BorderStyle.THIN, region, sheet);
        RegionUtil.setBorderRight(BorderStyle.THIN, region, sheet);
        sheet.addMergedRegion(region);
    }

    private XSSFCellStyle getDefaultCellStyle(XSSFWorkbook workbook) {
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        // 自动换行
        cellStyle.setWrapText(true);
        // 设置水平居中
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置下边框
        cellStyle.setBorderBottom(BorderStyle.THIN);
        // 设置上边框
        cellStyle.setBorderTop(BorderStyle.THIN);
        // 设置走边框
        cellStyle.setBorderLeft(BorderStyle.THIN);
        // 设置右边框
        cellStyle.setBorderRight(BorderStyle.THIN);
        // 设置字体
        XSSFFont font = workbook.createFont();
        font.setFontHeightInPoints((short) 11);
        font.setFontName("宋体");
        cellStyle.setFont(font);
        return cellStyle;
    }

    private int drawPersonBasic(XSSFSheet sheet, int startColumnIndex, int drawRowIndex, PersonEntity personEntity, String subjectId) {
        // 基本信息的开始行
        final int basicStartRowIndex = drawRowIndex;
        // 每行的最大详细属性数量
        int detailAttributeNum = 4;
        // 详细属性的结束列
        final int endColumnIndex = startColumnIndex + detailAttributeNum * 2 - 1;
        // 照片宽度
        int photoWidth = 2;
        int columnIndex = startColumnIndex;
        XSSFRow row1 = sheet.createRow(drawRowIndex++);
        drawCell(row1.createCell(columnIndex++), "姓名");
        drawCell(row1.createCell(columnIndex++), personEntity.getName());
        drawCell(row1.createCell(columnIndex++), "性别");
        drawCell(row1.createCell(columnIndex++), "0".equals(personEntity.getGender()) ? "男" : "女");
        drawCell(row1.createCell(columnIndex++), "籍贯");
        drawCell(row1.createCell(columnIndex++), personEntity.getRegisteredResidence());
        drawCell(row1.createCell(columnIndex++), "身份证号");
        drawCell(row1.createCell(columnIndex), personEntity.getIdNumber());

        columnIndex = startColumnIndex;
        XSSFRow row2 = sheet.createRow(drawRowIndex++);
        drawCell(row2.createCell(columnIndex++), "出生日期");
        drawCell(row2.createCell(columnIndex++), DATE_FORMATTER.format(DateUtil.utcToLocalDate(StringUtil.parseBirthdayFromIdNumber(personEntity.getIdNumber()))));
        drawCell(row2.createCell(columnIndex++), "民族");
        drawCell(row2.createCell(columnIndex++), StringUtils.isBlank(personEntity.getNation()) ? "" : dictRepository.findByTypeAndCode("nation", personEntity.getNation()).getName());
        drawCell(row2.createCell(columnIndex++), "婚姻状况");
        drawCell(row2.createCell(columnIndex++), StringUtils.isBlank(personEntity.getMaritalStatus()) ? "" : dictRepository.findByTypeAndCode("ps_marital_status", personEntity.getMaritalStatus()).getName());
        drawCell(row2.createCell(columnIndex++), "政治面貌");
        drawCell(row2.createCell(columnIndex), StringUtils.isBlank(personEntity.getPoliticalStatus()) ? "" : dictRepository.findByTypeAndCode("ps_political_status", personEntity.getPoliticalStatus()).getName());

        columnIndex = startColumnIndex;
        XSSFRow row3 = sheet.createRow(drawRowIndex++);
        drawCell(row3.createCell(columnIndex++), "联系方式");
        drawCell(row3.createCell(columnIndex++), personEntity.getContactInformation());
        drawCell(row3.createCell(columnIndex++), "户籍地");
        drawCell(row3.createCell(columnIndex++), personEntity.getRegisteredResidence());
        drawCell(row3.createCell(columnIndex++), "现住地");
        drawCell(row3.createCell(columnIndex++), personEntity.getCurrentResidence());
        drawCell(row3.createCell(columnIndex++), "现职业");
        drawCell(row3.createCell(columnIndex), personEntity.getCurrentJob());

        columnIndex = startColumnIndex;
        XSSFRow row4 = sheet.createRow(drawRowIndex++);
        final CommonExtentEntity commonExtentEntity = commonExtendRepository.findByRecordIdAndModule(personEntity.getId(), "person").orElse(new CommonExtentEntity());
        drawCell(row4.createCell(columnIndex++), "工作单位");
        drawCell(row4.createCell(columnIndex++), commonExtentEntity.getWorkUnit());
        drawCell(row4.createCell(columnIndex++), "QQ");
        drawCell(row4.createCell(columnIndex++), virtualIdentityRepository.findByPersonIdAndVirtualType(personEntity.getId(), "4").stream().findFirst().orElse(new VirtualIdentityEntity()).getVirtualNumber());
        drawCell(row4.createCell(columnIndex++), "微信");
        drawCell(row4.createCell(columnIndex++), virtualIdentityRepository.findByPersonIdAndVirtualType(personEntity.getId(), "5").stream().findFirst().orElse(new VirtualIdentityEntity()).getVirtualNumber());
        drawCell(row4.createCell(columnIndex++), "微博");
        drawCell(row4.createCell(columnIndex), virtualIdentityRepository.findByPersonIdAndVirtualType(personEntity.getId(), "7").stream().findFirst().orElse(new VirtualIdentityEntity()).getVirtualNumber());

        columnIndex = startColumnIndex;
        XSSFRow row5 = sheet.createRow(drawRowIndex++);
        drawCell(row5.createCell(columnIndex++), "其他电子信息");
        drawCell(row5.createCell(columnIndex++), virtualIdentityRepository.findByPersonIdAndVirtualType(personEntity.getId(), "99").stream().findFirst().orElse(new VirtualIdentityEntity()).getVirtualNumber());
        drawCell(row5.createCell(columnIndex++), "管控级别");
        ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(personEntity.getId(), subjectId).orElse(new ControlEntity());
        drawCell(row5.createCell(columnIndex++), StringUtils.isBlank(controlEntity.getControlLevel()) ? "" : dictRepository.findByTypeAndCode("ps_ww_control_level", controlEntity.getControlLevel()).getName());
        drawCell(row5.createCell(columnIndex++), "管控状态");
        drawCell(row5.createCell(columnIndex), dictRepository.findByTypeAndCode(DICT_TYPE_CONTROL_STATUS, personEntity.getControlStatus()).getName());
        mergeCell(sheet, row5.getRowNum(), row5.getRowNum(), columnIndex, endColumnIndex);
        //所属群体
        List<GroupEntity> groupEntities = groupRepository.findByPersonIdAndSubjectId(personEntity.getId(), subjectId);
        if (groupEntities.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row6 = sheet.createRow(drawRowIndex++);
            drawCell(row6.createCell(columnIndex), "所属群体");
            mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += 2;
            drawCell(row6.createCell(columnIndex), "");
            mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += 2;
            drawCell(row6.createCell(columnIndex), "活跃程度");
            mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += 2;
            drawCell(row6.createCell(columnIndex), "");
            mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
        } else {
            for (GroupEntity groupEntity : groupEntities) {
                columnIndex = startColumnIndex;
                XSSFRow row6 = sheet.createRow(drawRowIndex++);
                drawCell(row6.createCell(columnIndex), "所属群体");
                mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += 2;
                drawCell(row6.createCell(columnIndex), groupEntity.getName());
                mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += 2;
                drawCell(row6.createCell(columnIndex), "活跃程度");
                mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += 2;
                PersonGroupRelationEntity relate = personGroupRelationRepository.findByGroupIdAndPersonId(groupEntity.getId(), personEntity.getId());
                drawCell(row6.createCell(columnIndex), Objects.isNull(relate) ? "" : dictRepository.findByTypeAndCode("ps_activity_level", relate.getActivityLevel()).getName());
                mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
            }
        }
        List<LabelAttributesEntity> attributesEntityList =
                labelRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), subjectId).stream().map(labelEntity -> labelAttributesRepository.findByLabelId(labelEntity.getId())).collect(Collectors.toList());

        List<KeyValueVO> keyValueVOList = attributesEntityList.stream()
                .filter(Objects::nonNull)
                .map(attributesEntity -> {
                            LabelAttributeValueEntity labelAttributeValueEntity = labelAttributeValueRepository.findByAttributeId(attributesEntity.getId()).orElse(new LabelAttributeValueEntity());
                            KeyValueVO keyValueVO = new KeyValueVO();
                            keyValueVO.setKey(attributesEntity.getAttributeName());
                            keyValueVO.setValue(labelAttributeValueEntity.getValue());
                            return keyValueVO;
                        }
                )
                .collect(Collectors.toList());
        if (!keyValueVOList.isEmpty()) {
            XSSFRow row7 = null;
            for (int index = 0; index < keyValueVOList.size(); index++) {
                if (index % detailAttributeNum == 0) {
                    columnIndex = startColumnIndex;
                    row7 = sheet.createRow(drawRowIndex++);
                }

                KeyValueVO keyValueVO = keyValueVOList.get(index);
                drawCell(row7.createCell(columnIndex++), keyValueVO.getKey());
                drawCell(row7.createCell(columnIndex++), keyValueVO.getValue());
                // 当最后一个属性没在所在行的最后一个时，需要进行合并避免空白单元格
                if (index == attributesEntityList.size() - 1) {
                    // 最后一个是所在行的第几个
                    int lastAttributeNum = index % detailAttributeNum + 1;
                    // 空白的，需要合并的属性数（一个属性就是一个键值对，对应两个单元格）
                    int needMergeAttributeNum = detailAttributeNum - lastAttributeNum;
                    if (needMergeAttributeNum > 0) {
                        // 最后一个属性的value的所在列
                        int firstCol = columnIndex - 1;
                        mergeCell(
                                sheet,
                                row7.getRowNum(),
                                row7.getRowNum(),
                                firstCol,
                                firstCol + needMergeAttributeNum * 2);
                    }
                }
            }
        }
        int photoStartColumn = endColumnIndex + 1;
        List<FileStorageEntity> files = fileStorageRepository.findAllByPersonIdAndModule(personEntity.getId(),
                FileTypeEnum.IMAGE.getCode(),
                FileModuleEnum.BASIC_INFO_PHOTO.getCode(),
                null);
        if (files.size() == 0) {
            byte[] photo = photoService.findPhoto(personEntity.getIdNumber());
            if (Objects.isNull(photo)) {
                drawCell(sheet.getRow(1).createCell(photoStartColumn), "照片");
                mergeCell(sheet, 1, drawRowIndex - 1, photoStartColumn, endColumnIndex + photoWidth);
            } else {
                String photoFrom = Base64.getEncoder().encodeToString(photo);
                exportPersonPhoto(sheet, drawRowIndex, endColumnIndex, photoWidth, photoStartColumn, photoFrom);
            }

        } else {
            byte[] bytes = remoteStorageService.downloadFile(files.get(0).getGroupName(), files.get(0).getPath());
            String base64 = Base64.getEncoder().encodeToString(bytes);
            exportPersonPhoto(sheet, drawRowIndex, endColumnIndex, photoWidth, photoStartColumn, base64);
        }
        columnIndex = startColumnIndex;
        XSSFRow row8 = sheet.createRow(drawRowIndex++);
        drawCell(row8.createCell(columnIndex++), "主要诉求");
        drawCell(row8.createCell(columnIndex), commonExtentEntity.getMainDemand());
        mergeCell(
                sheet,
                drawRowIndex - 1,
                drawRowIndex - 1,
                columnIndex,
                endColumnIndex + photoWidth);

        columnIndex = startColumnIndex;
        XSSFRow row9 = sheet.createRow(drawRowIndex++);
        drawCell(row9.createCell(columnIndex++), "被依法处理情况");
        drawCell(row9.createCell(columnIndex), commonExtentEntity.getTreatment());
        mergeCell(
                sheet,
                drawRowIndex - 1,
                drawRowIndex - 1,
                columnIndex,
                endColumnIndex + photoWidth);

        drawCell(sheet.getRow(basicStartRowIndex).createCell(1), "基\n本\n信\n息");
        mergeCell(sheet, 1, drawRowIndex - 1, 1, 1);
        return drawRowIndex;
    }

    private void exportPersonPhoto(XSSFSheet sheet,
                                   int drawRowIndex,
                                   int endColumnIndex,
                                   int photoWidth,
                                   int photoStartColumn,
                                   String base64Photo) {
        XSSFDrawing drawingPatriarch = sheet.createDrawingPatriarch();
        ClientAnchor anchor =
                new XSSFClientAnchor(
                        0,
                        0,
                        0,
                        0,
                        photoStartColumn,
                        1,
                        (endColumnIndex + photoWidth + 1),
                        drawRowIndex);
        drawingPatriarch.createPicture(
                anchor,
                sheet.getWorkbook()
                        .addPicture(
                                Base64.getDecoder().decode(base64Photo),
                                Workbook.PICTURE_TYPE_JPEG));
    }

    private int drawControlDepartment(
            XSSFSheet sheet, int startColumnIndex, int drawRowIndex, PersonEntity personEntity, String subjectId) {
        final int controlStartRowIndex = drawRowIndex;
        final int controlDetailAttributeWidth = 2;
        CommonExtentEntity commonExtentEntity = commonExtendRepository.findByRecordIdAndModule(personEntity.getId(), "person").orElse(new CommonExtentEntity());
        int columnIndex = startColumnIndex;
        XSSFRow row10 = sheet.createRow(drawRowIndex++);
        drawCell(row10.createCell(columnIndex++), "政府行业主管部门");
        drawCell(row10.createCell(columnIndex++), commonExtentEntity.getGovernmentDepartment());
        drawCell(row10.createCell(columnIndex), "政府责任领导/职务/联系方式");
        mergeCell(
                sheet,
                row10.getRowNum(),
                row10.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);
        columnIndex += controlDetailAttributeWidth;
        drawCell(
                row10.createCell(columnIndex),
                Optional.ofNullable(commonExtentEntity.getGovernmentLeaderName()).orElse("")
                        + "/"
                        + Optional.ofNullable(commonExtentEntity.getGovernmentLeaderJob()).orElse("")
                        + "/"
                        + Optional.ofNullable(commonExtentEntity.getGovernmentLeaderTelephone())
                        .orElse(""));
        mergeCell(
                sheet,
                row10.getRowNum(),
                row10.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);
        columnIndex += controlDetailAttributeWidth;
        drawCell(row10.createCell(columnIndex), "责任人/职务/联系方式");
        mergeCell(
                sheet,
                row10.getRowNum(),
                row10.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);
        columnIndex += controlDetailAttributeWidth;
        drawCell(
                row10.createCell(columnIndex),
                Optional.ofNullable(commonExtentEntity.getGovernmentDutyName()).orElse("")
                        + "/"
                        + Optional.ofNullable(commonExtentEntity.getGovernmentDutyJob()).orElse("")
                        + "/"
                        + Optional.ofNullable(commonExtentEntity.getGovernmentDutyTelephone())
                        .orElse(""));
        mergeCell(
                sheet,
                row10.getRowNum(),
                row10.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);

        columnIndex = startColumnIndex;
        XSSFRow row11 = sheet.createRow(drawRowIndex++);
        ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(personEntity.getId(), subjectId).orElse(new ControlEntity());
        drawCell(row11.createCell(columnIndex++), "公安机关派出所");
        drawCell(row11.createCell(columnIndex++), controlEntity.getPoliceStationName());
        drawCell(row11.createCell(columnIndex), "派出所责任领导/职务/联系方式");
        mergeCell(
                sheet,
                row11.getRowNum(),
                row11.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);
        columnIndex += controlDetailAttributeWidth;
        drawCell(
                row11.createCell(columnIndex),
                Optional.ofNullable(controlEntity.getLeaderName()).orElse("")
                        + "/"
                        + Optional.ofNullable(controlEntity.getLeaderJob()).orElse("")
                        + "/"
                        + Optional.ofNullable(controlEntity.getLeaderContact()).orElse(""));
        mergeCell(
                sheet,
                row11.getRowNum(),
                row11.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);
        columnIndex += controlDetailAttributeWidth;
        drawCell(row11.createCell(columnIndex), "责任民警/职务/联系方式");
        mergeCell(
                sheet,
                row11.getRowNum(),
                row11.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);
        columnIndex += controlDetailAttributeWidth;
        drawCell(
                row11.createCell(columnIndex),
                Optional.ofNullable(controlEntity.getResponsibleName()).orElse("")
                        + "/"
                        + Optional.ofNullable(controlEntity.getResponsibleJob()).orElse("")
                        + "/"
                        + Optional.ofNullable(controlEntity.getResponsibleContact()).orElse(""));
        mergeCell(
                sheet,
                row11.getRowNum(),
                row11.getRowNum(),
                columnIndex,
                columnIndex + controlDetailAttributeWidth - 1);

        drawCell(sheet.getRow(controlStartRowIndex).createCell(1), "管\n控\n专\n班");
        mergeCell(sheet, controlStartRowIndex, row11.getRowNum(), 1, 1);

        return drawRowIndex;
    }

    /**
     * @param relations        关系数据
     * @param startRowIndex    开始行的位置
     * @param startColumnIndex 开始列的位置
     * @param sheet            sheet页
     * @param drawRowIndex     当前绘制的位置
     * @param title            二级标题
     * @return 绘制的位置，供后面使用
     */
    private int drawPersonRelation(
            List<RelationEntity> relations,
            int startRowIndex,
            int startColumnIndex,
            XSSFSheet sheet,
            int drawRowIndex,
            String title) {
        int relationDetailAttributeWidth = 2;

        int columnIndex = startColumnIndex;
        XSSFRow row12 = sheet.createRow(drawRowIndex++);
        drawCell(row12.createCell(columnIndex), "关系");
        mergeCell(sheet, row12.getRowNum(), row12.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += relationDetailAttributeWidth;
        drawCell(row12.createCell(columnIndex), "姓名");
        mergeCell(sheet, row12.getRowNum(), row12.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += relationDetailAttributeWidth;
        drawCell(row12.createCell(columnIndex), "身份证号码");
        mergeCell(sheet, row12.getRowNum(), row12.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += relationDetailAttributeWidth;
        drawCell(row12.createCell(columnIndex), "现住地");
        mergeCell(sheet, row12.getRowNum(), row12.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += relationDetailAttributeWidth;
        drawCell(row12.createCell(columnIndex), "联系方式");
        mergeCell(sheet, row12.getRowNum(), row12.getRowNum(), columnIndex, columnIndex + 1);
        if (relations.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row13 = sheet.createRow(drawRowIndex++);
            drawCell(row13.createCell(columnIndex), "");
            mergeCell(sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += relationDetailAttributeWidth;
            drawCell(row13.createCell(columnIndex), "");
            mergeCell(sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += relationDetailAttributeWidth;
            drawCell(row13.createCell(columnIndex), "");
            mergeCell(sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += relationDetailAttributeWidth;
            drawCell(row13.createCell(columnIndex), "");
            mergeCell(sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += relationDetailAttributeWidth;
            drawCell(row13.createCell(columnIndex), "");
            mergeCell(sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
        } else {
            for (RelationEntity relation : relations) {
                columnIndex = startColumnIndex;
                XSSFRow row13 = sheet.createRow(drawRowIndex++);
                drawCell(row13.createCell(columnIndex), relation.getRelation());
                mergeCell(
                        sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += relationDetailAttributeWidth;
                drawCell(row13.createCell(columnIndex), relation.getName());
                mergeCell(
                        sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += relationDetailAttributeWidth;
                drawCell(row13.createCell(columnIndex), relation.getIdNumber());
                mergeCell(
                        sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += relationDetailAttributeWidth;
                drawCell(row13.createCell(columnIndex), relation.getCurrentResidence());
                mergeCell(
                        sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += relationDetailAttributeWidth;
                drawCell(
                        row13.createCell(columnIndex),
                        relation.getContactInformation());
                mergeCell(
                        sheet, row13.getRowNum(), row13.getRowNum(), columnIndex, columnIndex + 1);
            }
        }
        drawCell(sheet.getRow(startRowIndex).createCell(1), title);
        mergeCell(sheet, startRowIndex, drawRowIndex - 1, 1, 1);

        return drawRowIndex;
    }

    private int drawPersonCar(
            XSSFSheet sheet, int startColumnIndex, int drawRowIndex, String personId) {
        final int carStartRowIndex = drawRowIndex;
        final List<VehicleEntity> cars = vehicleRepository.findByPersonId(personId);
        int columnIndex = startColumnIndex;
        XSSFRow row14 = sheet.createRow(drawRowIndex++);
        drawCell(row14.createCell(columnIndex), "类型");
        mergeCell(sheet, row14.getRowNum(), row14.getRowNum(), columnIndex, columnIndex + 2);
        columnIndex += 3;
        drawCell(row14.createCell(columnIndex), "车牌号码");
        mergeCell(sheet, row14.getRowNum(), row14.getRowNum(), columnIndex, columnIndex + 3);
        columnIndex += 4;
        drawCell(row14.createCell(columnIndex), "车辆所有人");
        mergeCell(sheet, row14.getRowNum(), row14.getRowNum(), columnIndex, columnIndex + 2);

        if (cars.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row15 = sheet.createRow(drawRowIndex++);
            drawCell(row15.createCell(columnIndex), "");
            mergeCell(sheet, row15.getRowNum(), row15.getRowNum(), columnIndex, columnIndex + 2);
            columnIndex += 3;
            drawCell(row15.createCell(columnIndex), "");
            mergeCell(sheet, row15.getRowNum(), row15.getRowNum(), columnIndex, columnIndex + 3);
            columnIndex += 4;
            drawCell(row15.createCell(columnIndex), "");
            mergeCell(sheet, row15.getRowNum(), row15.getRowNum(), columnIndex, columnIndex + 2);
        } else {
            for (VehicleEntity car : cars) {
                columnIndex = startColumnIndex;
                XSSFRow row15 = sheet.createRow(drawRowIndex++);
                drawCell(row15.createCell(columnIndex), StringUtils.isBlank(car.getType()) ? "" : dictRepository.findByTypeAndCode("ps_vehicle_type", car.getType()).getName());
                mergeCell(
                        sheet, row15.getRowNum(), row15.getRowNum(), columnIndex, columnIndex + 2);
                columnIndex += 3;
                drawCell(row15.createCell(columnIndex), car.getVehicleNumber());
                mergeCell(
                        sheet, row15.getRowNum(), row15.getRowNum(), columnIndex, columnIndex + 3);
                columnIndex += 4;
                drawCell(row15.createCell(columnIndex), car.getOwner());
                mergeCell(
                        sheet, row15.getRowNum(), row15.getRowNum(), columnIndex, columnIndex + 2);
            }
        }

        drawCell(sheet.getRow(carStartRowIndex).createCell(0), "车");
        mergeCell(sheet, carStartRowIndex, drawRowIndex - 1, 0, 1);

        return drawRowIndex;
    }

    private int drawActivity(XSSFSheet sheet, int startColumnIndex, int drawRowIndex, String personId, String subjectId) {
        final int activityStartRowIndex = drawRowIndex;
        int columnIndex = startColumnIndex;
        XSSFRow row16 = sheet.createRow(drawRowIndex++);
        drawCell(row16.createCell(columnIndex++), "序号");
        drawCell(row16.createCell(columnIndex++), "事件名称");
        drawCell(row16.createCell(columnIndex++), "类别");
        drawCell(row16.createCell(columnIndex++), "活动时间");
        drawCell(row16.createCell(columnIndex), "敏感节点情况");
        mergeCell(sheet, row16.getRowNum(), row16.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row16.createCell(columnIndex++), "活动场所");
        drawCell(row16.createCell(columnIndex), "涉事行为");
        mergeCell(sheet, row16.getRowNum(), row16.getRowNum(), columnIndex, columnIndex + 2);

        List<EventEntity> eventsOfPerson = eventRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        if (eventsOfPerson.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row17 = sheet.createRow(drawRowIndex++);
            drawCell(row17.createCell(columnIndex++), "");
            drawCell(row17.createCell(columnIndex++), "");
            drawCell(row17.createCell(columnIndex++), "");
            drawCell(row17.createCell(columnIndex++), "");
            drawCell(row17.createCell(columnIndex), "");
            mergeCell(sheet, row17.getRowNum(), row17.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += 2;
            drawCell(row17.createCell(columnIndex++), "");
            drawCell(row17.createCell(columnIndex), "");
            mergeCell(sheet, row17.getRowNum(), row17.getRowNum(), columnIndex, columnIndex + 2);
        } else {
            for (int index = 0; index < eventsOfPerson.size(); index++) {
                EventEntity eventEntity = eventsOfPerson.get(index);
                columnIndex = startColumnIndex;
                XSSFRow row17 = sheet.createRow(drawRowIndex++);
                drawCell(row17.createCell(columnIndex++), index + 1 + "");
                drawCell(row17.createCell(columnIndex++), eventEntity.getTitle());
                LabelEntity labelEntity = labelRepository.findByEventId(eventEntity.getId());
                drawCell(row17.createCell(columnIndex++), Objects.isNull(labelEntity)
                        ? ""
                        : labelEntity.getName());
                LocalDateTime occurrenceTime = eventEntity.getOccurrenceTime();
                if (Objects.isNull(occurrenceTime)) {
                    drawCell(row17.createCell(columnIndex++), "");
                    drawCell(row17.createCell(columnIndex), "");
                } else {
                    drawCell(row17.createCell(columnIndex++), DATE_TIME_FORMATTER.format(occurrenceTime));
                    drawCell(row17.createCell(columnIndex),
                            sensitiveTimeRepository.findAllByTimeRange(occurrenceTime.toLocalDate(), occurrenceTime.toLocalDate())
                                    .stream()
                                    .map(SensitiveTimeEntity::getName)
                                    .collect(Collectors.joining(",")));
                }

                mergeCell(sheet, row17.getRowNum(), row17.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += 2;
                drawCell(row17.createCell(columnIndex++), eventEntity.getAddress());
                List<String> behaviors = eventPersonRelationRepository.findByEventIdAndPersonId(eventEntity.getId(), personId).getBehaviors().stream().map(item -> dictRepository.findByTypeAndCode("event_behavior", item).getName()).collect(Collectors.toList());
                drawCell(row17.createCell(columnIndex), String.join(",", behaviors));
                mergeCell(sheet, row17.getRowNum(), row17.getRowNum(), columnIndex, columnIndex + 2);
            }
        }

        drawCell(sheet.getRow(activityStartRowIndex).createCell(0), "活动情况");
        mergeCell(sheet, activityStartRowIndex, drawRowIndex - 1, 0, 1);

        return drawRowIndex;
    }

    private int drawClue(XSSFSheet sheet, int startColumnIndex, int drawRowIndex, String personId, String subjectId) {
        final int clueStartRowIndex = drawRowIndex;
        int columnIndex = startColumnIndex;
        XSSFRow row18 = sheet.createRow(drawRowIndex++);
        drawCell(row18.createCell(columnIndex++), "序号");
        drawCell(row18.createCell(columnIndex++), "来源");
        drawCell(row18.createCell(columnIndex), "详情");
        mergeCell(sheet, row18.getRowNum(), row18.getRowNum(), columnIndex, columnIndex + 4);
        columnIndex += 5;
        drawCell(row18.createCell(columnIndex), "处置反馈情况");
        mergeCell(sheet, row18.getRowNum(), row18.getRowNum(), columnIndex, columnIndex + 2);

        List<ClueEntity> clueList = clueRepository.findAllByPersonIdAndSubjectId(personId, subjectId);
        if (clueList.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row19 = sheet.createRow(drawRowIndex++);
            drawCell(row19.createCell(columnIndex++), "");
            drawCell(row19.createCell(columnIndex++), "");
            drawCell(row19.createCell(columnIndex), "");
            mergeCell(sheet, row19.getRowNum(), row19.getRowNum(), columnIndex, columnIndex + 4);
            columnIndex += 5;
            drawCell(row19.createCell(columnIndex), "");
            mergeCell(sheet, row19.getRowNum(), row19.getRowNum(), columnIndex, columnIndex + 2);
        } else {
            for (int i = 0; i < clueList.size(); i++) {
                ClueEntity clueEntity = clueList.get(i);
                columnIndex = startColumnIndex;
                XSSFRow row19 = sheet.createRow(drawRowIndex++);
                drawCell(row19.createCell(columnIndex++), i + 1 + "");
                drawCell(row19.createCell(columnIndex++), dictRepository.findByTypeAndCode(subjectId.equals(WW_SUBJECT) ? "ps_ww_clue_source" : "ps_clue_source", clueEntity.getSource()).getName());
                drawCell(row19.createCell(columnIndex), clueEntity.getDetail());
                mergeCell(sheet, row19.getRowNum(), row19.getRowNum(), columnIndex, columnIndex + 4);
                columnIndex += 5;
                drawCell(row19.createCell(columnIndex), StringUtils.isBlank(clueEntity.getDisposalStatus())
                        ? ""
                        : dictRepository.findByTypeAndCode("ps_clue_disposal_status", clueEntity.getDisposalStatus()).getName());
                mergeCell(sheet, row19.getRowNum(), row19.getRowNum(), columnIndex, columnIndex + 2);
            }
        }

        drawCell(sheet.getRow(clueStartRowIndex).createCell(0), "情报线索");
        mergeCell(sheet, clueStartRowIndex, drawRowIndex - 1, 0, 1);

        return drawRowIndex;
    }

    private void drawPersonControl(
            XSSFSheet sheet, int startColumnIndex, int drawRowIndex, String personId) {
        final int visitStartRowIndex = drawRowIndex;
        int columnIndex = startColumnIndex;
        XSSFRow row20 = sheet.createRow(drawRowIndex++);
        drawCell(row20.createCell(columnIndex++), "走访时间");
        drawCell(row20.createCell(columnIndex++), "走访方式");
        drawCell(row20.createCell(columnIndex++), "是否在控");
        drawCell(row20.createCell(columnIndex++), "失控时间");
        drawCell(row20.createCell(columnIndex++), "当前去向");
        drawCell(row20.createCell(columnIndex), "走访情况");
        mergeCell(sheet, row20.getRowNum(), row20.getRowNum(), columnIndex, columnIndex + 3);
        columnIndex += 4;
        drawCell(row20.createCell(columnIndex), "走访工作人员");
        List<VisitRecordEntity> visitRecordList = visitRecordRepository.findByPersonIdOrderByTimeDesc(personId);
        if (visitRecordList.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row21 = sheet.createRow(drawRowIndex++);
            drawCell(row21.createCell(columnIndex++), "");
            drawCell(row21.createCell(columnIndex++), "");
            drawCell(row21.createCell(columnIndex++), "");
            drawCell(row21.createCell(columnIndex++), "");
            drawCell(row21.createCell(columnIndex++), "");
            drawCell(row21.createCell(columnIndex), "");
            mergeCell(sheet, row21.getRowNum(), row21.getRowNum(), columnIndex, columnIndex + 3);
            columnIndex += 4;
            drawCell(row21.createCell(columnIndex), "");
        } else {
            for (VisitRecordEntity visitRecordEntity : visitRecordList) {
                columnIndex = startColumnIndex;
                XSSFRow row21 = sheet.createRow(drawRowIndex++);
                drawCell(
                        row21.createCell(columnIndex++),
                        Objects.nonNull(visitRecordEntity.getTime())
                                ? DATE_TIME_FORMATTER.format(visitRecordEntity.getTime())
                                : "");
                drawCell(row21.createCell(columnIndex++), dictRepository.findByTypeAndCode("ps_person_visit_method", visitRecordEntity.getMethod()).getName());
                drawCell(row21.createCell(columnIndex++), dictRepository.findByTypeAndCode("ps_person_in_control", visitRecordEntity.getInControl()).getName());
                drawCell(row21.createCell(columnIndex++), Objects.isNull(visitRecordEntity.getOutOfControlTime())
                        ? "" : DATE_FORMATTER.format(visitRecordEntity.getOutOfControlTime()));
                drawCell(row21.createCell(columnIndex++), visitRecordEntity.getDestination());
                drawCell(row21.createCell(columnIndex), visitRecordEntity.getInfo());
                mergeCell(
                        sheet, row21.getRowNum(), row21.getRowNum(), columnIndex, columnIndex + 3);
                columnIndex += 4;
                drawCell(row21.createCell(columnIndex), visitRecordEntity.getVisitBy());
            }
        }

        drawCell(sheet.getRow(visitStartRowIndex).createCell(1), "走访工作记录");
        mergeCell(sheet, visitStartRowIndex, drawRowIndex - 1, 1, 1);

        drawCell(sheet.getRow(visitStartRowIndex).createCell(0), "人员管控情况");
        mergeCell(sheet, visitStartRowIndex, drawRowIndex - 1, 0, 0);
    }

    private int drawGroupBasic(
            XSSFSheet sheet, int startColumnIndex, int drawRowIndex, GroupEntity groupEntity) {
        // 基本信息的开始行
        final int basicStartRowIndex = drawRowIndex;
        // 详细属性的结束列
        int endColumnIndex = startColumnIndex + 9;

        int columnIndex = startColumnIndex;
        XSSFRow row1 = sheet.createRow(drawRowIndex++);
        drawCell(row1.createCell(columnIndex++), "群体类型");
        drawCell(
                row1.createCell(columnIndex),
                labelRepository.findByGroupIdAndSubjectId(groupEntity.getId(), WW_SUBJECT).stream()
                        .map(LabelEntity::getName)
                        .collect(Collectors.joining(",")));
        mergeCell(sheet, row1.getRowNum(), row1.getRowNum(), columnIndex, endColumnIndex);

        columnIndex = startColumnIndex;
        XSSFRow row2 = sheet.createRow(drawRowIndex++);
        drawCell(row2.createCell(columnIndex++), "基本情况");
        drawCell(row2.createCell(columnIndex), groupEntity.getBasicInfo());
        mergeCell(sheet, row2.getRowNum(), row2.getRowNum(), columnIndex, endColumnIndex);

        columnIndex = startColumnIndex;
        XSSFRow row3 = sheet.createRow(drawRowIndex++);
        drawCell(row3.createCell(columnIndex++), "主要诉求");
        CommonExtentEntity commonExtentEntity = commonExtendRepository.findByRecordIdAndModule(groupEntity.getId(), CommonExtentEntity.GROUP).orElse(new CommonExtentEntity());
        drawCell(row3.createCell(columnIndex), commonExtentEntity.getMainDemand());
        mergeCell(sheet, row3.getRowNum(), row3.getRowNum(), columnIndex, endColumnIndex);

        columnIndex = startColumnIndex;
        XSSFRow row4 = sheet.createRow(drawRowIndex++);
        drawCell(row4.createCell(columnIndex++), "政府行业主管部门");
        drawCell(row4.createCell(columnIndex), commonExtentEntity.getGovernmentDepartment());
        mergeCell(sheet, row4.getRowNum(), row4.getRowNum(), columnIndex, endColumnIndex);

        columnIndex = startColumnIndex;
        XSSFRow row5 = sheet.createRow(drawRowIndex++);
        drawCell(row5.createCell(columnIndex++), "政府牵头领导");
        drawCell(row5.createCell(columnIndex++), "姓名");
        drawCell(row5.createCell(columnIndex), commonExtentEntity.getGovernmentLeaderName());
        mergeCell(sheet, row5.getRowNum(), row5.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row5.createCell(columnIndex++), "职务");
        drawCell(row5.createCell(columnIndex), commonExtentEntity.getGovernmentLeaderJob());
        mergeCell(sheet, row5.getRowNum(), row5.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row5.createCell(columnIndex++), "联系电话");
        drawCell(row5.createCell(columnIndex), commonExtentEntity.getGovernmentLeaderTelephone());
        mergeCell(sheet, row5.getRowNum(), row5.getRowNum(), columnIndex, columnIndex + 1);

        columnIndex = startColumnIndex;
        XSSFRow row6 = sheet.createRow(drawRowIndex++);
        drawCell(row6.createCell(columnIndex++), "政府具体责任人");
        drawCell(row6.createCell(columnIndex++), "姓名");
        drawCell(row6.createCell(columnIndex), commonExtentEntity.getGovernmentDutyName());
        mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row6.createCell(columnIndex++), "职务");
        drawCell(row6.createCell(columnIndex), commonExtentEntity.getGovernmentDutyJob());
        mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row6.createCell(columnIndex++), "联系电话");
        drawCell(row6.createCell(columnIndex), commonExtentEntity.getGovernmentDutyTelephone());
        mergeCell(sheet, row6.getRowNum(), row6.getRowNum(), columnIndex, columnIndex + 1);

        drawCell(sheet.getRow(basicStartRowIndex).createCell(0), "群体基本情况");
        mergeCell(sheet, basicStartRowIndex, drawRowIndex - 1, 0, 0);
        return drawRowIndex;
    }

    private int drawGroupActivity(XSSFSheet sheet, int startColumnIndex, int drawRowIndex, GroupEntity groupEntity) {
        final int activityStartRow = drawRowIndex;
        int columnIndex = startColumnIndex;
        XSSFRow row7 = sheet.createRow(drawRowIndex++);
        drawCell(row7.createCell(columnIndex++), "事件名称");
        drawCell(row7.createCell(columnIndex++), "活动时间");
        drawCell(row7.createCell(columnIndex++), "敏感节点情况");
        drawCell(row7.createCell(columnIndex++), "活动场所");
        drawCell(row7.createCell(columnIndex), "事件内容");
        mergeCell(sheet, row7.getRowNum(), row7.getRowNum(), columnIndex, columnIndex + 2);
        columnIndex += 3;
        drawCell(row7.createCell(columnIndex), "事件具体处置情况");
        mergeCell(sheet, row7.getRowNum(), row7.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row7.createCell(columnIndex), "有无证据资料");

        List<EventEntity> eventOfGroup = eventRepository.findAllByGroupId(groupEntity.getId());
        if (eventOfGroup.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row8 = sheet.createRow(drawRowIndex++);
            drawCell(row8.createCell(columnIndex++), "");
            drawCell(row8.createCell(columnIndex++), "");
            drawCell(row8.createCell(columnIndex++), "");
            drawCell(row8.createCell(columnIndex++), "");
            drawCell(row8.createCell(columnIndex), "");
            mergeCell(sheet, row8.getRowNum(), row8.getRowNum(), columnIndex, columnIndex + 2);
            columnIndex += 3;
            drawCell(row8.createCell(columnIndex), "");
            mergeCell(sheet, row8.getRowNum(), row8.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += 2;
            drawCell(row8.createCell(columnIndex), "");
        } else {
            for (EventEntity eventEntity : eventOfGroup) {
                columnIndex = startColumnIndex;
                XSSFRow row8 = sheet.createRow(drawRowIndex++);
                drawCell(row8.createCell(columnIndex++), eventEntity.getTitle());
                LocalDateTime occurrenceTime = eventEntity.getOccurrenceTime();
                if (Objects.isNull(occurrenceTime)) {
                    drawCell(row8.createCell(columnIndex++), "");
                    drawCell(row8.createCell(columnIndex++), "");
                } else {
                    drawCell(row8.createCell(columnIndex++), DATE_TIME_FORMATTER.format(occurrenceTime));
                    drawCell(row8.createCell(columnIndex++),
                            sensitiveTimeRepository.findAllByTimeRange(occurrenceTime.toLocalDate(),
                                    occurrenceTime.toLocalDate())
                                    .stream()
                                    .map(SensitiveTimeEntity::getName)
                                    .collect(Collectors.joining(",")));
                }
                drawCell(row8.createCell(columnIndex++), eventEntity.getAddress());
                drawCell(row8.createCell(columnIndex), eventEntity.getContent());
                mergeCell(sheet, row8.getRowNum(), row8.getRowNum(), columnIndex, columnIndex + 2);
                columnIndex += 3;
                drawCell(row8.createCell(columnIndex), Objects.isNull(eventEntity.getDisposalStatus())
                        ? ""
                        : dictRepository.findByTypeAndCode("ps_event_disposal_status", eventEntity.getDisposalStatus()).getName());
                mergeCell(sheet, row8.getRowNum(), row8.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += 2;

                drawCell(row8.createCell(columnIndex),
                        fileStorageRepository.findAllByEventId(eventEntity.getId()).isEmpty() ? "无" : "有");
            }
        }

        drawCell(sheet.getRow(activityStartRow).createCell(0), "群体活动情况");
        mergeCell(sheet, activityStartRow, drawRowIndex - 1, 0, 0);
        return drawRowIndex;
    }

    private int drawGroupMembership(
            XSSFSheet sheet, int startColumnIndex, int drawRowIndex, String groupId) {
        List<PersonEntity> memberships = personRepository.findAllByGroupId(groupId);
        final int membershipStartRow = drawRowIndex;

        int columnIndex = startColumnIndex;
        XSSFRow row9 = sheet.createRow(drawRowIndex++);
        drawCell(row9.createCell(columnIndex++), "群体成员总数");
        drawCell(row9.createCell(columnIndex++), Integer.toString(memberships.size()));
        List<DictEntity> allDistrict = dictRepository.findAllByType("district");
        drawCell(row9.createCell(columnIndex++), "涉及区县");
        drawCell(
                row9.createCell(columnIndex),
                memberships.stream()
                        .map(person -> {
                            ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(person.getId(), WW_SUBJECT).orElse(null);
                            if (Objects.isNull(controlEntity) || StringUtils.isBlank(controlEntity.getPoliceStationCode())) {
                                return null;
                            }
                            String personDistrict = controlEntity.getPoliceStationCode().substring(0, 6);
                            return allDistrict.stream()
                                    .filter(dictEntity -> dictEntity.getCode().equals(personDistrict))
                                    .findFirst()
                                    .orElse(new DictEntity())
                                    .getName();
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(",")));
        mergeCell(sheet, row9.getRowNum(), row9.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        List<String> personId = personGroupRelationRepository.findAllByGroupId(groupId).stream().filter(item -> StringUtils.isNotBlank(item.getActivityLevel()) && "3".equals(item.getActivityLevel())).map(PersonGroupRelationEntity::getPersonId).collect(Collectors.toList());
        List<PersonEntity> leaders = memberships.stream()
                .filter(item -> personId.contains(item.getId()))
                .collect(Collectors.toList());
        drawCell(row9.createCell(columnIndex++), "挑头人员数");
        drawCell(row9.createCell(columnIndex++), Integer.toString(leaders.size()));
        drawCell(row9.createCell(columnIndex++), "涉及区县");
        drawCell(
                row9.createCell(columnIndex),
                leaders.stream()
                        .map(personVO -> {
                            ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(personVO.getId(), WW_SUBJECT).orElse(null);
                            if (Objects.isNull(controlEntity) || StringUtils.isBlank(controlEntity.getPoliceStationCode())) {
                                return null;
                            }
                            String personDistrict = controlEntity.getPoliceStationCode().substring(0, 6);
                            return allDistrict.stream()
                                    .filter(dictEntity -> dictEntity.getCode().equals(personDistrict))
                                    .findFirst()
                                    .orElse(new DictEntity())
                                    .getName();
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(",")));
        mergeCell(sheet, row9.getRowNum(), row9.getRowNum(), columnIndex, columnIndex + 1);

        columnIndex = startColumnIndex;
        XSSFRow row10 = sheet.createRow(drawRowIndex++);
        drawCell(row10.createCell(columnIndex++), "姓名");
        drawCell(row10.createCell(columnIndex), "身份证号码");
        mergeCell(sheet, row10.getRowNum(), row10.getRowNum(), columnIndex, columnIndex + 1);
        columnIndex += 2;
        drawCell(row10.createCell(columnIndex++), "人员类型");
        drawCell(row10.createCell(columnIndex++), "职业");
        drawCell(row10.createCell(columnIndex++), "责任区县");
        drawCell(row10.createCell(columnIndex++), "参与事件数");
        drawCell(row10.createCell(columnIndex++), "活跃程度");
        drawCell(row10.createCell(columnIndex++), "是否列控");
        drawCell(row10.createCell(columnIndex), "涉事行为");

        if (memberships.isEmpty()) {
            columnIndex = startColumnIndex;
            XSSFRow row11 = sheet.createRow(drawRowIndex++);
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex), "");
            mergeCell(
                    sheet, row11.getRowNum(), row11.getRowNum(), columnIndex, columnIndex + 1);
            columnIndex += 2;
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex++), "");
            drawCell(row11.createCell(columnIndex), "");
        } else {
            for (PersonEntity person : memberships) {
                columnIndex = startColumnIndex;
                XSSFRow row11 = sheet.createRow(drawRowIndex++);
                drawCell(row11.createCell(columnIndex++), person.getName());
                drawCell(row11.createCell(columnIndex), person.getIdNumber());
                mergeCell(
                        sheet, row11.getRowNum(), row11.getRowNum(), columnIndex, columnIndex + 1);
                columnIndex += 2;
                List<LabelEntity> labelEntityList = labelRepository.findAllByPersonIdAndSubjectId(person.getId(), WW_SUBJECT);
                drawCell(row11.createCell(columnIndex++), labelEntityList.isEmpty()
                        ? ""
                        : labelEntityList.stream().map(LabelEntity::getName).collect(Collectors.joining(",")));
                drawCell(row11.createCell(columnIndex++), person.getCurrentJob());
                drawCell(row11.createCell(columnIndex++), allDistrict.stream()
                        .filter(dictEntity -> dictEntity.getCode().equals(getControlStationCode(person.getId())))
                        .findFirst()
                        .orElse(new DictEntity())
                        .getName());
                List<EventPersonRelationEntity> eventsOfPerson = eventPersonRelationRepository.findAllByPersonId(person.getId());
                drawCell(row11.createCell(columnIndex++), Integer.toString(eventsOfPerson.size()));
                PersonGroupRelationEntity relate = personGroupRelationRepository.findByGroupIdAndPersonId(groupId, person.getId());

                drawCell(
                        row11.createCell(columnIndex++), StringUtils.isBlank(relate.getActivityLevel()) ? "" : dictRepository.findByTypeAndCode("ps_activity_level", relate.getActivityLevel()).getName());
                drawCell(row11.createCell(columnIndex++), dictRepository.findByTypeAndCode("ps_control_status", person.getControlStatus()).getName());
                drawCell(row11.createCell(columnIndex), eventsOfPerson.stream()
                        .map(EventPersonRelationEntity::getBehaviors)
                        .flatMap(List::stream)
                        .distinct()
                        .map(behavior -> dictRepository.findByTypeAndCode("event_behavior", behavior).getName())
                        .collect(Collectors.joining(",")));
            }
        }
        drawCell(sheet.getRow(membershipStartRow).createCell(0), "群体成员情况");
        mergeCell(sheet, membershipStartRow, drawRowIndex - 1, 0, 0);
        return drawRowIndex;
    }

    @Override
    public XSSFWorkbook getEventExcel(String eventId, String subjectId) {
        final XSSFWorkbook workbook = new XSSFWorkbook();
        EventEntity eventEntity = eventRepository.findById(eventId).orElse(null);
        if (Objects.isNull(eventEntity)) {
            return workbook;
        }

        final Sheet sheet = workbook.createSheet();

        final Function<Sheet, CellStyle> styleFunction =
                s -> {
                    final CellStyle cellStyle = s.getWorkbook().createCellStyle();

                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);

                    cellStyle.setBorderBottom(BorderStyle.THIN);
                    cellStyle.setBottomBorderColor(
                            HSSFColor.HSSFColorPredefined.BLACK.getIndex());
                    cellStyle.setBorderTop(BorderStyle.THIN);
                    cellStyle.setTopBorderColor(HSSFColor.HSSFColorPredefined.BLACK.getIndex());
                    cellStyle.setBorderLeft(BorderStyle.THIN);
                    cellStyle.setLeftBorderColor(
                            HSSFColor.HSSFColorPredefined.BLACK.getIndex());
                    cellStyle.setBorderRight(BorderStyle.THIN);
                    cellStyle.setRightBorderColor(
                            HSSFColor.HSSFColorPredefined.BLACK.getIndex());
                    cellStyle.setWrapText(true);

                    final Font font = s.getWorkbook().createFont();
                    font.setFontHeightInPoints((short) 11);
                    font.setFontName("宋体");
                    cellStyle.setFont(font);
                    return cellStyle;
                };

        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        fillCell(sheet, "群体事件档案", 0, 'A', 0, 'L', styleFunction);
        fillCell(sheet, "事件名称", 1, 'b', 1, 'c', styleFunction);
        fillCell(sheet, eventEntity.getTitle(), 1, 'd', 1, 'l', styleFunction);

        fillCell(sheet, "来源部门", 2, 'C', styleFunction);
        fillCell(sheet, "", 2, 'D', 2, 'E', styleFunction);
        fillCell(sheet, "来源时间", 2, 'F', styleFunction);
        fillCell(sheet, "", 2, 'G', 2, 'H', styleFunction);
        fillCell(sheet, "有关要求", 2, 'i', styleFunction);
        fillCell(sheet, eventEntity.getClaim(), 2, 'J', 2, 'L', styleFunction);

        fillCell(sheet, "事件(预)发生时间", 3, 'C', styleFunction);
        fillCell(sheet, eventEntity.getOccurrenceTime().format(formatter), 3, 'd', 3, 'f', styleFunction);
        fillCell(sheet, "事件(预)发生场所", 3, 'G', 3, 'H', styleFunction);
        fillCell(sheet, eventEntity.getAddress(), 3, 'i', 3, 'l', styleFunction);

        fillCell(sheet, "事件内容", 4, 'C', styleFunction);
        fillCell(sheet, eventEntity.getContent(), 4, 'D', 4, 'l', styleFunction);

        fillCell(sheet, "涉事群体", 5, 'B', styleFunction);

        final String groupNames =
                groupRepository.findAllByEventId(eventId).stream()
                        .map(GroupEntity::getName)
                        .collect(Collectors.joining(","));
        fillCell(sheet, groupNames, 5, 'C', 5, 'l', styleFunction);

        fillCell(sheet, "事件基本情况", 1, 'A', 5, 'a', styleFunction);

        final List<PersonEntity> personOfEvent = personRepository.findAllByEventId(eventId);

        final List<DictEntity> allDistrict = dictRepository.findAllByType("district");

        final Map<String, String> eventBehaviorDict =
                dictRepository.findAllByType("event_behavior").stream()
                        .collect(Collectors.toMap(DictEntity::getCode, DictEntity::getName));

        final Map<String, String> personBehaviorMap =
                eventPersonRelationRepository.findAllByEventId(eventId).stream()
                        .collect(
                                Collectors.toMap(
                                        EventPersonRelationEntity::getPersonId,
                                        ep ->
                                                ep.getBehaviors().stream()
                                                        .map(eventBehaviorDict::get)
                                                        .collect(Collectors.joining(","))));

        fillCell(sheet, "事件人员总数", 6, 'B', 6, 'C', styleFunction);
        fillCell(sheet, "" + personOfEvent.size(), 6, 'D', styleFunction);
        fillCell(sheet, "涉及区县", 6, 'e', styleFunction);
        fillCell(sheet, personOfEvent.stream().map(person -> {

                    String personDistrict = getControlStationCode(person.getId());
                    return allDistrict.stream()
                            .filter(dictEntity -> dictEntity.getCode().equals(personDistrict))
                            .findFirst()
                            .orElse(new DictEntity())
                            .getName();
                })
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(",")),
                6,
                'f',
                6,
                'l',
                styleFunction);

        fillCell(sheet, "姓名", 7, 'b', styleFunction);
        fillCell(sheet, "身份证号码", 7, 'c', 7, 'd', styleFunction);
        fillCell(sheet, "责任区县", 7, 'e', styleFunction);
        fillCell(sheet, "涉事行为", 7, 'f', styleFunction);
        fillCell(sheet, "是否列控", 7, 'g', styleFunction);

        for (int i = 0; i < personOfEvent.size(); i++) {
            fillCell(sheet, personOfEvent.get(i).getName(), 7 + i + 1, 'b', styleFunction);
            fillCell(
                    sheet,
                    personOfEvent.get(i).getIdNumber(),
                    7 + i + 1,
                    'c',
                    7 + i + 1,
                    'd',
                    styleFunction);

            String personDistrict = getControlStationCode(personOfEvent.get(i).getId());
            fillCell(
                    sheet,
                    allDistrict.stream().filter(dictEntity -> dictEntity.getCode().equals(personDistrict)).findFirst().orElse(new DictEntity()).getName(),
                    7 + i + 1,
                    'e',
                    styleFunction);
            fillCell(
                    sheet,
                    personBehaviorMap.getOrDefault(personOfEvent.get(i).getId(), ""),
                    7 + i + 1,
                    'f',
                    styleFunction);
            fillCell(
                    sheet,
                    dictRepository.findByTypeAndCode("ps_control_status", personOfEvent.get(i).getControlStatus()).getName(),
                    7 + i + 1,
                    'g',
                    styleFunction);
        }

        fillCell(sheet, "参与人员情况", 6, 'A', 7 + personOfEvent.size(), 'A', styleFunction);

        // 准备参与部门、指令相关数据
        List<BattleCommandEntity> commands = battleCommandRepository.findCommandById("system.t_event", eventId);
        final int commandCount = commands.size();
        final int commandReplyCount = eventRepository.findCommandReply(eventId);
        Set<String> unitGroup =
                commands.stream()
                        .map(BattleCommandEntity::getUnitName)
                        .collect(Collectors.toSet());
        int unitCount = unitGroup.size();
        final String unitList = String.join(",", unitGroup);
        fillCell(sheet, "参与部门情况", 8 + personOfEvent.size(), 'B', 8 + personOfEvent.size(), 'c', styleFunction);
        fillCell(sheet, "参与部门数", 8 + personOfEvent.size(), 'd', styleFunction);
        fillCell(sheet, String.valueOf(unitCount), 8 + personOfEvent.size(), 'e', styleFunction);
        fillCell(sheet, "具体部门名单", 8 + personOfEvent.size(), 'f', 8 + personOfEvent.size(), 'g', styleFunction);
        fillCell(sheet, unitList, 8 + personOfEvent.size(), 'h', 8 + personOfEvent.size(), 'l', styleFunction);
        fillCell(sheet, "指令流转情况", 9 + personOfEvent.size(), 'b', 9 + personOfEvent.size(), 'c', styleFunction);
        fillCell(sheet, "下发指令数", 9 + personOfEvent.size(), 'd', styleFunction);
        fillCell(sheet, String.valueOf(commandCount), 9 + personOfEvent.size(), 'e', styleFunction);
        fillCell(sheet, "反馈指令数", 9 + personOfEvent.size(), 'f', 9 + personOfEvent.size(), 'g', styleFunction);
        fillCell(sheet, String.valueOf(commandReplyCount), 9 + personOfEvent.size(), 'h', 9 + personOfEvent.size(), 'l', styleFunction);
        fillCell(sheet, "具体处置情况", 10 + personOfEvent.size(), 'b', 10 + personOfEvent.size(), 'c', styleFunction);
        // 事件相关合成
        final List<BattleRecordEntity> battles = battleRecordRepository.findRecordById("system.t_event", eventId);


        final String battleInfos = battles.stream()
                .map(b -> StringUtils.join(new String[]{"合成:", b.getTitle(), b.getUnitname(), b.getCrtbyname(), b.getCrttime().format(formatter)}, " "))
                .collect(Collectors.joining("\n"));
        final String commandInfos = commands.stream().map(b -> StringUtils.join(new String[]{"指令:", b.getTitle(), b.getUnitName(), b.getCrtbyname(), b.getCrttime().format(formatter)}, " "))
                .collect(Collectors.joining("\n"));

        fillCell(sheet, battleInfos + "\n" + commandInfos, 10 + personOfEvent.size(), 'd', 10 + personOfEvent.size(), 'l', styleFunction);
        List<FileStorageEntity> appendixOfEvent = fileStorageRepository.findAllByEventId(eventId);
        fillCell(sheet, "视频材料", 11 + personOfEvent.size(), 'c', styleFunction);
        fillCell(sheet, appendixOfEvent.stream().filter(fileStorageEntity -> "0".equals(fileStorageEntity.getType())).map(FileStorageEntity::getName).collect(Collectors.joining(",")), 11 + personOfEvent.size(), 'd', 11 + personOfEvent.size(), 'l', styleFunction);
        fillCell(sheet, "文档材料", 12 + personOfEvent.size(), 'c', styleFunction);
        fillCell(sheet, appendixOfEvent.stream().filter(fileStorageEntity -> "1".equals(fileStorageEntity.getType())).map(FileStorageEntity::getName).collect(Collectors.joining(",")), 12 + personOfEvent.size(), 'd', 12 + personOfEvent.size(), 'l', styleFunction);
        fillCell(sheet, "图片材料", 13 + personOfEvent.size(), 'c', styleFunction);
        fillCell(sheet, appendixOfEvent.stream().filter(fileStorageEntity -> "2".equals(fileStorageEntity.getType())).map(FileStorageEntity::getName).collect(Collectors.joining(",")), 13 + personOfEvent.size(), 'd', 13 + personOfEvent.size(), 'l', styleFunction);
        fillCell(sheet, "材料收集情况", 11 + personOfEvent.size(), 'B', 13 + personOfEvent.size(), 'B', styleFunction);
        fillCell(sheet, "事件处置情况", 8 + personOfEvent.size(), 'a', 13 + personOfEvent.size(), 'a', styleFunction);
        fillCell(sheet, "其他情况", 14 + personOfEvent.size(), 'a', 14 + personOfEvent.size(), 'b', styleFunction);
        fillCell(sheet, "", 14 + personOfEvent.size(), 'c', 14 + personOfEvent.size(), 'l', styleFunction);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            final Row row = sheet.getRow(i);
            row.setHeightInPoints(28);
        }
        sheet.getRow(0).setHeightInPoints(36);
        sheet.getRow(4).setHeightInPoints(98);
        return workbook;
    }

    /**
     * 向excel中单元格填充内容
     *
     * @param sheet         单元格
     * @param value         值
     * @param row           行
     * @param col           列
     * @param styleFunction excel格式
     */
    private static void fillCell(Sheet sheet, String value, int row, char col, Function<Sheet, CellStyle> styleFunction) {
        fillCell(sheet, value, row, col, row, col, styleFunction);
    }

    /**
     * 向excel中单元格填充内容
     *
     * @param sheet         单元格
     * @param value         值
     * @param startRow      起始行
     * @param startCol      起始列
     * @param endRow        结束行
     * @param endCol        结束列
     * @param styleFunction 样式
     */
    private static void fillCell(Sheet sheet, String value, int startRow, char startCol, int endRow, char endCol, Function<Sheet, CellStyle> styleFunction) {

        int startColNum = (StringUtils.upperCase(startCol + "").charAt(0) - 65);
        final int endColNum = (StringUtils.upperCase(endCol + "").charAt(0) - 65);

        final CellStyle cellStyle = styleFunction.apply(sheet);

        Row row = sheet.getRow(startRow);
        if (row == null) {
            row = sheet.createRow(startRow);
        }

        Cell cell = row.getCell(startColNum);
        if (cell == null) {
            cell = row.createCell(startColNum);
        }

        cell.setCellValue(StringUtils.trimToEmpty(value));
        cell.setCellStyle(cellStyle);

        if (endRow > startRow || endColNum > startColNum) {
            final CellRangeAddress cellRangeAddress =
                    new CellRangeAddress(startRow, endRow, startColNum, endColNum);
            RegionUtil.setBorderTop(cellStyle.getBorderTop(), cellRangeAddress, sheet);
            RegionUtil.setBorderRight(cellStyle.getBorderRight(), cellRangeAddress, sheet);
            RegionUtil.setBorderBottom(cellStyle.getBorderBottom(), cellRangeAddress, sheet);
            RegionUtil.setBorderLeft(cellStyle.getBorderLeft(), cellRangeAddress, sheet);
            sheet.addMergedRegion(cellRangeAddress);
        }
    }

    private String getControlStationCode(String personId) {
        ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(personId, WW_SUBJECT).orElse(null);
        if (Objects.isNull(controlEntity) || StringUtils.isBlank(controlEntity.getPoliceStationCode())) {
            return null;
        }
        return controlEntity.getPoliceStationCode().substring(0, 6);
    }
}
