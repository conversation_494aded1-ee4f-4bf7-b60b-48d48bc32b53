package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningPushLogEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * 预警推送配置持久层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/08
 **/
@Repository
public interface WarningPushLogRepository extends BaseRepository<WarningPushLogEntity, String> {

    /**
     * 查询今日是否已有推送记录
     *
     * @param warningType 预警类型
     * @param subjectId   专题id
     * @param idType      id类型
     * @param idValue     id值
     * @return 0/1  是否存在
     */
    @Query("select count(w) from WarningPushLogEntity w where to_char(w.time, 'yyyy-mm-dd') = to_char(sysdate, 'yyyy-mm-dd') and w.warningType = :warningType and w.subjectId = :subjectId and w.idType = :idType and w.idValue = :idValue")
    Integer findIfExistedInToday(String warningType, String subjectId, String idType, String idValue);

}
