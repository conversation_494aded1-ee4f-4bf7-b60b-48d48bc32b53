package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.BankCardEntity;

import java.util.List;

/**
 * 银行卡信息持久层
 *
 * <AUTHOR>
 */
public interface BankCardRepository extends BaseRepository<BankCardEntity,String>{

    /**
     * 查询人员银行卡信息
     *
     * @param personId 人员id
     * @return 人员基本信息 {@link BankCardEntity}
     * <AUTHOR>
     */
    List<BankCardEntity> findAllByPersonId(String personId);

    /**
     * 根据人员id和银行卡查询该信息是否出现
     *
     * @param personId 人员id
     * @param bankCardNumber 银行卡卡号
     * @return 是否存在
     */
    Boolean existsByPersonIdAndBankCardNumber(String personId, String bankCardNumber);
}
