package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.AssignEntity;
import com.trs.yq.police.subject.domain.entity.ControlEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.entity.UserEntity;
import com.trs.yq.police.subject.domain.vo.BureauVO;
import com.trs.yq.police.subject.domain.vo.ControlVO;
import com.trs.yq.police.subject.domain.vo.PoliceVO;
import com.trs.yq.police.subject.domain.vo.UnitVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.AssignRepository;
import com.trs.yq.police.subject.repository.ControlRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.repository.UserRepository;
import com.trs.yq.police.subject.service.ControlService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_PERSON;

/**
 * 管控信息业务层接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ControlServiceImpl implements ControlService {


    @Resource
    private ControlRepository controlRepository;

    @Resource
    private AssignRepository assignRepository;

    @Resource
    private UnitRepository unitRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private PersonService personService;

    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public ControlVO getControl(String personId, String subjectId) {
        personService.checkPersonExist(personId);
        ControlVO controlVO = new ControlVO();
        ControlEntity controlEntity = controlRepository.findByPersonIdAndSubjectId(personId, subjectId).orElse(null);
        if (Objects.nonNull(controlEntity)) {
            List<String> codeList = new ArrayList<>();
            UnitEntity unit = unitRepository.findByUnitCode(controlEntity.getPoliceStationCode());
            if (Objects.nonNull(unit)) {
                UnitEntity parentUnit = unitRepository.findById(unit.getParentId()).orElse(null);
                if (Objects.nonNull(parentUnit)) {
                    codeList.add(parentUnit.getUnitCode());
                }
                codeList.add(unit.getUnitCode());
            }

            controlVO.setPoliceStationCode(codeList);
            BeanUtil.copyPropertiesIgnoreNull(controlEntity, controlVO);
        }
        return controlVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateControl(String personId, String subjectId, ControlVO controlVO) {
        personService.checkPersonExist(personId);
        ControlEntity controlEntity = getControlEntity(personId, subjectId);
        ControlVO oldVO = new ControlVO();
        BeanUtil.copyPropertiesIgnoreNull(controlEntity, oldVO);

        String policeStationCode = controlVO.getPoliceStationCode().get(controlVO.getPoliceStationCode().size() - 1);
        controlEntity.setPoliceStationCode(policeStationCode);
        controlEntity.setPersonId(personId);
        controlEntity.setSubjectId(subjectId);
        BeanUtil.copyPropertiesIgnoreNull(controlVO, controlEntity);
        controlRepository.save(controlEntity);

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.CONTROL_INFO)
                .oldObj(JsonUtil.toJsonString(oldVO))
                .newObj(JsonUtil.toJsonString(controlVO))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personId)
                .desc("修改管控信息")
                .targetObjectType(OPERATION_LOG_TARGET_PERSON)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }

    }

    @Override
    public BureauVO getBureau(String personId, String subjectId) {
        personService.checkPersonExist(personId);
        BureauVO bureauVO = new BureauVO();
        AssignEntity assignEntity = getAssignEntity(personId, subjectId);
        if (Objects.nonNull(assignEntity)) {
            BeanUtil.copyPropertiesIgnoreNull(assignEntity, bureauVO);
        }
        return bureauVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void assignBureau(String personId, String subjectId, BureauVO bureauVO) {
        personService.checkPersonExist(personId);
        AssignEntity original = getAssignEntity(personId, subjectId);
        if (Objects.nonNull(original)) {
            BeanUtil.copyPropertiesIgnoreNull(bureauVO, original);
            assignRepository.save(original);
        } else {
            AssignEntity assignEntity = new AssignEntity();
            assignEntity.setPersonId(personId);
            assignEntity.setSubjectId(subjectId);
            assignEntity.setBureauCode(bureauVO.getBureauCode());
            assignEntity.setBureauName(bureauVO.getBureauName());
            assignRepository.save(assignEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void assignPoliceStation(String personId, String subjectId, ControlVO controlVO) {
        personService.checkPersonExist(personId);
        if (Objects.isNull(getAssignEntity(personId, subjectId))) {
            throw new ParamValidationException("该人员档案还未被分配到分局，请核实！");
        }
        ControlEntity original = getControlEntity(personId, subjectId);
        if (Objects.nonNull(original)) {
            BeanUtil.copyPropertiesIgnoreNull(controlVO, original);
            controlRepository.save(original);
        } else {
            ControlEntity controlEntity = new ControlEntity();
            BeanUtil.copyPropertiesIgnoreNull(controlVO, controlEntity);
            controlEntity.setPersonId(personId);
            controlEntity.setSubjectId(subjectId);
            controlRepository.save(controlEntity);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void assignPolice(String personId, String subjectId, ControlVO controlVO) {
        personService.checkPersonExist(personId);
        ControlEntity original = getControlEntity(personId, subjectId);
        if (Objects.nonNull(original)) {
            BeanUtil.copyPropertiesIgnoreNull(controlVO, original);
            if (Objects.isNull(original.getLeaderName()) || Objects.isNull(original.getLeaderJob()) || Objects.isNull(original.getLeaderContact())) {
                throw new ParamValidationException("请填写责任领导信息！");
            }
            controlRepository.save(original);
        } else {
            throw new ParamValidationException("该人员档案还未被分配派出所！");
        }
    }

    @Override
    public List<UnitVO> getBureauList() {
        List<UnitEntity> unitEntities = unitRepository.findByType("2");
        return unitEntities.stream().map(e -> new UnitVO(
                e.getUnitCode(),
                e.getUnitName()
        )).collect(Collectors.toList());
    }

    @Override
    public List<UnitVO> getPoliceStationList(String personId, String subjectId) {
        AssignEntity assignEntity = getAssignEntity(personId, subjectId);
        List<UnitVO> results = new ArrayList<>();
        if (Objects.nonNull(assignEntity)) {
            List<UnitEntity> unitEntities = unitRepository.findByAreaCodeAndType(assignEntity.getBureauCode().substring(0, 6), "4");
            results = unitEntities.stream().map(e -> new UnitVO(
                    e.getUnitCode(),
                    e.getUnitName()
            )).collect(Collectors.toList());
        }
        return results;
    }

    @Override
    public List<PoliceVO> getPoliceList(String unitCode, String name) {
        List<UserEntity> userEntities = userRepository.findByUnitCodeAndRealNameContaining(unitCode, name);
        return userEntities.stream().map(PoliceVO::userEntityToPoliceVO).collect(Collectors.toList());
    }

    @Override
    public List<UnitVO> getListUnitTree() {
        List<UnitEntity> areaUnits = unitRepository.findByType("2");
        List<UnitEntity> countyUnits = unitRepository.findByType("3");
        List<UnitEntity> stationUnits = unitRepository.findByType("4");
        List<UnitVO> results = new ArrayList<>();
        areaUnits.forEach(areaUnit -> {
            //机场分局不需要
            if ("510500430000".equals(areaUnit.getUnitCode())) {
                UnitVO unitVO = new UnitVO(areaUnit.getUnitName(), areaUnit.getUnitCode(), null);
                results.add(unitVO);
            } else {
                List<UnitVO> children = Stream.concat(
                        countyUnits.stream()
                                .filter(countyUnit -> countyUnit.getUnitCode().startsWith(areaUnit.getAreaCode().substring(0, 6)))
                                .map(child -> new UnitVO(child.getUnitCode(), child.getUnitName())),
                        stationUnits.stream()
                                .filter(countyUnit -> countyUnit.getUnitCode().startsWith(areaUnit.getAreaCode().substring(0, 6)))
                                .map(child -> new UnitVO(child.getUnitCode(), child.getUnitName()))
                ).collect(Collectors.toList());
                UnitVO areaValue = new UnitVO(areaUnit.getUnitName(), areaUnit.getUnitCode(), children);
                results.add(areaValue);
            }
        });
        return results;
    }

    /**
     * 查询分局分配信息并检验唯一
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 分局 {@link AssignEntity}
     */
    private AssignEntity getAssignEntity(String personId, String subjectId) {
        try {
            return assignRepository.getByPersonIdAndSubjectId(personId, subjectId);
        } catch (Exception e) {
            throw new ParamValidationException("该人员档案该专题下的分配信息不唯一，请核实！");
        }
    }

    /**
     * 查询派出所和民警信息并检验唯一
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 管控信息 {@link ControlEntity}
     */
    private ControlEntity getControlEntity(String personId, String subjectId) {
        try {
            return controlRepository.findByPersonIdAndSubjectId(personId, subjectId).orElse(new ControlEntity());
        } catch (Exception e) {
            throw new ParamValidationException("该人员档案该专题下的管控信息不唯一，请核实！");
        }
    }
}
