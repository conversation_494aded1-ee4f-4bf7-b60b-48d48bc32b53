package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.domain.entity.FilterValue;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.service.DepartmentService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 部门查询服务
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Service
public class DepartmentServiceImpl implements DepartmentService {

    @Resource
    private UnitRepository unitRepository;

    protected List<UnitEntity> allUnit;

    /**
     * 查询部门树形结构-派出所
     *
     * @return {@link FilterValue}
     */
    @Override
    public List<FilterValue> getPoliceStationList() {
        //查询出区县和派出所所有数据
        List<UnitEntity> stations = unitRepository.findByTypeAndCodeLike("4", "5105");
        List<UnitEntity> quxianStations = unitRepository.findByType("1");
        stations.addAll(quxianStations);
        Map<String, List<UnitEntity>> pidAndDeptMap = stations.stream()
                .filter(dept -> dept.getParentId() != null)
                .collect(Collectors.groupingBy(UnitEntity::getParentId));
        List<UnitEntity> unitEntities = new ArrayList<>();
        for (UnitEntity station : stations) {
            List<UnitEntity> children = pidAndDeptMap.get(station.getId());
            station.setChildren(children);
            if (Objects.equals(station.getType(), "1")) {
                unitEntities.add(station);
            }
        }
        return unitEntities.stream()
                .map((unitEntity)-> {
                    //排除掉泸州和四川省这一级，只展示区县一级
                    if (unitEntity.getUnitCode().length() == 6) {
                        return new FilterValue(unitEntity.getUnitCode(), unitEntity.getUnitName(), false, getAreaFilterValue(unitEntity.getChildren()));
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 组合区县及派出所节点
     *
     * @param childEntities 派出所单位实体
     * @return 树形结构的值
     */
    private List<FilterValue> getAreaFilterValue(List<UnitEntity> childEntities) {
        if (CollectionUtils.isEmpty(childEntities)) {
            return new ArrayList<>();
        }
        return childEntities.stream()
                .map(s -> {
                    FilterValue filterValue = new FilterValue(s.getUnitCode(), s.getUnitName(), false, null);
                    if (!CollectionUtils.isEmpty(s.getChildren())) {
                        filterValue.setChildren(getAreaFilterValue(s.getChildren()));
                    }
                    return filterValue;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<FilterValue> getAllUnitList() {
        List<UnitEntity> areaUnits = unitRepository.findByType("2");
        List<UnitEntity> countyUnits = unitRepository.findByType("3");
        List<UnitEntity> stationUnits = unitRepository.findByType("4");
        List<FilterValue> results = new ArrayList<>();
        areaUnits.forEach(areaUnit -> {
            //机场分局不需要
            if ("510500430000".equals(areaUnit.getUnitCode())) {
                results.add(new FilterValue(areaUnit.getUnitCode(), areaUnit.getUnitName(), false, null));
            } else {
                List<FilterValue> children = Stream.concat(countyUnits.stream().filter(countyUnit -> countyUnit.getUnitCode().startsWith(areaUnit.getAreaCode().substring(0, 6))).map(child -> new FilterValue(child.getUnitCode(), child.getUnitName(), false, null)), stationUnits.stream().filter(countyUnit -> countyUnit.getUnitCode().startsWith(areaUnit.getAreaCode().substring(0, 6))).map(child -> new FilterValue(child.getUnitCode(), child.getUnitName(), false, null))).collect(Collectors.toList());
                FilterValue areaFilterValue = new FilterValue(areaUnit.getUnitCode(), areaUnit.getUnitName(), false, children);
                results.add(areaFilterValue);
            }
        });
        return results;
    }

    @Override
    public List<FilterValue> getUnitTree() {
        allUnit = unitRepository.findAll();
        return  unitRepository.findAllByParentId(unitRepository.findByUnitCode("5105").getId())
                .stream()
                .map(u -> recursiveTree(new FilterValue(u.getUnitCode(), u.getShortname(), false, Collections.emptyList())))
                .collect(Collectors.toList());
    }

    private FilterValue recursiveTree(FilterValue filterValue) {
        //当前节点
        UnitEntity node = allUnit.stream()
                .filter(item -> item.getUnitCode().equals(filterValue.getId()))
                .findAny()
                .orElse(new UnitEntity());
        //当前节点的子节点
        List<UnitEntity> child = allUnit.stream().filter(item -> node.getId().equals(item.getParentId()) && !"6".equals(item.getType())).collect(Collectors.toList());
        //子节点不为空递归调用
        if (!child.isEmpty()) {
            List<FilterValue> collect = child.stream()
                    .map(u -> new FilterValue(u.getUnitCode(), u.getUnitName(), false, Collections.emptyList()))
                    .collect(Collectors.toList());
            filterValue.setChildren(collect);
            collect.forEach(this::recursiveTree);
        }
        return filterValue;
    }
}
