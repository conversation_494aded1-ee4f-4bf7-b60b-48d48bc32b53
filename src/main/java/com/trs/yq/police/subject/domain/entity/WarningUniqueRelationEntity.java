package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警和轨迹关联表
 *
 * <AUTHOR>
 * @date 2021/9/7 15:35
 */
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_UNIQUE_RELATION")
public class WarningUniqueRelationEntity implements Serializable {

    private static final long serialVersionUID = -5886451757065539763L;

    /**
     * 关系表主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 预警id
     */
    private String warningId;

    /**
     * 轨迹id
     */
    private String uniqueId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
