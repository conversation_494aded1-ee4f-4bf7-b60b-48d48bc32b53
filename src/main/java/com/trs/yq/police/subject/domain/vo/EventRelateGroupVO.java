package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.EventGroupRelationEntity;
import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.EventGroupRelationRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.PersonGroupRelationRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/13 18:17
 */
@Data
public class EventRelateGroupVO implements Serializable {
    private static final long serialVersionUID = 3342374731911261325L;
    /**
     * 群体id
     */
    private String groupId;
    /**
     * 群体名称
     */
    private String groupName;
    /**
     * 群体人数
     */
    private Integer memberCount;
    /**
     * 群体类别
     */
    private String groupType;
    /**
     * 线索-群体关联id
     */
    private String relationId;

    /**
     * 创建vo
     *
     * @param groupEntity {@link GroupEntity}
     * @param eventId     事件id
     * @return {@link ClueRelatedGroupVO}
     */
    public static EventRelateGroupVO of(GroupEntity groupEntity, String eventId) {
        EventRelateGroupVO vo = new EventRelateGroupVO();
        vo.setGroupId(groupEntity.getId());
        PersonGroupRelationRepository personGroupRelationRepository = BeanUtil.getBean(PersonGroupRelationRepository.class);
        vo.setMemberCount(personGroupRelationRepository.countAllByGroupId(groupEntity.getId()));
        vo.setGroupName(groupEntity.getName());

        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        List<LabelEntity> groupTypes = labelRepository.findByGroupIdAndSubjectId(groupEntity.getId(), groupEntity.getSubjectId());
        vo.setGroupType(groupTypes.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));

        EventGroupRelationRepository eventGroupRelationRepository = BeanUtil.getBean(EventGroupRelationRepository.class);
        EventGroupRelationEntity relation = eventGroupRelationRepository.findByEventIdAndGroupId(eventId, groupEntity.getId());
        vo.setRelationId(relation.getId());
        return vo;
    }
}
