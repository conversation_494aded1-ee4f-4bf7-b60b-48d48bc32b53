package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.RoleOperationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 角色-操作映射持久化层
 *
 * <AUTHOR>
 * @since 2021/8/23
 */
@Repository
public interface RoleOperationRepository extends BaseRepository<RoleOperationEntity, String>{

    /**
     * 根据操作名称查找角色id列表
     *
     * @param operationName 操作名称
     * @return 角色id列表
     */
    @Query(value = "select role_id from T_ADMIN_ROLE_OPERATION where operation_name = :operationName", nativeQuery = true)
    List<String> findRoleIdByOperationName(@Param("operationName") String operationName);

    /**
     * 根据角色id删除
     *
     * @param roleId 角色id
     */
    @Modifying
    void deleteByRoleId(String roleId);

    /**
     * 根据角色id查询操作名称列表
     *
     * @param roleId 角色id
     * @return 操作名称列表
     */
    @Query(value = "select operation_name from T_ADMIN_ROLE_OPERATION where role_id = :roleId", nativeQuery = true)
    List<String> findOperationListByRoleId(@Param("roleId") String roleId);

    /**
     * 根据角色id查询操作名称列表
     *
     * @param roleIds 角色id
     * @return 操作名称列表
     */
    @Query(value = "select operation_name from T_ADMIN_ROLE_OPERATION where role_id in :roleIds", nativeQuery = true)
    List<String> findOperationListByRoleIds(@Param("roleIds") List<String> roleIds);
}
