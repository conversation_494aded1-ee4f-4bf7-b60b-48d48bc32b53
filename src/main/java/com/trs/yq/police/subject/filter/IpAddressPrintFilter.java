package com.trs.yq.police.subject.filter;

import com.trs.yq.police.subject.utils.RemoteAddrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/08/19
 */
@Component
@Order(1)
@Slf4j
public class IpAddressPrintFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        String address = RemoteAddrUtil.getRemoteAddress(req);
        log.debug(String.format("remote address: [%s] is querying url: [%s] method is: [%s]", address, req.getRequestURI(), req.getMethod()));
        chain.doFilter(request, response);
    }
}
