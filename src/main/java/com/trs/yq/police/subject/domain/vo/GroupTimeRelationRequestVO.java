package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/14 11:31
 */
@Data
public class GroupTimeRelationRequestVO implements Serializable {
    private static final long serialVersionUID = -1536643968407314702L;
    /**
     * 群体id
     */
    @NotBlank(message = "群体id不能为空")
    private String groupId;
    /**
     * 敏感时间节点id
     */
    private List<String> nodeIds;
}
