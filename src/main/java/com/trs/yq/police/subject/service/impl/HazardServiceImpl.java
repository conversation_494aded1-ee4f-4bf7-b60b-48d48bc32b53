package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.vo.HazardBarNode;
import com.trs.yq.police.subject.domain.vo.HazardResult;
import com.trs.yq.police.subject.repository.ControlRepository;
import com.trs.yq.police.subject.repository.WarningRepository;
import com.trs.yq.police.subject.service.HazardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 风险洞察接口
 *
 * <AUTHOR>
 * @date 2022/04/13
 */
@Service
public class HazardServiceImpl implements HazardService {

    @Resource
    private WarningRepository warningRepository;

    @Resource
    private ControlRepository controlRepository;

    @Override
    public List<HazardResult> getHazardList() {
        List<WarningEntity> warningList = warningRepository.getRecentList();
        return warningList.stream().map(w -> {
            HazardResult result = new HazardResult();
            result.setId(w.getId());
            result.setCrttime(w.getWarningTime());
            result.setDetail(w.getWarningDetails());
            result.setType_v(w.getWarningType().equals("25") ? "涉稳人员高风险警情" : "精神病人员高风险警情");
            String idNumber = warningRepository.getRelatedPersonIdNumber(w.getId());
            result.setUnitname(controlRepository.getPoliceStationNameByIdNumber(idNumber));
            result.setUrl("/yq-app/police/maintenance/warning/detail/" + w.getId());
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public List<HazardBarNode> getHazardBarList() {
        LocalDate start = LocalDate.now().minusYears(1).withDayOfMonth(1);
        HazardBarNode node1 = new HazardBarNode();
        HazardBarNode node2 = new HazardBarNode();
        node1.setName("涉稳人员高风险警情");
        node1.setData(new ArrayList<>(12));
        node2.setName("精神病人员高风险警情");
        node2.setData(new ArrayList<>(12));
        for (int i = 0; i < 12; i++) {
            LocalDate date = start.plusMonths(i);
            node1.getData().add(new HazardBarNode.NodeData(date, warningRepository.getWarningCount(date.atStartOfDay(), date.plusMonths(1).atStartOfDay(), "25")));
            node2.getData().add(new HazardBarNode.NodeData(date, warningRepository.getWarningCount(date.atStartOfDay(), date.plusMonths(1).atStartOfDay(), "26")));
        }
        return Arrays.asList(node1, node2);
    }
}
