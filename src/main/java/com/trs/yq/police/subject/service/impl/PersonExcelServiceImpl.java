package com.trs.yq.police.subject.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.builder.PersonListPredicatesBuilder;
import com.trs.yq.police.subject.common.SkipResponseBodyAdvice;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.vo.PersonExportListVO;
import com.trs.yq.police.subject.domain.vo.ExportParams;
import com.trs.yq.police.subject.handler.CustomCellWriteHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.service.PersonExcelService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import javax.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.WW_SUBJECT;

/**
 * 批量导出业务层
 *
 * <AUTHOR>
 * @date 2021/7/29 17:24
 */
@Service
public class PersonExcelServiceImpl implements PersonExcelService {

    @Resource
    private PersonRepository personRepository;
    @Resource
    private SubjectRepository subjectRepository;
    @Resource
    private DictService dictService;
    @Resource
    private CommonExtendRepository commonExtendRepository;
    @Resource
    private PersonGroupRelationRepository personGroupRelationRepository;
    @Resource
    private GroupRepository groupRepository;

    @Override
    @SkipResponseBodyAdvice
    public void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request,
        String subjectId) throws IOException {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElseThrow(IOException::new);
        String fileName = String.format("%s-人员档案-%s.xlsx", subjectEntity.getName(),
            LocalDateTime.now().format(DATE_TIME_FORMATTER));
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        List<PersonEntity> personList = Boolean.TRUE.equals(request.getIsAll())
                    ? personRepository.findAll((root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = PersonListPredicatesBuilder.buildListFilterPredicates(subjectId,
                        request.getListParams().getFilterParams(), root, criteriaBuilder);
                    if (Objects.nonNull(request.getListParams().getSearchParams())) {
                        predicates.addAll(
                            PersonListPredicatesBuilder.buildSearchPredicates(request.getListParams().getSearchParams(),
                                root, criteriaBuilder));
                    }
                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                })
                    : personRepository.findByIds(request.getIds());
        List<PersonExportListVO> vos = new ArrayList<>();
        personList.forEach(personEntity -> {
            PersonExportListVO vo = new PersonExportListVO();
            BeanUtil.copyPropertiesIgnoreNull(personEntity, vo);
            //设置人员性别
            vo.setGender("0".equals(personEntity.getGender()) ? "男" : "女");
            if (StringUtils.isNotBlank(vo.getNation())) {
                //从码表中查询民族
                DictEntity nation = dictService.getDictEntityByTypeAndCode("nation", vo.getNation());
                vo.setNation(Objects.nonNull(nation) ? nation.getName() : vo.getNation());
            }
            if (StringUtils.isNotBlank(vo.getPoliticalStatus())) {
                //从码表中查询政治面貌
                DictEntity politicalStatus = dictService.getDictEntityByTypeAndCode("ps_political_status",
                    vo.getPoliticalStatus());
                vo.setPoliticalStatus(
                    Objects.nonNull(politicalStatus) ? politicalStatus.getName() : vo.getPoliticalStatus());
            }
            if (StringUtils.isNotBlank(vo.getMaritalStatus())) {
                //从码表中查询婚姻状态

                DictEntity maritalStatus = dictService.getDictEntityByTypeAndCode("ps_marital_status",
                    vo.getMaritalStatus());
                vo.setMaritalStatus(Objects.nonNull(maritalStatus) ? maritalStatus.getName() : vo.getMaritalStatus());
            }
            if (StringUtils.isNotBlank(vo.getControlStatus())) {
                //从码表中查询管控状态

                DictEntity controlStatus = dictService.getDictEntityByTypeAndCode("ps_control_status",
                    vo.getControlStatus());
                vo.setControlStatus(Objects.nonNull(controlStatus) ? controlStatus.getName() : vo.getControlStatus());
            }

            //处理维稳专题
            if (subjectId.equals(WW_SUBJECT)) {
                if (StringUtils.isNotBlank(vo.getControlLevel())) {
                    //维稳管控级别
                    DictEntity controlLevel = dictService.getDictEntityByTypeAndCode("ps_ww_control_level",
                        vo.getControlLevel());
                    vo.setControlLevel(Objects.nonNull(controlLevel) ? controlLevel.getName() : vo.getControlLevel());
                }
                //被依法处理情况 主要诉求
                Optional<CommonExtentEntity> extentEntity = commonExtendRepository.findByRecordIdAndModule(
                    personEntity.getId(), CommonExtentEntity.PERSON);
                extentEntity.ifPresent(e -> {
                    vo.setTreatment(e.getTreatment());
                    vo.setMainDemand(e.getMainDemand());
                });
                //涉事相关群体
                List<PersonGroupRelationEntity> relations = personGroupRelationRepository.findAllByPersonIdAndSubjectId(
                    personEntity.getId(), subjectId);
                String items = relations.stream()
                    .map(relation -> groupRepository.getById(relation.getGroupId()).getName())
                    .collect(Collectors.joining("、"));
                vo.setGroup(items);

            } else {
                if (StringUtils.isNotBlank(vo.getControlLevel())) {
                    //从码表中查询管控级别
                    DictEntity controlLevel = dictService.getDictEntityByTypeAndCode("ps_zb_control_level",
                        vo.getControlLevel());

                    vo.setControlLevel(Objects.nonNull(controlLevel) ? controlLevel.getName() : vo.getControlLevel());
                }
            }

            //设置群体类别
            List<String> groupType = personRepository.getGroupType(personEntity.getId(), subjectId);
            vo.setGroupType(groupType.isEmpty() ? "无" : String.join(",", groupType));

            //设置人员标签
            List<String> personLabel = personRepository.getPersonLabel(personEntity.getId(), subjectId);
            vo.setPersonLabel(personLabel.isEmpty() ? "无" : String.join(",", personLabel));
            vos.add(vo);
        });
        EasyExcelFactory.write(response.getOutputStream(), PersonExportListVO.class)
            .registerWriteHandler(new CustomCellWriteHandler())
            .includeColumnFiledNames(fieldNames)
            .sheet()
            .doWrite(vos);
    }

    @Override
    public JsonNode getExportPropertyList(String subjectId) {
        SubjectEntity subjectEntity = subjectRepository.findById(subjectId).orElse(null);
        if (Objects.isNull(subjectEntity)) {
            throw new NoSuchElementException("没有该主题: " + subjectId);
        }
        return JsonUtil.parseJsonNode(subjectEntity.getPersonListProperty());
    }

}
