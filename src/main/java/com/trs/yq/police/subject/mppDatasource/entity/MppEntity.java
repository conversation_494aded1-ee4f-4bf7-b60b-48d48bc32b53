package com.trs.yq.police.subject.mppDatasource.entity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 12345 entity
 */
@Entity
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Table(name = "shzy_12345rx_gdjcxx")
@Slf4j
public class MppEntity {

    @Id
    private String uuid;

    private String adddate;

    private String content;

    private String address;

    private String areaname;
}
