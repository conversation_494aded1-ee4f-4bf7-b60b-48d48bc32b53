package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.MobilityPageWithTotal;
import com.trs.yq.police.subject.domain.vo.MobilityVO;


/**
 * 流动信息业务层接口
 *
 * <AUTHOR>
 */
public interface MobilityService {

    /**
     * 分页查询流动信息 额外返回流入信息统计
     *
     * @param personId   人员id
     * @param pageParams 分页参数
     * @return 流动信息分页结果，增加流入信息统计字段
     */
    MobilityPageWithTotal getMovement(String personId, PageParams pageParams);

    /**
     * 新增流动信息
     *
     * @param personId   人员id
     * @param mobilityVO 流动信息
     */
    void addMovement(String personId, MobilityVO mobilityVO);

    /**
     * 更新流动信息
     *
     * @param personId   人员id
     * @param mobilityVO 流动信息
     */
    void updateMovement(String personId, MobilityVO mobilityVO);

    /**
     * 删除流动信息
     *
     * @param personId 人员主键
     * @param id       流动信息id
     */
    void deleteMovement(String personId, String id);
}
