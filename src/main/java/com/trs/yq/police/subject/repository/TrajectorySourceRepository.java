package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.TrajectorySourceEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 海贝轨迹表名字段映射关系查询接口
 *
 * <AUTHOR>
 * @date 2021/08/26
 */
@Repository
public interface TrajectorySourceRepository extends BaseRepository<TrajectorySourceEntity, String> {


    /**
     * 依据表名查询轨迹来源
     *
     * @param tableName 表名
     * @return 轨迹来源
     * <AUTHOR>
     * @since 2021/9/7 17:25
     */
    TrajectorySourceEntity findByTableName(String tableName);

    /**
     * 查询预警源实体
     *
     * @param tableNames 表名
     * @return 预警源信息
     */
    @SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
    @Query("select ts.name from TrajectorySourceEntity ts where ts.tableName in (:tableNames)")
    List<String> findAllNameByTableNameIn(@Param("tableNames") List<String> tableNames);
}
