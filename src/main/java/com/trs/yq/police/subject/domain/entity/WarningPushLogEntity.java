package com.trs.yq.police.subject.domain.entity;

import lombok.*;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 预警推送表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/08
 **/
@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "T_PS_WARNING_PUSH_LOG")
public class WarningPushLogEntity implements Serializable {

    private static final long serialVersionUID = -1295176854088596141L;


    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 预警类型
     */
    private String warningType;

    /**
     * 专题id
     */
    private String subjectId;

    /**
     * id的类型
     * idNumber - 身份证、
     * phoneNumber - 手机号、
     * imei、imsi、mac - 设备码、
     * plate - 车牌号
     */
    private String idType;

    /**
     * id的值 格式与idType对应
     */
    private String idValue;

    /**
     * 推送时间
     */
    private LocalDateTime time;
}
