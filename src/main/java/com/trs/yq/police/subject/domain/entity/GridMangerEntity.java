package com.trs.yq.police.subject.domain.entity;

import java.io.Serializable;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.GenericGenerator;

/**
 * (TPsGridManger)数据访问类
 *
 * <AUTHOR>
 * @since 2022-07-28 15:21:30
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_GRID_MANGER")
public class GridMangerEntity implements Serializable {
    
    private static final long serialVersionUID = 247283943004495424L;
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;
    /**
     * 网格名 
     */ 
    private String gridName;
                                                                                            
    /**
     * 镇街 
     */ 
    private String street;
                                                                                            
    /**
     * 社区 
     */ 
    private String community;
                                                                                            
    /**
     * 警务区民警姓名 
     */ 
    private String policeName;
                                                                                            
    /**
     * 警务区民警身份证号 
     */ 
    private String policeIdNumber;
                                                                                            
    /**
     * 警务区民警电话号 
     */ 
    private String policePhoneNumber;
                                                                                            
    /**
     * 网格管理员姓名 
     */ 
    private String gridManagerName;
                                                                                            
    /**
     * 网格管理员身份证号 
     */ 
    private String gridManagerPhoneNumber;
                                                                                            
    /**
     * 地区code 
     */ 
    private String areaCode;

}
