package com.trs.yq.police.subject.domain.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * jwzh_dict2
 *
 * <AUTHOR>
 * @since 2022/1/19
 */
@Data
@Entity
@Table(name = "JWZH_DICT2")
public class JwzhDictEntity {

    /**
     * id
     */
    @Id
    private String id;

    /**
     * 字段编号
     */
    private String zdbh;
    /**
     * 字段名称
     */
    private String zdmc;
    /**
     * 代码
     */
    private String dm;
    /**
     * 中文名称
     */
    private String ct;
}
