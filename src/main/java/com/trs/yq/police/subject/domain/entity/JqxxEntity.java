package com.trs.yq.police.subject.domain.entity;

import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;

/**
 * jwzh_asj_jqxx/警务综合-案事件-警情信息
 *
 * <AUTHOR>
 * @since 2022/1/18
 */
@Data
@Entity
@Table(name = "JWZH_ASJ_JQXX")
public class JqxxEntity {

    /**
     * 警情编号
     */
    @Id
    private String jqbh;
    /**
     * 接警单编号
     */
    private String jjdbh;
    /**
     * 警情类别代码
     */
    private String jqlbdm;
    /**
     * 简要案情
     */
    private String jyaq;
    /**
     * 事发地址
     */
    private String sfdzDzmc;
    /**
     * 处警单位
     */
    private String cjdwGajgmc;
    /**
     * 报警时间
     */
    private LocalDateTime bjsjRqsj;
    /**
     * 警情状态代码
     */
    private String jqztdm;
    /**
     * 出警单位代码
     */
    private String cjdwGajgdm;
}
