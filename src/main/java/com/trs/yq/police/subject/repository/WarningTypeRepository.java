package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningTypeEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 预警类型持久层接口
 *
 * <AUTHOR>
 * @date 2021/9/7 10:16
 */
@Repository
public interface WarningTypeRepository extends BaseRepository<WarningTypeEntity, String> {

    /**
     * 根据英文名查询
     *
     * @param enName 英文名
     * @return 预警类型
     */
    WarningTypeEntity findByEnName(String enName);

    /**
     * 根据英文名查询所有
     *
     * @param enName 英文名
     * @return 预警类型
     */
    List<WarningTypeEntity> findAllByEnNameIn(List<String> enName);

    /**
     * 根据专题id查询所有
     *
     * @param subjectId 专题id
     * @return 预警类型
     */
    @Query("select t from WarningTypeEntity t where t.subjectId = :subjectId order by t.showOrder asc")
    List<WarningTypeEntity> findAllBySubjectId(@Param("subjectId") String subjectId);


    /**
     * 按照行政区划查询预警类别的统计
     *
     * @param subjectId 专题id
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param areaCode  行政区划代码
     * @return 统计结果
     */
    @Query(nativeQuery = true, value = "SELECT cn_name as personType," +
            "( SELECT COUNT( 1 ) " +
            "   FROM T_PS_WARNING w " +
            "   WHERE w.WARNING_TYPE = wt.id " +
            "   AND instr(w.AREA_CODE, :areaCode) = 1 " +
            "   AND w.WARNING_TIME>=:beginTime " +
            "   AND w.WARNING_TIME<=:endTime) as personCount " +
            "FROM " +
            "T_PS_WARNING_TYPE wt " +
            "WHERE " +
            "wt.subject_id = :subjectId")
    List<Map<String, Object>> countByAreaCodeAndSubjectId(@Param("subjectId") String subjectId,
                                                          @Param("beginTime") LocalDateTime beginTime,
                                                          @Param("endTime") LocalDateTime endTime,
                                                          @Param("areaCode") String areaCode);

    /**
     * 根据中文名查询所有
     *
     * @param cnName 中文名
     * @return 预警类型
     */
    @Query("SELECT t FROM WarningTypeEntity t where t.cnName like concat('%',:cnName,'%')")
    List<WarningTypeEntity> findAllByCnName(@Param("cnName") String cnName);
}
