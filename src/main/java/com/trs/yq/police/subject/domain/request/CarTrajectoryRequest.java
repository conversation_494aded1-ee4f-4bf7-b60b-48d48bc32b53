package com.trs.yq.police.subject.domain.request;

import com.trs.yq.police.subject.domain.params.TimeParams;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 合成作战/指令列表VO
 *
 * <AUTHOR>
 * @date 2023/03/09 10:17
 */
@Data
public class CarTrajectoryRequest {

    @NotBlank(message = "车牌号不能为空！！")
    private String carNumber;
    @NotNull(message = "时间参数不能为空!!")
    private TimeParams timeParams;

}
