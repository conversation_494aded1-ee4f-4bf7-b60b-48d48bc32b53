package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.vo.WarningJudgeVO;
import com.trs.yq.police.subject.operation.OperationLog;
import com.trs.yq.police.subject.service.WarningDetailsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 预警详情controller层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/9 16:30
 **/
@RestController
@RequestMapping("/warning")
@Validated
public class WarningDetailsController {

    @Resource
    private WarningDetailsService warningDetailsService;

    /**
     * [预警档案] 研判
     * http://192.168.200.192:3001/project/4897/interface/api/130765
     *
     * @param warningId      预警id
     * @param warningJudgeVO 研判VO
     */
    @PostMapping("/{warningId}/judge")
    public void doJudge(@PathVariable String warningId, @RequestBody WarningJudgeVO warningJudgeVO) {
        warningDetailsService.doJudge(warningId, warningJudgeVO);
    }

    /**
     * [预警档案] 签收
     * http://192.168.200.192:3001/project/4897/interface/api/130447
     *
     * @param warningId 预警id
     */
    @PostMapping("/{warningId}/sign")
    @OperationLog(operator = Operator.SIGN, module = OperateModule.WARNING, desc = "签收", primaryKey = "warningId", targetObjectType = TargetObjectTypeEnum.WARNING)
    public void doSign(@PathVariable String warningId) {
        warningDetailsService.doSign(warningId);
    }
}
