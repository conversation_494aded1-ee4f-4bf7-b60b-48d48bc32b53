package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class GroupExportListVO implements Serializable {
    private static final long serialVersionUID = -5523702024519228147L;

    @ExcelProperty(value = "群体名称", order = 0)
    private String groupName;

    @ExcelProperty(value = "群体类别", order = 1)
    private String groupType;

    @ExcelProperty(value = "基本情况", order = 2)
    private String basicInfo;

    @ExcelProperty(value = "录入单位", order = 3)
    private String createDeptName;

    @ExcelProperty(value = "录入时间", order = 4)
    private String createTime;

    @ExcelProperty(value = "更新时间", order = 5)
    private String updateTime;

    @ExcelProperty(value = "主要诉求", order = 6)
    private String demand;
}
