package com.trs.yq.police.subject.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 预警消息
 *
 * <AUTHOR>
 * @date 2021/9/6 14:24
 */
@Getter
@Setter
@ToString
public class StreamWarningMessageDTO implements Serializable {

    private static final long serialVersionUID = -9209564065076939144L;

    /**
     * 预警类型
     */
    private List<String> type;

    /**
     * 轨迹详情
     */
    private List<WarningTrajectoryListDTO> trajectories;
}
