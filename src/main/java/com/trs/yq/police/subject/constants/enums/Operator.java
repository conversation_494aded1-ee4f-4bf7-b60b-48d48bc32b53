package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 操作枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 15:18
 */
public enum Operator {

    /**
     * enums
     */
    ADD("0", "ADD", "新增"),
    DELETE("1", "DELETE", "删除"),
    EDIT("2", "EDIT", "编辑"),
    QUERY("3", "QUERY", "查看"),
    EXPORT("4", "EXPORT", "导出"),
    IMPORT("5", "IMPORT", "导入"),
    ARCHIVE("6", "ARCHIVE", "归档"),
    ACTIVATE("7", "ACTIVATE", "激活"),
    DOWNLOAD("8", "DOWNLOAD", "下载档案"),
    BATCH_DELETE("9", "BATCH_DELETE", "批量删除"),
    RELATE("10", "RELATE", "关联"),
    DE_RELATE("11", "DE_RELATE", "取消关联"),
    EDIT_ACTIVE("12", "EDIT_ACTIVE", "更改活跃度"),
    SIGN("13", "SIGN", "签收"),
    JUDGE("14", "JUDGE", "反馈"),
    COMMAND("15", "COMMAND", "指令"),
    DEMAND("16", "DEMAND", "协作"),
    RECORD("17", "BATTLE", "合成"),
    PUSH("18", "PUSH", "推送预警"),
    SMS("19", "SMS", "推送短信"),
    UPLOAD("20", "UPLOAD", "上传"),
    UPDATE("21", "UPDATE", "系统比对"),
    DISPOSE("22", "DISPOSE", "处置"),
    REPORT("23", "REPORT", "上报"),
    CONTROL("24", "CONTROL", "列管"),
    CANCEL_CONTROL("25", "CANCEL_CONTROL", "取消列管"),
    ;


    /**
     * fields
     */
    @Getter
    private final String code;

    @Getter
    private final String type;

    @Getter
    private final String name;

    /**
     * constructor
     *
     * @param code 操作码
     * @param type 操作类型
     * @param name 操作名
     */
    Operator(String code, String type, String name) {
        this.code = code;
        this.type = type;
        this.name = name;
    }

    /**
     * 通过模块码获取枚举
     *
     * @param code 操作符码
     * @return 操作符 {@link Operator}
     * <AUTHOR>
     * @since 2021/7/27 17:55
     */
    public static Operator codeOf(String code) {
        if (StringUtils.isNotBlank(code)) {
            return Arrays.stream(Operator.values())
                    .filter(module -> module.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
}
