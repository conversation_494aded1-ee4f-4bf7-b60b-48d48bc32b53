package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.EducationEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 教育信息持久层
 *
 * <AUTHOR>
 */
@Repository
public interface EducationRepository extends BaseRepository<EducationEntity, String> {
    /**
     * 查询人员教育信息
     *
     * @param personId 人员id
     * @return 人员基本信息 {@link EducationEntity}
     * <AUTHOR>
     */
    List<EducationEntity> findAllByPersonId(String personId);

    /**
     * 依据人员主键计数
     *
     * @param personId 人员主键
     * @return 数量
     */
    int countAllByPersonId(String personId);
}
