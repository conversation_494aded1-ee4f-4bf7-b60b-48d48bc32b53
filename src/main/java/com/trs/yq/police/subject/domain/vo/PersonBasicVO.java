package com.trs.yq.police.subject.domain.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 人员基本信息
 *
 * <AUTHOR>
 * @date 2021/7/30 15:03
 */
@Getter
@Setter
@ToString
public class PersonBasicVO implements Serializable {

    private static final long serialVersionUID = 1072239117725867504L;

    /**
     * 专题id
     */
    private String subjectId;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 姓名
     */
    @NotBlank(message = "人员姓名缺失")
    private String name;

    /**
     * 身份证号码
     */
    @NotBlank(message = "证件号码缺失")
    private String idNumber;

    /**
     * 身份证号码
     */
    private String idType="1";

    /**
     * 性别
     */
    private String gender;

    /**
     * 联系方式
     */
    private String contactInformation;

    /**
     * 户籍地
     */
    private String registeredResidence;

    /**
     * 现住地
     */
    private String currentResidence;

    /**
     * 曾用名
     */
    private String formerName;

    /**
     * 绰号
     */
    private String nickName;

    /**
     * 民族
     */
    private String nation;

    /**
     * 政治面貌
     */
    private String politicalStatus;

    /**
     * 宗教信仰
     */
    private String religiousBelief;

    /**
     * 现职业
     */
    private String currentJob;

    /**
     * 管控状态
     */
    private String controlStatus;

    /**
     * 管控级别
     */
    private String controlLevel;

    /**
     * 流入时间
     */
    private LocalDateTime inTime;

    /**
     * 来自省市
     */
    private String inDirection;

    /**
     * 基本情况
     */
    private String basicInfo;

    /**
     * 婚姻状态
     * 码表类型：ps_marital_status_group
     */
    private String maritalStatus;

    /**
     * 群体类别
     */
    private List<IdNameVO> groups;

    /**
     * 人员类别
     */
    private List<IdNameVO> types;

    /**
     * 人员标签
     */
    private List<PersonLabelVO> labels;

    /**
     * 头像图片
     */
    private List<ImageVO> images;

    /**
     * 主要诉求
     */
    private String mainDemand;

    /**
     * 依法处理情况
     */
    private String treatment;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 生日
     */
    private long birthday;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 重点管控依据
     */
    private String keyControlBasis;
}
