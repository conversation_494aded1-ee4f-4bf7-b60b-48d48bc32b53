package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;

/**
 * 教育信息实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_EDUCATION")
public class EducationEntity extends BaseEntity {
    
    private static final long serialVersionUID = 1343543142466594459L;
    /**
     * 人员ID
     */
    private String personId;

    /**
     * 起始时间
     */
    private LocalDate beginTime;

    /**
     * 结束时间
     */
    private LocalDate endTime;

    /**
     * 毕业院校
     */
    private String school;

    /**
     * 专业
     */
    private String subject;
}
