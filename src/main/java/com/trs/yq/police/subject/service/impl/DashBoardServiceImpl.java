package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.constants.enums.ControlLevelEnum;
import com.trs.yq.police.subject.domain.entity.DictEntity;
import com.trs.yq.police.subject.domain.entity.JwzhDictEntity;
import com.trs.yq.police.subject.domain.entity.UnitPcsEntity;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.DashBoardService;
import com.trs.yq.police.subject.utils.CalculateUtils;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/12/20 11:18
 */
@Service
public class DashBoardServiceImpl implements DashBoardService {

    @Resource
    private AlarmBkxxRyRepository alarmBkxxRyRepository;
    @Resource
    private JqRepository jqRepository;
    @Resource
    private UnitPcsRepository unitPcsRepository;
    @Resource
    private DictRepository dictRepository;
    @Resource
    private JwzhDictRepository jwzhDictRepository;

    private String getCountyName(String code) {
        DictEntity countycode = dictRepository.findByTypeAndCode("countycode", code);
        if (countycode == null) {
            return null;
        }
        String name = countycode.getName();
        return name.substring(0, 2);
    }

    @Override
    public List<BigScreenStatisticVO> statisticByRylb(StatisticRequestVO statisticRequestVO) {
        String district = statisticRequestVO.getDistrict();
        TimeParams timeParams = statisticRequestVO.getTimeParams();
        return dictRepository.findAllByType("emphasistype")
                .stream()
                .filter(e -> Objects.isNull(e.getPCode()))
                .map(e -> {
                    Long measure = alarmBkxxRyRepository.statisticByRylb(timeParams.getBeginTime(), timeParams.getEndTime(),
                            getCountyName(district), e.getCode().substring(0, 2));
                    return new BigScreenStatisticVO(e.getName(), measure, null);
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> statisticByYjjb(StatisticRequestVO statisticRequestVO) {
        TimeParams timeParams = statisticRequestVO.getTimeParams();
        String district = statisticRequestVO.getDistrict();
        List<Integer> bkjb = Arrays.asList(10, 20, 30);
        ArrayList<BigScreenStatisticVO> result = new ArrayList<>();
        for (TimePeriodVO timePeriodVO : TimePeriodVO.listOf(timeParams)) {
            List<Map<String, Object>> maps = alarmBkxxRyRepository.statisticByBkjb(timePeriodVO.getBeginTime(), timePeriodVO.getEnTime(), getCountyName(district));
            List<BigScreenStatisticVO> collect = getBkxxRyStatisticVOS(bkjb, timePeriodVO.getName(), maps);
            result.addAll(collect);
        }
        return result;
    }

    private static List<BigScreenStatisticVO> getBkxxRyStatisticVOS(List<Integer> bkjb, String dimension, List<Map<String, Object>> maps) {
        return bkjb.stream()
                .map(e -> new BigScreenStatisticVO(dimension, 0L, e.toString())).peek(e -> {
                    String legend = e.getLegend();
                    for (Map<String, Object> map : maps) {
                        if (legend.equals(map.get("LEGEND").toString())) {
                            BigDecimal measure = (BigDecimal) map.get("MEASURE");
                            e.setMeasure(measure.longValue());
                            break;
                        }
                    }
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getJqfxJjtop(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        return jqRepository.getJqfxJjtop(requestVO.getType(), timeParams.getBeginTime(), timeParams.getEndTime())
                .stream()
                .map(item -> {
                    final BigScreenStatisticVO vo = new BigScreenStatisticVO();
                    vo.setDimension(item.get("dimension").toString());
                    vo.setMeasure(((BigDecimal) item.get("count")).longValue());
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getQxjjqk(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        return unitPcsRepository.findAllByType(1).parallelStream()
                .map(unit -> {
                    final BigScreenStatisticVO vo = new BigScreenStatisticVO();
                    vo.setDimension(unit.getShortName());
                    vo.setMeasure(jqRepository.getJqslByType(requestVO.getType(), unit.getUnitCode(), timeParams.getBeginTime(), timeParams.getEndTime()));
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getJqlb(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        return jwzhDictRepository.getJwzhDict(requestVO.getType()).stream().map(dict -> {
            final BigScreenStatisticVO vo = new BigScreenStatisticVO();
            vo.setDimension(dict.getCt());
            vo.setMeasure(jqRepository.getJqslByType(dict.getDm(), null, timeParams.getBeginTime(), timeParams.getEndTime()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getJqqs(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        final List<JwzhDictEntity> jwzhDict = jwzhDictRepository.getJwzhDict(requestVO.getType());
        return TimePeriodVO.listOf(timeParams).stream().flatMap(timePeriodVO -> jwzhDict.stream().map(dict -> {
            final BigScreenStatisticVO vo = new BigScreenStatisticVO();
            vo.setDimension(timePeriodVO.getName());
            vo.setLegend(dict.getCt());
            vo.setMeasure(jqRepository.getJqslByType(dict.getDm(), StringUtil.getPoliceStationPrefix(requestVO.getDistrict()),
                    timePeriodVO.getBeginTime(), timeParams.getEndTime()));
            return vo;
        })).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getJwxz(StatisticRequestVO requestVO) {
        return dictRepository.findAllByType("demandtype")
                .stream()
                .filter(e -> Objects.nonNull(e.getCode()))
                .map(e -> {
                    String code = e.getCode();
                    Long jwxz = jqRepository.getJwxz(code, requestVO.getTimeParams().getBeginTime(), requestVO.getTimeParams().getEndTime());
                    return new BigScreenStatisticVO(e.getName(), jwxz, null);
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getJwhz(StatisticRequestVO requestVO, String type) {
        TimeParams timeParams = requestVO.getTimeParams();
        List<BigScreenStatisticVO> statisticVOS = dictRepository.findAllByType(type).stream()
                .filter(e -> StringUtils.isNotBlank(e.getCode()))
                .map(e -> {
                    Long measure = 0L;
                    if ("battletype".equals(type)) {
                        measure = jqRepository.getHczb(e.getCode(), timeParams.getBeginTime(), timeParams.getEndTime());
                    } else if ("demand_methodtype".equals(type)) {
                        measure = jqRepository.getXzsd(e.getCode(), timeParams.getBeginTime(), timeParams.getEndTime());
                    }
                    return new BigScreenStatisticVO(e.getName(), measure, null);
                }).sorted(Comparator.comparingLong(o -> -(Long) o.getMeasure()))
                .collect(Collectors.toList());
        //总数
        long total = 0L;
        for (BigScreenStatisticVO statisticVO : statisticVOS) {
            total += (Long) statisticVO.getMeasure();
        }
        //计算百分比
        for (BigScreenStatisticVO statisticVO : statisticVOS) {
            Long measure = (Long) statisticVO.getMeasure();
            String percentage = CalculateUtils.calculatePercentage(measure, total);
            statisticVO.setMeasure(percentage);
        }
        return statisticVOS;
    }

    @Override
    public List<BigScreenStatisticVO> getJwhz(StatisticRequestVO requestVO) {
        return dictRepository.findAllByType("demand_methodtype")
                .stream()
                .filter(e -> Objects.nonNull(e.getCode()))
                .map(e -> {
                    String code = e.getCode();
                    Long jwxz = jqRepository.getJwhz(code, requestVO.getTimeParams().getBeginTime(), requestVO.getTimeParams().getEndTime());
                    return new BigScreenStatisticVO(e.getName(), jwxz, null);
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getXsaj(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        return jwzhDictRepository.getXsajlbDict().stream().map(dict -> {
            final BigScreenStatisticVO vo = new BigScreenStatisticVO();
            vo.setDimension(dict.getCt());
            vo.setMeasure(jqRepository.getXsajslByType(StringUtil.getPoliceStationPrefix(dict.getDm()),
                    StringUtil.getPoliceStationPrefix(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getZaaj(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        return unitPcsRepository.findAllByType(1).parallelStream()
                .map(unit -> {
                    final BigScreenStatisticVO vo = new BigScreenStatisticVO();
                    vo.setDimension(unit.getShortName());
                    vo.setMeasure(jqRepository.getXzajslByType(null, unit.getUnitCode(), timeParams.getBeginTime(), timeParams.getEndTime()));
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getJmll(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        List<BigScreenStatisticVO> result = new ArrayList<>();
        result.add(new BigScreenStatisticVO("抢劫", jqRepository.getQjSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), null));
        result.add(new BigScreenStatisticVO("抢夺", jqRepository.getQdSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), null));
        result.add(new BigScreenStatisticVO("盗窃车内财物", jqRepository.getDqcncwSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), null));
        result.add(new BigScreenStatisticVO("盗窃机动车", jqRepository.getDqjdcSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), null));
        result.add(new BigScreenStatisticVO("接触性诈骗", jqRepository.getJcxzpSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), null));
        result.add(new BigScreenStatisticVO("扒窃", jqRepository.getPqSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), null));
        return result;
    }

    @Override
    public List<BigScreenStatisticVO> getDjzl(StatisticRequestVO requestVO) {
        List<BigScreenStatisticVO> result = new ArrayList<>();
        final TimeParams timeParams = requestVO.getTimeParams();
        TimePeriodVO.listOf(timeParams).forEach(timePeriodVO -> {
            result.add(new BigScreenStatisticVO(timePeriodVO.getName(), jqRepository.getXsajXyrSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), "刑事案件"));
            result.add(new BigScreenStatisticVO(timePeriodVO.getName(), jqRepository.getXzajXyrSl(StringUtil.getPrefixCode(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()), "治安案件"));
        });
        return result;
    }

    @Override
    public List<BigScreenStatisticVO> getXzdw(StatisticRequestVO requestVO) {
        HashMap<String, String> unitHashMap = getDistrictMap();
        unitHashMap.put("510500", "市局");

        return unitHashMap.entrySet().stream().map(e -> {
            String unitCodePrefix = e.getKey();
            String unitName = e.getValue();
            Long xzdw = jqRepository.getXzdw(unitCodePrefix, requestVO.getTimeParams().getBeginTime(), requestVO.getTimeParams().getEndTime());
            return new BigScreenStatisticVO(unitName, xzdw, null);
        }).collect(Collectors.toList());
    }

    @NotNull
    private static HashMap<String, String> getDistrictMap() {
        HashMap<String, String> districtMap = new HashMap<>();
        districtMap.put("510502", "江阳区");
        districtMap.put("510503", "纳溪区");
        districtMap.put("510504", "龙马潭");
        districtMap.put("510521", "泸县");
        districtMap.put("510522", "合江区");
        districtMap.put("510524", "叙永县");
        districtMap.put("510525", "古蔺县");
        return districtMap;
    }

    @Override
    public BigScreenStatisticVO getAjhcl(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        final BigScreenStatisticVO vo = new BigScreenStatisticVO();
        vo.setDimension("案件合成率");
        vo.setMeasure(CalculateUtils.calculatePercentage(
                jqRepository.getXzhcSl(StringUtil.getPoliceStationPrefix(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()),
                jqRepository.getXzSl(StringUtil.getPoliceStationPrefix(requestVO.getDistrict()), timeParams.getBeginTime(), timeParams.getEndTime()))
        );
        return vo;
    }

    @Override
    public List<BigScreenStatisticVO> getZafx(StatisticRequestVO requestVO) {
        TimeParams timeParams = requestVO.getTimeParams();
        HashMap<String, String> areaMap = getDistrictMap();
        return areaMap.entrySet().stream().map(e -> {
            Long measure = jqRepository.getZafx(e.getKey(), timeParams.getBeginTime(), timeParams.getEndTime());
            return new BigScreenStatisticVO(e.getValue(), measure, null);
        }).collect(Collectors.toList());
    }

    @Override
    public List<FxsbVO> getFxsb(StatisticRequestVO requestVO) {
        TimeParams timeParams = requestVO.getTimeParams();
        List<Map<String, Object>> fxsb = jqRepository.getFxsb(timeParams.getBeginTime(), timeParams.getEndTime());
        return JsonUtil.parseArray(JsonUtil.toJsonString(fxsb).toLowerCase(), FxsbVO.class);
    }

    @Override
    public List<BigScreenStatisticVO> getRdfx(StatisticRequestVO requestVO) {
        HashMap<String, String> typeMap = new HashMap<>();
        typeMap.put("1", "地名");
        typeMap.put("2", "人名");
        typeMap.put("3", "实体");
        TimeParams timeParams = requestVO.getTimeParams();
        return typeMap.entrySet().stream().map(e -> {
            Long measure = jqRepository.getRdfx(e.getKey(), timeParams.getBeginTime(), timeParams.getEndTime());
            return new BigScreenStatisticVO(e.getValue(), measure, null);
        }).collect(Collectors.toList());
    }

    @Override
    public SystemLogVO getZxrsrzl(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        final SystemLogVO vo = new SystemLogVO();
        vo.setRzl(jqRepository.countLog(timeParams.getBeginTime(), timeParams.getEndTime()));
        vo.setYwsqsl(jqRepository.countYwsqsl(timeParams.getBeginTime(), timeParams.getEndTime()));
        int i = new Random().nextInt(200);
        vo.setZxrs((long) i);
        return vo;
    }

    @Override
    public List<BigScreenStatisticVO> getSygn(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        final Long all = jqRepository.getAllSygn(requestVO.getDistrict(), timeParams.getBeginTime(),
                timeParams.getEndTime());
        return jqRepository.getSygn(requestVO.getDistrict(), timeParams.getBeginTime(), timeParams.getEndTime())
                .stream()
                .filter(item -> Objects.nonNull(item.get("name")))
                .map(item -> {
                    final BigScreenStatisticVO vo = new BigScreenStatisticVO();
                    vo.setDimension(item.get("name").toString());
                    final Long count = ((BigDecimal) item.get("num")).longValue();
                    vo.setMeasure(CalculateUtils.calculatePercentage(count, all));
                    return vo;
                }).collect(Collectors.toList());
    }

    @Override
    public List<BigScreenStatisticVO> getFwfb(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        List<BigScreenStatisticVO> result = new ArrayList<>();
        getAllUnit().forEach(unit -> {
            result.add(new BigScreenStatisticVO(unit.getShortName(),
                    jqRepository.countApp(StringUtil.getPoliceStationPrefix(unit.getUnitCode()), timeParams.getBeginTime(),
                            timeParams.getEndTime()), "APP"));
            result.add(new BigScreenStatisticVO(unit.getShortName(),
                    jqRepository.countPc(StringUtil.getPoliceStationPrefix(unit.getUnitCode()), timeParams.getBeginTime(),
                            timeParams.getEndTime()), "PC"));
        });
        return result;
    }

    @Override
    public List<BigScreenStatisticVO> getPthyqs(StatisticRequestVO requestVO) {
        final TimeParams timeParams = requestVO.getTimeParams();
        return getAllUnit().stream().map(unit -> {
            final BigScreenStatisticVO vo = new BigScreenStatisticVO();
            vo.setDimension(unit.getShortName());
            vo.setMeasure(
                    jqRepository.countPthyqs(StringUtil.getPoliceStationPrefix(unit.getUnitCode()), timeParams.getBeginTime(),
                            timeParams.getEndTime()));
            return vo;
        }).collect(Collectors.toList());

    }

    private List<UnitPcsEntity> getAllUnit() {
        final List<UnitPcsEntity> pcs = unitPcsRepository.findAllByType(1);
        final UnitPcsEntity unitPcsEntity = new UnitPcsEntity();
        unitPcsEntity.setUnitCode("510500");
        unitPcsEntity.setShortName("泸州市局");
        pcs.add(unitPcsEntity);
        return pcs;
    }

    @Override
    public List<BigScreenStatisticVO> getZlzx(StatisticRequestVO requestVO) {
        TimeParams timeParams = requestVO.getTimeParams();
        return getDistrictMap().entrySet().stream()
                .map(e -> {
                    Long measure = jqRepository.getZlzx(e.getKey(), timeParams.getBeginTime(), timeParams.getEndTime());
                    return new BigScreenStatisticVO(e.getValue(), measure, null);
                }).collect(Collectors.toList());
    }

    @Override
    public List<YjxxVO> getYjxx(StatisticRequestVO requestVO) {
        TimeParams timeParams = requestVO.getTimeParams();
        return jqRepository.getYjxx(timeParams.getBeginTime(), timeParams.getEndTime()).stream()
                .map(e -> {
                    YjxxVO yjxx = new YjxxVO();
                    String sfzh = (String) e.get("GLSFZH");
                    String xb = StringUtil.parseGenderFromIdNumber(sfzh, true);
                    BigDecimal bkjb = (BigDecimal) e.get("BKJB");
                    ControlLevelEnum controlLevelEnum = ControlLevelEnum.codeOf(bkjb.toString());

                    yjxx.setXm(e.get("GLXM"));
                    yjxx.setXb(xb);
                    yjxx.setSj(e.get("YJSJ"));
                    yjxx.setYjyy(e.get("BKSY"));
                    yjxx.setZycd(controlLevelEnum == null ? "" : controlLevelEnum.getName());
                    yjxx.setSb(e.get("HDLYBCN"));
                    yjxx.setYjdd(e.get("HDFSDXZ"));
                    yjxx.setPictureUrl(e.get("PICURLS"));
                    return yjxx;
                }).collect(Collectors.toList());
    }
}
