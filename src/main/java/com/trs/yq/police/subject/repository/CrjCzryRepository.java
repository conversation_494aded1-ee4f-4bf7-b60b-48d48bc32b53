package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjCzryEntity;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 出入境常住人员
 *
 * <AUTHOR>
 */
@Repository
public interface CrjCzryRepository extends BaseRepository<CrjCzryEntity, String> {

    /**
     * 分页查询常住人员
     *
     * @param searchValue 模糊检索参数
     * @param deptCode    部门code
     * @param beginTime   起始时间
     * @param endTime     结束时间
     * @param status      居住状态
     * @param page        分页
     * @return {@link CrjCzryEntity}
     */
    @Query("select c from CrjCzryEntity c where c.createTime > :beginTime and c.createTime < :endTime "
        + "and (:status is null or c.livingStatus =:status) "
        + "and (:deptCode is null or c.livingPoliceStation like concat(:deptCode,'%')) "
        + "and (:searchValue is null or concat(c.certificateNumber, c.cnName, c.enName) like concat('%',:searchValue,'%') ) ")
    Page<CrjCzryEntity> findCzryPageList(@Param("searchValue") String searchValue,
        @Param("deptCode") String deptCode,
        @Param("beginTime") LocalDateTime beginTime,
        @Param("endTime") LocalDateTime endTime,
        @Param("status") String status,
        Pageable page);

    /**
     * 通过身份证检查人员是否与专题有关联关系
     *
     * @param certificateNumber  身份证
     * @return 是否存在
     */
    @Query("select case when count(p.id) > 0 then true else false end "
        + "from CrjCzryEntity p where p.certificateNumber = :certificateNumber ")
    boolean checkExistByCertificateNumber(@Param("certificateNumber") String certificateNumber);

    /**
     * 根据证件号码查询
     *
     * @param certificateNumber 证件号码
     * @return 结果
     */
    CrjCzryEntity findByCertificateNumber(@Param("certificateNumber") String certificateNumber);

    /**
     * 查询签证到期时间在指定日期前的
     *
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return 结果
     */
    List<CrjCzryEntity> findByVisaValidityBetween(@Param("beginTime") LocalDateTime beginTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据证件号码查询人员名称（czry和jwry）
     *
     * @param idNumber 证件号码
     * @return 结果
     */
    @Query(nativeQuery = true,
            value = "select * from (SELECT tpcc.EN_NAME AS name FROM T_PS_CRJ_CZRY tpcc WHERE tpcc.CERTIFICATE_NUMBER = :idNumber UNION SELECT tpcjd.EN_NAME FROM T_PS_CRJ_JWRY_DETAIL tpcjd WHERE tpcjd.ID_NUMBER = :idNumber ) a where ROWNUM = 1")
    String selectNameFromCrjByCertificateNumber(@Param("idNumber") String idNumber);

    /**
     * 根据id查询列表
     *
     * @param recordIds id列表
     * @return 人员列表
     */
    @Query("SELECT crj FROM CrjCzryEntity crj WHERE crj.id in (:recordIds)")
    List<CrjCzryEntity> findByIds(@Param("recordIds") List<String> recordIds);

}
