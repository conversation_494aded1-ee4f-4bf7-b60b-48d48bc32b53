package com.trs.yq.police.subject.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.domain.entity.CrjSfryReportEntity;
import com.trs.yq.police.subject.domain.entity.GridMangerEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.mysqlDatasource.entity.CrjSfryReportSourceEntity;
import com.trs.yq.police.subject.mysqlDatasource.repository.CrjSfryReportSourceRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.GridMangerRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import java.text.SimpleDateFormat;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date 2023/10/8 16:52
 */
@Data
public class SfryExportVO {

    @ExcelProperty(value = "举报信息")
    private String content;
    @ExcelProperty(value = "网格员")
    private String gridManagerName;
    @ExcelProperty(value = "网格员编号")
    private String gridCode;
    @ExcelProperty(value = "警务区民警")
    private String policeName;
    @ExcelProperty(value = "民警电话")
    private String policePhoneNumber;
    @ExcelProperty(value = "举报日期")
    private String reportTime;
    @ExcelProperty(value = "核实状态")
    private String verifyResult;
    @ExcelProperty(value = "举报位置")
    private String address;
    @ExcelProperty(value = "核实时间")
    private String verifyTime;
    @ExcelProperty(value = "接受方")
    private String acceptor;
    @ExcelProperty(value = "备注")
    private String description;

    /**
     * 转vo
     *
     * @param crjSfryReportEntity entitu
     * @param key                 key
     * @return vo
     */
    public static SfryExportVO toExportVO(CrjSfryReportEntity crjSfryReportEntity, String key) {

        SfryExportVO vo = new SfryExportVO();
        CrjSfryReportSourceRepository crjSfryReportRepository = BeanUtil.getBean(CrjSfryReportSourceRepository.class);
        CrjSfryReportSourceEntity sourceEntity = crjSfryReportRepository.findByUuid(crjSfryReportEntity.getReportId());
        vo.setContent(sourceEntity.getContent());
        GridMangerRepository gridMangerRepository = BeanUtil.getBean(GridMangerRepository.class);
        GridMangerEntity gridManger = gridMangerRepository.findByGridManagerPhoneNumber(
            StringUtil.aesDecrypt(sourceEntity.getPhone(), key));
        if (Objects.nonNull(gridManger)) {
            vo.setGridManagerName(gridManger.getGridManagerName());
            vo.setGridCode(gridManger.getId());
            vo.setPoliceName(gridManger.getPoliceName());
            vo.setPolicePhoneNumber(gridManger.getPolicePhoneNumber());
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        if (Objects.nonNull(crjSfryReportEntity.getReportTime())) {
            vo.setReportTime(simpleDateFormat.format(crjSfryReportEntity.getReportTime()));
        }
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        if(crjSfryReportEntity.getIsVerified()){
            vo.setVerifyResult(
                dictRepository.findByTypeAndCode(CrjConstants.SFRY_VERIFY_STATUS, crjSfryReportEntity.getVerifyResult())
                    .getName());
            if (Objects.nonNull(crjSfryReportEntity.getVerifyTime())) {
                vo.setVerifyTime(simpleDateFormat.format(crjSfryReportEntity.getVerifyTime()));
            }
        }
        if(StringUtils.isNotBlank(crjSfryReportEntity.getAcceptor())){
            UnitRepository unitRepository = BeanUtil.getBean(UnitRepository.class);
            UnitEntity byUnitCode = unitRepository.findByUnitCode(crjSfryReportEntity.getAcceptor());
            vo.setAcceptor(byUnitCode.getShortname());
        }
        vo.setDescription(crjSfryReportEntity.getDescription());
        vo.setAddress(sourceEntity.getAddress());
        return vo;
    }
}
