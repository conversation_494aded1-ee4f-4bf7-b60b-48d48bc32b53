package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.LocationEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/21 11:33
 */
@Repository
public interface LocationRepository extends BaseRepository<LocationEntity, Integer> {
    /**
     * 模糊查询
     *
     * @param searchValue 模糊查询关键词
     * @return {@link LocationEntity}
     */
    @Query("select l from LocationEntity l where :searchValue is null or ( l.name like concat('%',:searchValue,'%') or l.address like concat('%',:searchValue,'%') )")
    List<LocationEntity> findByNameLike(@Param("searchValue") String searchValue);
}
