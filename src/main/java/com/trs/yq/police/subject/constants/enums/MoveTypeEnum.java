package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2021/08/17
 */
public enum MoveTypeEnum {
    /**
     * enums
     */
    IN("1", "流入"),
    OUT("2", "流出");

    MoveTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private final String code;

    @Getter
    private final String name;
}
