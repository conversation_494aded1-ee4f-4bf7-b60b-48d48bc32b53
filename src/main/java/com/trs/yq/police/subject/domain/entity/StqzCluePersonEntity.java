package com.trs.yq.police.subject.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 省厅情指-线索人员
 *
 * <AUTHOR>
 * @date 2025/7/30
 */
@Data
@TableName("zhzh_qzx_xsryxx")
public class StqzCluePersonEntity {

    /**
     * 线索id
     */
    private String xsid;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 身份证号码
     */
    private String sfz;

    /**
     * 户籍地
     */
    private String hjdDm;

    public PersonEntity of(){
        PersonEntity personEntity = new PersonEntity();
        personEntity.setName(xm);
        personEntity.setIdNumber(sfz);
        personEntity.setRegisteredResidence(hjdDm);
        return personEntity;
    }
}
