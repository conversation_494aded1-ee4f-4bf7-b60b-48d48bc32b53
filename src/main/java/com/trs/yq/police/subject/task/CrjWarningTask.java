package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.GeoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 签证超期预警
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ConditionalOnProperty(value = "com.trs.crj.qzcqyj.task.enable", havingValue = "true")
public class CrjWarningTask {

    @Resource
    private CrjJwryDetailRepository crjJwryDetailRepository;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private CrjJwryRepository crjJwryRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private AlarmYjgjRepository yjgjRepository;

    /**
     * 签证超期预警
     */
    @Scheduled(cron = "${com.trs.crj.qzcqyj.task}")
    @Transactional(rollbackFor = RuntimeException.class)
    public void signOverdueSchedulingTask() {
        log.info("开始同步出入境专题预警");
        LocalDateTime now = LocalDate.now().atStartOfDay();
        LocalDateTime time = now.plusDays(7);
        List<CrjJwryDetailEntity> jwryOverdue = crjJwryDetailRepository.findByInChinaTimeBetween(toDate(now), toDate(time));
        jwryOverdue.forEach(this::jwryToWarning);
        List<AlarmYjgjEntity> track1 = yjgjRepository.getYjgj("未办住宿登记外国人");
        track1.forEach(this::wblzsdjToWarning);
        List<AlarmYjgjEntity> track2 = yjgjRepository.getYjgj("离开泸州外国人");
        track2.forEach(this::lklzwgrToWarning);
        log.info("出入境专题预警同步完毕！");
    }

    private Date toDate(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 常驻人员签证超期预警
     *
     * @param czry 常住人员
     */
    private void czryToWarning(CrjCzryEntity czry) {
        WarningEntity entity = new WarningEntity();
        entity.setWarningTime(LocalDateTime.now());
        entity.setWarningLevel("4");
        entity.setWarningSource(new ArrayList<>(Collections.singleton("常住人员")));
        entity.setWarningStatus("1");
        entity.setWarningDetails(czry.getEnName() + "(" + czry.getCertificateNumber() + ")触发签证超期预警，到期时间：" +
                czry.getVisaValidity().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        entity.setSubjectId("8");
        entity.setWarningType("48");
        entity.setWarningKey(czry.getCertificateNumber());
        entity.setWarningKeyType("0");
        entity.setAreaCode(czry.getLivingPoliceStation().substring(0, 6));
        warningRepository.save(entity);

        WarningTrajectoryEntity trajectory = new WarningTrajectoryEntity();
        trajectory.setIdNumber(czry.getCertificateNumber());
        trajectory.setSourceId(entity.getId());
        trajectory.setDateTime(entity.getWarningTime());
        trajectory.setAreaCode(czry.getLivingPoliceStation().substring(0, 6));
        warningTrajectoryRepository.save(trajectory);

        WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
        relation.setWarningId(entity.getId());
        relation.setTrajectoryId(trajectory.getId());
        warningTraceRelationRepository.save(relation);
    }

    /**
     * 临住人员签证超期
     *
     * @param jwry 临住人员
     */
    private void jwryToWarning(CrjJwryDetailEntity jwry) {
        WarningEntity entity = new WarningEntity();
        entity.setWarningTime(LocalDateTime.now());
        entity.setWarningLevel("4");
        entity.setWarningSource(new ArrayList<>(Collections.singleton("临住人员")));
        entity.setWarningStatus("1");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        if (Objects.isNull(jwry.getInChinaTime())) {
            return;
        }
        entity.setWarningDetails(jwry.getEnName() + "(" + jwry.getIdNumber() + ")触发签证超期预警，到期时间："
                + format.format(jwry.getInChinaTime()));
        entity.setSubjectId("8");
        entity.setWarningType("48");
        entity.setWarningKey(jwry.getIdNumber());
        entity.setWarningKeyType("0");
        List<CrjJwryEntity> jwryList = crjJwryRepository.findByIdNumberAndIdType(jwry.getIdNumber(), jwry.getIdType());
        String areaCode = null;
        if (!jwryList.isEmpty()) {
            String acceptor = jwryList.get(0).getAcceptor();
            if (StringUtils.isNotBlank(acceptor)) {
                areaCode = acceptor.substring(0, 6);
                entity.setAreaCode(areaCode);
            }
        }
        warningRepository.save(entity);

        WarningTrajectoryEntity trajectory = new WarningTrajectoryEntity();
        trajectory.setIdNumber(jwry.getIdNumber());
        trajectory.setSourceId(entity.getId());
        trajectory.setDateTime(entity.getWarningTime());
        trajectory.setAreaCode(areaCode);
        warningTrajectoryRepository.save(trajectory);

        WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
        relation.setWarningId(entity.getId());
        relation.setTrajectoryId(trajectory.getId());
        warningTraceRelationRepository.save(relation);
    }

    /**
     * 未办理住宿登记人员预警轨迹
     *
     * @param yjgj 预警轨迹
     */
    private void wblzsdjToWarning(AlarmYjgjEntity yjgj) {
        yjgjToWarning(yjgj, "49");
    }

    /**
     * 离开泸州外国人预警轨迹
     *
     * @param yjgj 预警轨迹
     */
    private void lklzwgrToWarning(AlarmYjgjEntity yjgj) {
        yjgjToWarning(yjgj, "22");
    }

    private void yjgjToWarning(AlarmYjgjEntity yjgj, String warningType) {
        //如果该轨迹已经生成过预警就跳过
        if (warningRepository.selectByRawData(yjgj.getId()) > 0) {
            log.info("轨迹: {} 已经生成过预警", yjgj.getId());
            return;
        }
        WarningEntity warning = new WarningEntity();
        warning.setWarningTime(LocalDateTime.now());
        warning.setWarningLevel("4");
        warning.setWarningSource(new ArrayList<>(Collections.singleton(yjgj.getHdlybcn())));
        warning.setWarningStatus("1");
        warning.setWarningDetails(yjgj.getHdxq());
        warning.setSubjectId("8");
        warning.setWarningType(warningType);
        warning.setWarningKey(yjgj.getGlsfzh());
        warning.setWarningKeyType("0");
        String areaCode = GeoUtil.getDistrictCode(yjgj.getHdjd(), yjgj.getHdwd());
        warning.setAreaCode(areaCode);
        warningRepository.save(warning);

        WarningTrajectoryEntity trajectory = new WarningTrajectoryEntity();
        trajectory.setIdNumber(yjgj.getGlsfzh());
        trajectory.setIdType("passport");
        trajectory.setSourceId(yjgj.getId());
        trajectory.setDateTime(yjgj.getHdfssj());
        trajectory.setAreaCode(areaCode);
        trajectory.setLng(yjgj.getHdjd());
        trajectory.setLat(yjgj.getHdwd());
        trajectory.setAddress(yjgj.getHdfsdd());
        if (StringUtils.isNotBlank(yjgj.getPicurls())) {
            String[] split = yjgj.getPicurls().split(",");
            if (split.length > 0) {
                trajectory.setImageUrl(split[0]);
            }
            if (split.length > 1) {
                trajectory.setCropUrl(split[0]);
            }
        }
        warningTrajectoryRepository.save(trajectory);

        WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
        relation.setWarningId(warning.getId());
        relation.setTrajectoryId(trajectory.getId());
        warningTraceRelationRepository.save(relation);
    }

}
