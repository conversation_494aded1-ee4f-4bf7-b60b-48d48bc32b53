package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.CrjMessageSendEntity;
import com.trs.yq.police.subject.utils.BeanUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/28 17:42
 */
@Data
public class CrjMessageSendVO implements Serializable {

    private static final long serialVersionUID = 6208914911623528649L;

    private String id;
    /**
     * 发送人
     */
    private String sender;
    /**
     * 接受人
     */
    private String receiver;
    /**
     * 接收人电话
     */
    private String phone;
    /**
     * 详情
     */
    private String content;
    /**
     * 详情
     */
    private LocalDateTime sendTime;

    /**
     * 实体类转vo
     *
     * @param entity 实体类
     * @return vo
     */
    public static CrjMessageSendVO of(CrjMessageSendEntity entity) {
        final CrjMessageSendVO vo = new CrjMessageSendVO();
        BeanUtil.copyPropertiesIgnoreNull(entity, vo);
        vo.setSender(entity.getCrByName());
        return vo;
    }
}
