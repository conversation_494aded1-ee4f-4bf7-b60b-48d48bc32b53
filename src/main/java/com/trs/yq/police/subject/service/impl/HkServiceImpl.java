package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.hikvision.artemis.sdk.ArtemisHttpUtil;
import com.hikvision.artemis.sdk.config.ArtemisConfig;
import com.trs.yq.police.subject.service.HkService;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/01/19
 */
@Slf4j
@Service
public class HkServiceImpl implements HkService {

    private static final String ARTEMIS_PATH = "/artemis";

    /**
     * 查询摄像头实时播放url
     *
     * @param cameraId 摄像头id
     * @return url
     */
    @Override
    public String getCameraPreviewUrl(String cameraId) {
        ArtemisConfig.host = "***********:443";
        // 合作方Key
        ArtemisConfig.appKey = "24898191";
        // 合作方Secret
        ArtemisConfig.appSecret = "W0fK8eaHTLyPvs5j4MGk";
        String getCamApi = ARTEMIS_PATH + "/api/video/v1/cameras/previewURLs";
        Map<String, String> paramMap = new HashMap<String, String>(2) {
            private static final long serialVersionUID = -1119301706903311897L;

            {
                put("cameraIndexCode", cameraId);
                put("protocol", "hls");
            }
        };
        String body = JsonUtil.toJsonString(paramMap);
        Map<String, String> path = new HashMap<String, String>(2) {
            private static final long serialVersionUID = -7931231531294221770L;

            {
                put("https://", getCamApi);
            }
        };
        try {
            String response = ArtemisHttpUtil.doPostStringArtemis(path, body, null, null, "application/json");
            JsonNode jsonNode = JsonUtil.parseJsonNode(response);
            return jsonNode.get("data").get("url").asText();
        } catch (Exception exception) {
            log.error("海康摄像头播放地址请求失败！", exception);
        }
        return null;
    }
}
