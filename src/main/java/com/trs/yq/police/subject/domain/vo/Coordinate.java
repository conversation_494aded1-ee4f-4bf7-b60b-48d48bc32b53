package com.trs.yq.police.subject.domain.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.lucene.util.SloppyMath;

/**
 * <AUTHOR>
 * @date 2021/11/23
 */
@Data
@AllArgsConstructor
public class Coordinate implements Serializable {
    private static final long serialVersionUID = -1419365693979657536L;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 计算两个经纬度坐标的距离
     *
     * @param other other
     * @return 距离 单位：米
     */
    public Double distanceTo(Coordinate other) {
        return SloppyMath.haversinMeters(lat, lng, other.lat, other.lng);
    }
}
