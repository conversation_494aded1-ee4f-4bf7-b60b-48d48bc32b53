package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Date 2022/12/20 11:34
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_ALARM_BKXX_RY")
public class AlarmBkxxRyEntity {

    @Id
    private String id;

    private String bkzj;

    private String xm;

    private Integer xb;

    private String rylbDm;

    private String ryxlDm;
}
