package com.trs.yq.police.subject.message;

import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.entity.WarningTypeEntity;

/**
 * 预警推送相关服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/08
 **/
public interface WarningPushService {

    /**
     * 推送技侦手段预警的短信
     *
     * @param warning  预警实体
     * @param idNumber 身份证号
     */
    void pushJzsdMessage(WarningEntity warning, String idNumber);

    /**
     * 有实名的推送
     *
     * @param warningType 预警类型
     * @param warning     预警实体
     * @param idNumber    身份证号
     */
    void push(WarningTypeEntity warningType, WarningEntity warning, String idNumber);
}
