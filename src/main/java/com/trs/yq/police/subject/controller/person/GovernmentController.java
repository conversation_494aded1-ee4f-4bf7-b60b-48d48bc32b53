package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import com.trs.yq.police.subject.domain.vo.GovernmentInfoVO;
import com.trs.yq.police.subject.service.GovernmentService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2021/12/23 15:51
 */
@RestController
@RequestMapping("/person")
@Validated
public class GovernmentController {
    @Resource
    private GovernmentService governmentService;

    /**
     * 获取政府主管部门信息
     * http://192.168.200.192:3001/project/4897/interface/api/134353
     *
     * @param personId 人员id
     * @return {@link GovernmentInfoVO}
     */
    @GetMapping("{personId}/government")
    public GovernmentInfoVO getGovernmentInfo(@PathVariable String personId) {
        return governmentService.getGovernmentByModuleAndRecordId(CommonExtentEntity.PERSON, personId);
    }

    /**
     * 更新政府主管部门信息
     * http://192.168.200.192:3001/project/4897/interface/api/134355
     *
     * @param governmentInfoVO {@link GovernmentInfoVO}
     * @param personId         人员id
     */
    @PutMapping("{personId}/government")
    public void updateGovernmentInfo(@PathVariable String personId, @RequestBody GovernmentInfoVO governmentInfoVO) {
        governmentService.updateGovernmentInfo(CommonExtentEntity.PERSON, personId, governmentInfoVO);
    }
}
