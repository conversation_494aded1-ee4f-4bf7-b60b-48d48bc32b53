package com.trs.yq.police.subject.excel;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.REPEAT_STRATEGY_STOP;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.trs.yq.police.subject.constants.enums.CrjCzryFieldEnum;
import com.trs.yq.police.subject.constants.enums.PersonFieldEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjCzryEntity;
import com.trs.yq.police.subject.domain.entity.JwzhDictEntity;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.vo.ImportResultListVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.CrjCzryRepository;
import com.trs.yq.police.subject.repository.JwzhDictRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 出入境-常住人员导入
 *
 * <AUTHOR>
 */
@Slf4j
@Builder
public class ReadCrjCzryExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    /**
     * 常住人员
     */
    private final CrjCzryRepository crjCzryRepository;
    /**
     * 字典持久层
     */
    private final JwzhDictRepository jwzhDictRepository;
    /**
     * 派出所
     */
    private final UnitRepository unitRepository;
    /**
     * 当前用户
     */
    private final LoginUser currentUser;

    /**
     * 重复策略 1-不导入, 2-覆盖导入，3-继续导入
     */
    private final String repeatStrategy;

    /**
     * 字段映射
     */
    @Getter
    private final Map<Integer, CrjCzryFieldEnum> columnMap = new ConcurrentHashMap<>();

    /**
     * 处理总量
     */
    @Getter
    private final AtomicInteger analysisCount = new AtomicInteger();

    /**
     * 表头行数
     */
    @Getter
    private final AtomicInteger headCount = new AtomicInteger(0);

    /**
     * 失败记录
     */
    @Getter
    private final List<ImportResultListVO> failResult = new LinkedList<>();

    /**
     * 成功记录
     * key - 行数
     * value - key : 属性， value : 值
     */
    @Getter
    private final Map<Integer, Map<Integer, String>> success = new HashMap<>();

    /**
     * 失败记录
     */
    @Getter
    private final List<Map<Integer, String>> failRows = new LinkedList<>();

    private List<UnitEntity> policeStation;
    private List<JwzhDictEntity> dictList;

    private final Pattern phoneNumberPattern = Pattern.compile("^1[3-9][0-9]{9}$");

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 检验是否为正常的header
        if (checkHeader(headMap)) {
            // 通过header 转换字段结构
            headMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    if (CrjCzryFieldEnum.cnNameOf(entry.getValue()) != null) {
                        columnMap.put(entry.getKey(), CrjCzryFieldEnum.cnNameOf(entry.getValue()));
                    }
                });

            if (!columnMap.containsValue(CrjCzryFieldEnum.certificateNumber)) {
                throw new ParamValidationException("导入的表格格式不正确，请核实！");
            }

            // 缓存字典
            policeStation = unitRepository.findByType("4");
            dictList = jwzhDictRepository.getJwzhDict("GA_D_CYZJDM");
        }
        headCount.incrementAndGet();
    }

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext context) {
        // 1.先找到当前行的身份证信息
        if (checkAssignColumn(data)) {
            success.put(analysisCount.get() + headCount.get(), data);
        } else {
            failRows.add(data);
        }
        // 计数
        analysisCount.incrementAndGet();
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 存储
        success.forEach((key, value) -> {
            Map<String, String> personMap = new LinkedHashMap<>();
            value.forEach((k, v) -> {
                CrjCzryFieldEnum fieldEnum = columnMap.get(k);
                if (fieldEnum != null) {
                    personMap.put(fieldEnum.getField(), v);
                }
            });
            // 生成人员
            generatePerson(personMap);
        });
    }

    private boolean checkHeader(Map<Integer, String> headMap) {
        return headMap.values().stream().anyMatch(value -> Objects.nonNull(PersonFieldEnum.cnNameOf(value)));
    }

    private boolean checkAssignColumn(Map<Integer, String> rows) {
        AtomicBoolean validate = new AtomicBoolean(true);
        // 校验身份证
        if (validate.get()) {
            // 姓名校验通过才校验身份证
            checkIdNumber(rows, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.country, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.certificateType, validate);
            checkCertificateType(rows, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.certificateNumber, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.en_name, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.tel, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.gender, validate);
            checkGender(rows, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.livingPoliceStation, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.livingCounty, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.livingAddress, validate);
            checkDateExist(rows, CrjCzryFieldEnum.birthday, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.residenceMark, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.residencePersonType, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.stayReason, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.livingCheck, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.createUser, validate);
            checkDateExist(rows, CrjCzryFieldEnum.createTime, validate);
            checkDateExist(rows, CrjCzryFieldEnum.entryTime, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.entryReason, validate);
            checkColumnExist(rows, CrjCzryFieldEnum.country, validate);
            checkDateExist(rows, CrjCzryFieldEnum.certificateValidity, validate);
        }
        return validate.get();
    }

    private void checkColumnExist(Map<Integer, String> rows, CrjCzryFieldEnum field, AtomicBoolean validate) {
        columnMap.entrySet().stream()
                .filter(entry -> field.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkColumnIsBlank(rows.get(entry.getKey()), field.getCnName(), validate));
    }

    private void checkColumnIsBlank(String column, String field, AtomicBoolean validate) {
        if (StringUtils.isBlank(column)) {
            // 如果名字不为空，则将标识符改为true
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg(field + "不能为空"))
                    .build();
            failResult.add(validFailResult);
        }
    }

    private void checkDateExist(Map<Integer, String> rows, CrjCzryFieldEnum field, AtomicBoolean validate) {
        columnMap.entrySet().stream()
                .filter(entry -> field.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkDateValidate(rows.get(entry.getKey()), field.getCnName(), validate));
    }

    private void checkDateValidate(String dateStr, String field, AtomicBoolean validate) {
        if (StringUtils.isNotBlank(dateStr)) {
            try {
                LocalDate.parse(dateStr, formatter);
            } catch (Exception e) {
                validate.set(false);
                final ImportResultListVO validFailResult = ImportResultListVO.builder()
                        .status(1)
                        .desc(generateFailMsg(field + "格式错误，有效日期格式例:2023-01-01"))
                        .build();
                failResult.add(validFailResult);
            }
        }
    }

    private void checkGender(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet().stream()
                .filter(entry -> CrjCzryFieldEnum.gender.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkGenderValidate(rows.get(entry.getKey()), validate));
    }

    private void checkGenderValidate(String str, AtomicBoolean validate) {
        if (!str.equals("男") && !str.equals("女")) {
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("性别格式错误，有效性别格式例：男/女"))
                    .build();
            failResult.add(validFailResult);
        }
    }

    private void checkCertificateType(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet().stream()
                .filter(entry -> CrjCzryFieldEnum.certificateType.equals(entry.getValue()))
                .findFirst()
                .ifPresent(entry -> checkCertificateTypeValidate(rows.get(entry.getKey()), validate));
    }

    private void checkCertificateTypeValidate(String str, AtomicBoolean validate) {
        Optional<JwzhDictEntity> dictEntity = dictList.stream()
                .filter(dict -> dict.getCt().equals(str))
                .findFirst();
        if (!dictEntity.isPresent()) {
            validate.set(false);
            final ImportResultListVO validFailResult = ImportResultListVO.builder()
                    .status(1)
                    .desc(generateFailMsg("证件类型错误！"))
                    .build();
            failResult.add(validFailResult);
        }
    }

    private void checkIdNumber(Map<Integer, String> rows, AtomicBoolean validate) {
        columnMap.entrySet().stream()
            .filter(entry -> CrjCzryFieldEnum.certificateNumber.equals(entry.getValue()))
            .findFirst()
            .ifPresent(entry -> checkIdNumberCompliance(rows.get(entry.getKey()), entry.getKey(), validate));
    }

    private void checkIdNumberCompliance(String idNumber, Integer rowIndex, AtomicBoolean validate) {
        if (validate.get()) {
            // 校验是否与分析完成的文件重复
            success.entrySet().stream()
                .filter(successEntry -> StringUtils.equals(successEntry.getValue().get(rowIndex), idNumber))
                .findFirst()
                .ifPresent(successEntry -> checkIdNumberFileRepeat(validate, successEntry));
        }

        if (validate.get()) {
            // 校验是否与数据库重复
            checkIdNumberDatabaseRepeat(idNumber, validate);
        }
    }

    private void checkIdNumberDatabaseRepeat(String idNumber, AtomicBoolean validate) {
        if (repeatStrategy.equals(REPEAT_STRATEGY_STOP)
            && crjCzryRepository.checkExistByCertificateNumber(idNumber)) {
            // 如果选择为不导入，则当前用户为不能导入，则为失败
            final ImportResultListVO repeatFailResult = ImportResultListVO.builder()
                .status(1)
                .desc(generateFailMsg("证件号码与常住人员库重复"))
                .build();
            failResult.add(repeatFailResult);
            validate.compareAndSet(true, false);
        }
    }

    private void checkIdNumberFileRepeat(AtomicBoolean validate, Map.Entry<Integer, Map<Integer, String>> successEntry) {
        final Integer repeatIndex = successEntry.getKey();
        final int currentIndex = headCount.get() + analysisCount.get() + 1;
        // 身份证重复
        final ImportResultListVO initialRepeatFail = ImportResultListVO.builder()
            .status(1)
            .desc("行" + repeatIndex + "与行" + currentIndex + "数据重复")
            .build();
        failResult.add(initialRepeatFail);
        final ImportResultListVO repeatFail = ImportResultListVO.builder()
            .status(1)
            .desc("行" + currentIndex + "与行" + repeatIndex + "数据重复")
            .build();
        failResult.add(repeatFail);
        // 去除成功的数据,增加至错误信息
        final Map<Integer, String> remove = success.remove(repeatIndex);
        failRows.add(remove);
        validate.compareAndSet(true, false);
    }

    private String generateFailMsg(String errorMsg) {
        return "第".concat(String.valueOf(analysisCount.get() + headCount.get() + 1))
            .concat("行 ").concat(errorMsg);
    }

    private void generatePerson(Map<String, String> personMap) {
        // 读取的excel信息转换为人员实体
        CrjCzryEntity person = JsonUtil.parseObject(JsonUtil.toJsonString(personMap), CrjCzryEntity.class);
        assert person != null;
        // 验证人员是否存在
        final String idNumber = person.getCertificateNumber();
        // 回填身份证大写
        person.setCertificateNumber(idNumber);
        final CrjCzryEntity entity = crjCzryRepository.findByCertificateNumber(idNumber);
        if (Objects.isNull(entity)) {
            // 不存在则补充创建人信息
            person.setCrBy(currentUser.getId());
            person.setCrByName(currentUser.getRealName());
            person.setCrTime(LocalDateTime.now());
            person.setCrDept(currentUser.getUnitName());
            person.setCrDeptCode(currentUser.getUnitCode());
        } else {
            // 存在则不更新部分使用老数据
            BeanUtil.copyPropertiesIgnoreNull(person, entity);
            person = entity;
        }
        person.setUpBy(currentUser.getId());
        person.setUpByName(currentUser.getRealName());
        person.setUpTime(LocalDateTime.now());
        //处理时间类型
        person.setBirthday(processTime(personMap.get("birthday")));
        person.setVisaSignDate(processTime(personMap.get("visaSignDate")));
        person.setVisaValidity(processTime(personMap.get("visaValidity")));
        person.setCreateTime(processTime(personMap.get("createTime")));
        person.setEntryTime(processTime(personMap.get("entryTime")));

        person.setLivingPoliceStation(getPoliceStationCode(personMap.get("livingPoliceStation")));
        crjCzryRepository.save(person);
    }

    private String getPoliceStationCode(String stationName) {
        UnitEntity unit = policeStation.stream()
                .filter(unitEntity -> StringUtils.isNotBlank(stationName)
                        && unitEntity.getUnitName().contains(stationName))
                .findAny().orElse(null);
        return unit == null ? null : unit.getUnitCode();
    }

    private LocalDateTime processTime(String timeStr) {
        if (StringUtils.isNotBlank(timeStr)) {
            LocalDate date = LocalDate.parse(timeStr, formatter);
            if (date != null) {
                return date.atStartOfDay();
            }
        }
        return null;
    }

}
