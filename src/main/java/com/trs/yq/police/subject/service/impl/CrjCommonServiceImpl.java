package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.constants.enums.CrjDispatchStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwryRegistrationStatusEnum;
import com.trs.yq.police.subject.constants.enums.CrjJwrySourceTypeEnum;
import com.trs.yq.police.subject.constants.enums.TimeRangeEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.CrjReadRecordEntity;
import com.trs.yq.police.subject.domain.entity.FilterValue;
import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.entity.UnitEntity;
import com.trs.yq.police.subject.domain.vo.UnitVO;
import com.trs.yq.police.subject.domain.vo.UnreadRecordVO;
import com.trs.yq.police.subject.repository.CrjJwryRepository;
import com.trs.yq.police.subject.repository.CrjReadRecordRepository;
import com.trs.yq.police.subject.repository.CrjSfryReportRepository;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.UnitRepository;
import com.trs.yq.police.subject.service.CrjCommonService;
import com.trs.yq.police.subject.service.DepartmentService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/24 14:04
 */
@Service
public class CrjCommonServiceImpl implements CrjCommonService {

    @Resource
    private UnitRepository unitRepository;

    @Resource
    private CrjReadRecordRepository crjReadRecordRepository;

    @Resource
    private CrjSfryReportRepository crjSfryReportRepository;

    @Resource
    private DictRepository dictRepository;

    @Resource
    private CrjJwryRepository crjJwryRepository;
    @Resource
    private DepartmentService departmentService;

    @Value("${com.trs.crj.jwry.planLeaveLimitTime}")
    public Integer planLeaveLimitTime = 24;

    @Override
    public List<UnitVO> getAcceptor() {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        String unitCode = currentUser.getUnitCode();
        if (CrjConstants.SJZD.equals(unitCode)) {
            return CrjConstants.SQSXZD.stream().map(code -> {
                UnitEntity byUnitCode = unitRepository.findByUnitCode(code);
                return new UnitVO(byUnitCode.getShortname(), byUnitCode.getUnitCode(), this.getPcs(code));
            }).collect(Collectors.toList());
        } else if (CrjConstants.SQSXZD.contains(unitCode)) {
            return getPcs(unitCode);
        } else {
            return Collections.emptyList();
        }
    }

    //获取派出所
    private List<UnitVO> getPcs(String unitCode) {
        UnitEntity byUnitCode = unitRepository.findByUnitCode(unitCode);
        return unitRepository.findByTypeAndCodeLike("4", byUnitCode.getAreaCode()).stream()
                .map(e -> new UnitVO(e.getUnitCode(), e.getShortname())).collect(Collectors.toList());
    }

    @Override
    public UnreadRecordVO statisticUnread() {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        String unitCode = currentUser.getUnitCode();
        Long sfryDisPatch = 0L;
        Long sfryVerify;
        Long jwryDispatch = 0L;
        Long jwryNotRegister = 0L;
        Long aboutToLeave = 0L;

        //多个登记指向同一个人时，只显示一个
        List<String> latestRecordIds = crjJwryRepository.findLatestRecordIds();
        //即将离开
        long limitTime = new java.util.Date().getTime() + (planLeaveLimitTime * 60 * 60 * 1000);
        Date limitDate = new Date(limitTime);

        if (CrjConstants.SJZD.equals(unitCode)) {
            List<String> dispatchStatus = Arrays.asList(CrjDispatchStatusEnum.DISPATCHED_TO_QX.getCode(),
                    CrjDispatchStatusEnum.DISPATCHED.getCode());
            //未分派未读
            sfryDisPatch =
                    crjSfryReportRepository.countUnread(currentUser.getId(), null, null,
                            CrjConstants.SFRY_REPORT_RECORD_MODULE,
                            Collections.singletonList(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode()), null);
            //未核实未读
            sfryVerify =
                    crjSfryReportRepository.countUnread(currentUser.getId(), null, null,
                            CrjConstants.SFRY_REPORT_RECORD_MODULE, dispatchStatus, Boolean.FALSE);

            if (!latestRecordIds.isEmpty()) {
                //档案未分派未读
                jwryDispatch = crjJwryRepository.countUnread(currentUser.getId(), null, null,
                        CrjConstants.JWRY_RECORD_MODULE, Collections.singletonList(CrjDispatchStatusEnum.NOT_DISPATCHED.getCode()),
                        CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode());
                //档案-未登记未读
                jwryNotRegister = crjJwryRepository.countUnread(currentUser.getId(), null, null,
                        CrjConstants.JWRY_RECORD_MODULE, dispatchStatus,
                        CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode());
                //档案-即将离开
                aboutToLeave = crjJwryRepository.countAboutToLeave(null, null, limitDate);
            }
        } else if (CrjConstants.SQSXZD.contains(unitCode)) {
            UnitEntity unit = unitRepository.findByUnitCode(unitCode);
            //未分派未读
            sfryDisPatch =
                    crjSfryReportRepository.countUnread(currentUser.getId(), null, unit.getAreaCode(),
                            CrjConstants.SFRY_REPORT_RECORD_MODULE,
                            Collections.singletonList(CrjDispatchStatusEnum.DISPATCHED_TO_QX.getCode()), null);
            //未核实未读
            sfryVerify =
                    crjSfryReportRepository.countUnread(currentUser.getId(), null, unit.getAreaCode(),
                            CrjConstants.SFRY_REPORT_RECORD_MODULE,
                            Collections.singletonList(CrjDispatchStatusEnum.DISPATCHED.getCode()), Boolean.FALSE);
            if (!latestRecordIds.isEmpty()) {
                //档案未分派未读
                jwryDispatch = crjJwryRepository.countUnread(currentUser.getId(), null, unit.getAreaCode(),
                        CrjConstants.JWRY_RECORD_MODULE, Collections.singletonList(CrjDispatchStatusEnum.DISPATCHED_TO_QX.getCode()),
                        CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode());
                //档案-未登记未读
                jwryNotRegister = crjJwryRepository.countUnread(currentUser.getId(), null, unit.getAreaCode(),
                        CrjConstants.JWRY_RECORD_MODULE,
                        Collections.singletonList(CrjDispatchStatusEnum.DISPATCHED.getCode()),
                        CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode());
                //档案-即将离开
                aboutToLeave = crjJwryRepository.countAboutToLeave(null, unit.getAreaCode(), limitDate);
            }
        } else {
            //未核实未读
            sfryVerify =
                    crjSfryReportRepository.countUnread(currentUser.getId(), unitCode, null,
                            CrjConstants.SFRY_REPORT_RECORD_MODULE,
                            Collections.singletonList(CrjDispatchStatusEnum.DISPATCHED.getCode()), Boolean.FALSE);
            if (!latestRecordIds.isEmpty()) {
                //档案-未登记未读
                jwryNotRegister = crjJwryRepository.countUnread(currentUser.getId(), unitCode, null,
                        CrjConstants.JWRY_RECORD_MODULE,
                        Collections.singletonList(CrjDispatchStatusEnum.DISPATCHED.getCode()),
                        CrjJwryRegistrationStatusEnum.NOT_REGISTER.getCode());
                //档案-即将离开
                aboutToLeave = crjJwryRepository.countAboutToLeave(unitCode, null, limitDate);

            }
        }
        return new UnreadRecordVO(sfryDisPatch, sfryVerify, jwryDispatch, jwryNotRegister, aboutToLeave);

    }

    @Override
    public void addReadRecord(String recordId, String userId, String module) {
        Optional<CrjReadRecordEntity> readRecord = crjReadRecordRepository.findByRecordIdAndModuleAndUserId(
                recordId, module, userId);
        if (!readRecord.isPresent()) {
            CrjReadRecordEntity readRecordEntity = new CrjReadRecordEntity(recordId, userId, module);
            crjReadRecordRepository.save(readRecordEntity);
        }
    }

    @Override
    public List<ListFilter> getListFilter(String listName) {
        LinkedList<ListFilter> filters = new LinkedList<>();

        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        switch (listName) {
            case "sfryNotDispatch":
                getDistrictFilter(filters);
                break;
            case "sfryDispatch":
                getDistrictFilter(filters);
                getAcceptorFilter(currentUser, filters);
                break;
            case "sfryVerify":
                getDistrictFilter(filters);
                filters.add(getDictFilter("核实状态", "verifyResult", CrjConstants.SFRY_VERIFY_STATUS));
                break;
            case "sfryNotVerify":
                getDistrictFilter(filters);
                break;
            case "jwryNotDispatch":
                getDistrictFilter(filters);
                break;
            case "jwryDispatch":
                getDistrictFilter(filters);
                getBigRegistrationStatusFilter(filters);
                getAcceptorFilter(currentUser, filters);
                break;
            case "jwryAll":
                getDistrictFilter(filters);
                filters.add(getDictFilter("登记状态", "registrationStatus", CrjConstants.SFRY_REGISTRATION_STATUS));
                getLeaveStatusFilter(filters);
                break;
            case "jwryUnRegistered":
                getDistrictFilter(filters);
                break;
            case "jwryLiving":
                getDistrictFilter(filters);
                getLeaveStatusFilter(filters);
                break;
            case "jwryNotRegistered":
                getDistrictFilter(filters);
                break;
            case "leave":
                getDistrictFilter(filters);
                break;
            case "jwryList":
                getDistrictListFilter(filters);
                ListFilter sourceType = getJwrySourceType("登记类型", "sourceType");
                sourceType.getValue().add(0,new FilterValue("all", "全部", false, null));
                filters.add(sourceType);
                filters.add(getDictFilter("登记状态", "registrationStatus", CrjConstants.SFRY_REGISTRATION_STATUS));
                filters.add(getTimeParams("录入时间","jwryCreateTime"));
                break;
            case "sfryList":
                getDistrictListFilter(filters);
                List<FilterValue> isDispatched = new ArrayList<>();
                isDispatched.add(new FilterValue("all", "全部", false, null));
                isDispatched.add(new FilterValue("true", "已分派", false, null));
                isDispatched.add(new FilterValue("false", "未分派", false, null));
                filters.add(new ListFilter("分派状态", "option", "isDispatched", "single", isDispatched));
                List<FilterValue> isVerified = new ArrayList<>();
                isVerified.add(new FilterValue("all", "全部", false, null));
                isVerified.add(new FilterValue("true", "已核实", false, null));
                isVerified.add(new FilterValue("false", "未核实", false, null));
                filters.add(new ListFilter("核实状态", "option", "isVerified", "single", isVerified));
                filters.add(getTimeParams("举报时间","reportTime"));
                break;
            case "czryList":
                getDistrictListFilter(filters);
                filters.add(getTimeParams("录入时间","createTime"));
                List<FilterValue> czryList = new ArrayList<>();
                czryList.add(new FilterValue("all", "全部", false, null));
                czryList.add(new FilterValue("1", "有效", false, null));
                czryList.add(new FilterValue("0", "到期", false, null));
                filters.add(new ListFilter("居留许可状态", "select", "livingStatus", "single", czryList));
                break;
            case "czryListApp":
                getDistrictFilter(filters);
                filters.add(getTimeParams("录入时间","createTime"));
                break;
            default:
        }

        return filters;
    }

    private void getDistrictFilter(LinkedList<ListFilter> filters) {
        LinkedList<FilterValue> listFilters =
                dictRepository.findAllByTypeAndCodeStartingWith(CrjConstants.DISTRICT, CrjConstants.DISTRICT_CODE).stream()
                        .filter(e -> !CrjConstants.DISTRICT_CODE.equals(e.getCode()))
                        .map(e -> new FilterValue(e.getCode(), e.getName(), Boolean.FALSE, null))
                        .collect(Collectors.toCollection(LinkedList::new));
        listFilters.addFirst(CrjConstants.FILTER_VALUE_ALL);
        filters.add(new ListFilter("所属区域", "option", CrjConstants.DISTRICT, "single", listFilters));
    }

    private void getDistrictListFilter(LinkedList<ListFilter> filters) {
        List<FilterValue> unitTree = departmentService.getUnitTree();
        unitTree.add(0,CrjConstants.FILTER_VALUE_ALL);
        filters.add(new ListFilter("所属区域", "tree", CrjConstants.DISTRICT, "single", unitTree));
    }
    private ListFilter getDictFilter(String displayName, String key, String dictType) {
        LinkedList<FilterValue> filterValues = dictRepository.findAllByType(dictType).stream()
                .filter(dictEntity -> "1".equals(dictEntity.getFlag()))
                .map(e -> new FilterValue(e.getCode(), e.getName(), Boolean.FALSE, null))
                .collect(Collectors.toCollection(LinkedList::new));
        filterValues.addFirst(CrjConstants.FILTER_VALUE_ALL);
        return new ListFilter(displayName, "option", key, "single", filterValues);
    }

    private void getAcceptorFilter(LoginUser currentUser, LinkedList<ListFilter> filters) {
        String unitCode = currentUser.getUnitCode();
        LinkedList<FilterValue> filterValues;
        if (CrjConstants.SJZD.equals(unitCode)) {

            filterValues = CrjConstants.SQSXZD.stream().map(code -> {
                //派出所
                List<FilterValue> collect = this.getPcs(code).stream()
                        .map(e -> new FilterValue(e.getCode(), e.getName(), Boolean.FALSE, null))
                        .collect(Collectors.toList());
                //区县
                UnitEntity byUnitCode = unitRepository.findByUnitCode(code);
                return new FilterValue(byUnitCode.getUnitCode(), byUnitCode.getShortname(), Boolean.FALSE, collect);
            }).collect(Collectors.toCollection(LinkedList::new));

        } else if (CrjConstants.SQSXZD.contains(unitCode)) {

            filterValues = this.getPcs(unitCode).stream()
                    .map(e -> new FilterValue(e.getCode(), e.getName(), Boolean.FALSE, null))
                    .collect(Collectors.toCollection(LinkedList::new));

        } else {
            return;
        }
        filterValues.addFirst(CrjConstants.FILTER_VALUE_ALL);
        filters.add(new ListFilter("接收方", "tree", "acceptor", "single", filterValues));
    }

    private void getLeaveStatusFilter(LinkedList<ListFilter> filters) {
        ArrayList<FilterValue> filterValues = new ArrayList<>();
        filterValues.add(CrjConstants.FILTER_VALUE_ALL);
        ListFilter listFilter = new ListFilter("拟离开状态", "option", "leaveStatus", "single", filterValues);
        filterValues.add(new FilterValue("aboutToLeave", "即将离开", Boolean.FALSE, null));
        filterValues.add(new FilterValue("normal", "正常", Boolean.FALSE, null));
        filters.add(listFilter);
    }

    private void getBigRegistrationStatusFilter(LinkedList<ListFilter> filters) {
        ArrayList<FilterValue> filterValues = new ArrayList<>();
        ListFilter listFilter = new ListFilter("登记状态", "option", "normalRegistrationStatus", "single", filterValues);
        filters.add(listFilter);
        filterValues.add(CrjConstants.FILTER_VALUE_ALL);
        filterValues.add(new FilterValue("1", "未登记", Boolean.FALSE, null));
        filterValues.add(new FilterValue("2", "已登记", Boolean.FALSE, null));
        filterValues.add(new FilterValue("3", "不登记", Boolean.FALSE, null));

    }

    @Override
    public List<String> getModule() {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        assert currentUser != null;
        if (CrjConstants.SJZD.equals(currentUser.getUnitCode()) || CrjConstants.SQSXZD.contains(
                currentUser.getUnitCode())) {
            return CrjConstants.ALL_MODULE;
        }
        return CrjConstants.BASE_MODULE;
    }

    private ListFilter getTimeParams(String displayName,String key) {
        List<String> integers = Arrays.asList("0","1","2","3","5","99");
        return new ListFilter(displayName, "time", key, "single", Arrays.stream(TimeRangeEnum.values())
            .filter(item->integers.contains(item.getCode()))
            .map(e -> new FilterValue(e.getCode(), e.getName(), false, null))
            .collect(Collectors.toList()));
    }

    private ListFilter getJwrySourceType(String displayName,String key){
        return new ListFilter(displayName, "option", key, "single", Arrays.stream(CrjJwrySourceTypeEnum.values())
            .map(e -> new FilterValue(e.getCode().toString(), e.getName(), false, null))
            .collect(Collectors.toList()));
    }
}
