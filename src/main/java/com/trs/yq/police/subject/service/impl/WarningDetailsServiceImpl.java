package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.dto.ProfessionalWarningDTO;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.vo.WarningJudgeVO;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.WarningRepository;
import com.trs.yq.police.subject.repository.WarningTrajectoryRepository;
import com.trs.yq.police.subject.service.WarningDetailsService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_WARNING;
import static com.trs.yq.police.subject.constants.enums.WarningTypeEnum.*;
import static com.trs.yq.police.subject.utils.JsonUtil.OBJECT_MAPPER;

/**
 * 预警详情实现层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/10 9:56
 **/
@Service
@Slf4j
public class WarningDetailsServiceImpl implements WarningDetailsService {

    @Resource
    private WarningRepository warningRepository;

    @Resource
    private OperationLogHandler operationLogHandler;

    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;

    @Value("${jz-kafka.bootstrap-servers}")
    private String jzKafkaBootstrapServers;

    @Value("${jz-kafka.topic}")
    private String jzKafkaSaveTopic;

    private KafkaTemplate<String, String> kafkaTemplate;

    @PostConstruct
    void initKafkaTemplate() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, jzKafkaBootstrapServers);
        configs.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configs.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        kafkaTemplate = new KafkaTemplate<>(new DefaultKafkaProducerFactory<>(configs));
    }

    @Override
    public void doJudge(String warningId, WarningJudgeVO warningJudgeVO) {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        WarningEntity warningEntity = warningRepository.findById(warningId).orElse(null);
        if (Objects.isNull(warningEntity)) {
            throw new NoSuchElementException("没有找到该预警");
        }
        //研判
        warningEntity.setJudgeResult(warningJudgeVO.getJudgeResult());
        warningEntity.setJudgeRemark(warningJudgeVO.getJudgeRemark());
        warningEntity.setJudgeTime(LocalDateTime.now());
        warningEntity.setWarningStatus(WarningStatusEnum.JUDGED.getCode());
        if (Objects.nonNull(warningJudgeVO.getFileIds()) && !warningJudgeVO.getFileIds().isEmpty()) {
            warningEntity.setJudgeFileIds(String.join(",", warningJudgeVO.getFileIds()));
        }
        warningRepository.save(warningEntity);

        //如果是技侦手段预警，需要回推kafka
        List<String> list = Arrays.asList(FK_JZSDYJ_LKCS.getCode(),
                FK_JZSDYJ_DDCS.getCode(),
                FK_JZSDYJ_QJJJ.getCode(),
                FK_JZSDYJ_HJHK.getCode(),
                FK_JZSDYJ_KUASHENG.getCode(),
                FK_JZSDYJ_KUASHI.getCode(),
                FK_JZSDYJ_KUAXIAN.getCode(),
                FK_JZSDYJ_LKCZD.getCode(),
                WW_JZSDYJ_KUASHENG.getCode(),
                WW_JZSDYJ_KUASHI.getCode(),
                WW_JZSDYJ_KUAXIAN.getCode(),
                WW_JZSDYJ_LKCS.getCode(),
                WW_JZSDYJ_DDCS.getCode(),
                WW_JZSDYJ_LKCZD.getCode());
        if (list.contains(warningEntity.getWarningType())) {
            warningTrajectoryRepository.findAllByWarningId(warningId).forEach(track -> {
                final ProfessionalWarningDTO dto = JsonUtil.parseObject(track.getRawData(), ProfessionalWarningDTO.class);
                if (Objects.isNull(dto)) {
                    log.debug("技侦手段json格式错误: {}", track.getRawData());
                    return;
                }

                String dataText = dto.getDATA();
                ProfessionalWarningDTO.JzsdData data = JsonUtil.parseObject(dataText, ProfessionalWarningDTO.JzsdData.class);
                if (Objects.isNull(data)) {
                    log.debug("技侦手段json DATA字段格式错误: {}", track.getRawData());
                    return;
                }

                ObjectNode feedBack = OBJECT_MAPPER.createObjectNode();
                feedBack.put("jobType", dto.getJOBTYPE());
                feedBack.put("jobId", dto.getJOBID());
                feedBack.put("jobName", dto.getJOBNAME());
                feedBack.put("resId", dto.getRESID());
                feedBack.put("district", dto.getDISTRICT());
                feedBack.put("police", dto.getPOLICE());
                feedBack.put("userId", currentUser.getId());
                feedBack.put("userName", currentUser.getRealName());
                feedBack.put("policeId", "");
                feedBack.put("idCard", currentUser.getIdCard());

                ProfessionalWarningDTO.JzsdFeedBack feedBackData = new ProfessionalWarningDTO.JzsdFeedBack();
                BeanUtils.copyProperties(data, feedBackData);
                feedBackData.setInfo_true(warningJudgeVO.getJudgeResult().equals("0") ? "是" : "否");
                feedBackData.setRemark(warningJudgeVO.getJudgeRemark());
                String dataString = JsonUtil.toJsonString(Collections.singletonList(feedBackData));
                feedBack.put("dataList", dataString);
                kafkaTemplate.send(jzKafkaSaveTopic, feedBack.toString());
                log.info("技侦手段预警回推kafka成功: {}", feedBack.toPrettyString());
            });
        }

        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.JUDGE)
                .module(OperateModule.WARNING)
                .newObj(JsonUtil.toJsonString(warningJudgeVO))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(warningId)
                .desc("反馈")
                .targetObjectType(OPERATION_LOG_TARGET_WARNING)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }

    }

    @Override
    public void doSign(String warningId) {
        WarningEntity warningEntity = warningRepository.findById(warningId).orElse(null);
        if (Objects.isNull(warningEntity)) {
            throw new NoSuchElementException("没有找到该预警");
        }
        ///签收
        warningEntity.setSignTime(LocalDateTime.now());
        warningEntity.setWarningStatus(WarningStatusEnum.SIGN_TO_JUDGE.getCode());
        warningRepository.save(warningEntity);
    }
}
