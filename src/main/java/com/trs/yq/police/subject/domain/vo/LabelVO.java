package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.validation.RuleGroup;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 标签传输封装类
 *
 * <AUTHOR>
 * @date 2021/7/31 19:02
 */
@Getter
@Setter
@ToString
public class LabelVO implements Serializable {

    private static final long serialVersionUID = 7980504825407958738L;

    /**
     * 标签id
     */
    @NotBlank(message = "标签主键不可为空", groups = {RuleGroup.Update.class})
    private String id;

    /**
     * 姓名
     */
    @NotBlank(message = "标签名不可为空", groups = {RuleGroup.Create.class})
    private String name;

    /**
     * 专题id
     */
    @NotBlank(message = "关联专题不可为空", groups = {RuleGroup.Create.class})
    private String subjectId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建方式 0-手动创建，1-大数据平台推送
     */
    private String createType;

    /**
     * 标签根据entity生成vo
     *
     * @param label entity
     */
    public LabelVO(LabelEntity label) {
        this.id = label.getId();
        this.name = label.getName();
        this.remark = label.getRemark();
        this.createType = label.getCreateType();
        this.subjectId = label.getSubjectId();
    }

}
