package com.trs.yq.police.subject.domain.entity;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.LocalDateTime;
import org.hibernate.annotations.GenericGenerator;

/**
 * 社会资源_基层治理_境外三非人员信息(TPsCrjSfry)数据访问类
 *
 * <AUTHOR>
 * @since 2022-07-06 15:34:47
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_CRJ_SFRY")
public class CrjSfryEntity implements Serializable {

    private static final long serialVersionUID = -22120607663151536L;
    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据创建时间
     */
    private LocalDateTime updateTime;
    /**
     * 境外住宿登记ID
     */
    private String hotelApplyId;

    /**
     * 英文姓
     */
    private String ywx;

    /**
     * 英文名
     */
    private String ywm;

    /**
     * 英文姓名
     */
    private String ywxm;

    /**
     * 中文姓名
     */
    private String zwxm;

    /**
     * 性别
     */
    private String xb;

    /**
     * 性别名称
     */
    private String xbDesc;

    /**
     * 民族
     */
    private String mz;

    /**
     * 民族名称
     */
    private String mzDesc;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 国家地区
     */
    private String gjdq;

    /**
     * 证件种类
     */
    private String zjzl;

    /**
     * 证件种类
     */
    private String zjzlDesc;

    /**
     * 证件号码
     */
    private String zjhm;

    /**
     * 省市区行政区划代码
     */
    private String areaCode;

    /**
     * 省市区行政区划代码中文
     */
    private String areaCodeDesc;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 签证种类（206）
     */
    private String qzzl;

    /**
     * 签证号码
     */
    private String qzhm;

    /**
     * 停留期限
     */
    private String tingliuqixian;

    /**
     * 入境日期（yyyyMMdd格式）
     */
    private String rjrq;

    /**
     * 入境口岸（字典907）
     */
    private String rjka;

    /**
     * 接待单位
     */
    private String jddw;

    /**
     * 接待人
     */
    private String jdr;

    /**
     * 入住时间
     */
    private LocalDateTime rzsj;

    /**
     * 入住房号
     */
    private String rzfh;

    /**
     * 退房时间
     */
    private LocalDateTime tfsj;

    /**
     * 酒店旅馆编码
     */
    private String qiyebianma;

    /**
     * 企业名称
     */
    private String qiyebmc;

    /**
     * 旅馆业务代码类别
     */
    private String yewulbbm;

    /**
     * 旅馆业务代码名称
     */
    private String yewulb;

    /**
     * 管辖单位编码
     */
    private String gxdwbm;

    /**
     * 管辖单位名称
     */
    private String gxdwmc;

    /**
     * 数据类型1.境外人员住宿登记 2.旅店业
     */
    private String sjlx;

    /**
     * 数据状态1.新增2.修改
     */
    private String state;

    /**
     * 云墙处理状态0.未处理 1.已处理 -1.处理失败
     */
    private String yqclState;

    /**
     * 云墙处理失败原因
     */
    private String yqclMsg;

    /**
     * 云墙处理时间
     */
    private LocalDateTime yqclTime;

    private String depActionFlag;

    private LocalDateTime depActionTime;

    private LocalDateTime depFirstenterTime;
    /**
     * 是否发送过短信
     */
    private Boolean isSendMessage;
}
