package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningPushConfigEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 预警推送配置持久层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/08
 **/
@Repository
public interface WarningPushConfigRepository extends BaseRepository<WarningPushConfigEntity, String> {

    /**
     * 根据预警类型查询配置
     *
     * @param warningType 预警类型
     * @return {@link WarningPushConfigEntity}
     */
    @Query("select w from WarningPushConfigEntity w where w.warningType = :warningType")
    List<WarningPushConfigEntity> findByWarningType(String warningType);

    /**
     * 根据预警类型和区域代码查询配置
     *
     * @param warningType 预警类型
     * @param areaCode    区域代码
     * @return {@link WarningPushConfigEntity}
     */
    @Query("select w from WarningPushConfigEntity w where w.warningType = :warningType and w.areaCode in :areaCode")
    List<WarningPushConfigEntity> findByWarningTypeAndAreaCode(String warningType, List<String> areaCode);

    /**
     * 根据预警类型查询配置
     *
     * @param warningType 预警类型
     * @param areaCode    区域代码
     * @return {@link WarningPushConfigEntity}
     */
    @Query("select w from WarningPushConfigEntity w where w.warningType = :warningType and (w.areaCode in :areaCode or w.areaCode is null)")
    List<WarningPushConfigEntity> findByWarningTypeAndAreaCodeIncludeNull(String warningType, List<String> areaCode);

}
