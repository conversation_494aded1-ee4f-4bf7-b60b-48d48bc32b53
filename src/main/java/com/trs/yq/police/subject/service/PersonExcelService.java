package com.trs.yq.police.subject.service;

import com.fasterxml.jackson.databind.JsonNode;

import com.trs.yq.police.subject.domain.vo.ExportParams;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 批量导出接口类
 *
 * <AUTHOR>
 * @date 2021/7/29 17:23
 */
public interface PersonExcelService {


    /**
     * 根据传来的属性导出excel
     *
     * @param response   响应体
     * @param fieldNames 导出的属性
     * @param request  导出条件
     * @param subjectId  主题id
     * @throws IOException IO异常
     */
    void downLoadExcel(HttpServletResponse response, List<String> fieldNames, ExportParams request, String subjectId) throws IOException;

    /**
     * 获得不同专题下批量导出需要的属性
     *
     * @param subjectId 专题Id
     * @return 属性json
     */
    JsonNode getExportPropertyList(String subjectId);
}
