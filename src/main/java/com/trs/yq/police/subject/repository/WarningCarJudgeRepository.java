package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.WarningCarJudgeEntity;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2023/3/15 15:32
 */
@Repository
public interface WarningCarJudgeRepository extends BaseRepository<WarningCarJudgeEntity, String> {

    /**
     * 根据车牌号获取研判结果
     *
     * @param plateNumber 车牌号
     * @return WarningCarJudgeEntity
     */
    WarningCarJudgeEntity getByPlateNumber(@Param("plateNumber") String plateNumber);
}
