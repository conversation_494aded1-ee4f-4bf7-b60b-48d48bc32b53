package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.RoleDataEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色数据表
 */
@Repository
public interface RoleDataRepository extends BaseRepository<RoleDataEntity, String> {

    /**
     * 根据用户id查询所拥有的查看权限
     *
     * @param userId 用户id
     * @return 权限列表
     */
    @Query(value = "SELECT * FROM T_ADMIN_ROLE_DATA d WHERE d.ROLE_ID IN (SELECT role_id FROM T_ADMIN_ROLE_USER WHERE user_id=:userId )  order by VIEW_RANGE ASC", nativeQuery = true)
    List<RoleDataEntity> findViewRangesByUserId(@Param("userId") String userId);
}
