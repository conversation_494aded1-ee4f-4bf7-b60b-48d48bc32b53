package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.common.SkipResponseBodyAdvice;
import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.PersonArchiveStatusEnum;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.request.TrajectoryListRequest;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.operation.OperationLog;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.UnsupportedEncodingException;
import java.util.List;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.*;

/**
 * 人员档案接口类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 14:09
 */
@Validated
@RestController
@RequestMapping("/person")
@Slf4j
public class PersonController {

    @Resource
    private PersonService personService;
    @Resource
    private ModuleService moduleService;
    @Resource
    private TrajectoryService trajectoryService;
    @Resource
    private GroupService groupService;
    @Resource
    private ClueService clueService;
    @Resource
    private EventService eventService;
    @Resource
    private ExportExcelService excelService;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private WarningService warningService;
    @Resource
    private CallService callService;


    /**
     * 新增人员 http://***************:3001/project/4897/interface/api/129630
     *
     * @param person 接收人员信息
     * @return id
     */
    @PostMapping("")
    public String createPerson(@NotNull(message = REQUEST_PARAMS_MISSING) @RequestBody @Valid PersonBasicVO person) {
       return personService.createPerson(person);
    }

    /**
     * 移除人员档案 http://***************:3001/project/4897/interface/api/129640
     *
     * @param personId  人员主键
     * @param subjectId 专题主键
     */
    @DeleteMapping("")
    @OperationLog(operator = Operator.DELETE, module = OperateModule.ARCHIVES_MANAGE, desc = "删除人员档案", primaryKey = "personId")
    public void deletePerson(@NotBlank(message = PERSON_ID_MISSING) String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        personService.deletePerson(personId, subjectId);
    }

    /**
     * 编辑人员基本信息 http://***************:3001/project/4897/interface/api/129635
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param person    人员信息
     */
    @PutMapping("/{personId}/base")
    public void updatePersonBasicInfo(@NotBlank(message = PERSON_ID_MISSING) @PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId,
        @NotNull(message = REQUEST_PARAMS_MISSING) @RequestBody @Valid PersonBasicVO person) {

        personService.updatePersonBasicInfo(personId, subjectId, person);
    }

    /**
     * 获取人员基本信息 http://***************:3001/project/4897/interface/api/129610
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 人员信息 {@link PersonBasicVO}
     * @throws javax.validation.ValidationException                         校验失败异常
     * @throws com.trs.yq.police.subject.exception.ParamValidationException 参数校验异常
     */
    @GetMapping("/{personId}/base")
    public PersonBasicVO getPersonBasicInfo(@PathVariable @NotBlank(message = PERSON_ID_MISSING) String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return personService.getBasicInfo(personId, subjectId);
    }

    /**
     * 根据身份证号查询人员基本信息 http://***************:3001/project/4897/interface/api/130000
     *
     * @param idNumber 人员id
     * @return 人员信息 {@link PersonBasicVO}
     * @throws javax.validation.ValidationException                         校验失败异常
     * @throws com.trs.yq.police.subject.exception.ParamValidationException 参数校验异常
     */
    @GetMapping("/base")
    public PersonBasicVO getPersonBasicInfoByIdNumber(@NotBlank(message = ID_NUMBER_MISSING) String idNumber) {
        return personService.getBasicInfoByIdNumber(idNumber);
    }

    /**
     * 批量导入 http://***************:3001/project/4897/interface/api/129670
     *
     * @param importVO 导入参数
     * @return 批量导入结果
     */
    @PostMapping("/import")
    public ImportResultVO importPerson(ImportVO importVO) {
        return personService.importPerson(importVO);
    }

    /**
     * 获取失败记录表 http://***************:3001/project/4897/interface/api/130109
     *
     * @param subjectId 专题id
     * @param initialId 原始文件id
     * @return 导入失败记录表格
     * @throws UnsupportedEncodingException 暂不支持的URL编码
     */
    @GetMapping("/import/fail")
    @SkipResponseBodyAdvice
    public ResponseEntity<ByteArrayResource> downloadImportFailure(
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId, @NotBlank(message = "原始文件主键缺失") String initialId)
        throws UnsupportedEncodingException {

        return personService.downloadImportFailure(subjectId, initialId);
    }

    /**
     * 下载批量导入模板 http://***************:3001/project/4897/interface/api/129675
     *
     * @param subjectId  专题编号
     * @param personType 人员类别
     * @return 模板文件二进制
     */
    @GetMapping("/import/template")
    @SkipResponseBodyAdvice
    public ResponseEntity<ByteArrayResource> downloadImportTemplate(
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId, @Nullable String personType)
        throws UnsupportedEncodingException {
        return personService.downloadImportTemplate(subjectId, personType);
    }

    /**
     * 查询档案目录 http://***************:3001/project/4897/interface/api/129765
     *
     * @param subjectId 专题id
     * @return 档案目录
     */
    @GetMapping("/content")
    public List<ContentVO> getArchiveContent(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return moduleService.getArchiveContent(subjectId, "person");
    }

    /**
     * 查询人员类别 http://***************:3001/project/4897/interface/api/129895
     *
     * @param subjectId 专题id
     * @return 人员类别
     */
    @GetMapping("/types")
    public List<IdNameVO> getPersonTypes(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return personService.getTypes(subjectId);
    }

    /**
     * 更新人员基本信息图片 http://***************:3001/project/4897/interface/api/129955
     *
     * @param personId 人员id
     * @param images   图片信息
     */
    @PutMapping("/{personId}/base/image")
    @OperationLog(operator = Operator.EDIT, module = OperateModule.BASIC_INFO, desc = "更新人员照片", primaryKey = "personId")
    public void updatePersonBaseImage(@PathVariable String personId, @RequestBody List<ImageVO> images) {
        personService.updateBaseImages(personId, images);
    }

    /**
     * 查询人员存在状态 http://***************:3001/project/4897/interface/api/129805
     *
     * @param idNumber 身份证号码
     * @return 存在状态
     */
    @GetMapping("/storage-status")
    public StorageStatusVO getStorageStatus(@NotBlank(message = ID_NUMBER_MISSING) String idNumber) {
        return personService.getPersonStorageStatus(idNumber);
    }

    /**
     * 查询人员归档状态 http://***************:3001/project/4897/interface/api/130100
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 归档状态
     */
    @GetMapping("/{personId}/archiveStatus")
    public String getArchiveStatus(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return personService.getPersonArchiveStatus(personId, subjectId).getCode();
    }

    /**
     * 查询人员归档状态 http://***************:3001/project/4897/interface/api/130100
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 归档状态
     */
    @GetMapping("/{personId}/archiveStatus/enum")
    public KeyValueVO getArchiveStatusEnum(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        final PersonArchiveStatusEnum archiveStatus = personService.getPersonArchiveStatus(personId, subjectId);
        return new KeyValueVO(archiveStatus.getCode(), archiveStatus.getName());
    }

    /**
     * 人员归档 http://***************:3001/project/4897/interface/api/130094
     *
     * @param personId        人员id
     * @param subjectId       专题id
     * @param personArchiveVO vo {@link PersonArchiveVO}
     */
    @PostMapping("/{personId}/archive")
    @OperationLog(operator = Operator.ARCHIVE, module = OperateModule.ARCHIVES_MANAGE, desc = "归档人员", primaryKey = "personId")
    public void archivePerson(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId,
        @Validated @RequestBody PersonArchiveVO personArchiveVO) {
        personService.archivePerson(personId, subjectId, personArchiveVO);
    }

    /**
     * 人员激活 http://***************:3001/project/4897/interface/api/130097
     *
     * @param personId  人员id
     * @param subjectId 专题id
     */
    @PostMapping("/{personId}/activate")
    @OperationLog(operator = Operator.ACTIVATE, module = OperateModule.ARCHIVES_MANAGE, desc = "激活人员", primaryKey = "personId")
    public void activePerson(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        personService.activePerson(personId, subjectId);
    }


    /**
     * 批量修改人员群体关联 http://***************:3001/project/4897/interface/api/131023
     *
     * @param personId              人员id
     * @param personGroupRelationVO {@link PersonGroupRelationVO}
     */
    @PutMapping("/{personId}/group/relations")
    public void addGroupPerson(@PathVariable String personId,
        @Validated @RequestBody PersonGroupRelationVO personGroupRelationVO) {
        personGroupRelationVO.setPersonId(personId);
        personService.updatePersonGroupsRelation(personGroupRelationVO);
    }


    /**
     * 轨迹来源查询 http://***************:3001/project/4897/interface/api/130285
     *
     * @param personId 人员id
     * @param trajectoryType 轨迹类型
     * @return 轨迹来源列表
     */
    @GetMapping("/{personId}/trajectory/sources")
    public List<IdNameCountVO> getTrajectorySources(@PathVariable String personId, String trajectoryType) {
        return trajectoryService.getTrajectorySourceList(personId, trajectoryType);
    }

    /**
     * 轨迹计数查询
     *
     * @param personId 人员id
     * @param sourceId 来源id
     * @return 轨迹计数列表
     */
    @GetMapping("/{personId}/trajectory/count")
    public List<DateCountVO> getTrajectoryCount(@PathVariable String personId,
        @NotBlank(message = "来源编号不能为空！") String sourceId) {
        return trajectoryService.getTrajectoryCountList(personId, sourceId);
    }

    /**
     * 轨迹列表查询 http://192.168.200.13:8081/jenkins/job/yq-police-subject/382/console
     *
     * @param personId 人员id
     * @param request  查询参数
     * @return 轨迹列表
     */
    @PostMapping("/{personId}/trajectory/list")
    public PageResult<PersonTrajectoryVO> getTrajectoryList(@PathVariable String personId,
        @Validated @RequestBody TrajectoryListRequest request) {
        return trajectoryService.getPersonTrajectoryList(personId, request);
    }


    /**
     * 人员涉事相关群体查询 http://***************:3001/project/4897/interface/api/130717
     *
     * @param pageParams 分页参数 {@link PageParams}
     * @param personId   人员id
     * @param subjectId  专题id
     * @return {@link PersonRelatedGroupVO}
     */
    @PostMapping("/{personId}/group/list")
    public PageResult<PersonRelatedGroupVO> getClueGroup(@PathVariable String personId,
        @RequestParam @NotBlank(message = "专题id不能为空！") String subjectId,
        @RequestBody @Validated PageParams pageParams) {
        return personService.getPersonRelatedGroupList(personId, subjectId, pageParams);
    }

    /**
     * 修改人员群体关联活跃度 http://***************:3001/project/4897/interface/api/134444
     *
     * @param relationId 关联id
     * @param vo         {@link GroupPersonActivityLevelVO}
     */
    @PutMapping("/group/relation/{relationId}/activity-level")
    public void updatePersonGroupActive(@PathVariable String relationId,
        @RequestBody @Valid GroupPersonActivityLevelVO vo) {
        vo.setRelationId(relationId);
        groupService.updateGroupPersonActivityLevel(ModuleEnum.PERSON, vo);
    }

    /**
     * [人员档案][不分页]根据人员id查询所有已关联的群体 http://***************:3001/project/4897/interface/api/131683
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedGroupVO}
     */
    @GetMapping("/{personId}/group/related")
    public List<PersonRelatedGroupVO> getPersonRelatedGroup(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return personService.getPersonRelatedGroupList(personId, subjectId);
    }

    /**
     * [人员档案] 条件查询群体列表（用于人员档案关联群体弹出对话框） http://***************:3001/project/4897/interface/api/131689
     *
     * @param personId 人员id
     * @param request  {@link DialogGroupListRequestVO}
     * @return {@link DialogGroupListVO}
     */
    @PostMapping("/{personId}/group/list/condition")
    public PageResult<DialogGroupListVO> getDialogPersonGroupList(@PathVariable String personId,
        @RequestBody @Validated DialogGroupListRequestVO request) {
        return groupService.getDialogGroupListVO(request);
    }

    /**
     * [人员档案] 查询事件列表（分页） http://***************:3001/project/4897/interface/api/134385
     *
     * @param pageParams 分页参数 {@link PageParams}
     * @param personId   人员id
     * @param subjectId  专题id
     * @return {@link PersonRelatedGroupVO}
     */
    @PostMapping("/{personId}/event/list")
    public PageResult<PersonRelatedEventVO> getPersonRelatedEventList(@PathVariable String personId,
        @RequestParam @NotBlank(message = "专题id不能为空！") String subjectId,
        @RequestBody @Validated PageParams pageParams) {
        return personService.getPersonRelatedEventList(personId, subjectId, pageParams);
    }

    /**
     * [人员档案] 根据人员id查询群体列表（不分页） http://***************:3001/project/4897/interface/api/134384
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedEventVO}
     */
    @GetMapping("/{personId}/event/related")
    public List<PersonRelatedEventVO> getPersonRelatedEvent(@PathVariable String personId,
        @RequestParam @NotBlank(message = "专题id不能为空！") String subjectId) {
        return personService.getPersonRelatedEventList(personId, subjectId);
    }

    /**
     * [人员档案] 条件查询事件列表（用于人员档案关联事件弹出对话框） http://***************:3001/project/4897/interface/api/131707
     *
     * @param personId 人员id
     * @param request  {@link DialogEventListRequestVO}
     * @return {@link DialogEventListVO}
     */
    @PostMapping("/{personId}/event/list/condition")
    public PageResult<DialogEventListVO> getDialogPersonEventList(@PathVariable String personId,
        @RequestBody @Valid DialogEventListRequestVO request) {
        return eventService.getDialogEventList(request);
    }

    /**
     * [人员档案] 条件查询线索列表（用于人员档案关联线索弹出对话框） http://***************:3001/project/4897/interface/api/131707
     *
     * @param personId 人员id
     * @param request  {@link DialogClueListRequestVO}
     * @return {@link DialogGroupListVO}
     */
    @PostMapping("/{personId}/clue/list/condition")
    public PageResult<DialogClueListVO> getDialogPersonClueList(@PathVariable String personId,
        @RequestBody @Valid DialogClueListRequestVO request) {
        return clueService.getDialogClueList(request);
    }

    /**
     * 批量修改人员群体关联 http://***************:3001/project/4897/interface/api/134390
     *
     * @param personId               人员id
     * @param personEventsRelationVO {@link PersonEventsRelationVO}
     */
    @PutMapping("/{personId}/event/relations")
    public void updatePersonEventsRelations(@PathVariable String personId,
        @RequestBody PersonEventsRelationVO personEventsRelationVO) {
        personEventsRelationVO.setPersonId(personId);
        personService.updatePersonEventsRelation(personEventsRelationVO);
    }

    /**
     * 移除事件关联人员 http://***************:3001/project/4897/interface/api/134454
     *
     * @param relationId 关系id
     */
    @DeleteMapping("/event/relation/{relationId}")
    public void removePersonFromEvent(@PathVariable String relationId) {
        eventService.removePersonFromEvent(ModuleEnum.PERSON, relationId);
    }

    /**
     * 批量修改人员群体关联 http://***************:3001/project/4897/interface/api/134389
     *
     * @param personId              人员id
     * @param personCluesRelationVO {@link PersonCluesRelationVO}
     */
    @PutMapping("/{personId}/clue/relations")
    public void updatePersonCluesRelation(@PathVariable String personId,
        @RequestBody PersonCluesRelationVO personCluesRelationVO) {
        personCluesRelationVO.setPersonId(personId);
        personService.updatePersonCluesRelation(personCluesRelationVO);
    }

    /**
     * [人员档案] 根据人员id查询线索列表 http://***************:3001/project/4897/interface/api/131725
     *
     * @param pageParams 分页参数 {@link PageParams}
     * @param personId   人员id
     * @param subjectId  专题id
     * @return {@link PersonRelatedGroupVO}
     */
    @PostMapping("/{personId}/clue/list")
    public PageResult<PersonRelatedClueVO> getPersonRelatedClueList(@PathVariable String personId,
        @RequestParam @NotBlank(message = "专题id不能为空！") String subjectId,
        @RequestBody @Validated PageParams pageParams) {
        return personService.getPersonRelatedClueList(personId, subjectId, pageParams);
    }

    /**
     * [人员档案][不分页]根据人员id查询所有已关联的线索 http://***************:3001/project/4897/interface/api/131719
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link PersonRelatedGroupVO}
     */
    @GetMapping("/{personId}/clue/related")
    public List<PersonRelatedClueVO> getPersonRelatedClueList(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return personService.getPersonRelatedClueList(personId, subjectId);
    }


    /**
     * 移除线索关联人员 http://***************:3001/project/4897/interface/api/134449
     *
     * @param relationId 关系id
     */
    @DeleteMapping("/clue/relation/{relationId}")
    public void removePersonFromClue(@PathVariable String relationId) {
        clueService.removePersonFromClue(ModuleEnum.PERSON, relationId);
    }

    /**
     * 活动相关人员列表 http://***************:3001/project/4897/interface/api/134459
     *
     * @param personId     人员id
     * @param requestParam 检索参数
     * @return 活动相关人员
     */
    @PostMapping("/{personId}/activity-relevant-members")
    public PageResult<ActivityRelatedPersonVO> getEventRelatedPerson(@PathVariable String personId,
        @RequestBody @Validated RequestParams requestParam) {
        return eventService.getEventRelatedPerson(personId, requestParam.getPageParams());
    }

    /**
     * 导出excel
     *
     * @param response  响应体
     * @param personId  人员id
     * @param subjectId 专题id
     */
    @GetMapping("/{personId}/export/{subjectId}")
    @OperationLog(operator = Operator.EXPORT, module = OperateModule.ARCHIVES_MANAGE, desc = "下载人员档案", primaryKey = "personId", targetObjectType = TargetObjectTypeEnum.PERSON)
    public void exportPerson(@PathVariable String personId, @PathVariable String subjectId,
        HttpServletResponse response) {
        PersonEntity personEntity = personRepository.findById(personId).orElse(new PersonEntity());
        excelService.export(response, "涉稳重点人员档案-" + personEntity.getName(),
            excelService.getPersonExcel(personEntity, subjectId));
    }

    /**
     * 批量删除人员(维稳) http://***************:3001/project/4897/interface/api/134939
     *
     * @param personIds 人员id
     * @param subjectId 专题id
     */
    @DeleteMapping("/batch")
    public void batchDeletePersons(@RequestParam String subjectId, @RequestBody List<String> personIds) {
        personService.batchDeletePersons(subjectId, personIds);
    }

    /**
     * 查询人员关联预警列表
     *
     * @param personId    人员id
     * @param subjectId   专题id
     * @param requestBody 分页参数
     * @return {@link WarningListVO}
     */
    @PostMapping("/{personId}/warning")
    public PageResult<WarningListVO> getPersonWarningList(@PathVariable String personId, @RequestParam String subjectId,
        @RequestBody RequestParams requestBody) {
        return warningService.getPersonWarningList(requestBody.getPageParams(), subjectId, personId);
    }

    /**
     * 人员列管
     *
     * @param personId        人员id
     * @param subjectId       专题id
     */
    @PutMapping("/{personId}/control-status")
    @OperationLog(operator = Operator.CONTROL, module = OperateModule.ARCHIVES_MANAGE, desc = "列管", primaryKey = "personId")
    public void controlPerson(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        personService.controlPerson(personId, subjectId);
    }

    /**
     * 取消人员列管
     *
     * @param personId        人员id
     * @param subjectId       专题id
     */
    @PutMapping("/{personId}/control-status/cancel")
    @OperationLog(operator = Operator.CANCEL_CONTROL, module = OperateModule.ARCHIVES_MANAGE, desc = "取消列管", primaryKey = "personId")
    public void cancelControlPerson(@PathVariable String personId,
        @NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        personService.cancelControlPerson(personId, subjectId);
    }

    /**
     * 话单列表
     *
     * @param personId 人员id
     * @param pageParams 分页参数
     * @return 列表
     */
    @PostMapping("/{personId}/call/list")
    public PageResult<CallVO> getCallListByPersonId(@PathVariable String personId, @RequestBody PageParams pageParams) {
        return callService.getCallListByPersonId(personId, pageParams);
    }

    /**
     * 添加话单
     *
     * @param personId 人员id
     * @param callVO 话单
     */
    @PostMapping("/{personId}/call")
    public void addCall(@PathVariable String personId, @RequestBody CallVO callVO) {
        callService.addCall(personId, callVO);
    }

    /**
     * 修改话单
     *
     * @param personId 人员id
     * @param callVO 话单
     */
    @PutMapping("/{personId}/call")
    public void modifyCall(@PathVariable String personId, @RequestBody CallVO callVO) {
        callService.modifyCall(personId, callVO);
    }

    /**
     * 删除话单
     *
     * @param personId 人员id
     * @param id 话单id
     */
    @DeleteMapping("/{personId}/call/{id}")
    public void deleteCall(@PathVariable String personId, @PathVariable String id) {
        callService.deleteCall(personId, id);
    }

    /**
     * 资金列表
     *
     * @param personId 人员id
     * @param pageParams 分页参数
     * @return 列表
     */
    @PostMapping("/{personId}/fund/list")
    public PageResult<FundVO> getFundListByPersonId(@PathVariable String personId, @RequestBody PageParams pageParams) {
        return callService.getFundListByPersonId(personId, pageParams);
    }

    /**
     * 添加资金
     *
     * @param personId 人员id
     * @param fundVO 资金
     */
    @PostMapping("/{personId}/fund")
    public void addFund(@PathVariable String personId, @RequestBody FundVO fundVO) {
        callService.addFund(personId, fundVO);
    }

    /**
     * 修改资金
     *
     * @param personId 人员id
     * @param fundVO 资金
     */
    @PutMapping("/{personId}/fund")
    public void modifyFund(@PathVariable String personId, @RequestBody FundVO fundVO) {
        callService.modifyFund(personId, fundVO);
    }

    /**
     * 删除资金
     *
     * @param personId 人员id
     * @param id 资金id
     */
    @DeleteMapping("/{personId}/fund/{id}")
    public void deleteFund(@PathVariable String personId, @PathVariable String id) {
        callService.deleteFund(personId, id);
    }
}
