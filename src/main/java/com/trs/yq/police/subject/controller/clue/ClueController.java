package com.trs.yq.police.subject.controller.clue;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

/**
 * 线索信息接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/clue")
@Slf4j
public class ClueController {

    @Resource
    private ClueService clueService;

    @Resource
    private SubjectService subjectService;

    @Resource
    private ModuleService moduleService;

    @Resource
    private ClueExcelService clueExcelService;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private PersonService personService;

    @Resource
    private GroupService groupService;

    /**
     * 根据人员id查询线索详情
     * http://192.168.200.192:3001/project/4897/interface/api/130621
     *
     * @param clueId 人员id
     * @return 线索详情vo
     */
    @GetMapping("/{clueId}")
    public ClueVO getClue(@PathVariable @NotBlank(message = "线索id不能为空！") String clueId) {
        return clueService.getClueBasicInfo(clueId);
    }

    /**
     * 添加一条线索信息
     * http://192.168.200.192:3001/project/4897/interface/api/130855
     *
     * @param clueVO {@link ClueVO}
     * @return id
     */
    @PostMapping("")
    public String addClue(@Valid @RequestBody ClueVO clueVO) {
        return clueService.addClue(clueVO);
    }

    /**
     * 删除一条线索以及线索关联的人员和文件
     *
     * @param clueId 线索详情
     */
    @DeleteMapping("/{clueId}")
    public void deleteOne(@PathVariable String clueId) {
        clueService.deleteOne(clueId);
    }

    /**
     * 更新一条线索信息
     * http://192.168.200.192:3001/project/4897/interface/api/130627
     *
     * @param clueId 线索id
     * @param clueVO 线索详情
     */
    @PutMapping("/{clueId}")
    public void updateOne(@PathVariable String clueId, @RequestBody ClueVO clueVO) {
        clueVO.setClueId(clueId);
        clueService.updateOne(clueVO);
    }

    /**
     * 查询线索列表筛选条件
     *
     * @param subjectId 专题id
     * @return 线索列表筛选条件
     */
    @GetMapping("/list/filters")
    public List<ListFilter> getListFilters(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return subjectService.getClueListQueryFilters(subjectId);
    }

    /**
     * 线索列表查询
     * http://192.168.200.192:3001/project/4897/interface/api/130561
     *
     * @param subjectId 专题主键
     * @param request   查询参数
     * @return 分页查询结果
     */
    @PostMapping("/list")
    public PageResult<ClueListVO> getClueList(@NotBlank(message = "专题编号缺失") String subjectId,
                                              @RequestBody @Valid ListRequestVO request) {
        return clueService.getClueList(subjectId, request);
    }

    /**
     * 批量导出列表
     * http://192.168.200.192:3001/project/4897/interface/api/130597
     *
     * @param response  {@link HttpServletResponse}
     * @param exportParams   {@link ExportParams}
     * @throws IOException io异常
     */
    @PostMapping("/list/export")
    public void listExport(HttpServletResponse response,
                           @Validated @RequestBody ExportParams exportParams) throws IOException {
        clueExcelService.downLoadExcel(response, exportParams, exportParams.getSubjectId());
    }

    /**
     * 根据专题id出查询可导出的字段信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getPersonProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return clueExcelService.getExportPropertyList(subjectId);
    }

    /**
     * 查询专题下所有线索类别
     * http://192.168.200.192:3001/project/4897/interface/api/130915
     *
     * @param subjectId subjectId
     * @return 线索类别列表
     */
    @GetMapping("/types")
    public List<IdNameVO> getClueTypeList(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        List<LabelEntity> types = labelRepository.findAllBySubjectId(subjectId, "clue");
        return types.stream().map(type -> new IdNameVO(type.getId(), type.getName())).collect(Collectors.toList());
    }

    /**
     * 查询档案目录
     * http://192.168.200.192:3001/project/4897/interface/api/130609
     *
     * @param subjectId 专题id
     * @return 模块列表 {@link ContentVO}
     */
    @GetMapping("/content")
    public List<ContentVO> getArchiveContent(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return moduleService.getArchiveContent(subjectId, "clue");
    }

    /**
     * 批量删除线索
     * http://192.168.200.192:3001/project/4897/interface/api/130669
     *
     * @param clueIds 线索id
     */
    @DeleteMapping("/delete/batch")
    public void deleteClues(@RequestBody @NotEmpty(message = "线索Id缺失") List<String> clueIds) {
        clueService.deleteClues(clueIds);
    }

    /**
     * 线索关联多个群体
     * http://192.168.200.192:3001/project/4897/interface/api/130939
     *
     * @param clueId 线索id
     * @param vo     {@link ClueGroupRelationVO}
     */
    @PutMapping("/{clueId}/group/relations")
    public void updateGroupClueRelation(@PathVariable String clueId, @RequestBody @Validated ClueGroupRelationVO vo) {
        vo.setClueId(clueId);
        clueService.updateGroupClueRelation(vo);
    }

    /**
     * 线索关联多个人员
     * http://192.168.200.192:3001/project/4897/interface/api/130945
     *
     * @param clueId 线索id
     * @param vo     {@link CluePersonRelationVO }
     */
    @PutMapping("/{clueId}/person/relations")
    public void updatePersonClueRelation(@PathVariable String clueId, @RequestBody @Validated CluePersonRelationVO vo) {
        vo.setClueId(clueId);
        clueService.updateCluePersonRelations(vo);
    }

    /**
     * 移除线索关联人员
     * http://192.168.200.192:3001/project/4897/interface/api/130747
     *
     * @param relationId 关系id
     */
    @DeleteMapping("/person/relation/{relationId}")
    public void removePersonFromClue(@PathVariable String relationId) {
        clueService.removePersonFromClue(ModuleEnum.CLUE, relationId);
    }

    /**
     * 移除线索关联群体
     * http://192.168.200.192:3001/project/4897/interface/api/130753
     *
     * @param relationId 关系id
     */
    @DeleteMapping("/group/relation/{relationId}")
    public void removeGroupFromClue(@PathVariable String relationId) {
        clueService.removeGroupFromClue(relationId);
    }


    /**
     * 分页查询线索中群体信息
     * http://192.168.200.192:3001/project/4897/interface/api/130939
     *
     * @param clueId        线索Id
     * @param requestParams 分页参数
     * @return 线索中人员信息
     */
    @PostMapping("/{clueId}/group/list")
    public PageResult<ClueRelatedGroupVO> getGroupsPageFromClue(@PathVariable String clueId,
                                                                @RequestBody @Validated RequestParams requestParams) {
        return clueService.getRelatedGroupList(clueId, requestParams.getPageParams());
    }

    /**
     * 不分页查询线索已经关联的群体列表
     *
     * @param clueId 线索id
     * @return {@link ClueRelatedGroupVO}
     */
    @GetMapping("/{clueId}/group/related")
    public List<ClueRelatedGroupVO> getClueRelatedGroup(@PathVariable String clueId) {
        return clueService.getGroupsFromClue(clueId);
    }

    /**
     * 线索关联群体对话框分页查询
     * http://192.168.200.192:3001/project/4897/interface/api/130843
     *
     * @param clueId  线索id
     * @param request {@link DialogGroupListRequestVO}
     * @return {@link DialogGroupListVO}
     */
    @PostMapping("/{clueId}/group/list/condition")
    public PageResult<DialogGroupListVO> getDialogGroupListVO(@PathVariable String clueId, @RequestBody @Validated DialogGroupListRequestVO request) {
        return groupService.getDialogGroupListVO(request);
    }

    /**
     * 查询线索已经关联的人员列表
     * http://192.168.200.192:3001/project/4897/interface/api/130615
     *
     * @param clueId        线索id
     * @param requestParams {@link RequestParams}
     * @return {@link ClueRelatedPersonVO}
     */
    @PostMapping("/{clueId}/person/list")
    public PageResult<ClueRelatedPersonVO> getGroupAssociatedPersonVO(@PathVariable String clueId,
                                                                      @RequestBody @Valid RequestParams requestParams) {
        return clueService.getRelatedPersonList(clueId, requestParams.getPageParams());
    }

    /**
     * 不分页查询线索已经关联的人员列表
     *
     * @param clueId 线索id
     * @return {@link ClueRelatedPersonVO}
     */
    @GetMapping("/{clueId}/person/related")
    public List<ClueRelatedPersonVO> getGroupRelatedPerson(@PathVariable String clueId) {
        return clueService.getPersonsFromClue(clueId);
    }

    /**
     * 线索关联人员对话框中查询人员列表
     * http://192.168.200.192:3001/project/4897/interface/api/130849
     *
     * @param clueId  线索id
     * @param request {@link DialogPersonListRequestVO}
     * @return {@link DialogPersonListVO}
     */
    @PostMapping("/{clueId}/person/list/condition")
    public PageResult<DialogPersonListVO> getDialogPersonListRequestVOList(@PathVariable String clueId,
                                                                           @Valid @RequestBody DialogPersonListRequestVO request) {
        return personService.getDialogPersonListVOPageResult(request);
    }

    /**
     * 根据线索id查询合成作战信息
     * http://192.168.200.192:3001/project/4897/interface/api/134107
     *
     * @param clueId 线索id
     * @return {@link BattleRecordCommandListVO}
     */
    @GetMapping("/{clueId}/composite/list")
    public List<BattleRecordCommandListVO> getBattleRecordList(@PathVariable String clueId) {
        return clueService.getBattleRecordList(clueId);
    }

    /**
     * 根据线索id指令信息
     * http://192.168.200.192:3001/project/4897/interface/api/134107
     *
     * @param clueId 线索id
     * @return {@link BattleRecordCommandListVO}
     */
    @GetMapping("/{clueId}/instruct/list")
    public List<BattleRecordCommandListVO> getBattleCommandList(@PathVariable String clueId) {
        return clueService.getBattleCommandList(clueId);
    }

    /**
     * 获取线索关联的材料信息
     * http://192.168.200.192:3001/project/4897/interface/api/134191
     *
     * @param clueId 线索id
     * @return {@link FileInfoVO}
     */
    @GetMapping("/{clueId}/material")
    public List<FileInfoVO> getClueFileInfo(@PathVariable String clueId) {
        return clueService.getClueFileInfo(clueId);
    }

    /**
     * 添加线索材料关联
     * http://192.168.200.192:3001/project/4897/interface/api/134209
     *
     * @param clueId           线索id
     * @param attachmentVOList 材料信息
     */
    @PostMapping("/{clueId}/file/relation")
    public void addClueFileRelation(@PathVariable String clueId, @RequestBody List<AttachmentVO> attachmentVOList) {
        clueService.addClueFileRelation(clueId, attachmentVOList);
    }

    /**
     * 删除线索材料关联
     * http://192.168.200.192:3001/project/4897/interface/api/134215
     *
     * @param clueId 线索id
     * @param fileId 材料id
     */
    @DeleteMapping("/{clueId}/file/{fileId}")
    public void deleteClueFileRelation(@PathVariable String clueId, @PathVariable String fileId) {
        clueService.deleteClueFileRelation(clueId, fileId);
    }

    /**
     * 获取线索展示的按钮
     *
     * @param clueId 线索id
     * @return {@link ButtonVO}
     */
    @GetMapping("/{clueId}/button")
    public ButtonVO getButton(@PathVariable String clueId) {
        return clueService.getButton(clueId);
    }

    /**
     * 上报
     *
     * @param clueId 线索id
     * @return {@link ReportInfoVO}
     */
    @PostMapping("/{clueId}/report")
    public ReportInfoVO reportToSuperior(@PathVariable @NotBlank String clueId) {
        LoginUser currentUser = AuthHelper.getCurrentUser();
        return clueService.reportToSuperior(clueId, currentUser);
    }

    /**
     * 改变线索的处置状态
     *
     * @param clueId 线索id
     * @param status 状态
     */
    @PostMapping("/{clueId}/dispose")
    public void changeDisposeStatus(@PathVariable @NotBlank String clueId, @NotNull String status) {

        clueService.changeDisposeStatus(clueId, status);
    }
}
