package com.trs.yq.police.subject.service.impl;

import com.trs.hybase.client.TRSConnection;
import com.trs.hybase.client.TRSException;
import com.trs.hybase.client.TRSRecord;
import com.trs.hybase.client.TRSResultSet;
import com.trs.hybase.client.params.ConnectParams;
import com.trs.hybase.client.params.SearchParams;
import com.trs.yq.police.subject.constants.PoliceSubjectConstants;
import com.trs.yq.police.subject.constants.enums.TimeRangeEnum;
import com.trs.yq.police.subject.domain.entity.MobilePhoneEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.TrajectorySourceEntity;
import com.trs.yq.police.subject.domain.entity.VehicleEntity;
import com.trs.yq.police.subject.domain.entity.WarningTypeEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.request.TrajectoryListRequest;
import com.trs.yq.police.subject.domain.vo.DateCountVO;
import com.trs.yq.police.subject.domain.vo.IdNameCountVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.PersonTrajectoryVO;
import com.trs.yq.police.subject.repository.MobilePhoneRepository;
import com.trs.yq.police.subject.repository.PersonRepository;
import com.trs.yq.police.subject.repository.TrajectorySourceRepository;
import com.trs.yq.police.subject.repository.VehicleRepository;
import com.trs.yq.police.subject.repository.WarningTrajectoryRepository;
import com.trs.yq.police.subject.repository.WarningTypeRepository;
import com.trs.yq.police.subject.service.HkTrajectoryService;
import com.trs.yq.police.subject.service.TrajectoryService;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/08/29
 */
@Service
@Slf4j
public class TrajectoryServiceImpl implements TrajectoryService {

    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private VehicleRepository vehicleRepository;

    @Resource
    private MobilePhoneRepository mobilePhoneRepository;

    @Resource
    private HkTrajectoryService hkTrajectoryService;

    @Resource
    private WarningTypeRepository warningTypeRepository;

    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;

    @Value("${com.trs.hybase.url}")
    private String url;
    @Value("${com.trs.hybase.user}")
    private String user;
    @Value("${com.trs.hybase.pwd}")
    private String pwd;
    private TRSConnection trsConnection;

    /**
     * 异常行为轨迹
     */
    private static final String ABNORMAL_ACTION_TRACE = "1";
    /**
     * 感知源轨迹
     */
    private static final String SOURCE_TRACE = "2";


    /**
     * 创建hybase数据库链接
     */
    @PostConstruct
    public void initSearchItem() {
        trsConnection = new TRSConnection(url, user, pwd, new ConnectParams());
    }

    /**
     * 查询所有轨迹来源
     *
     * @param personId 人员id
     * @return 轨迹来源
     */
    @Override
    public List<IdNameCountVO> getTrajectorySourceList(String personId, String trajectoryType) {

        List<TrajectorySourceEntity> all = trajectorySourceRepository.findAll();

        //人员信息
        PersonEntity person = personRepository.getById(personId);
        //车辆信息
        List<VehicleEntity> vehicles = vehicleRepository.findByPersonId(personId);
        //电话信息
        List<MobilePhoneEntity> mobilePhones = mobilePhoneRepository.findByPersonId(personId);

        List<IdNameCountVO> results = new ArrayList<>();

        if (Objects.equals(trajectoryType, ABNORMAL_ACTION_TRACE)) {
            List<WarningTypeEntity> warningTypes = warningTypeRepository.findAllBySubjectId(PoliceSubjectConstants.ZB_SUBJECT);
            warningTypes.forEach(type -> {
                int count = warningTrajectoryRepository.countByPersonIdAndSubjectIdAndWarningType(
                    person.getIdNumber(), PoliceSubjectConstants.ZB_SUBJECT, type.getId());
                results.add(new IdNameCountVO(type.getId(), type.getCnName(), count));
            });
        } else if (Objects.equals(trajectoryType, SOURCE_TRACE)) {
            all.forEach(source -> {
                //去海贝查询数据
                TimeParams timeParams = new TimeParams();
                timeParams.setRange(TimeRangeEnum.ALL.getCode());
                if (!"24".equals(source.getId())) {
                    List<PersonTrajectoryVO> trajectories = queryHybase(source.getId(), timeParams, person.getIdNumber(), mobilePhones, vehicles)
                        .stream().collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(PersonTrajectoryVO::getTime)
                            )), ArrayList::new));
                    results.add(new IdNameCountVO(source.getId(), source.getName(), trajectories.size()));
                } else {
                    Integer integer = hkTrajectoryService.countTrajectNum(person.getIdNumber());
                    results.add(new IdNameCountVO(source.getId(), source.getName(), integer));
                }
            });
        }
        results.add(0, new IdNameCountVO("0", "全部", results.stream().mapToInt(IdNameCountVO::getCount).sum()));
        return results;
    }

    /**
     * 查询轨迹
     *
     * @param request 请求
     * @return 轨迹
     */
    @Override
    public PageResult<PersonTrajectoryVO> getPersonTrajectoryList(String personId, TrajectoryListRequest request) {

        String sourceId = request.getSourceId();
        TimeParams timeParams = request.getTimeParams();
        PageParams pageParams = request.getPageParams();

        //人员信息
        PersonEntity person = personRepository.findById(personId).orElse(null);
        if (Objects.isNull(person)) {
            return PageResult.empty(pageParams.getPageNumber(), pageParams.getPageSize());
        }

        //车辆信息
        List<VehicleEntity> vehicles = vehicleRepository.findByPersonId(personId);

        //电话信息
        List<MobilePhoneEntity> mobilePhones = mobilePhoneRepository.findByPersonId(personId);
        if ("24".equals(sourceId)) {
            log.info("进入海康");
            return hkTrajectoryService.traject(personId, request);
        } else {
            List<PersonTrajectoryVO> results = queryHybase(sourceId, timeParams, person.getIdNumber(), mobilePhones, vehicles)
                    .stream().collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(PersonTrajectoryVO::getTime).thenComparing(PersonTrajectoryVO::getSource)
                            )), ArrayList::new));

            //分页并返回
            List<PersonTrajectoryVO> pageItems = results.stream()
                    .filter(vo -> Objects.nonNull(vo.getTime()))
                    .sorted(Comparator.comparing(PersonTrajectoryVO::getTime).reversed())
                    .skip(pageParams.getOffset())
                    .limit(pageParams.getPageSize())
                    .collect(Collectors.toList());
            return PageResult.of(pageItems, pageParams.getPageNumber(), results.size(), pageParams.getPageSize());
        }
        //去海贝查询数据

    }

    @Override
    public List<DateCountVO> getTrajectoryCountList(String personId, String sourceId) {
        List<DateCountVO> results = new ArrayList<>();

        //人员信息
        PersonEntity person = personRepository.findById(personId).orElse(null);
        if (Objects.isNull(person)) {
            return Collections.emptyList();
        }

        //车辆信息
        List<VehicleEntity> vehicles = vehicleRepository.findByPersonId(personId);

        //电话信息
        List<MobilePhoneEntity> mobilePhones = mobilePhoneRepository.findByPersonId(personId);

        for (int i = 11; i >= 0; --i) {
            TimeParams timeParams = new TimeParams();
            timeParams.setRange(TimeRangeEnum.CUSTOM.getCode());
            LocalDateTime beginTime = LocalDate.now().minusMonths(i).with(ChronoField.DAY_OF_MONTH, 1).atStartOfDay();
            timeParams.setBeginTime(beginTime);
            timeParams.setEndTime(beginTime.plusMonths(1));
            //去海贝查询数据
            List<PersonTrajectoryVO> trajectories = queryHybase(sourceId, timeParams, person.getIdNumber(), mobilePhones, vehicles)
                    .stream().collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                    Comparator.comparing(PersonTrajectoryVO::getTime).thenComparing(PersonTrajectoryVO::getSource)
                            )), ArrayList::new));
            DateCountVO vo = new DateCountVO();
            vo.setDate(beginTime);
            vo.setCount(trajectories.size());
            results.add(vo);
        }

        return results;
    }

    @NotNull
    private List<PersonTrajectoryVO> queryHybase(String sourceId,
                                                 TimeParams timeParams,
                                                 String idNumber,
                                                 List<MobilePhoneEntity> mobilePhones,
                                                 List<VehicleEntity> vehicles) {
        //轨迹来源
        List<TrajectorySourceEntity> trajectoryQueryTableEntities = trajectorySourceRepository.findAll();
        List<PersonTrajectoryVO> results = new ArrayList<>();
        for (TrajectorySourceEntity source : trajectoryQueryTableEntities) {
            // sourceId=0 表示查全部
            if ("0".equals(sourceId) || source.getId().equals(sourceId)) {
                try {
                    TRSResultSet resultSet = trsConnection.executeSelect(source.getTableName(),
                            buildSearchCondition(source, timeParams, idNumber, mobilePhones, vehicles),
                            0,
                            10000,
                            new SearchParams().setSortMethod(String.format("-%s", source.getTimeColumn())));
                    for (int i = 0; i < resultSet.size(); i++) {
                        results.add(buildResult(source, resultSet.getRecord(i)));
                    }
                } catch (Exception e) {
                    log.error(String.format("execute select hybase failed!!! table:[%s]", source.getTableName()));
                }
            }
        }
        return results;
    }

    /**
     * 构建查询条件
     *
     * @param source       表字段
     * @param timeParam    时间条件
     * @param idNumber     人员身份证号
     * @param mobilePhones 人员关联手机号
     * @param vehicles     人员关联车辆
     * @return 查询条件
     */
    private String buildSearchCondition(TrajectorySourceEntity source,
                                        TimeParams timeParam,
                                        String idNumber,
                                        List<MobilePhoneEntity> mobilePhones,
                                        List<VehicleEntity> vehicles) {
        String condition = String.format("%s:[%s TO %s] AND ", source.getTimeColumn(),
                Objects.nonNull(timeParam.getBeginTime())
                        ? DateTimeFormatter.ofPattern(source.getTimeFormat()).format(timeParam.getBeginTime())
                        : "*",
                Objects.nonNull(timeParam.getEndTime())
                        ? DateTimeFormatter.ofPattern(source.getTimeFormat()).format(timeParam.getEndTime())
                        : "*");

        String indexType = source.getIndexType();
        String indexColumn = source.getIndexColumn();
        if (TrajectorySourceEntity.CAR_NUMBER.equals(indexType)) {
            condition += String.format("%s#LIST:%s", indexColumn, vehicles.isEmpty() ? "0" :
                    vehicles.stream().map(VehicleEntity::getVehicleNumber).collect(Collectors.joining(",")));
        } else if (TrajectorySourceEntity.PHONE_NUMBER.equals(indexType)) {
            condition += String.format("%s#LIST:%s", indexColumn, mobilePhones.isEmpty() ? "0" :
                    mobilePhones.stream().map(MobilePhoneEntity::getPhoneNumber).collect(Collectors.joining(",")));
        }
        //默认是身份证号码
        else {
            condition += String.format("%s:%s", indexColumn, idNumber);
        }
        return condition;
    }

    private PersonTrajectoryVO buildResult(TrajectorySourceEntity queryTableEntity,
                                           TRSRecord trsRecord) throws TRSException {
        PersonTrajectoryVO vo = new PersonTrajectoryVO();
        vo.setSource(queryTableEntity.getName());
        vo.setId(getStringByField(queryTableEntity.getIndexColumn(), trsRecord));
        vo.setPlace(getStringByField(queryTableEntity.getPlaceColumn(), trsRecord));
        vo.setAddress(getStringByField(queryTableEntity.getAddressColumn(), trsRecord));
        Date date = trsRecord.getDate(queryTableEntity.getTimeColumn());
        vo.setTime(Objects.nonNull(date) ? new Timestamp(date.getTime()).toLocalDateTime() : null);
        vo.setLat(getStringByField(queryTableEntity.getLatColumn(), trsRecord));
        vo.setLng(getStringByField(queryTableEntity.getLngColumn(), trsRecord));
        List<String> imageUrls = getStringListByField(queryTableEntity.getImageColumn(), trsRecord)
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        vo.setImageUrls(imageUrls);

        String markTemplate = queryTableEntity.getTemplate();
        if (StringUtils.isNotEmpty(markTemplate)) {
            markTemplate = markTemplate.replaceAll("(?<=:)(.*?)(?=$|;)", "%{#map[$1]}");
            ParserContext context = new TemplateParserContext("%{", "}");
            StandardEvaluationContext evaluationContext = new StandardEvaluationContext();

            String jsonText = trsRecord.toString("json");
            Map<String, Object> map = JsonUtil.parseMap(jsonText, Object.class);
            evaluationContext.setVariable("map", map);
            String value = new SpelExpressionParser().parseExpression(markTemplate, context)
                    .getValue(evaluationContext, String.class);
            vo.setDetail(value);
        }
        return vo;
    }

    private String getStringByField(String field, TRSRecord trsRecord) throws TRSException {
        return StringUtils.isNotEmpty(field) ? trsRecord.getString(field) : null;
    }

    private List<String> getStringListByField(String field, TRSRecord trsRecord) {
        if (StringUtils.isNotBlank(field)) {
            String[] columns = field.split(",");
            return Arrays.stream(columns).map(column -> {
                try {

                    String fieldUrl = getStringByField(column, trsRecord);
                    if ("picture_uri".equals(field) && StringUtils.contains(url, "80.75.89.48")) {
                        /*
                         * 泸州依图人像图片数据特殊处理  代码逻辑不会影响其他项目
                         * 依图人像图片地址80.75.89.48代理的大图地址有问题  需要修正
                         */
                        fieldUrl = "http://80.75.89.48:11180/storage/v1/image/global?cluster_id=LUZHOU_NT&uri_base64=" + StringUtils.substringAfterLast(url, "uri_base64=");
                    }
                    return fieldUrl;
                } catch (TRSException e) {
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
