package com.trs.yq.police.subject.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 妨害国（边）境管理人员（人）(数量统计)
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/17 16:07
 **/
@AllArgsConstructor
@Data
public class HarmfulBorderVO {
    /**
     * 赌博人员
     */
    private Integer gamblingCount;
    /**
     * 非法出入境
     */
    private Integer entryExit;

    /**
     * 诈骗数量
     */
    private Integer swindleCount;

}
