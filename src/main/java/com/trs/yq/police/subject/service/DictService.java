package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.entity.DictEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 码表服务接口
 *
 * <AUTHOR>
 * @date 2021/07/29
 */
public interface DictService {

    /**
     * 根据type字段查询字典项
     *
     * @param type 类型
     * @return 字典项
     */
    List<DictEntity> getDictEntitiesByType(@NotBlank(message = "type不能为空！") String type);

    /**
     * 根据type和code查询字典项
     *
     * @param type type字段
     * @param code code字段
     * @return 字典项
     */
    DictEntity getDictEntityByTypeAndCode(@NotBlank(message = "type不能为空！") String type,
                                          @NotBlank(message = "code不能为空！") String code);

}
