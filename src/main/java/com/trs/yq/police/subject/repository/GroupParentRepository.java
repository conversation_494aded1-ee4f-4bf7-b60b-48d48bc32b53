package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.GroupParentEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 群体-上级群体关联表
 *
 * <AUTHOR>
 * @since 2021/11/29
 */
@Repository
public interface GroupParentRepository extends BaseRepository<GroupParentEntity, String> {

    /**
     * 分页查询该群体的上级群体
     *
     * @param groupId 群体id
     * @param pageable 分页参数
     * @return 上级群体
     */
    Page<GroupParentEntity> findAllByGroupId(String groupId, Pageable pageable);

    /**
     * 查询该群体的上级群体
     *
     * @param groupId 群体id
     * @return 上级群体
     */
    List<GroupParentEntity> findAllByGroupId(String groupId);

    /**
     * 删除群体id的所有上级群体
     *
     * @param groupId 群体id
     */
    void deleteAllByGroupId(String groupId);
}
