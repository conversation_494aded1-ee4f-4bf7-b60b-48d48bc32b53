package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDate;
import java.util.Objects;

/**
 * 落脚点地址实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_FOOTHOLD")
public class FootholdEntity extends BaseEntity {

    private static final long serialVersionUID = 6465233833211305303L;

    /**
     * 人员id
     */
    private String personId;
    /**
     * 开始居住时间
     */
    private LocalDate startTime;
    /**
     * 停止居住时间
     */
    private LocalDate endTime;
    /**
     * 居住地址
     */
    private String address;
    /**
     * 居住状态 1=在住 0=未住
     */
    private String state;
    /**
     * 备注
     */
    private String note;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FootholdEntity that = (FootholdEntity) o;
        return personId.equals(that.personId) && startTime.equals(that.startTime) && Objects.equals(endTime, that.endTime) && address.equals(that.address) && Objects.equals(state, that.state) && Objects.equals(note, that.note);
    }

    @Override
    public int hashCode() {
        return Objects.hash(personId, startTime, endTime, address, state, note);
    }
}
