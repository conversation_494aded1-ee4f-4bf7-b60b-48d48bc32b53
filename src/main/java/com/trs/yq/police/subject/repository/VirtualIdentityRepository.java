package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.VirtualIdentityEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 虚拟身份类持久层
 *
 * <AUTHOR>
 */
@Repository
public interface VirtualIdentityRepository extends BaseRepository<VirtualIdentityEntity, String> {

    /**
     * 获取人员虚拟信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link VirtualIdentityEntity}
     * <AUTHOR>
     */
    List<VirtualIdentityEntity> findAllByPersonId(String personId);

    /**
     * 获取所有虚拟身份
     *
     * @return 虚拟身份
     */
    @Query("select v.virtualType, v.virtualNumber from VirtualIdentityEntity v where v.virtualType <= 3")
    List<Map<String, String>> findAllPhoneVirtual();

    /**
     * 根据人员id和虚拟信息和虚拟身份类型查询关联关系是否存在
     *
     * @param personId      人员id
     * @param virtualNumber 虚拟身份号码
     * @param virtualType   虚拟身份类型
     * @return 结果
     */
    Boolean existsByPersonIdAndVirtualNumberAndVirtualType(String personId, String virtualNumber, String virtualType);

    /**
     * 根据人员id合虚拟身份类类型查询虚拟身份
     *
     * @param personId    人员id
     * @param virtualType 虚拟身份类型
     * @return {@link VirtualIdentityEntity}
     */
    List<VirtualIdentityEntity> findByPersonIdAndVirtualType(String personId, String virtualType);

    /**
     * 删除人员所有虚拟身份
     *
     * @param personId 人员id
     */
    void deleteAllByPersonId(String personId);
}
