package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.EventEntity;
import com.trs.yq.police.subject.domain.entity.EventGroupRelationEntity;
import com.trs.yq.police.subject.repository.EventGroupRelationRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/28
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class GroupRelatedEventVO extends RelatedEventVO {

    private static final long serialVersionUID = 8469120548465756928L;

    /**
     * 构造器
     *
     * @param eventEntity {@link EventEntity}
     * @param groupId     群体id
     */
    public GroupRelatedEventVO(EventEntity eventEntity, String groupId) {
        super(eventEntity);
        EventGroupRelationRepository eventGroupRelationRepository = BeanUtil.getBean(EventGroupRelationRepository.class);
        EventGroupRelationEntity relation = eventGroupRelationRepository.findByEventIdAndGroupId(eventEntity.getId(), groupId);
        this.relationId = relation.getId();
    }
}
