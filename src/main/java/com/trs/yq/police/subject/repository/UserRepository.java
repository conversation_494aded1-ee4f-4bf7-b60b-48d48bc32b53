package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.UserEntity;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 用户表持久化层
 *
 * <AUTHOR>
 * @since 2021/8/9
 */
@Repository
public interface UserRepository extends BaseRepository<UserEntity, String> {

    /**
     * 按单位编号精确、姓名模糊查询用户列表
     *
     * @param unitCode 单位编号
     * @param realName 真实姓名
     * @return 用户列表 {@link UserEntity}
     */
    List<UserEntity> findByUnitCodeAndRealNameContaining(String unitCode, String realName);

    /**
     * 根据手机号查询用户
     *
     * @param phoneList 手机号列表
     * @return 用户列表
     */
    List<UserEntity> findByMobilePhoneIn(List<String> phoneList);


    /**
     * 通过部门查询用户uid
     *
     * @param unitCode 部门code
     * @return {@link String}
     */
    @Query("select t.id  from UserEntity t where t.unitCode =:unitCode")
    Set<String> findIdByUnitCode(@Param("unitCode") String unitCode);

    /**
     * 通过部门前缀查询用户uid
     *
     * @param unitCodePrefix 部门code
     * @return {@link String}
     */
    @Query("select t.id  from UserEntity t where t.unitCode like concat(:unitCodePrefix,'%') ")
    List<String> findIdByUnitCodePrefix(@Param("unitCodePrefix") String unitCodePrefix);


    /**
     * 根据证件号码查询
     *
     * @param idCard 证件号码
     * @return 用户
     */
    UserEntity findByIdCard(String idCard);
}
