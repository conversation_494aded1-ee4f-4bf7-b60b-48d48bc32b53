package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.domain.vo.ContentVO;

import java.util.List;

/**
 * 档案目录服务层
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
public interface ModuleService {

    /**
     * 查询档案目录
     *
     * @param subjectId 专题主键
     * @param type      类型
     * @return 档案目录
     */
    List<ContentVO> getArchiveContent(String subjectId, String type);

    /**
     * 获取人员操作日志目录
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 模板列表
     */
    List<ContentVO> getPersonOperationLogContent(String personId, String subjectId);

    /**
     * 获取操作日志目录
     *
     * @param groupId  群体id
     * @param subjectId 专题id
     * @param type 目标对象类型 person/group/clue
     * @return 模板列表
     */
    List<ContentVO> getOperationLogContent(String groupId, String subjectId, TargetObjectTypeEnum type);

}
