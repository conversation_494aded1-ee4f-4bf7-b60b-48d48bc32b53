package com.trs.yq.police.subject.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * 日期工具类
 *
 * <AUTHOR>
 * @date 2021/07/14
 */
@Slf4j
public class DateUtil {
    private DateUtil() {

    }

    /**
     * 时间戳转时间
     *
     * @param utc 时间戳
     * @return 时间对象
     */
    public static LocalDateTime utcToLocalDateTime(long utc) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(utc), ZoneId.systemDefault());
    }

    /**
     * 时间戳转日期
     *
     * @param utc 时间戳
     * @return 日期对象
     */
    public static LocalDate utcToLocalDate(long utc) {
        return utcToLocalDateTime(utc).toLocalDate();
    }

    /**
     * 时间转时间戳
     *
     * @param dateTime 时间
     * @return 时间戳
     */
    public static Long dateTimeToUtc(LocalDateTime dateTime) {
        return dateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 日期转时间戳
     *
     * @param date 日期
     * @return 时间戳
     */
    public static Long dateToUtc(LocalDate date) {
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 判断日期是否相等
     *
     * @param date1 date1
     * @param date2 date2
     * @return 比较结果
     */
    public static boolean isSame(Date date1, Date date2) {
        if ((date1 == null && date2 != null) || (date1 != null && date2 == null)) {
            return false;
        } else if (date1 == null) {
            return true;
        } else {
            return DateUtils.isSameInstant(date1, date2);
        }
    }

}
