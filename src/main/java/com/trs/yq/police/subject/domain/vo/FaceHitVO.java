package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.FaceHitEntity;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2021/11/24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FaceHitVO extends FaceHitEntity {

    private static final long serialVersionUID = -3204801400230726759L;

    /**
     * entity to vo
     *
     * @param faceHitEntity entity
     * @return vo
     */
    public static FaceHitVO of(FaceHitEntity faceHitEntity) {
        FaceHitVO vo = new FaceHitVO();
        BeanUtil.copyPropertiesIgnoreNull(faceHitEntity, vo);
        return vo;
    }
}
