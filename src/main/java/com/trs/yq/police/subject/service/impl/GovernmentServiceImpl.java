package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import com.trs.yq.police.subject.domain.vo.GovernmentInfoVO;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.CommonExtendRepository;
import com.trs.yq.police.subject.service.GovernmentService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_GROUP;
import static com.trs.yq.police.subject.constants.OperationLogConstants.OPERATION_LOG_TARGET_PERSON;

/**
 * <AUTHOR>
 * @date 2021/12/23 15:59
 */
@Service
public class GovernmentServiceImpl implements GovernmentService {
    @Resource
    CommonExtendRepository commonExtendRepository;
    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public GovernmentInfoVO getGovernmentByModuleAndRecordId(String module, String recordId) {
        CommonExtentEntity commonExtentEntity = commonExtendRepository.findByRecordIdAndModule(recordId, module).orElse(new CommonExtentEntity());
        GovernmentInfoVO governmentInfoVO = new GovernmentInfoVO();
        BeanUtil.copyPropertiesIgnoreNull(commonExtentEntity, governmentInfoVO);
        return governmentInfoVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGovernmentInfo(String module, String recordId, GovernmentInfoVO governmentInfoVO) {
        CommonExtentEntity old = commonExtendRepository.findByRecordIdAndModule(recordId, module).orElse(new CommonExtentEntity());
        final String oldVal = JsonUtil.toJsonString(old);
        BeanUtil.copyPropertiesIgnoreNull(governmentInfoVO, old);
        old.setRecordId(recordId);
        old.setModule(module);
        commonExtendRepository.save(old);


        //记录操作日志
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(StringUtils.equals(CommonExtentEntity.GROUP, module) ? OperateModule.GROUP_GOV_CONTROL_INFO : OperateModule.GOV_CONTROL_INFO)
                .oldObj(oldVal)
                .newObj(JsonUtil.toJsonString(old))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(recordId)
                .desc("修改政府行业主管部门")
                .targetObjectType(StringUtils.equals(CommonExtentEntity.GROUP, module) ? OPERATION_LOG_TARGET_GROUP : OPERATION_LOG_TARGET_PERSON)
                .build();
        if (java.util.Objects.nonNull(operationLogHandler)) {
            // 转发操作日志至队列
            operationLogHandler.publishEvent(logRecord);
        }

    }
}
