package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 出入境走访记录VO
 *
 * <AUTHOR>
 * @date 2021/9/17 14:43
 */
@Data
public class CrjVisitListVO implements Serializable {
    private static final long serialVersionUID = 3109494252653093113L;
    private String id;
    /**
     * 姓名
     */
    private String name;
    /**
     * 证件类型
     */
    private String certificateType;
    /**
     * 证件号码
     */
    private String certificateNumber;
    /**
     * 国籍
     */
    private String nationality;
    /**
     * 走访方式
     */
    private String visitType;
    /**
     * 走访时间
     */
    private LocalDateTime visitTime;
    /**
     * 走访单位
     */
    private String deptName;
}
