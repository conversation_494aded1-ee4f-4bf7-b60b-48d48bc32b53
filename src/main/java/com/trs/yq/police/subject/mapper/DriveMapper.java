package com.trs.yq.police.subject.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.trs.yq.police.subject.domain.vo.DrivingNoLicenseAnalyzeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/19 16:24
 * @since 1.0
 */
@Mapper
@DS("trino")
public interface DriveMapper {

    /**
     * 无证驾驶驾驶分析
     *
     * @return 无证驾驶驾驶分析结果
     */
    List<DrivingNoLicenseAnalyzeVO> timingAnalyze();

    /**
     * customSql<BR>
     *
     * @param customSql 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 09:46
     */
    List<DrivingNoLicenseAnalyzeVO> customSql(@Param("customSql") String customSql);
}
