package com.trs.yq.police.subject.message.startegy;


import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.constants.enums.TargetObjectTypeEnum;
import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.dto.CarWarningDTO;
import com.trs.yq.police.subject.domain.entity.WarningEntity;
import com.trs.yq.police.subject.domain.entity.WarningTraceRelationEntity;
import com.trs.yq.police.subject.domain.entity.WarningTrajectoryEntity;
import com.trs.yq.police.subject.domain.entity.WarningTypeEntity;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.TrajectorySourceRepository;
import com.trs.yq.police.subject.repository.WarningRepository;
import com.trs.yq.police.subject.repository.WarningTraceRelationRepository;
import com.trs.yq.police.subject.repository.WarningTrajectoryRepository;
import com.trs.yq.police.subject.repository.WarningTypeRepository;
import com.trs.yq.police.subject.utils.JsonUtil;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/10/27 11:09
 */
@Slf4j
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class CarWarningProcess implements WarningProcess {

    private static final String TYPE_EN_NAME = "jj_ffyyclyj";

    private static final List<String> WARNING_SOURCE = Collections.singletonList("交警卡口");

    private static final String WARNING_SOURCE_TABLE_NAME = "xds.JiaoJingKaKouXinXi";

    private static final String ID_TYPE = "plate";

    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private OperationLogHandler operationLogHandler;

    @Override
    public void process(String message) {
        CarWarningDTO warningMessage = JsonUtil.parseObject(message, CarWarningDTO.class);
        // 验证数据
        if (Objects.isNull(warningMessage)) {
            log.error("receive error call list warning message! message = {}", message);
            return;
        }
        String warningId = insertWarning(warningMessage);

        String trajectoryId = insertTrajectoryId(warningMessage);

        establishRelation(warningId,trajectoryId);

        final OperationLogRecord logRecord = OperationLogRecord.builder()
            .operator(Operator.PUSH)
            .module(OperateModule.WARNING)
            .currentUser(AuthHelper.getCurrentUser())
            .primaryKey(warningId)
            .targetObjectType(TargetObjectTypeEnum.WARNING.getCode())
            .desc("推送预警消息")
            .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }


    private String insertWarning(CarWarningDTO warningMessage) {

        WarningTypeEntity warningType = warningTypeRepository.findByEnName(TYPE_EN_NAME);

        WarningEntity warning = new WarningEntity();
        warning.setWarningTime(LocalDateTime.now());
        warning.setWarningLevel(warningType.getDefaultLevel());
        warning.setWarningSource(WARNING_SOURCE);
        warning.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
        warning.setWarningDetails(generateWarningDetail(warningType.getContentTemplate(), warningMessage));
        warning.setSubjectId(warningType.getSubjectId());
        warning.setWarningType(warningType.getId());


        return warningRepository.save(warning).getId();
    }


    private String generateWarningDetail(String contentTemplate, CarWarningDTO message) {
        SpelExpressionParser parser = new SpelExpressionParser();
        EvaluationContext context = new StandardEvaluationContext();
        final Expression expression = parser.parseExpression(contentTemplate);
        context.setVariable("plateNo", StringUtils.isEmpty(message.getPlateNo())? "- -":message.getPlateNo());
        return expression.getValue(context,String.class);
    }

    private String insertTrajectoryId(CarWarningDTO warningMessage) {
        String warningSourceId = trajectorySourceRepository.findByTableName(WARNING_SOURCE_TABLE_NAME).getId();
        WarningTrajectoryEntity trajectoryEntity = new WarningTrajectoryEntity();
        trajectoryEntity.setSourceId(warningSourceId);
        trajectoryEntity.setIdType(ID_TYPE);
        trajectoryEntity.setIdValue(warningMessage.getPlateNo());
        trajectoryEntity.setPlace(WARNING_SOURCE.get(0));
        trajectoryEntity.setSimilarity(warningMessage.getResult());
        trajectoryEntity.setDateTime(LocalDateTime.now());
        trajectoryEntity.setWarningExplain(JsonUtil.toJsonString(warningMessage.getExplain()));
        return warningTrajectoryRepository.save(trajectoryEntity).getId();
    }

    private void establishRelation(String warningId,String trajectoryId){
        WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
        relation.setWarningId(warningId);
        relation.setTrajectoryId(trajectoryId);
        relation.setCreateTime(LocalDateTime.now());
        warningTraceRelationRepository.save(relation);
    }
}
