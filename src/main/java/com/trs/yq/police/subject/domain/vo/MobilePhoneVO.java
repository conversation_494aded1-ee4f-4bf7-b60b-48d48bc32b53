package com.trs.yq.police.subject.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 手机号VO
 *
 * <AUTHOR>
 * @date 2021/7/28 14:16
 */
@Data
public class MobilePhoneVO implements Serializable {

    private static final long serialVersionUID = -8630893321470367609L;

    /**
     * 主键
     */
    private String id;

    /**
     * 关联人Id
     */
    private String personId;

    /**
     * 是否常用
     */
    @JsonProperty("isCommonly")
    private String phoneUseStatus;

    /**
     * 手机号
     */
    @JsonProperty("mobileNum")
    private String phoneNumber;

    /**
     * 是否在用
     */
    @JsonProperty("isInUse")
    private String phoneStatus;

    /**
     * 是否为自动更新的
     */
    private String isAutomated;
}
