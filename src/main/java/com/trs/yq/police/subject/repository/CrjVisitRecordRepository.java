package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjVisitRecordEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入境走访记录
 *
 * <AUTHOR>
 * @since 2021/9/16
 */
@Repository
public interface CrjVisitRecordRepository extends BaseRepository<CrjVisitRecordEntity, String> {
    /**
     * [专题首页-出入境] 获取走访记录列表
     *
     * @param visitType   走访类别
     * @param deptName    走访单位
     * @param searchValue 查询关键词
     * @param beginTime   起始时间
     * @param endTime     结束时间
     * @param pageable    分页参数
     * @return {@link CrjVisitRecordEntity}
     */
    @Query("select  t from  CrjVisitRecordEntity t " +
            " where t.visitTime> :beginTime and t.visitTime< :endTime" +
            " and  (:visitType is null  or t.visitType = :visitType)" +
            " and  (:deptName is null or t.crDept like  concat('%',:deptName,'%') )" +
            " and  (:searchValue is null or concat(t.firstName,t.lastName,t.certificateNumber) like concat('%',:searchValue,'%') )" +
            " order by t.visitTime desc ")
    Page<CrjVisitRecordEntity> getCrjVisitRecordList(@Param("visitType") String visitType,
                                                     @Param("deptName") String deptName,
                                                     @Param("searchValue") String searchValue,
                                                     @Param("beginTime") LocalDateTime beginTime,
                                                     @Param("endTime") LocalDateTime endTime,
                                                     Pageable pageable);

    /**
     * 查询所有走访单位
     *
     * @return 走访单位信息
     */
    @Query("select t.crDept from CrjVisitRecordEntity  t group by t.crDept ")
    List<String> getDeptNames();

    /**
     * 获取走访记录列表
     *
     * @param searchValue 查询关键词
     * @param beginTime   起始时间
     * @param endTime     结束时间
     * @return {@link CrjVisitRecordEntity}
     */
    @Query("select t from  CrjVisitRecordEntity t " +
            " where t.visitTime>= :beginTime and t.visitTime<= :endTime" +
            " and  (:searchValue is null or concat(t.firstName,' ',t.lastName,t.certificateNumber,t.livingInn,t.livingInfo) like concat('%',:searchValue,'%') )" +
            " order by t.visitTime desc ")
    List<CrjVisitRecordEntity> getAppVisitRecordList(@Param("searchValue") String searchValue,
                                                     @Param("beginTime") LocalDateTime beginTime,
                                                     @Param("endTime") LocalDateTime endTime);

    /**
     * 获取走访记录列表
     *
     * @param certificateNumber 证件号码
     * @param searchValue       查询关键词
     * @param beginTime         起始时间
     * @param endTime           结束时间
     * @return {@link CrjVisitRecordEntity}
     */
    @Query("select  t from  CrjVisitRecordEntity t " +
            " where t.visitTime> :beginTime and t.visitTime< :endTime" +
            " and  (:certificateNumber is null or t.certificateNumber = :certificateNumber)" +
            " and  (:searchValue is null or concat(t.firstName,' ',t.lastName,t.certificateNumber,t.livingInn,t.livingInfo) like concat('%',:searchValue,'%') )" +
            " order by t.visitTime desc ")
    List<CrjVisitRecordEntity> getAppVisitByNumber(@Param("certificateNumber") String certificateNumber,
                                                   @Param("searchValue") String searchValue,
                                                   @Param("beginTime") LocalDateTime beginTime,
                                                   @Param("endTime") LocalDateTime endTime);
}
