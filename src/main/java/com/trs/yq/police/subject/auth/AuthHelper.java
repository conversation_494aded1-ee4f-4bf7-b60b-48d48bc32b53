package com.trs.yq.police.subject.auth;

import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;

/**
 * 权限工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 10:12
 */
@Slf4j
public class AuthHelper {

    private AuthHelper() {
    }

    /**
     * 获取当前线程用户
     *
     * @return 当前登录用户
     */
    public static LoginUser getCurrentUser() {
        OAuth2Authentication authentication =
                (OAuth2Authentication) SecurityContextHolder.getContext().getAuthentication();
        try {
            return JsonUtil.OBJECT_MAPPER.convertValue(
                    authentication.getUserAuthentication().getDetails(), LoginUser.class);
        } catch (Exception ignore) {
            return null;
        }

    }
}
