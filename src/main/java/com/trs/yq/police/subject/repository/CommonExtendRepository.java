package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * group|person 公用信息接口类
 *
 * <AUTHOR>
 * @date 2021/12/8 15:35
 */
@Repository
public interface CommonExtendRepository extends BaseRepository<CommonExtentEntity, String> {

    /**
     * 根据记录id和模块查询
     *
     * @param recordId 记录id
     * @param module 模块
     * @return {@link CommonExtentEntity}
     */
    Optional<CommonExtentEntity> findByRecordIdAndModule(String recordId, String module);
}
