package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 虚拟身份vo
 *
 * <AUTHOR>
 */
@Data
public class VirtualIdentityVO implements Serializable {

    private static final long serialVersionUID = 8002930697899834283L;

    /**
     * 虚拟id
     */
    private String virtualId;

    /**
     * 身份类型
     */
    private String virtualType;

    /**
     * 虚拟号码
     */
    private String virtualNumber;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 自定义虚拟身份名称
     */
    private String virtualTypeName;

    /**
     * 是否为自动更新的
     */
    private String isAutomated;
}
