package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.domain.entity.OperationLogEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作日志持久层
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 17:17
 */
@Repository
public interface OperationLogRepository extends BaseRepository<OperationLogEntity, String> {

    /**
     * 分页查询操作日志
     *
     * @param queryKey         主键
     * @param modules          操作模块
     * @param targetObjectType 目标对象类型
     * @param pageable         分页参数
     * @return 分页查询结果
     */
    @Query("select ole from OperationLogEntity ole "
            + "where ole.targetObjectId = :queryKey "
            + "and ole.operateModule in (:modules) "
            + "and ole.targetObjectType = :targetObjectType "
            + "order by ole.crTime desc, ole.crBy asc")
    Page<OperationLogEntity> findPage(@Param("queryKey") String queryKey,
                                      @Param("modules") List<OperateModule> modules,
                                      @Param("targetObjectType") Integer targetObjectType,
                                      Pageable pageable);

    /**
     * 不分页查询操作日志
     *
     * @param queryKey         主键
     * @param modules          操作模块
     * @param targetObjectType 目标对象类型
     * @return 分页查询结果
     */
    @Query("select ole from OperationLogEntity ole "
            + "where ole.targetObjectId = :queryKey "
            + "and ole.operateModule in (:modules) "
            + "and ole.targetObjectType = :targetObjectType "
            + "order by ole.crTime desc, ole.crBy asc")
    List<OperationLogEntity> findAll(@Param("queryKey") String queryKey,
                                      @Param("modules") List<OperateModule> modules,
                                      @Param("targetObjectType") Integer targetObjectType);


    /**
     * 通过目标id和模块统计数量
     *
     * @param targetObjectId   目标id
     * @param module           操作模块
     * @param targetObjectType 目标对象类型 0-person，1-group, 2-clue
     * @return 数量
     */
    int countAllByTargetObjectIdAndOperateModuleAndTargetObjectType(String targetObjectId, OperateModule module, Integer targetObjectType);

    /**
     * 已经目标移除操作记录
     *
     * @param targetObjectId 目标id
     */
    @Modifying
    void removeAllByTargetObjectId(@Param("targetObjectId") String targetObjectId);

    /**
     * 根据目标对象id查询操作记录列表
     *
     * @param targetObjectId 目标对象id
     * @param targetObjectType 目标对象类型
     * @return 操作记录列表
     */
    List<OperationLogEntity> findAllByTargetObjectIdAndTargetObjectType(@Param("warningId") String targetObjectId, @Param("targetObjectType")Integer targetObjectType);
}
