package com.trs.yq.police.subject.domain.request;

import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import com.trs.yq.police.subject.domain.vo.KeyValueTypeVO;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 列表查询参数
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ListParamsRequest {

    /**
     * 分页参数
     */
    private PageParams pageParams;

    /**
     * 文本检索参数
     */
    private SearchParams searchParams;

    /**
     * 排序参数
     */
    private SortParams sortParams;

    /**
     * 动态参数
     */
    private List<KeyValueTypeVO> filterParams;

    /**
     * 只分页的列表查询请求
     *
     * @param pageParams 分页
     */
    public ListParamsRequest(PageParams pageParams) {
        this.pageParams = pageParams;
        this.searchParams = new SearchParams();
        this.filterParams = new ArrayList<>();
        this.sortParams = new SortParams();
    }

    /**
     * 构建参数
     *
     * @param filterParams 动态参数
     */
    public ListParamsRequest(List<KeyValueTypeVO> filterParams) {
        this.pageParams = new PageParams();
        this.searchParams = new SearchParams();
        this.filterParams = filterParams;
        this.sortParams = new SortParams();
    }
}
