package com.trs.yq.police.subject.domain.response;

import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发生事件响应
 *
 * <AUTHOR>
 * @since 2021/7/19 16:43
 * @version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OccurredEventResponse {

    /**事件名称*/
    private String title;

    /**事发时间*/
    private LocalDateTime time;

    /**事发地点*/
    private String address;

    /**事件详情*/
    private String content;
}
