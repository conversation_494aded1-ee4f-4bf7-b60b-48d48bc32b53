package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.constants.CrjConstants;
import com.trs.yq.police.subject.domain.entity.CrjJwryVisitEntity;
import com.trs.yq.police.subject.domain.entity.DictEntity;
import com.trs.yq.police.subject.domain.entity.UserEntity;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.repository.UserRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/16 15:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CrjJwryVisitVO {
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 居住地址
     */
    private String liveAddress;
    /**
     * 工作地址
     */
    private String workAddress;
    /**
     * 拟定离开时间
     */
    private Date planLeaveTime;
    /**
     * 走访时间
     */
    private Date visitTime;
    /**
     * 即时定位
     */
    private String instantAddress;
    /**
     * 走访情况
     */
    private String detail;
    /**
     * 照片
     */
    private List<String> attachment;
    /**
     * 走访人信息
     */
    private String crByName;
    /**
     * 是否在住
     */
    private Boolean isLiving;

    private String registrationStatus;

    private String userPhoto;

    private LocalDateTime crTime;

    private String visaType;

    private String visaNumber;

    private Date inChinaTime;
    /**
     * 转为实体类
     *
     * @return {@link CrjJwryVisitEntity}
     */
    public CrjJwryVisitEntity toEntity() {
        CrjJwryVisitEntity crjForeignerVisitEntity = new CrjJwryVisitEntity();
        BeanUtils.copyProperties(this, crjForeignerVisitEntity);
        if (Objects.nonNull(this.getAttachment())) {
            crjForeignerVisitEntity.setAttachment(JsonUtil.toJsonString(this.getAttachment()));
        }

        return crjForeignerVisitEntity;
    }

    /**
     * 转vo
     *
     * @param entity 实体类
     * @param isRVisa 是不是r签证
     * @return {@link CrjJwryVisitVO}
     */
    public static CrjJwryVisitVO of(CrjJwryVisitEntity entity,Boolean isRVisa) {
        CrjJwryVisitVO crjJwryVisitVO = new CrjJwryVisitVO();
        BeanUtils.copyProperties(entity, crjJwryVisitVO);
        crjJwryVisitVO.setAttachment(JsonUtil.parseArray(entity.getAttachment(), String.class));
        DictRepository dictRepository = BeanUtil.getBean(DictRepository.class);
        DictEntity byTypeAndCode = dictRepository.findByTypeAndCode(CrjConstants.SFRY_REGISTRATION_STATUS,
            crjJwryVisitVO.getRegistrationStatus());
        if (Objects.nonNull(byTypeAndCode)) {
            crjJwryVisitVO.setRegistrationStatus(byTypeAndCode.getName());
        }
        UserRepository userRepository = BeanUtil.getBean(UserRepository.class);
        UserEntity byId = userRepository.getById(entity.getCrBy());
        if (StringUtils.isNotBlank(byId.getPhoto())){
            crjJwryVisitVO.setUserPhoto(CrjConstants.USER_PHOTO_PREFIX + byId.getPhoto());
        }
        if (!isRVisa){
            crjJwryVisitVO.setWorkAddress(null);
        }
        return crjJwryVisitVO;
    }


}
