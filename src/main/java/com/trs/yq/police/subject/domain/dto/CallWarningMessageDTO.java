package com.trs.yq.police.subject.domain.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 话单预警
 *
 * <AUTHOR>
 * @date 2021/9/8 22:42
 */
@Getter
@Setter
@ToString
public class CallWarningMessageDTO implements Serializable {

    private static final long serialVersionUID = 3813691890504606870L;

    /**
     * 预警类型
     */
    private String type;

    /**
     * 挖掘出来的号码
     */
    private String warningNumber;

    /**
     * 预警时间
     */
    private String warningTime;

    /**
     * 命中数据
     */
    private List<WarningCallHitDTO> hits;
}
