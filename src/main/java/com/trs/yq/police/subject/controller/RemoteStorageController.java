package com.trs.yq.police.subject.controller;

import static javax.servlet.http.HttpServletResponse.SC_NOT_FOUND;

import com.trs.yq.police.subject.common.SkipResponseBodyAdvice;
import com.trs.yq.police.subject.domain.vo.AttachmentVO;
import com.trs.yq.police.subject.domain.vo.ImageVO;
import com.trs.yq.police.subject.handler.PersonInfoHandler;
import com.trs.yq.police.subject.service.RemoteStorageService;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 远端存储接口
 *
 * <AUTHOR>
 * @date 2021/7/30 9:47
 */
@RestController
public class RemoteStorageController {

    @Resource
    private RemoteStorageService remoteStorageService;

    @Value("${com.trs.fastdfs.group_name}")
    private String groupName;

    @Resource
    private PersonInfoHandler personInfoHandler;

    /**
     * 上传图片 http://***************:3001/project/4897/interface/api/129605
     *
     * @param image 图片文件
     * @return 存储地址 / 图片id
     * @throws IOException 文件异常
     */
    @PostMapping("/file/image")
    public ImageVO uploadImage(MultipartFile image) throws IOException {
        return remoteStorageService.uploadImage(image);
    }

    /**
     * 批量上传图片 http://***************:3001/project/4897/interface/api/129760
     *
     * @param images 图片列表
     * @return 图片路径
     * @throws IOException 文件处理异常
     */
    @PostMapping("/file/images")
    public List<ImageVO> uploadImages(List<MultipartFile> images) throws IOException {
        return remoteStorageService.uploadImages(images);
    }

    /**
     * 文件上传 http://***************:3001/project/4897/interface/api/130867
     *
     * @param file 文件
     * @return 文件路径
     * @throws IOException 文件处理异常
     */
    @PostMapping("/file")
    public String uploadFile(@RequestParam MultipartFile file) throws IOException {
        final String[] paths = remoteStorageService.uploadFile(file.getBytes(), groupName,
            FilenameUtils.getExtension(file.getOriginalFilename()));
        return String.join("/", paths[0], paths[1]);
    }

    /**
     * 上传附件
     *
     * @param files 文件
     * @return {@link AttachmentVO}
     */
    @PostMapping("/file/attachments")
    public List<AttachmentVO> uploadAttachments(List<MultipartFile> files) {
        return remoteStorageService.uploadAttachments(files);
    }

    /**
     * 下载图片 http://***************:3001/project/4897/interface/api/129795
     *
     * @param fileName  文件名
     * @param groupName 组名
     * @param disc      存储盘
     * @param dir1      文件夹1
     * @param dir2      文件夹2
     * @param response  servlet响应
     * @throws IOException 文件操作异常
     */
    @GetMapping("/file/{groupName}/{disc}/{dir1}/{dir2}/{fileName}")
    @SkipResponseBodyAdvice
    public void getFile(@PathVariable String fileName, @PathVariable String groupName, @PathVariable String disc,
        @PathVariable String dir1, @PathVariable String dir2, HttpServletResponse response) throws IOException {
        String imagePath = String.join("/", disc, dir1, dir2, fileName);
        try {
            byte[] data = remoteStorageService.downloadFile(groupName, imagePath);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(data);
            outputStream.flush();
        } catch (Exception exception) {
            response.sendError(SC_NOT_FOUND);
        }
    }

    /**
     * 下载附件
     *
     * @param fileId 文件id
     * @return {@link ResponseEntity}
     * @throws UnsupportedEncodingException UrlEncoder编码异常
     */
    @GetMapping("/file/{fileId}")
    @SkipResponseBodyAdvice
    public ResponseEntity<ByteArrayResource> getFile(@PathVariable String fileId) throws UnsupportedEncodingException {
        return remoteStorageService.downLoadFile(fileId);
    }

    /**
     * 移除图片 http://***************:3001/project/4897/interface/api/130873
     *
     * @param fileName  文件名
     * @param groupName 组名
     * @param disc      光盘
     * @param dir1      文件夹1
     * @param dir2      文件夹2
     * @param response  servlet响应
     * @throws IOException io异常
     */
    @DeleteMapping("/file/{groupName}/{disc}/{dir1}/{dir2}/{fileName}")
    @SkipResponseBodyAdvice
    public void deleteImage(@PathVariable String fileName, @PathVariable String groupName, @PathVariable String disc,
        @PathVariable String dir1, @PathVariable String dir2, HttpServletResponse response) throws IOException {
        String imagePath = String.join("/", disc, dir1, dir2, fileName);
        try {
            remoteStorageService.deleteFile(groupName, imagePath);
        } catch (Exception exception) {
            response.sendError(SC_NOT_FOUND);
        }
    }

    /**
     * 获取大头照
     *
     * @param idNumber 身份证号
     * @param response response
     * @throws IOException io
     */
    @GetMapping("/file/getPhoto/{idNumber}")
    @SkipResponseBodyAdvice
    public void getPhoto(@PathVariable String idNumber, HttpServletResponse response) throws IOException {
        personInfoHandler.downloadPersonPhoto(idNumber);
        try {
            byte[] data = remoteStorageService.downloadPhoto(idNumber);
            ServletOutputStream outputStream = response.getOutputStream();
            outputStream.write(data);
            outputStream.flush();
        } catch (Exception exception) {
            response.sendError(SC_NOT_FOUND);
        }
    }
}
