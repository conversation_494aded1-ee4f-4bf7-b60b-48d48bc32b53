package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * 走访记录实体类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_VISIT_RECORD")
public class VisitRecordEntity extends BaseEntity {

    private static final long serialVersionUID = -4617853221990892986L;

    /**
     * 人员id
     */
    private String personId;

    /**
     * 走访时间
     */
    private LocalDateTime time;

    /**
     * 走访方式
     */
    private String method;

    /**
     * 是否在控
     */
    private String inControl;

    /**
     * 失控时间
     */
    private LocalDateTime outOfControlTime;

    /**
     * 当前去向
     */
    private String destination;

    /**
     * 走访情况
     */
    private String info;

    /**
     * 走访工作人员
     */
    private String visitBy;

    /**
     * 经度
     */
    private Double lat;

    /**
     * 纬度
     */
    private Double lng;
}

