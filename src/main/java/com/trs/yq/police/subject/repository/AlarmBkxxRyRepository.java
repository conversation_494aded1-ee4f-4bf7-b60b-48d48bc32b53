package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.AlarmBkxxRyEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2022/12/20 11:37
 */
@Repository
public interface AlarmBkxxRyRepository extends BaseRepository<AlarmBkxxRyEntity, String> {

    /**
     * 统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param district 区域
     * @param rylb 人员类别
     * @return map 统计结果
     */
    @Query(nativeQuery = true, value = "select count(1) FROM T_ALARM_BKXX_RY t " +
            "JOIN T_ALARM_BKXX bk ON t.BKZJ = bk.ID " +
            "where bk.BKQSSJ between :beginTime and :endTime " +
            "and (:district is null or bk.COUNTY like :district||'%') " +
            "and t.RYLB_DM like :rylb||'%'")
    Long statisticByRylb(@Param("beginTime") LocalDateTime beginTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("district") String district,@Param("rylb")String rylb);


    /**
     * 统计
     *
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param district 区域
     * @return map 统计结果
     */
    @Query(nativeQuery = true, value = "SELECT count(1) as measure,tab.BKJB as legend FROM T_ALARM_BKXX tab " +
            "JOIN T_ALARM_YJGJ tay ON tay.BKXXZJ = tab.ID " +
            "where tay.YJSJ between :beginTime and :endTime " +
            "and (:district is null or tab.COUNTY like :district||'%') " +
            "GROUP BY tab.BKJB ")
    List<Map<String, Object>> statisticByBkjb(@Param("beginTime") LocalDateTime beginTime,
                                              @Param("endTime") LocalDateTime endTime,
                                              @Param("district") String district);


}
