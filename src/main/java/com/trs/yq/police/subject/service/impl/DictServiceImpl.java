package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.domain.entity.DictEntity;
import com.trs.yq.police.subject.repository.DictRepository;
import com.trs.yq.police.subject.service.DictService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021/07/29
 */
@Service
public class DictServiceImpl implements DictService {

    @Resource
    private DictRepository dictRepository;

    /**
     * 根据type字段查询字典项
     *
     * @param type 类型
     * @return 字典项
     */
    @Override
    public List<DictEntity> getDictEntitiesByType(String type) {
        return dictRepository.findAllByType(type);
    }

    @Override
    public DictEntity getDictEntityByTypeAndCode(String type, String code) {
        return dictRepository.findByTypeAndCode(type, code);
    }


}
