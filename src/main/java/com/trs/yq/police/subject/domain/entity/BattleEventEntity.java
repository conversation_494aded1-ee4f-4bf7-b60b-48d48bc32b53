package com.trs.yq.police.subject.domain.entity;

import com.trs.yq.police.subject.jpa.converter.EventTimeConverter;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 事件信息
 *
 * <AUTHOR>
 * @date 2021/9/29 11:27
 */
@Entity
@Table(name = "T_BATTLE_EVENT")
@Data
public class BattleEventEntity implements Serializable {

    private static final long serialVersionUID = 1172008946549582953L;
    /**
     * 主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;
    /**
     * 预警类型
     */
    private String savetype;
    /**
     * 事件详情
     */
    private String detail;
    /**
     * 电话号
     */
    private String callphone;
    /**
     * 发生地点
     */
    private String eventaddr;
    /**
     * 发生时间
     */
    @Convert(converter = EventTimeConverter.class)
    private LocalDateTime eventtime;
    /**
     * 预警tag
     */
    private String typeval;

    private String  warnlevel;

    private String districtcode;

    private String keyval;

    private String srctable;
}
