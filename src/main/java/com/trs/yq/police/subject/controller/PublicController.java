package com.trs.yq.police.subject.controller;

import com.trs.yq.police.subject.constants.enums.TimeRangeEnum;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.WarningCarTrajectoryVO;
import com.trs.yq.police.subject.repository.HybaseDao;
import com.trs.yq.police.subject.utils.BeanUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/3/23 14:55
 */
@Validated
@RestController
@RequestMapping("public")
@Slf4j
public class PublicController {

    /**
     * 获取车辆轨迹
     *
     * @param carNumber 车牌号
     * @param range     时间参数
     * @return {@link  WarningCarTrajectoryVO}
     */
    @GetMapping("/car/track")
    List<WarningCarTrajectoryVO> getCarTrajectory(@RequestParam String carNumber, @RequestParam String range) {
        TimeParams timeParams = new TimeParams();
        timeParams.setRange(StringUtils.isNotBlank(range) ? range : TimeRangeEnum.TODAY.getCode());
        return BeanUtil.getBean(HybaseDao.class).getWarningCarTrajectory(carNumber, "蓝色", timeParams, 1000);
    }

}
