package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.OperationLogEntity;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志列表参数
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 18:21
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class OperationLogVo implements Serializable {

    private static final long serialVersionUID = -7364326189113476376L;

    /**
     * 主键
     */
    private String operateLogId;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 用户名
     */
    private String username;

    /**
     * 操作模块
     */
    private String operateModule;

    /**
     * 操作类型
     */
    private String operator;

    /**
     * 操作概述
     */
    private String overview;

    /**
     * 操作详情
     */
    private String detail;

    /**
     * 创建时间
     */
    private LocalDateTime operateTime;

    /**
     * 操作日志信息
     *
     * @param operationLog 操作日志
     */
    public OperationLogVo(OperationLogEntity operationLog) {
        operateLogId = operationLog.getId();
        realName = operationLog.getCrByName();
        username = operationLog.getCrByUsername();
        operateModule = operationLog.getOperateModule().getName();
        operator = operationLog.getOperator().getName();
        overview = StringUtils.trimToEmpty(operationLog.getOverview());
        detail = StringUtils.trimToEmpty(operationLog.getDetail());
        operateTime = operationLog.getCrTime();
    }
}
