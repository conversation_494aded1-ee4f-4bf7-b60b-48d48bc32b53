package com.trs.yq.police.subject.operation;

import com.trs.yq.police.subject.utils.RemoteAddrUtil;
import javassist.*;
import javassist.bytecode.CodeAttribute;
import javassist.bytecode.LocalVariableAttribute;
import javassist.bytecode.MethodInfo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 操作日志切面
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/27 15:03
 */
@Aspect
@Component
@Slf4j
public class OperationLogAspect {

    @Resource
    private OperationLogService operationLogService;

    @Pointcut("@annotation(com.trs.yq.police.subject.operation.OperationLog)")
    private void pointCut() {
    }

    @AfterReturning(pointcut = "pointCut()&&@annotation(operateLog)", returning = "returnValue")
    private void afterReturnOperation(JoinPoint point, OperationLog operateLog, Object returnValue) throws NotFoundException, ClassNotFoundException {

        final Map<String, Object> params = extractParams(point);

        final ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert attributes != null;
        HttpServletRequest request = attributes.getRequest();

        final String ipAddress = RemoteAddrUtil.getRemoteAddress(request);

        // 创建时从返回值获取主键
        if (operateLog.needReturnVal()) {
            params.put(operateLog.primaryKey(), returnValue);
        }

        operationLogService.recordOperationLog(operateLog, params, ipAddress);
        log.info("record operation log : success !");
    }

    /**
     * 抽取请求参数
     *
     * @param point 切入点
     * @return 获取参数列表
     * @throws NotFoundException 没有找到相应的对象
     */
    private Map<String, Object> extractParams(JoinPoint point) throws NotFoundException, ClassNotFoundException {

        Map<String, Object> params = new LinkedHashMap<>();

        // 参数值
        final Object[] args = point.getArgs();

        // 获取类对象
        final String targetClassType = point.getTarget().getClass().getName();
        final Class<?> clazz = Class.forName(targetClassType);
        final String targetClassName = clazz.getName();
        final ClassPool classPool = ClassPool.getDefault();
        ClassClassPath classPath = new ClassClassPath(this.getClass());
        classPool.insertClassPath(classPath);
        final CtClass ctClass = classPool.get(targetClassName);

        // 获取方法
        final Signature signature = point.getSignature();
        final String methodName = signature.getName();
        final CtMethod ctMethod = ctClass.getDeclaredMethod(methodName);

        // 获取参数
        final MethodInfo methodInfo = ctMethod.getMethodInfo();
        final CodeAttribute codeAttribute = methodInfo.getCodeAttribute();
        final LocalVariableAttribute attribute = (LocalVariableAttribute) codeAttribute.getAttribute(LocalVariableAttribute.tag);

        if (Objects.isNull(attribute)) {
            log.error("reflect param fail! 反射参数错误！");
            return params;
        }
        // 非静态参数第一个参数为this
        final int startIndex = Modifier.isStatic(ctMethod.getModifiers()) ? 0 : 1;

        // 获取参数
        for (int i = 0; i < ctMethod.getParameterTypes().length; i++) {
            params.put(attribute.variableName(i + startIndex), args[i]);
        }

        // 释放内存
        ctClass.detach();
        return params;
    }


}
