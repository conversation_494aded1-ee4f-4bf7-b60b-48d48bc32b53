package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.entity.FilterValue;

import java.util.List;

/**
 * 部门查询服务
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
public interface DepartmentService {

    /**
     * 查询部门树形结构-派出所
     *
     * @return {@link FilterValue}
     */
    List<FilterValue> getPoliceStationList();

    /**
     * 查询部门树形结构-全部门
     *
     * @return {@link FilterValue}
     */
    List<FilterValue> getAllUnitList();

    /**
     * 全部门的树，带层级
     *
     * @return 树
     */
    List<FilterValue> getUnitTree();

}
