package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 存储信息表
 *
 * <AUTHOR>
 * @date 2021/7/30 10:05
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_FILE_STORAGE")
public class FileStorageEntity extends BaseEntity {

    private static final long serialVersionUID = -6822255490703046117L;

    /**
     * 文件名
     */
    private String name;

    /**
     * 扩展名
     */
    private String extensionName;

    /**
     * 图片路径
     */
    private String path;

    /**
     * 图片存储url
     */
    private String url;

    /**
     * 存储组名
     */
    private String groupName;

    /**
     * 文件类型 0-视频，1-文档，2-图片，3-其他
     */
    private String type;

    /**
     * md5值
     */
    private String md5;

    /**
     * 文件大小
     */
    private Long fileSize;
    /**
     * 视频文件预览图片url
     */
    private String previewImage;
}

