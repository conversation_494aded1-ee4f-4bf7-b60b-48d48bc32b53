package com.trs.yq.police.subject.domain.response;

import java.util.List;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 风险研判返回类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/7/16 14:51
 */
@Getter
@Setter
@ToString
public class RiskDetailResponse {

    /** 事件编号 */
    private String eventId;

    /** 群体类别 */
    private List<String> groupType;

    /** 诉求地 */
    private String appealPlace;

    /** 行为名称 */
    private Set<String> behaviors;

    /** 涉事人员规模 */
    private Integer personCount;

    /** 页码 */
    private Integer pageNumber;

    /** 页面大小 */
    private Integer pageSize;

    /** 总数 */
    private Long total;

    /** 风险等级 */
    private String riskLevel;

    /** 参与人员 */
    private List<RiskDetailPersonResponse> participants;
}
