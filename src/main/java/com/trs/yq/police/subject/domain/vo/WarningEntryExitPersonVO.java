package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * [专题首页-出入境] 人员预警VO
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/9/17 9:54
 **/
@Data
public class WarningEntryExitPersonVO {

    /**
     * 预警id
     */
    private String warningId;
    /**
     * 预警级别：1: 红色 2: 橙色 3:黄色 4:蓝色
     */
    private String warningLevel;
    /**
     * 人员名称
     */
    private String personName;
    /**
     * 头像
     */
    private List<ImageVO> images;
    /**
     * 类型名称
     */
    private String typeName;
    /**
     * 预警时间
     */
    private LocalDateTime warningTime;

}
