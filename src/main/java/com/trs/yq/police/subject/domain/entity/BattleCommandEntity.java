package com.trs.yq.police.subject.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "T_BATTLE_COMMAND")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BattleCommandEntity {
    @Id
    private String Id;

    /**
     * 地区编码
     */
    @Column(name = "UNITCODE")
    private String unitCode;
    /**
     * 已签收单位
     */
    @Column(name = "SIGNEDUNITS")
    private String signedUnits;
    /**
     * 已反馈单位
     */
    @Column(name = "REPLIEDUNITS")
    private String repliedUnits;
    /**
     * 发布时间
     */
    @Column(name = "PUBLISHTIME")
    private LocalDateTime publishTime;

    /**
     * 状态(1-待审核  2-已发布  3-已驳回 4-已完成)
     */
    private Integer state;
    /**
     * 指令标题
     */
    private String title;

    /**
     * 发起单位
     */
    @Column(name = "unitname")
    private String unitName;
    /**
     * 创建人
     */
    private String crtbyname;
    /**
     * 创建时间
     */
    private LocalDateTime crttime;
}
