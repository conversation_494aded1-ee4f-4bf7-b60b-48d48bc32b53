package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 归档状态vo
 *
 * <AUTHOR>
 * @date 2021/08/10
 */
@Data
public class PersonArchiveVO implements Serializable {
    private static final long serialVersionUID = -723482676066704336L;

    /**
     * 流出时间
     */
    private LocalDateTime outTime;

    /**
     * 流出方向
     */
    private String outDestination;
}
