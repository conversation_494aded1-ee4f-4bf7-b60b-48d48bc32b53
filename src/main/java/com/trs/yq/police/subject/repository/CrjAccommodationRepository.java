package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.CrjAccommodationEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 出入境住宿登记
 *
 * <AUTHOR>
 * @since 2021/9/16
 */
@Repository
public interface CrjAccommodationRepository extends BaseRepository<CrjAccommodationEntity, String> {

    /**
     * 查询人员教育信息
     *
     * @param warningId 预警id
     * @return 人员基本信息 {@link CrjAccommodationEntity}
     * <AUTHOR>
     */
    @Query(nativeQuery = true,
            value = "select a.* from T_PS_CRJ_ACCOMMODATION a,T_PS_WARNING_ACCOM_RELATION b " +
                    "where a.ID = b.ACCOMMODATION_ID " +
                    "and b.WARNING_ID = :warningId")
    CrjAccommodationEntity findByWarningId(@Param("warningId") String warningId);

    /**
     * 【专题首页-出入境】 住宿记录列表
     *
     * @param beginTime   起始时间
     * @param endTime     结束时间
     * @param searchValue 关键词
     * @return {@link CrjAccommodationEntity}
     */
    @Query("select  t from  CrjAccommodationEntity t " +
            " where t.crTime> :beginTime and t.crTime< :endTime" +
            " and  (:searchValue is null or concat(t.firstName,' ',t.lastName,t.certificateNumber) like concat('%',:searchValue,'%') )" +
            " order by t.crTime desc ")
    List<CrjAccommodationEntity> getCrjAccommodationRecordList(LocalDateTime beginTime, LocalDateTime endTime, String searchValue);

    /**
     * 【专题首页-出入境】 住宿记录列表
     *
     * @param beginTime   起始时间
     * @param endTime     结束时间
     * @param searchValue 关键词
     * @param pageable    分页参数
     * @return {@link CrjAccommodationEntity}
     */
    @Query("select  t from  CrjAccommodationEntity t " +
            " where t.crTime> :beginTime and t.crTime< :endTime" +
            " and  (:searchValue is null or concat(t.firstName,' ',t.lastName,t.certificateNumber) like concat('%',:searchValue,'%') )" +
            " order by t.crTime desc ")
    Page<CrjAccommodationEntity> getCrjAccommodationRecordPageList(LocalDateTime beginTime, LocalDateTime endTime, String searchValue, Pageable pageable);
}
