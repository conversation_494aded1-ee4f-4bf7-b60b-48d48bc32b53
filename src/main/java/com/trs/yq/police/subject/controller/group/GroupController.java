package com.trs.yq.police.subject.controller.group;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.domain.entity.CommonExtentEntity;
import com.trs.yq.police.subject.domain.entity.ListFilter;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.ErrorMessage.SUBJECT_ID_MISSING;

/**
 * 群体Http接口
 *
 * <AUTHOR>
 * @date 2021/8/3 9:28
 */
@RestController
@RequestMapping("/group")
@Slf4j
@Validated
public class GroupController {
    @Resource
    private GroupService groupService;

    @Resource
    private ModuleService moduleService;

    @Resource
    private SubjectService subjectService;

    @Resource
    private GroupExcelService groupExcelService;

    @Resource
    private PersonService personService;

    @Resource
    private ClueService clueService;

    @Resource
    private EventService eventService;

    @Resource
    private GovernmentService governmentService;

    /**
     * 新增群体信息
     * http://192.168.200.192:3001/project/4897/interface/api/130729
     *
     * @param groupVO 群体信息
     * @return 群体Id
     */
    @PostMapping("")
    public String createGroup(@NotNull(message = "群体参数缺失") @RequestBody @Valid GroupVO groupVO) {
        return groupService.createGroup(groupVO);
    }

    /**
     * 查看群体详细信息
     * http://192.168.200.192:3001/project/4897/interface/api/130531
     *
     * @param groupId 群体Id
     * @return 群体信息
     */
    @GetMapping("/{groupId}")
    public GroupVO getGroupInfo(@NotBlank(message = "群体主键缺失") @PathVariable String groupId) {
        return groupService.getGroup(groupId);
    }

    /**
     * 批量删除群体信息
     * http://192.168.200.192:3001/project/4897/interface/api/130525
     *
     * @param groupIds 群体Id
     */
    @DeleteMapping("/batch")
    public void deleteGroup(@RequestBody @NotEmpty(message = "群体主键缺失") List<String> groupIds) {
        groupService.deleteGroups(groupIds);
    }

    /**
     * 编辑群体信息
     * http://192.168.200.192:3001/project/4897/interface/api/130723
     *
     * @param groupId groupId
     * @param groupVO 修改的内容
     */
    @PutMapping("/{groupId}")
    public void updateGroup(@NotBlank(message = "群体主键缺失") @PathVariable String groupId,
                            @NotNull(message = "群体参数缺失") @RequestBody @Valid GroupVO groupVO) {
        groupService.updateGroup(groupId, groupVO);
    }


    /**
     * 查询群体列表筛选条件
     * http://192.168.200.192:3001/project/4897/interface/api/130519
     *
     * @param subjectId 专题id
     * @return 群体列表筛选条件
     */
    @GetMapping("/list/filters")
    public List<ListFilter> getListFilters(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return subjectService.getGroupListQueryFilters(subjectId);
    }

    /**
     * 群体列表查询
     * http://192.168.200.192:3001/project/4897/interface/api/129810
     *
     * @param subjectId 专题主键
     * @param request   查询参数
     * @return 分页查询结果
     */
    @PostMapping("/list")
    public PageResult<GroupListVO> listGroups(@NotBlank(message = "专题编号缺失") String subjectId,
                                              @RequestBody @Valid ListRequestVO request) {
        return groupService.listGroups(subjectId, request);
    }

    /**
     * 批量导出列表
     * http://192.168.200.192:3001/project/4897/interface/api/130573
     *
     * @param response  {@link HttpServletResponse}
     * @param request   {@link GroupListExportRequest}
     * @throws IOException io异常
     */
    @PostMapping("/list/export")
    public void listExport(HttpServletResponse response,
                           @Validated @RequestBody ExportParams request) throws IOException {
        groupExcelService.downLoadExcel(response, request, request.getSubjectId());
    }

    /**
     * 根据专题id出查询可导出的字段信息
     *
     * @param subjectId 专题id
     * @return 属性json
     */
    @GetMapping("/list/export/checklist")
    public JsonNode getPersonProperties(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return groupExcelService.getExportPropertyList(subjectId);
    }

    /**
     * 查询档案目录
     * http://192.168.200.192:3001/project/4897/interface/api/130435
     *
     * @param subjectId 专题id
     * @return 模块列表 {@link ContentVO}
     */
    @GetMapping("/content")
    public List<ContentVO> getArchiveContent(@NotBlank(message = SUBJECT_ID_MISSING) String subjectId) {
        return moduleService.getArchiveContent(subjectId, "group");
    }


    /**
     * 更新群体和线索关联
     * http://192.168.200.192:3001/project/4897/interface/api/130681
     *
     * @param groupId 群体id
     * @param vo      {@link GroupClueRelationVO}
     */
    @PutMapping("/{groupId}/clue/relations")
    public void updateGroupClueRelation(@PathVariable String groupId, @RequestBody @Valid GroupClueRelationVO vo) {
        vo.setGroupId(groupId);
        groupService.updateGroupClueRelation(vo);
    }

    /**
     * 更新群体和人员关联
     * http://192.168.200.192:3001/project/4897/interface/api/130699
     *
     * @param groupId 群体id
     * @param vo      {@link GroupClueRelationVO}
     */
    @PutMapping("/{groupId}/person/relations")
    public void updatePersonClueRelation(@PathVariable String groupId, @RequestBody @Valid GroupPersonRelationVO vo) {
        vo.setGroupId(groupId);
        groupService.updateGroupPersonRelation(vo);
    }

    /**
     * 取消群体和线索关联
     * http://192.168.200.192:3001/project/4897/interface/api/130639
     *
     * @param relationId 群体线索关联id
     */
    @DeleteMapping("/clue/relation/{relationId}")
    public void deleteGroupClueRelation(@PathVariable String relationId) {
        groupService.deleteGroupClueRelation(relationId);
    }

    /**
     * 修改人员群体关联活跃度
     * http://192.168.200.192:3001/project/4897/interface/api/130705
     *
     * @param relationId 关联id
     * @param vo         {@link GroupPersonActivityLevelVO}
     */
    @PutMapping("/person/relation/{relationId}/activity-level")
    public void updateGroupPersonActive(@PathVariable String
                                                relationId, @RequestBody @Valid GroupPersonActivityLevelVO vo) {
        vo.setRelationId(relationId);
        groupService.updateGroupPersonActivityLevel(ModuleEnum.GROUP, vo);
    }


    /**
     * 取消群体人员关联
     * http://192.168.200.192:3001/project/4897/interface/api/130675
     *
     * @param relationId 群体人员关联id
     */
    @DeleteMapping("/person/relation/{relationId}")
    public void cancelGroupPersonRelation(@PathVariable String relationId) {
        groupService.cancelGroupPersonRelation(relationId);
    }

    /**
     * 获取专题下所有群体类别
     * http://192.168.200.192:3001/project/4897/interface/api/130537
     *
     * @param subjectId 专题id
     * @return 类别list
     */
    @GetMapping("/types")
    public List<IdNameVO> getGroupType(@NotBlank(message = "专题id缺失") String subjectId) {
        return groupService.getGroupTypes(subjectId);
    }

    /**
     * 查询群体下关联的线索列表
     * http://192.168.200.192:3001/project/4897/interface/api/130633
     *
     * @param groupId       群体id
     * @param requestParams 分页参数
     * @return 线索列表
     */
    @PostMapping("/{groupId}/clue/list")
    public PageResult<GroupRelatedClueVO> getClueGroup(@PathVariable String groupId,
                                                       @RequestBody @Validated RequestParams requestParams) {
        return groupService.getGroupRelatedClue(groupId, requestParams.getPageParams());
    }

    /**
     * 不分页查询群体下关联的线索列表
     * http://192.168.200.192:3001/project/4897/interface/api/130975
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedClueVO}
     */
    @GetMapping("/{groupId}/clue/related")
    public List<GroupRelatedClueVO> getClueGroup(@PathVariable String groupId) {
        return groupService.getGroupRelatedClue(groupId);
    }

    /**
     * 不分页查询群体下关联的人员列表
     * http://192.168.200.192:3001/project/4897/interface/api/130969
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedPersonVO}
     */
    @GetMapping("/{groupId}/person/related")
    public List<GroupRelatedPersonVO> getCluePerson(@PathVariable String groupId) {
        return groupService.getGroupRelatedPerson(groupId);
    }


    /**
     * 根据群体id查询群体人员列表
     * http://192.168.200.192:3001/project/4897/interface/api/130663
     *
     * @param requestParams 请求参数
     * @param groupId       群体人员关联
     * @return 团体人员关联列表
     */
    @PostMapping("/{groupId}/person/list")
    public PageResult<GroupRelatedPersonVO> getGroupPersonRelation(@PathVariable String groupId,
                                                                   @RequestBody @Valid RequestParams requestParams) {
        return groupService.getGroupRelatedPerson(groupId, requestParams);
    }

    /**
     * 群体关联线索对话框中查询线索列表
     * http://192.168.200.192:3001/project/4897/interface/api/130657
     *
     * @param groupId   群体id
     * @param requestVO {@link DialogClueListRequestVO}
     * @return {@link DialogClueListVO}
     */
    @PostMapping("/{groupId}/clue/list/condition")
    public PageResult<DialogClueListVO> getDialogPersonListRequestVOList(@PathVariable String groupId, @RequestBody @Valid DialogClueListRequestVO requestVO) {
        return clueService.getDialogClueList(requestVO);
    }

    /**
     * 群体关联人员对话框中查询人员列表
     * http://192.168.200.192:3001/project/4897/interface/api/130711
     *
     * @param groupId 群体id
     * @param request {@link DialogPersonListRequestVO}
     * @return {@link DialogPersonListVO}
     */
    @PostMapping("/{groupId}/person/list/condition")
    public PageResult<DialogPersonListVO> getDialogPersonListRequestVOList(@PathVariable String groupId,
                                                                           @Valid @RequestBody DialogPersonListRequestVO request) {
        return personService.getDialogPersonListVOPageResult(request);
    }

    /**
     * [群体档案] 根据群体id查询上级群体列表
     * http://192.168.200.192:3001/project/4897/interface/api/133825
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return 线索列表
     */
    @PostMapping("/{groupId}/parent/list")
    public PageResult<GroupParentVO> getParentGroupList(@PathVariable String groupId,
                                                        @RequestBody @Validated PageParams pageParams) {
        return groupService.getParentGroupList(groupId, pageParams);
    }

    /**
     * 不分页查询群体已经关联的上级群体列表
     * http://192.168.200.192:3001/project/4897/interface/api/133861
     *
     * @param groupId 群体id
     * @return {@link GroupParentVO}
     */
    @GetMapping("/{groupId}/parent/related")
    public List<GroupParentVO> getRelatedParentGroup(@PathVariable String groupId) {
        return groupService.getParentGroups(groupId);
    }

    /**
     * 更新群体和上级群体关联
     * http://192.168.200.192:3001/project/4897/interface/api/133831
     *
     * @param groupId   群体id
     * @param parentIds 上级群体id列表
     */
    @PutMapping("/{groupId}/parent/relations")
    public void updateGroupParentRelation(@PathVariable String groupId, @RequestBody @Valid List<String> parentIds) {
        groupService.updateGroupParentRelation(groupId, parentIds);
    }

    /**
     * 取消群体和上级群体关联
     * http://192.168.200.192:3001/project/4897/interface/api/133837
     *
     * @param relationId 关联id
     */
    @DeleteMapping("/parent/relation/{relationId}")
    public void deleteGroupParentRelation(@PathVariable String relationId) {
        groupService.deleteGroupParentRelation(relationId);
    }

    /**
     * 获取政府主管部门信息
     * http://192.168.200.192:3001/project/4897/interface/api/134357
     *
     * @param groupId 群体id
     * @return {@link GovernmentInfoVO}
     */
    @GetMapping("{groupId}/government")
    public GovernmentInfoVO getGovernmentInfo(@PathVariable String groupId) {
        return governmentService.getGovernmentByModuleAndRecordId(CommonExtentEntity.GROUP, groupId);
    }

    /**
     * 更新政府主管部门信息
     * http://192.168.200.192:3001/project/4897/interface/api/134356
     *
     * @param groupId          群体id
     * @param governmentInfoVO 政府主管部门vo
     */
    @PutMapping("{groupId}/government")
    public void updateGovernmentInfo(@PathVariable String groupId, @RequestBody @Valid GovernmentInfoVO governmentInfoVO) {
        governmentService.updateGovernmentInfo(CommonExtentEntity.GROUP, groupId, governmentInfoVO);
    }

    /**
     * [群体档案] 查询关联的事件 (不分页)
     * http://192.168.200.192:3001/project/4897/interface/api/134386
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedEventVO}
     */
    @GetMapping("/{groupId}/event/related")
    public List<GroupRelatedEventVO> getGroupRelatedEvent(@PathVariable String groupId) {
        return groupService.getGroupRelatedEvent(groupId);
    }


    /**
     * [群体档案] 查询关联的事件(分页)
     * http://192.168.200.192:3001/project/4897/interface/api/134387
     *
     * @param groupId    群体id
     * @param pageParams 查询参数
     * @return 分页查询事件结果
     */
    @PostMapping("/{groupId}/event/list")
    public PageResult<GroupRelatedEventVO> getGroupRelatedEvent(@PathVariable String groupId,
                                                                @RequestBody @Validated PageParams pageParams) {
        return groupService.getGroupRelatedEvent(groupId, pageParams);
    }

    /**
     * [群体档案] 更新群体事件关联
     * http://192.168.200.192:3001/project/4897/interface/api/134392
     *
     * @param groupId 群体id
     * @param vo      群体事件关联vo
     */
    @PutMapping("/{groupId}/event/relations")
    public void updateGroupEventRelations(@PathVariable String groupId, @RequestBody @Valid GroupEventsRelationVO vo) {
        vo.setGroupId(groupId);
        groupService.updateGroupEventRelations(vo);
    }

    /**
     * [群体档案] 条件查询事件列表（用于群体档案关联事件弹出对话框）
     * http://192.168.200.192:3001/project/4897/interface/api/134393
     *
     * @param groupId 群体id
     * @param request {@link DialogClueListRequestVO}
     * @return {@link DialogGroupListVO}
     */
    @PostMapping("/{groupId}/event/list/condition")
    public PageResult<DialogEventListVO> getDialogPersonClueList(@PathVariable String groupId,
                                                                 @RequestBody @Valid DialogEventListRequestVO request) {
        return eventService.getDialogEventList(request);
    }

    /**
     * [群体档案] 删除群体和事件的关联。
     *
     * @param relationId 关联id
     */
    @DeleteMapping("/event/relation/{relationId}")
    public void deleteEventRelation(@PathVariable String relationId) {
        groupService.deleteGroupEventRelation(relationId);
    }

    /**
     * 群体关联敏感时间节点列表查询
     * http://192.168.200.192:3001/project/4897/interface/api/134539
     *
     * @param groupId    群体id
     * @param pageParams 分页参数
     * @return {@link GroupRelatedTimeVO}
     */
    @PostMapping("/{groupId}/time/list")
    public PageResult<GroupRelatedTimeVO> getSensitiveList(@RequestBody PageParams pageParams, @PathVariable String groupId) {
        return groupService.getSensitiveList(groupId, pageParams);
    }

    /**
     * [群体档案] 更新群体敏感时间节点关联
     * http://192.168.200.192:3001/project/4897/interface/api/134544
     *
     * @param vo 群体事件关联vo
     */
    @PutMapping("/time/relation")
    public void updateGroupTimeRelations(@RequestBody @Valid GroupTimeRelationRequestVO vo) {
        groupService.updateGroupTimeRelations(vo);
    }

    /**
     * [群体档案] 删除群体和事件的关联。
     * http://192.168.200.192:3001/project/4897/interface/api/134549
     *
     * @param relationId 关联id
     */
    @DeleteMapping("/time/relation/{relationId}")
    public void deleteGroupTimeRelation(@PathVariable String relationId) {
        groupService.deleteGroupTimeRelation(relationId);
    }

    /**
     * [群体档案] 查询关联的敏感时间节点 (不分页)
     * http://192.168.200.192:3001/project/4897/interface/api/134567
     *
     * @param groupId 群体id
     * @return {@link GroupRelatedEventVO}
     */
    @GetMapping("/{groupId}/time/related")
    public List<GroupRelatedTimeVO> getGroupRelatedTime(@PathVariable String groupId) {
        return groupService.getGroupRelatedTime(groupId);
    }
}
