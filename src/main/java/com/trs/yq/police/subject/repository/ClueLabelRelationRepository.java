package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ClueLabelRelationEntity;
import org.springframework.stereotype.Repository;

/**
 * 线索类别关系接口
 *
 * <AUTHOR>
 * @date 2021/09/06
 */
@Repository
public interface ClueLabelRelationRepository extends BaseRepository<ClueLabelRelationEntity, String> {

    /**
     * 根据线索id删除所有类别关联
     *
     * @param clueId 线索id
     */
    void deleteAllByClueId(String clueId);
}
