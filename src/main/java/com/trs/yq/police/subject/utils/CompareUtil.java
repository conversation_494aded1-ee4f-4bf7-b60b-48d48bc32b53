package com.trs.yq.police.subject.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.Objects;

/**
 * 对象比较工具类
 *
 * <AUTHOR>
 * @date 2021/8/25 16:49
 */
public class CompareUtil {

    private CompareUtil() {
    }

    /**
     * 深度比较对象
     *
     * @param newObj 新实体
     * @param oldObj 旧实体
     * @return 是否相同，true - 相同， false - 不同
     */
    public static boolean equals(Object newObj, Object oldObj) {

        if (Objects.isNull(newObj) && Objects.isNull(oldObj)) {
            return true;
        } else if (Objects.isNull(newObj) || Objects.isNull(oldObj)) {

            return (Objects.isNull(newObj)
                    && oldObj instanceof Collection
                    && CollectionUtils.isEmpty((Collection<?>) oldObj))
                    || (Objects.isNull(oldObj)
                    && newObj instanceof Collection
                    && CollectionUtils.isEmpty((Collection<?>) newObj));
        }

        final String newObjType = newObj.getClass().getName();
        final String oldObjType = oldObj.getClass().getName();

        if (!StringUtils.equals(newObjType, oldObjType)) {
            return false;
        }

        if (newObj instanceof Collection) {
            // 判断集合是否不存在不一致的
            return CollectionUtils.isEmpty(CollectionUtils.disjunction((Collection<?>) newObj, (Collection<?>) oldObj));
        }

        return Objects.equals(newObj, oldObj);
    }


    /**
     * 深度比较对象
     *
     * @param newObj 新实体
     * @param oldObj 旧实体
     * @return 是否相同，false - 相同， true - 不同
     */
    public static boolean nonEquals(Object newObj, Object oldObj) {
        return !equals(newObj, oldObj);
    }


}
