package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2021/12/8 16:33
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_ClUE_PARENT_RELATION")
public class ClueParentEntity extends BaseEntity{
    private static final long serialVersionUID = -892489758061656320L;
    /**
     * 群体id
     */
    private String clueId;
    /**
     * 上级群体id
     */
    private String parentId;
}
