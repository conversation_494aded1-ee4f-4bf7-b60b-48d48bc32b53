package com.trs.yq.police.subject.operation.handler;

import com.lmax.disruptor.EventFactory;
import com.lmax.disruptor.TimeoutBlockingWaitStrategy;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.WorkHandler;
import com.trs.yq.police.subject.operation.event.OperationLogEvent;
import com.trs.yq.police.subject.operation.event.OperationLogEventFactory;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;

import java.util.concurrent.TimeUnit;

/**
 * 操作日志处理器
 *
 * <AUTHOR>
 * @date 2021/8/18 11:35
 */
@Slf4j
public class OperationLogHandler extends AbstractEventHandler<OperationLogRecord, OperationLogEvent> {

    private static final int RING_BUFFER_NO_GC_DEFAULT_SIZE = 4 * 1024;
    private static final int DEFAULT_WORK_SIZE = 4;

    private final ApplicationContext applicationContext;

    /**
     * 构造器
     *
     * @param applicationContext 应用程序上下文
     */
    public OperationLogHandler(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @Override
    protected int getBufferSize() {
        return RING_BUFFER_NO_GC_DEFAULT_SIZE;
    }

    @Override
    protected WaitStrategy getWaitStrategy() {
        return new TimeoutBlockingWaitStrategy(10L, TimeUnit.MILLISECONDS);
    }

    @Override
    protected WorkHandler<OperationLogEvent>[] getWorkers() {
        WorkHandler<OperationLogEvent>[] workHandlers = new OperationLogWorkHandler[DEFAULT_WORK_SIZE];
        for (int i = 0; i < DEFAULT_WORK_SIZE; i++) {
            workHandlers[i] = applicationContext.getBean(OperationLogWorkHandler.class);
        }
        return workHandlers;
    }

    @Override
    protected EventFactory<OperationLogEvent> getEventFactory() {
        return new OperationLogEventFactory();
    }
}
