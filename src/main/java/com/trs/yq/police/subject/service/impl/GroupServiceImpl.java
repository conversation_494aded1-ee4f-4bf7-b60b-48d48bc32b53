package com.trs.yq.police.subject.service.impl;

import com.google.common.collect.ImmutableMap;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.GroupListPredicatesBuilder;
import com.trs.yq.police.subject.constants.enums.ModuleEnum;
import com.trs.yq.police.subject.constants.enums.OperateModule;
import com.trs.yq.police.subject.constants.enums.Operator;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.exception.InteractException;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.operation.OperationLogServiceImpl;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.GroupService;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import com.trs.yq.police.subject.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.OperationLogConstants.*;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;

/**
 * 群体业务层实现
 *
 * <AUTHOR>
 * @date 2021/8/3 9:32
 */
@Service
@Slf4j
@Transactional(readOnly = true, rollbackFor = Exception.class)
public class GroupServiceImpl implements GroupService {

    @Resource
    private GroupRepository groupRepository;

    @Resource
    private PersonGroupRelationRepository personGroupRelationRepository;

    @Resource
    private GroupClueRelationRepository groupClueRelationRepository;

    @Resource
    private ClueRepository clueRepository;

    @Resource
    private GroupLabelRelationRepository groupLabelRelationRepository;

    @Resource
    private OperationLogHandler operationLogHandler;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private GroupParentRepository groupParentRepository;

    @Resource
    private EventRepository eventRepository;

    @Resource
    private EventGroupRelationRepository eventGroupRelationRepository;

    @Resource
    private CommonExtendRepository commonExtendRepository;
    @Resource
    private SensitiveTimeRepository sensitiveTimeRepository;
    @Resource
    private GroupTimeRelationRepository groupTimeRelationRepository;

    @Override
    public PageResult<GroupListVO> listGroups(String subjectId, ListRequestVO request) {
        Specification<GroupEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = GroupListPredicatesBuilder.buildListFilterPredicates(subjectId, request.getFilterParams(), root, criteriaBuilder).stream()
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(request.getSearchParams())) {
                predicates.addAll(GroupListPredicatesBuilder.buildSearchPredicates(request.getSearchParams(), root, criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        //排序
        final Pageable pageable = request.getPageParams().toPageable(Sort.by(Sort.Direction.DESC, "upTime"));

        Page<GroupEntity> groupList = groupRepository.findAll(specification, pageable);
        List<GroupListVO> items = groupList.getContent().stream()
                .map(pe -> GroupListVO.of(pe, subjectId))
                .collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), groupList.getTotalElements(), request.getPageParams().getPageSize());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String createGroup(GroupVO groupVO) {
        GroupEntity groupEntity = new GroupEntity();
        groupEntity.setBasicInfo(groupVO.getBasicInfo());
        groupEntity.setName(groupVO.getGroupName());
        groupEntity.setSubjectId(groupVO.getSubjectId());
        String groupId = groupRepository.save(groupEntity).getId();
        if (WW_SUBJECT.equals(groupVO.getSubjectId())) {
            //群体在创建时关联默认有效的敏感时间节点
            List<GroupTimeRelationEntity> relationEntityList = sensitiveTimeRepository.findAllByNodeType(SensitiveTimeEntity.SCOPE_ALL).stream().map(time -> {
                GroupTimeRelationEntity groupTimeRelationEntity = new GroupTimeRelationEntity();
                groupTimeRelationEntity.setGroupId(groupId);
                groupTimeRelationEntity.setSensitiveTimeId(time.getId());
                return groupTimeRelationEntity;
            }).collect(Collectors.toList());
            groupTimeRelationRepository.saveAll(relationEntityList);
            if (StringUtils.isNotBlank(groupVO.getMainDemand())) {
                CommonExtentEntity commonExtentEntity = new CommonExtentEntity();
                commonExtentEntity.setModule(CommonExtentEntity.GROUP);
                commonExtentEntity.setMainDemand(groupVO.getMainDemand());
                commonExtentEntity.setRecordId(groupId);
                commonExtendRepository.save(commonExtentEntity);
            }

        }
        //处理关联关系
        processGroupTypeRelation(groupId, groupVO.getGroupTypes());

        //处理操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.ADD)
                .module(OperateModule.GROUP_ARCHIVE_MANAGE)
                .newObj(JsonUtil.toJsonString(groupVO))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(groupId)
                .desc("新增群体信息")
                .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }

        return groupRepository.save(groupEntity).getId();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteGroups(List<String> groupIds) {

        //批量删除群体
        if (Objects.nonNull(groupIds)) {
            groupIds.forEach(groupId -> {

                //存储操作记录
                final GroupVO groupVO = getGroup(groupId);


                final OperationLogRecord logRecord = OperationLogRecord.builder()
                        .operator(Operator.BATCH_DELETE)
                        .module(OperateModule.GROUP_ARCHIVE_MANAGE)
                        .oldObj(JsonUtil.toJsonString(groupVO))
                        .currentUser(AuthHelper.getCurrentUser())
                        .primaryKey(groupId)
                        .desc("批量删除群体")
                        .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                        .build();
                if (Objects.nonNull(operationLogHandler)) {
                    // 记录操作
                    operationLogHandler.publishEvent(logRecord);
                }

                //删除
                personGroupRelationRepository.removeAllByGroupId(groupId);
                groupClueRelationRepository.removeAllByGroupId(groupId);
                groupLabelRelationRepository.removeAllByGroupId(groupId);
            });
            groupRepository.deleteAllById(groupIds);
        }
    }

    @Override
    public GroupVO getGroup(String id) {
        GroupEntity groupEntity = groupRepository.findById(id).orElse(null);
        if (Objects.isNull(groupEntity)) {
            throw new NoSuchElementException("当前群体不存在，请核实！！");
        }
        GroupVO groupVO = new GroupVO();
        BeanUtil.copyPropertiesIgnoreNull(groupEntity, groupVO);
        List<LabelEntity> groupTypes = labelRepository.findByGroupIdAndSubjectId(id, groupEntity.getSubjectId());
        //处理关联关系- 群体类别
        List<IdNameVO> vos = groupTypes.stream().map(type -> {
            IdNameVO result = new IdNameVO();
            result.setId(type.getId());
            result.setName(type.getName());
            return result;
        }).collect(Collectors.toList());
        groupVO.setGroupTypes(vos);
        groupVO.setGroupName(groupEntity.getName());
        groupVO.setCreateDept(groupEntity.getCrDept());
        groupVO.setCreateTime(groupEntity.getCrTime());
        if (WW_SUBJECT.equals(groupEntity.getSubjectId())) {
            CommonExtentEntity commonExtentEntity = commonExtendRepository.findByRecordIdAndModule(groupEntity.getId(), CommonExtentEntity.GROUP).orElse(new CommonExtentEntity());
            groupVO.setMainDemand(commonExtentEntity.getMainDemand());
        }
        return groupVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroup(String id, GroupVO groupVO) {
        GroupEntity groupEntity = groupRepository.findById(id).orElse(null);
        if (Objects.isNull(groupEntity)) {
            throw new NoSuchElementException("没有找到该群体！");
        }

        //生成操作记录
        final GroupVO oldGroup = getGroup(id);
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT)
                .module(OperateModule.GROUP_BASIC_INFO)
                .oldObj(JsonUtil.toJsonString(oldGroup))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(id)
                .desc("修改群体详情")
                .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                .build();

        //修改群体信息
        groupEntity.setName(groupVO.getGroupName());
        groupEntity.setBasicInfo(groupVO.getBasicInfo());
        groupRepository.save(groupEntity);

        //维稳专题特殊处理
        if (WW_SUBJECT.equals(groupEntity.getSubjectId())) {
            CommonExtentEntity commonExtentEntity = commonExtendRepository.findByRecordIdAndModule(id, CommonExtentEntity.GROUP).orElse(new CommonExtentEntity());
            commonExtentEntity.setModule(CommonExtentEntity.GROUP);
            commonExtentEntity.setRecordId(id);
            commonExtentEntity.setMainDemand(groupVO.getMainDemand());
            commonExtendRepository.save(commonExtentEntity);
        }
        //处理关联关系
        updateGroupTypeRelation(id, groupVO.getGroupTypes());

        //存储更新内容
        logRecord.setNewObj(JsonUtil.toJsonString(groupVO));
        if (Objects.nonNull(operationLogHandler)) {
            operationLogHandler.publishEvent(logRecord);
        }
    }


    /**
     * 关联群体与类别
     *
     * @param groupId 群体id
     * @param types   类型id列表
     */
    private void processGroupTypeRelation(String groupId, List<IdNameVO> types) {

        if (Objects.isNull(types) || types.isEmpty()) {
            return;
        }
        final List<GroupLabelRelationEntity> relations = types.stream()
                .filter(Objects::nonNull)
                .map(type -> {
                    GroupLabelRelationEntity relation = new GroupLabelRelationEntity();
                    relation.setGroupId(groupId);
                    relation.setLabelId(type.getId());
                    return relation;
                }).collect(Collectors.toList());
        groupLabelRelationRepository.saveAll(relations);
    }

    /**
     * 更新群体-类别关系
     *
     * @param groupId    群体id
     * @param groupTypes 群体类型
     */
    private void updateGroupTypeRelation(String groupId, List<IdNameVO> groupTypes) {
        groupLabelRelationRepository.deleteAllByGroupId(groupId);
        List<GroupLabelRelationEntity> relations = groupTypes.stream().map(type -> {
            GroupLabelRelationEntity relation = new GroupLabelRelationEntity();
            relation.setGroupId(groupId);
            relation.setLabelId(type.getId());
            return relation;
        }).collect(Collectors.toList());
        groupLabelRelationRepository.saveAll(relations);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupClueRelation(GroupClueRelationVO groupClueRelationVO) {
        final String groupId = groupClueRelationVO.getGroupId();
        final List<String> newRelatedClueIds = groupClueRelationVO.getClueIds();
        final List<String> oldRelatedClueIds = groupClueRelationRepository.findAllByGroupId(groupId)
                .stream().map(GroupClueRelationEntity::getClueId)
                .collect(Collectors.toList());

        //旧值和新值的并集
        List<String> allRelatedClueIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedClueIds, newRelatedClueIds);

        //处理群体-线索关联关系变更
        groupClueRelationRepository.deleteAllByGroupId(groupId);
        List<GroupClueRelationEntity> groupClueRelationEntities = newRelatedClueIds.stream()
                .map(clueId -> {
                    GroupClueRelationEntity groupClueRelationEntity = new GroupClueRelationEntity();
                    groupClueRelationEntity.setGroupId(groupId);
                    groupClueRelationEntity.setClueId(clueId);
                    return groupClueRelationEntity;
                }).collect(Collectors.toList());
        groupClueRelationRepository.saveAll(groupClueRelationEntities);

        //存储操作记录
        allRelatedClueIds.forEach(clueId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.GROUP_RELATED_CLUE)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(groupId)
                    .desc("修改群体-线索关联")
                    .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                    .build();

            if (oldRelatedClueIds.contains(clueId)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of(CLUE_PK, clueId)));
            }
            if (newRelatedClueIds.contains(clueId)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of(CLUE_PK, clueId)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });

    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteGroupClueRelation(String groupClueRelationId) {
        //记录群体线索关联关系
        GroupClueRelationEntity groupClue = groupClueRelationRepository.getById(groupClueRelationId);
        Map<String, String> relateInfo = new HashMap<>(0);
        relateInfo.put(CLUE_PK, groupClue.getClueId());

        //删除关联
        groupClueRelationRepository.deleteById(groupClueRelationId);

        //存储操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DE_RELATE)
                .module(OperateModule.GROUP_RELATED_CLUE)
                .oldObj(JsonUtil.toJsonString(relateInfo))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(groupClue.getGroupId())
                .desc("取消关联群体-线索")
                .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupPersonRelation(GroupPersonRelationVO vo) {
        final String groupId = vo.getGroupId();
        final List<String> newRelatedPersonIds = vo.getPersonIds();
        final List<String> oldRelatedPersonIds = personGroupRelationRepository.findAllByGroupId(groupId)
                .stream().map(PersonGroupRelationEntity::getPersonId)
                .collect(Collectors.toList());

        //旧值和新值的并集
        final List<String> allRelations = OperationLogServiceImpl.getModifiedRelations(oldRelatedPersonIds, newRelatedPersonIds);

        //存储操作记录
        allRelations.forEach(id -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.GROUP_MEMBER)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(groupId)
                    .desc("修改群体-人员关联")
                    .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                    .build();

            if (oldRelatedPersonIds.contains(id)) {
                Map<String, String> oldMap = new HashMap<>(0);
                oldMap.put(PERSON_PK, id);
                logRecord.setOldObj(JsonUtil.toJsonString(oldMap));
            }
            if (newRelatedPersonIds.contains(id)) {
                Map<String, String> newMap = new HashMap<>(0);
                newMap.put(PERSON_PK, id);
                logRecord.setNewObj(JsonUtil.toJsonString(newMap));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });

        //处理群体-人员关联关系变更
        newRelatedPersonIds.removeAll(oldRelatedPersonIds);
        final List<PersonGroupRelationEntity> personGroupRelationEntities = newRelatedPersonIds.stream()
            .map(personId -> new PersonGroupRelationEntity(personId, groupId))
            .collect(Collectors.toList());
        personGroupRelationRepository.saveAll(personGroupRelationEntities);
        oldRelatedPersonIds.removeAll(vo.getPersonIds());
        personGroupRelationRepository.deleteByIdIn(oldRelatedPersonIds);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void cancelGroupPersonRelation(String groupPersonRelationId) {
        //记录群体-人员关联信息
        Map<String, String> relateInfo = new HashMap<>(0);
        PersonGroupRelationEntity personGroup = personGroupRelationRepository.getById(groupPersonRelationId);
        relateInfo.put(PERSON_PK, personGroup.getPersonId());

        //删除关联
        personGroupRelationRepository.deleteById(groupPersonRelationId);

        //在群体档案存储操作记录
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.DE_RELATE)
                .module(OperateModule.GROUP_MEMBER)
                .oldObj(JsonUtil.toJsonString(relateInfo))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personGroup.getGroupId())
                .desc("取消关联群体-人员")
                .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }

        //在人员档案存储操作记录
        final OperationLogRecord logRecordPerson = OperationLogRecord.builder()
                .operator(Operator.DE_RELATE)
                .module(OperateModule.RELATE_TO_GROUP)
                .oldObj(JsonUtil.toJsonString(ImmutableMap.of(GROUP_PK, personGroup.getGroupId())))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(personGroup.getPersonId())
                .desc("取消关联人员-群体")
                .targetObjectType(OPERATION_LOG_TARGET_PERSON)
                .build();
        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecordPerson);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupPersonActivityLevel(ModuleEnum module, GroupPersonActivityLevelVO vo) {
        final String activityLevel = vo.getActivityLevel();
        final String relationId = vo.getRelationId();

        PersonGroupRelationEntity personGroupRelationEntity = personGroupRelationRepository.findById(relationId).orElse(null);
        if (Objects.isNull(personGroupRelationEntity)) {
            throw new ParamValidationException("人员-群体关联不存在！");
        }

        //存储操作记录
        Map<String, String> oldInfo = new HashMap<>(0);
        oldInfo.put("activityLevel", personGroupRelationEntity.getActivityLevel());
        final OperationLogRecord logRecord = OperationLogRecord.builder()
                .operator(Operator.EDIT_ACTIVE)
                .module(module.equals(ModuleEnum.PERSON) ? OperateModule.RELATE_TO_GROUP : OperateModule.GROUP_MEMBER)
                .oldObj(JsonUtil.toJsonString(oldInfo))
                .currentUser(AuthHelper.getCurrentUser())
                .primaryKey(module.equals(ModuleEnum.PERSON) ? personGroupRelationEntity.getPersonId() : personGroupRelationEntity.getGroupId())
                .desc("更改活跃程度")
                .targetObjectType(module.equals(ModuleEnum.PERSON) ? OPERATION_LOG_TARGET_PERSON : OPERATION_LOG_TARGET_GROUP)
                .build();

        //存储活跃度
        personGroupRelationEntity.setActivityLevel(activityLevel);
        personGroupRelationRepository.save(personGroupRelationEntity);

        Map<String, String> newInfo = new HashMap<>(0);
        newInfo.put("activityLevel", activityLevel);
        logRecord.setNewObj(JsonUtil.toJsonString(newInfo));

        if (Objects.nonNull(operationLogHandler)) {
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        }
    }

    @Override
    public PageResult<GroupRelatedClueVO> getGroupRelatedClue(String groupId, PageParams pageParams) {
        Page<ClueEntity> clueEntities = clueRepository.findAllByGroupId(groupId, pageParams.toPageable());
        List<GroupRelatedClueVO> items = clueEntities.stream()
                .map(clueEntity -> GroupRelatedClueVO.of(clueEntity, groupId))
                .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), clueEntities.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    public List<GroupRelatedClueVO> getGroupRelatedClue(String groupId) {
        List<ClueEntity> clueEntities = clueRepository.findAllByGroupId(groupId);
        return clueEntities.stream()
                .map(clueEntity -> GroupRelatedClueVO.of(clueEntity, groupId))
                .collect(Collectors.toList());
    }

    @Override
    public List<GroupRelatedPersonVO> getGroupRelatedPerson(String groupId) {
        List<PersonEntity> personEntities = personRepository.findAllByGroupId(groupId);
        GroupEntity groupEntity = groupRepository.getById(groupId);
        return personEntities.stream()
            .map(personEntity -> GroupRelatedPersonVO.of(personEntity, groupEntity))
            .collect(Collectors.toList());
    }

    @Override
    public PageResult<GroupRelatedPersonVO> getGroupRelatedPerson(String groupId, RequestParams requestParams) {
        final PageParams pageParams = requestParams.getPageParams();
        final SortParams sortParams = requestParams.getSortParams();
        Page<PersonGroupRelationEntity> result = findPageByGroupId(groupId, sortParams, pageParams);
        GroupEntity groupEntity = groupRepository.getById(groupId);
        List<GroupRelatedPersonVO> items = result.stream()
                .map(relation -> {
                    PersonEntity personEntity = personRepository.getById(relation.getPersonId());
                    return GroupRelatedPersonVO.of(personEntity, groupEntity);
                })
                .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), result.getTotalElements(), pageParams.getPageSize());
    }

    Page<PersonGroupRelationEntity> findPageByGroupId(String groupId, SortParams sortParams, PageParams pageParams) {
        // 组装查询条件
        Specification<PersonGroupRelationEntity> specification =
            (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("groupId"), groupId);

        //排序
        Sort sort = Objects.isNull(sortParams)
            ? Sort.by(Sort.Direction.DESC, "activityLevel")
            : sortParams.toSort();
        Pageable pageable = pageParams.toPageable(sort);

        return personGroupRelationRepository.findAll(specification, pageable);
    }

    @Override
    public List<IdNameVO> getGroupTypes(String subjectId) {
        //维稳群体和人员公用一套label
        String module = "group";
        if (subjectId.equals(WW_SUBJECT)) {
            module = "person";
        }
        return labelRepository.findAllBySubjectId(subjectId, module).stream().map(groupTypeEntity -> {
            IdNameVO vo = new IdNameVO();
            vo.setId(groupTypeEntity.getId());
            vo.setName(groupTypeEntity.getName());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<DialogGroupListVO> getDialogGroupListVO(DialogGroupListRequestVO request) {
        String subjectId = request.getOtherParams().getSubjectId();
        String clueTypeId = request.getOtherParams().getGroupTypeId();
        String createDeptId = request.getOtherParams().getCreateDeptId();
        String searchValue = Objects.isNull(request.getSearchParams()) ? null : request.getSearchParams().getSearchValue();
        Page<GroupEntity> groupEntities = groupRepository.findAllBySubjectId(subjectId,
                clueTypeId, StringUtil.getPoliceStationPrefix(createDeptId), searchValue, request.getPageParams().toPageable());
        List<DialogGroupListVO> items = groupEntities.stream()
                .map(DialogGroupListVO::of)
                .collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), groupEntities.getTotalElements(), request.getPageParams().getPageSize());
    }

    @Override
    public PageResult<GroupParentVO> getParentGroupList(String groupId, PageParams pageParams) {
        Page<GroupParentEntity> relations = groupParentRepository.findAllByGroupId(groupId, pageParams.toPageable());
        List<GroupParentVO> vos = relations.getContent().stream().map(this::of).collect(Collectors.toList());
        return PageResult.of(vos, pageParams.getPageNumber(), relations.getTotalElements(), pageParams.getPageSize());
    }

    private GroupParentVO of(GroupParentEntity relation) {
        GroupParentVO vo = new GroupParentVO();
        GroupEntity groupEntity = groupRepository.findById(relation.getParentId()).orElse(null);
        if (Objects.nonNull(groupEntity)) {
            vo.setGroupId(groupEntity.getId());
            vo.setGroupName(groupEntity.getName());
            vo.setMemberCount(personGroupRelationRepository.countAllByGroupId(groupEntity.getId()));
            List<LabelEntity> groupTypes = labelRepository.findByGroupIdAndSubjectId(groupEntity.getId(), groupEntity.getSubjectId());
            vo.setGroupType(groupTypes.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));
        }
        vo.setRelationId(relation.getId());
        return vo;
    }

    @Override
    public List<GroupParentVO> getParentGroups(String groupId) {
        return groupParentRepository.findAllByGroupId(groupId).stream().map(this::of).collect(Collectors.toList());
    }

    @Override
    public List<GroupRelatedEventVO> getGroupRelatedEvent(String groupId) {
        List<EventEntity> events = eventRepository.findAllByGroupId(groupId);
        return events.stream()
                .map(event -> new GroupRelatedEventVO(event, groupId))
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<GroupRelatedEventVO> getGroupRelatedEvent(String groupId, PageParams pageParams) {
        Page<EventEntity> eventEntities = eventRepository.findAllByGroupId(groupId, pageParams.toPageable());
        List<GroupRelatedEventVO> items = eventEntities.stream()
                .map(event -> new GroupRelatedEventVO(event, groupId))
                .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), eventEntities.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupEventRelations(GroupEventsRelationVO vo) {
        final String groupId = vo.getGroupId();
        final List<String> newRelatedEventIds = vo.getEventIds();
        final List<String> oldRelatedEventIds = eventGroupRelationRepository.findAllByGroupId(groupId)
                .stream().map(EventGroupRelationEntity::getEventId)
                .collect(Collectors.toList());

        //旧值和新值的并集
        List<String> allRelations = OperationLogServiceImpl.getModifiedRelations(oldRelatedEventIds, newRelatedEventIds);

        //处理群体-人员关联关系变更
        eventGroupRelationRepository.deleteAllByGroupId(groupId);
        final List<EventGroupRelationEntity> eventGroupRelationEntities = newRelatedEventIds.stream()
                .map(eventId -> new EventGroupRelationEntity(eventId, groupId))
                .collect(Collectors.toList());
        eventGroupRelationRepository.saveAll(eventGroupRelationEntities);

        //存储操作记录
        allRelations.forEach(id -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.GROUP_RELATED_EVENT)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(groupId)
                    .desc("修改群体-事件关联")
                    .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                    .build();

            if (oldRelatedEventIds.contains(id)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of(EVENT_PK, id)));
            }
            if (newRelatedEventIds.contains(id)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of(EVENT_PK, id)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupParentRelation(String groupId, List<String> parentIds) {
        List<String> oldParentIds = groupParentRepository.findAllByGroupId(groupId)
                .stream().map(GroupParentEntity::getGroupId)
                .collect(Collectors.toList());

        //旧值和新值的并集
        List<String> allRelatedGroupIds = OperationLogServiceImpl.getModifiedRelations(oldParentIds, parentIds);

        //处理线索-群体关联关系变更
        groupParentRepository.deleteAllByGroupId(groupId);
        parentIds.forEach(parentId -> {
            //判断是否已经存在关联关系
            GroupParentEntity parentEntity = new GroupParentEntity();
            parentEntity.setGroupId(groupId);
            parentEntity.setParentId(parentId);
            groupParentRepository.save(parentEntity);
        });

        //存储操作记录
        allRelatedGroupIds.forEach(id -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.GROUP_PARENT)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(groupId)
                    .desc("修改群体-上级群体关联")
                    .targetObjectType(OPERATION_LOG_TARGET_CLUE)
                    .build();
            if (oldParentIds.contains(id)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of(GROUP_PK, id)));
            }
            if (parentIds.contains(id)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of(GROUP_PK, id)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteGroupParentRelation(String relationId) {
        groupParentRepository.findById(relationId)
                .ifPresent(gp -> {
                    groupParentRepository.deleteById(relationId);

                    Optional.ofNullable(operationLogHandler)
                            .ifPresent(handler -> {
                                //存储操作记录
                                final OperationLogRecord logRecord = OperationLogRecord.builder()
                                        .operator(Operator.DE_RELATE)
                                        .module(OperateModule.GROUP_PARENT)
                                        .oldObj(JsonUtil.toJsonString(gp))
                                        .currentUser(AuthHelper.getCurrentUser())
                                        .primaryKey(gp.getGroupId())
                                        .desc("取消关联上级群体")
                                        .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                                        .build();
                                // 记录操作
                                operationLogHandler.publishEvent(logRecord);
                            });
                });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteGroupEventRelation(String relationId) {
        eventGroupRelationRepository.findById(relationId)
                .ifPresent(r -> {
                    eventGroupRelationRepository.deleteById(relationId);

                    // 操作成功后记录日志
                    Optional.ofNullable(operationLogHandler)
                            .ifPresent(handler -> {
                                //存储操作记录
                                final OperationLogRecord logRecord = OperationLogRecord.builder()
                                        .operator(Operator.DE_RELATE)
                                        .module(OperateModule.GROUP_RELATED_EVENT)
                                        .oldObj(JsonUtil.toJsonString(r))
                                        .currentUser(AuthHelper.getCurrentUser())
                                        .primaryKey(r.getGroupId())
                                        .desc("取消群体-事件关联")
                                        .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                                        .build();
                                // 记录操作
                                operationLogHandler.publishEvent(logRecord);
                            });
                });
    }

    @Override
    public PageResult<GroupRelatedTimeVO> getSensitiveList(String groupId, PageParams pageParams) {
        Page<SensitiveTimeEntity> sensitiveTimeEntity = sensitiveTimeRepository.findAllByGroupId(groupId, pageParams.toPageable());
        List<GroupRelatedTimeVO> collect = sensitiveTimeEntity.getContent().stream().map(entity -> GroupRelatedTimeVO.of(entity, groupId)).collect(Collectors.toList());
        return PageResult.of(collect, pageParams.getPageNumber(), sensitiveTimeEntity.getTotalElements(), pageParams.getPageSize());
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void updateGroupTimeRelations(GroupTimeRelationRequestVO vo) {
        final String groupId = vo.getGroupId();
        final List<String> newRelatedIds = vo.getNodeIds();
        final List<String> oldRelatedIds = groupTimeRelationRepository.findAllByGroupId(groupId)
                .stream().map(GroupTimeRelationEntity::getSensitiveTimeId)
                .collect(Collectors.toList());

        groupTimeRelationRepository.deleteAllByGroupId(vo.getGroupId());
        List<GroupTimeRelationEntity> collect = vo.getNodeIds().stream().map(nodeId -> {
            GroupTimeRelationEntity relation = new GroupTimeRelationEntity();
            relation.setGroupId(vo.getGroupId());
            relation.setSensitiveTimeId(nodeId);
            return relation;
        }).collect(Collectors.toList());
        groupTimeRelationRepository.saveAll(collect);

        //旧值和新值的并集
        List<String> relatedIds = OperationLogServiceImpl.getModifiedRelations(oldRelatedIds, newRelatedIds);

        //存储操作记录
        relatedIds.forEach(id -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.RELATE)
                    .module(OperateModule.GROUP_SENSITIVE_TIME)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(groupId)
                    .desc("修改群体-敏感时间节点关联")
                    .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                    .build();

            if (oldRelatedIds.contains(id)) {
                logRecord.setOldObj(JsonUtil.toJsonString(ImmutableMap.of("sensitiveTimeId", id)));
            }
            if (newRelatedIds.contains(id)) {
                logRecord.setNewObj(JsonUtil.toJsonString(ImmutableMap.of("sensitiveTimeId", id)));
            }
            // 记录操作
            operationLogHandler.publishEvent(logRecord);
        });
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteGroupTimeRelation(String relationId) {
        groupTimeRelationRepository.findById(relationId)
                .ifPresent(groupTimeRelationEntity -> sensitiveTimeRepository.findById(groupTimeRelationEntity.getSensitiveTimeId())
                        .ifPresent(sensitiveTimeEntity -> {
                            if (SensitiveTimeEntity.SCOPE_ALL == sensitiveTimeEntity.getNodeType()) {
                                throw new InteractException("当前敏感时间节点为默认有效，关联关系不能被移除！");
                            }
                            groupTimeRelationRepository.deleteById(relationId);

                            // 操作成功后记录日志
                            Optional.ofNullable(operationLogHandler)
                                    .ifPresent(handler -> {
                                        //存储操作记录
                                        final OperationLogRecord logRecord = OperationLogRecord.builder()
                                                .operator(Operator.DE_RELATE)
                                                .module(OperateModule.GROUP_SENSITIVE_TIME)
                                                .oldObj(JsonUtil.toJsonString(ImmutableMap.of("sensitiveTimeId", sensitiveTimeEntity.getId())))
                                                .currentUser(AuthHelper.getCurrentUser())
                                                .primaryKey(groupTimeRelationEntity.getGroupId())
                                                .desc("取消群体-敏感时间节点关联")
                                                .targetObjectType(OPERATION_LOG_TARGET_GROUP)
                                                .build();
                                        // 记录操作
                                        operationLogHandler.publishEvent(logRecord);
                                    });
                        }));
    }

    @Override
    public List<GroupRelatedTimeVO> getGroupRelatedTime(String groupId) {
        return sensitiveTimeRepository.findAllByGroupId(groupId).stream().map(entity -> GroupRelatedTimeVO.of(entity, groupId)).collect(Collectors.toList());
    }
}
