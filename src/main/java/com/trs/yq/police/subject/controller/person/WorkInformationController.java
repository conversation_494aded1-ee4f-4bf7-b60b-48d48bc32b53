package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.WorkInformationVO;
import com.trs.yq.police.subject.service.WorkInformationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 工作信息类接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/person")
@Slf4j
public class WorkInformationController {

    @Resource
    private WorkInformationService workInformationService;

    /**
     * 获取人员工作信息d
     *
     * @param personId 人员id
     * @return 工作信息 {@link WorkInformationVO}
     * <AUTHOR>
     */
    @GetMapping("/{personId}/job")
    public List<WorkInformationVO> getAll(@PathVariable String personId) {
        return workInformationService.getAllByPersonId(personId);
    }

    /**
     * 删除人员工作信息
     *
     * @param personId   人员id
     * @param workInfoId 工作信息id
     * <AUTHOR>
     */
    @DeleteMapping("/{personId}/job")
    public void deleteOne(@PathVariable String personId, @NotBlank(message = "工作信息主键缺失") String workInfoId) {
        workInformationService.deleteOne(personId, workInfoId);
    }

    /**
     * 增加人员的工作信息
     *
     * @param personId          人员id
     * @param workInformationVO 人员工作信息
     * <AUTHOR>
     */
    @PostMapping("/{personId}/job")
    public void addOne(@PathVariable String personId, @Valid @RequestBody WorkInformationVO workInformationVO) {
        workInformationService.addOne(personId, workInformationVO);
    }

    /**
     * 更新人员工作信息
     *
     * @param personId          人员id
     * @param workInformationVO 人员工作信息
     * <AUTHOR>
     */
    @PutMapping("/{personId}/job")
    public void updateOne(@PathVariable String personId, @Valid @RequestBody WorkInformationVO workInformationVO) {
        workInformationService.updateOne(personId, workInformationVO);
    }
}
