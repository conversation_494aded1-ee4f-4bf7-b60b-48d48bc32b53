package com.trs.yq.police.subject.mysqlDatasource.repository;

import com.trs.yq.police.subject.mysqlDatasource.entity.CrjJwrySourceEntity;
import com.trs.yq.police.subject.repository.BaseRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> yanghy
 * @date : 2022/11/29 11:34
 */
public interface CrjJwrySourceRepository extends BaseRepository<CrjJwrySourceEntity,String> {

    /**
     * 获取未处理消息
     *
     * @param recordIds id
     * @return {@link CrjJwrySourceEntity}
     */
    @Query("select t from CrjJwrySourceEntity t where t.hotelApplyId not in (:recordIds)")
    List<CrjJwrySourceEntity> findAllNotSync(@Param("recordIds")List<String> recordIds);

    /**
     * 获取信息
     *
     * @param idType   证件类型
     * @param idNumber 证件号码
     * @param secretKey aes密钥
     * @return {@link CrjJwrySourceEntity}
     */
    @Query(nativeQuery = true,value = "SELECT * FROM lz_yq_info t  "
        + "WHERE "
        + "t.zjzl =:idType "
        + "AND aes_decrypt(from_base64(t.zjhm),:secretKey) =:idNumber  "
        + "ORDER BY "
        + "t.create_time DESC  "
        + "LIMIT 1")
    Optional<CrjJwrySourceEntity> findFirstByZjzlAndZjhm(@Param("idType") String idType,
        @Param("idNumber") String idNumber,@Param("secretKey")String secretKey);
}
