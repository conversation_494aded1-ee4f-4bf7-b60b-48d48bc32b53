package com.trs.yq.police.subject.domain.entity;

import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 话单分析
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_PERSON_CALL")
public class PersonCallEntity extends BaseEntity {

    private static final long serialVersionUID = 7922327160608683572L;

    private String personId;

    private String topologyGraph;

    private String callFile;

    private String callTimeFeature;

    private String callPositionTrack;

    private String attachments;

    private String remark;
}
