package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.EventEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 事件数据层
 *
 * <AUTHOR>
 * @date 2021/12/13 16:44
 */
@Repository
public interface EventRepository extends BaseRepository<EventEntity, String> {

    /**
     * 查询所有线索
     *
     * @param eventIds 线索id
     * @return {@link EventEntity}
     */
    List<EventEntity> findAllByIdIn(List<String> eventIds);

    /**
     * 更新事件上报状态
     *
     * @param eventId      事件id
     * @param reportStatus 上报状态
     */
    @Modifying
    @Query("update EventEntity e set e.reportStatus=:reportStatus where e.id=:eventId")
    void updateReportStatusByEventId(String eventId, String reportStatus);

    /**
     * 根据人员id和专题id查询事件列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param pageable  {@link Pageable}
     * @return {@link EventEntity}
     */
    @Query("SELECT t2 FROM EventPersonRelationEntity t1 JOIN EventEntity t2 ON t1.eventId=t2.id WHERE t1.personId=:personId AND t2.subjectId=:subjectId order by t1.upTime")
    Page<EventEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId, Pageable pageable);


    /**
     * 根据人员id和专题id查询事件列表
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return {@link EventEntity}
     */
    @Query("SELECT t2 FROM EventPersonRelationEntity t1 JOIN EventEntity t2 ON t1.eventId=t2.id WHERE t1.personId=:personId AND t2.subjectId=:subjectId")
    List<EventEntity> findAllByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId);

    /**
     * 根据专题id查询所有事件实体
     *
     * @param subjectId   专题id
     * @param eventTypeId 线索类别id
     * @param controlDept 创建单位id
     * @param searchValue 检索文字
     * @param pageable    {@link org.springframework.data.domain.Pageable}
     * @return {@link ClueEntity}
     */
    @Query("SELECT t1 " +
            "FROM EventEntity t1 " +
            "WHERE t1.subjectId=:subjectId " +
            "AND (:eventTypeId is null or exists (SELECT t2 FROM EventLabelRelationEntity t2 WHERE t2.labelId=:eventTypeId AND t2.eventId=t1.id)) " +
            "AND (:searchValue is null or t1.title like concat('%',:searchValue,'%')) " +
            "AND (:controlDept is null or t1.controlDept like concat(:controlDept,'%') ) " +
            "ORDER BY t1.upTime DESC")
    Page<EventEntity> findAllBySubjectId(@Param("subjectId") String subjectId,
                                         @Param("eventTypeId") String eventTypeId,
                                         @Param("controlDept") String controlDept,
                                         @Param("searchValue") String searchValue,
                                         Pageable pageable);

    /**
     * 根据群体id查询事件
     *
     * @param groupId 群体id
     * @return {@link EventEntity}
     */
    @Query("SELECT t2 FROM EventGroupRelationEntity t1 JOIN EventEntity t2 ON t1.eventId=t2.id WHERE t1.groupId=:groupId")
    List<EventEntity> findAllByGroupId(String groupId);

    /**
     * 根据群体id查询事件（分页）
     *
     * @param groupId  群体id
     * @param pageable {@link Pageable}
     * @return {@link EventEntity}
     */
    @Query("SELECT t2 FROM EventGroupRelationEntity t1 JOIN EventEntity t2 ON t1.eventId=t2.id WHERE t1.groupId=:groupId")
    Page<EventEntity> findAllByGroupId(String groupId, Pageable pageable);

    /**
     * 获取反馈指令数量
     *
     * @param eventId 事件id
     * @return 数量
     */
    @Query(nativeQuery = true,value = "select COUNT(1) from T_BATTLE_COMMAND_REPLY c where c.COMMANDID in :eventId")
    int findCommandReply(@Param("eventId") String eventId);

    /**
     * 查询已发指令的未处置线索
     *
     * @return 线索id列表
     */
    @Modifying
    @Query(value = "select e.ID from T_EVENT e " +
            "where e.DISPOSAL_STATUS = '0' " +
            "and exists(select 1 from T_BATTLE_EVENT be, T_BATTLE_COMMAND bc " +
            "    where be.SRCTABLE='system.t_event' " +
            "    and be.KEYVAL = e.ID " +
            "    and INSTR(bc.EVENTIDS, be.ID)> 0" +
            ")", nativeQuery = true)
    List<String> selectDisposalStatusByCommand();

    /**
     * 查询已发合成的未处置线索
     *
     * @return 线索id列表
     */
    @Modifying
    @Query(value = "select e.ID from T_EVENT e " +
            "where e.DISPOSAL_STATUS = '0' " +
            "and exists(select 1 from T_BATTLE_EVENT be, T_BATTLE_RECORD br " +
            "    where be.SRCTABLE='system.t_event' " +
            "    and be.KEYVAL = e.ID " +
            "    and INSTR(br.EVENTIDS, be.ID)> 0" +
            ")", nativeQuery = true)
    List<String> selectDisposalStatusByRecord();

    /**
     * 更新处置状态
     *
     * @param eventId 事件id
     * @param status 处置状态
     */
    @Modifying
    @Query("update EventEntity e set e.disposalStatus = :status where e.id = :eventId")
    void updateDisposalStatusByEventId(@Param("eventId") String eventId, @Param("status") String status);
}
