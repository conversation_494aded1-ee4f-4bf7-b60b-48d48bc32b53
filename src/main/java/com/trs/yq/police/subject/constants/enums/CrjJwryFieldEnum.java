package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 出入境
 *
 * <AUTHOR>
 * @date 2021/7/31 17:18
 */
public enum CrjJwryFieldEnum {

    /*
     * 基础属性 enums
     */
    IDNUMBER("idNumber", "证件号码"),
    ID_TYPE("idType", "证件种类"),
    GJDM("gjdm", "国家/地区"),

    EN_NAME("enName", "英文姓名"),
    CN_Name("cnName", "中文姓名"),
    GENDER("gender", "性别"),
    BIRTHDAY("birthday", "出生日期"),
    JDDW("jddw", "接待单位"),
    LIVE_STATUS("liveStatus","居住状态"),
    LKRQ("lkrq", "离开日期"),
    CREATE_TIME("createTime", "录入时间"),

    VISA_TYPE("visaType", "签证(注)种类"),
    VISA_NUMBER("visaNumber", "签证(注)号码"),
    IN_CHINA_TIME("inChinaTime", "签证(注)停留有效期至"),

    EN_FIRST_NAME("enFirstName", "英文名"),
    EN_LAST_NAME("enLastName", "英文姓"),
    ZSRQ("zsrq","住宿日期"),

    PLAN_LEAVE_TIME("planLeaveTime", "拟离开日期"),
    PGIS("pgis","pgis地址"),
    JZLX("JZLX","居住类型"),
    LXHZ("lxhz","留宿单位(户主)"),
    LXDW("lsdw","留宿单位(户主)电话"),
    POLICE_STATION("policeStation","派出所名称"),
    ENTRY_TIME("entryTime", "入境日期"),
    ENTRY_PORT("entryPort", "入境口岸"),
    TJLSY("tjlsy","停居留事由"),
    SFZ("sfz","身份证"),
    DWDM("dwdm","临时住宿登记单位代码"),
    CSSJ("cssj","传送时间"),
    CSWJM("cswjm","传送文件名"),
    FH("fh","房号"),
    LRR("lrr","录入人"),
    RKSJ("rksj","入库时间"),
    RYBH("rybh","人员编号"),
    RYDYLB("rydylb","人员地域类别"),
    DATA_SOURCE("dataSource","数据来源"),
    TBDWBH("tbdwbh","填表单位编号"),
    ZHXGR("zhxgr","最后修改人"),
    SSBZ("ssbz","上送标志"),
    ZZBZ("zzbz","注销标志"),
    ZDJWRYLB("zdjwrylb","重点境外人员类别"),
    BZ("bz","备注"),
    CRJXXHCBZ("crjxxhcbz","出入境信息核查标志"),
    HLC("hlc","何处来"),
    HCQ("hcq","何处去"),
    HCZT("hczt","核查状态"),
    JSBZ("jsbz","接收标志"),
    JWRYGX("jwrygx","境外人员关系"),
    LSDW("lsdw","留宿单位(户主)地址"),
    LIVE_ADDRESS("liveAddress", "临时住宿登记单名称"),
    LXZSDJDWXZQH("lxzsdjdwxzqh","临时住宿登记单位行政区划"),
    LZJWRYYWBH("lzjwryywbh","临住境外人员业务编号"),
    QFJG("qfjg","签发机关"),
    SCBZ("scbz","删除标志"),
    SFCJ("sfcj","是否出境"),
    SFCS("sfcs","是否出省"),
    SFZDJWRY("sfzdjwry","是否重点境外人员"),

    ;


    /**
     * 属性名
     */
    @Getter
    private final String field;

    /**
     * 中文名
     */
    @Getter
    private final String cnName;


    /**
     * 私有构造方法
     *
     * @param field  属性名
     * @param cnName 中文名
     */
    CrjJwryFieldEnum(String field, String cnName) {
        this.field = field;
        this.cnName = cnName;
    }

    /**
     * 通过中文转换
     *
     * @param cnName 中文名
     * @return 人员信息表结构枚举
     */
    public static CrjJwryFieldEnum cnNameOf(String cnName) {

        if (StringUtils.isNotBlank(cnName)) {
            cnName = cnName.replace("（必填）", "");
            for (CrjJwryFieldEnum person : CrjJwryFieldEnum.values()) {
                if (StringUtils.equals(person.cnName, cnName)) {
                    return person;
                }
            }
        }
        return null;
    }

    /**
     * 英文属性名转换表结构枚举
     *
     * @param field 英文属性名
     * @return 人员信息表结构枚举
     */
    public static CrjJwryFieldEnum fieldOf(String field) {

        if (StringUtils.isNotBlank(field)) {
            for (CrjJwryFieldEnum fieldEnum : CrjJwryFieldEnum.values()) {
                if (StringUtils.equals(fieldEnum.field, field)) {
                    return fieldEnum;
                }
            }
        }
        return null;
    }
}
