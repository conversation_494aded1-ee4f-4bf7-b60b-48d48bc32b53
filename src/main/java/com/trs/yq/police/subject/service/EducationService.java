package com.trs.yq.police.subject.service;

import com.trs.yq.police.subject.domain.vo.EducationVO;

import java.util.List;

/**
 * 教育信息业务层接口
 *
 * <AUTHOR>
 */
public interface EducationService {
    /**
     * 查询人员教育信息
     *
     * @param personId 人员id
     * @return 人员基本信息 {@link EducationVO}
     * <AUTHOR>
     */
    List<EducationVO> findAllEducation(String personId);

    /**
     * 删除该人员一条教育信息
     *
     * @param personId    人员主键
     * @param educationId 教育信息id
     * <AUTHOR>
     */
    void deleteEducation(String personId, String educationId);

    /**
     * 增加该人员一条教育信息
     *
     * @param personId 人员id
     * @param entity   教育信息
     * <AUTHOR>
     */
    void addEducation(String personId, EducationVO entity);

    /**
     * 修改该人员一条教育信息
     *
     * @param personId 人员id
     * @param entity   教育信息
     * <AUTHOR>
     */
    void updateEducation(String personId, EducationVO entity);
}
