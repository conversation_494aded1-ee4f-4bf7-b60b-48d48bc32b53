package com.trs.yq.police.subject.domain.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * 区县人员类型分布
 *
 * <AUTHOR>
 * @date 2021/9/10 13:12
 */
@Getter
@Setter
@ToString
public class AreaDistributeVO implements Serializable {

    private static final long serialVersionUID = -9068649530683931515L;

    /**
     * 区县编码
     */
    private String areaCode;

    /**
     * 区县名称
     */
    private String areaName;

    /**
     * 人员类型分布
     */
    private List<CountTypeResponseVO> countPersonType;
}
