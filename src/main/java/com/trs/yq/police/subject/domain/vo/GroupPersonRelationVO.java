package com.trs.yq.police.subject.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/09/07
 */
@Data
public class GroupPersonRelationVO implements Serializable {

    private static final long serialVersionUID = 8288800402814324128L;
    
    /**
     * 群体Id
     */
    private String groupId;

    /**
     * 人员Id
     */
    @NotNull(message = "personIds不能为空！")
    private List<String> personIds;
}
