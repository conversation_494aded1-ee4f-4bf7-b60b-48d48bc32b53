package com.trs.yq.police.subject.validation;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static java.lang.annotation.RetentionPolicy.RUNTIME;


/**
 * 枚举验证
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = Enum.EnumValidator.class)
public @interface Enum {

    /**
     * message
     *
     * @return {enum.invalid}
     */
    String message() default "{enum.invalid}";

    /**
     * groups
     *
     * @return {}
     */
    Class<?>[] groups() default {};

    /**
     * 允许的值 为空将允许所有值
     *
     * @return 允许值
     */
    String[] allowValues() default {};

    /**
     * 是否忽略大小写
     *
     * @return 是否忽略大小写
     */
    boolean ignoreCase() default false;

    /**
     * payload
     *
     * @return {}
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * IdentityValidator
     */
    class EnumValidator implements ConstraintValidator<Enum, String> {
        private Set<String> allowValueSet;
        private boolean ignoreCase = false;

        @Override
        public void initialize(Enum constraintAnnotation) {

            ignoreCase = constraintAnnotation.ignoreCase();
            if (ignoreCase) {
                allowValueSet =
                        Arrays.stream(constraintAnnotation.allowValues()).map(String::toUpperCase).collect(Collectors.toSet());

            } else {
                allowValueSet =
                        Arrays.stream(constraintAnnotation.allowValues()).collect(Collectors.toSet());
            }


        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {

            if (ignoreCase) {
                return allowValueSet.contains(value.toUpperCase());
            } else {
                return allowValueSet.contains(value);
            }
        }
    }


}
