package com.trs.yq.police.subject.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.builder.WarningListPredicatesBuilder;
import com.trs.yq.police.subject.constants.enums.DisplayTypeEnum;
import com.trs.yq.police.subject.constants.enums.WarningStatusEnum;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import com.trs.yq.police.subject.domain.params.TimeParams;
import com.trs.yq.police.subject.domain.vo.*;
import com.trs.yq.police.subject.domain.vo.CarTrajectoryVO.TimeTrajectoryVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.properties.WarningProperties;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.WarningService;
import com.trs.yq.police.subject.task.DriveNoLicenseAnalysisTask;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_FORMATTER;
import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.enums.WarningTypeEnum.WW_ZDRYFS;
import static com.trs.yq.police.subject.constants.enums.WarningTypeEnum.WW_ZDRYJJ;

/**
 * 预警服务层实现
 *
 * <AUTHOR>
 * @since 2021/9/1
 */
@Service
@Slf4j
public class WarningServiceImpl implements WarningService {

    @Resource
    private WarningRepository warningRepository;

    @Resource
    private PersonRepository personRepository;

    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;


    @Resource
    private WarningTypeRepository warningTypeRepository;

    @Resource
    private LabelRepository labelRepository;

    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;

    @Resource
    private WarningCallRepository warningCallRepository;

    @Resource
    private RoleDataRepository roleDataRepository;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WarningProperties warningProperties;

    @Resource
    private WarningCarJudgeRepository warningCarJudgeRepository;

    @Resource
    private CrjCzryRepository crjCzryRepository;

    @Resource
    private AlarmYjgjRepository yjgjRepository;

    @Resource
    private DriveNoLicenseAnalysisTask task;

    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 根据预警类型获取显示类型
     *
     * @param warningType 预警类型
     * @return 显示类型
     */
    @Override
    public DisplayTypeEnum getDisplayTypeByWarningType(String warningType) {
        WarningTypeEntity type = warningTypeRepository.findById(warningType).orElse(null);
        if (Objects.isNull(type)) {
            throw new ParamValidationException("该预警类型不存在，请核实！");
        }
        DisplayTypeEnum displayType = DisplayTypeEnum.codeOf(type.getDisplayType());
        if (Objects.isNull(displayType)) {
            throw new ParamValidationException("该预警显示类型不存在，请核实！");
        }
        return displayType;
    }

    @Override
    public PageResult<WarningListVO> getWarningList(String subjectId, ListRequestVO request) {
        LoginUser user = AuthHelper.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new ParamValidationException("用户不存在！");
        }
        List<RoleDataEntity> roleData = roleDataRepository.findViewRangesByUserId(user.getId());
        List<String> viewRanges = roleData.stream().map(RoleDataEntity::getViewRange).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> operationRanges = roleData.stream().map(RoleDataEntity::getOperationRange).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> warningTypes = roleData.stream().map(RoleDataEntity::getWarningType).filter(Objects::nonNull)
                .collect(Collectors.toList());

        Specification<WarningEntity> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = WarningListPredicatesBuilder.buildListFilterPredicates(viewRanges, subjectId,
                            request.getFilterParams(), root, criteriaBuilder).stream()
                    .filter(Objects::nonNull).collect(Collectors.toList());
            if (Objects.nonNull(request.getSearchParams())) {
                predicates.addAll(WarningListPredicatesBuilder.buildSearchPredicates(request.getSearchParams(), root,
                        criteriaBuilder));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        //排序
        Sort sort = getSort(request.getSortParams());
        final Pageable pageable = request.getPageParams().toPageable(sort);

        Page<WarningEntity> warningList = warningRepository.findAll(specification, pageable);
        List<WarningListVO> items = warningList.getContent().stream()
                .map(item -> entity2VO(item, operationRanges, warningTypes)).collect(Collectors.toList());
        return PageResult.of(items, request.getPageParams().getPageNumber(), warningList.getTotalElements(),
                request.getPageParams().getPageSize());
    }

    /**
     * 预警entity转化为列表vo
     *
     * @param warning {@link WarningEntity}
     * @return {@link WarningListVO}
     */
    private WarningListVO entity2VO(WarningEntity warning, List<String> operationRanges, List<String> warningTypes) {
        WarningListVO vo = WarningListVO.of(warning);
        final DisplayTypeEnum displayType = getDisplayTypeByWarningType(warning.getWarningType());
        final String subjectId = warning.getSubjectId();
        switch (displayType) {
            case SINGLE_PERSON: {
                PersonEntity person = getPersonEntity(warning);
                if (Objects.nonNull(person)) {
                    vo.setName(person.getName());
                    vo.setIdNumber(person.getIdNumber());
                    //人员类别
                    List<LabelEntity> types = labelRepository.findAllByPersonIdAndSubjectId(person.getId(), subjectId);
                    if (!labelRepository.findAllBySubjectId(subjectId, "person").isEmpty()) {
                        vo.setPersonType(types.stream().map(LabelEntity::getName).collect(Collectors.joining("、")));
                    }
                }
                break;
            }
            case MULTI_PERSON: {
                List<WarningTrajectoryEntity> trajectories = warningTrajectoryRepository.findTraceListByWarningId(
                        warning.getId());
                List<String> names = new ArrayList<>();
                List<String> idNumbers = new ArrayList<>();
                for (WarningTrajectoryEntity trajectory : trajectories) {
                    PersonEntity person = warningTrajectoryRepository.findPersonByTrajectoryId(trajectory.getId());
                    if (Objects.nonNull(person)) {
                        names.add(person.getName());
                        idNumbers.add(person.getIdNumber());
                    }
                }
                vo.setName(String.join(",", names));
                vo.setIdNumber(String.join(",", idNumbers));
                break;
            }
            case CAR_DETAIL_RECORD:
                List<WarningTrajectoryEntity> allByWarningId = warningTrajectoryRepository.findAllByWarningId(
                        warning.getId());
                if (!allByWarningId.isEmpty()) {
                    vo.setIdNumber(allByWarningId.get(0).getIdValue());
                }
                break;
            case CRJ:
                WarningTrajectoryEntity track = warningTrajectoryRepository.findTraceByWarningId(warning.getId());
                String yjgjId = track.getRawData();
                yjgjRepository.findById(yjgjId).ifPresent(yjgj -> {
                    vo.setIdNumber(yjgj.getGlsfzh());
                    vo.setName(yjgj.getGlxm());
                });
                break;
            default:
        }
        if (displayType.equals(DisplayTypeEnum.NO_TRACK) && subjectId.equals("8")) {
            String name = crjCzryRepository.selectNameFromCrjByCertificateNumber(warning.getWarningKey());
            vo.setName(name);
            vo.setIdNumber(warning.getWarningKey());
        }
        vo.setCanOperate(checkCanOperate(warning, operationRanges, warningTypes));
        return vo;
    }

    private Boolean checkCanOperate(WarningEntity entity, List<String> operationRanges, List<String> warningTypes) {
        return (operationRanges.contains("5105") || operationRanges.contains(entity.getAreaCode()))
                && (warningTypes.contains(entity.getWarningType()));
    }

    @Override
    public List<StabilityWarningListVO> getStabilityWarningList(String status) {
        List<WarningEntity> warningList = warningRepository.findAllByWarningTypeInAndStatus(
                Arrays.asList(WW_ZDRYJJ.getCode(), WW_ZDRYFS.getCode()), status);
        return warningList.stream().map(warning -> {
            StabilityWarningListVO vo = new StabilityWarningListVO();
            vo.setWarningId(warning.getId());
            vo.setWarningContent(warning.getWarningDetails());
            vo.setWarningType(warning.getWarningType());
            vo.setWarningTime(warning.getWarningTime());
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public StabilityWarningCountVO getStabilityWarningCount() {
        List<WarningEntity> unsigned = warningRepository.findAllByWarningTypeInAndStatus(
                Arrays.asList(WW_ZDRYJJ.getCode(), WW_ZDRYFS.getCode()), WarningStatusEnum.WAIT_SIGN.getCode());
        List<WarningEntity> signed = warningRepository.findAllByWarningTypeInAndStatus(
                Arrays.asList(WW_ZDRYJJ.getCode(), WW_ZDRYFS.getCode()), WarningStatusEnum.SIGN_TO_JUDGE.getCode());
        return new StabilityWarningCountVO(unsigned.size(), signed.size());
    }

    @Override
    public PageResult<WarningListVO> getPersonWarningList(PageParams pageParams, String subjectId, String personId) {
        Page<WarningEntity> queryResult = warningRepository.findAllByPersonIdAndSubjectId(personId, subjectId,
                pageParams.toPageable());
        List<WarningListVO> items = queryResult.getContent().stream().map(WarningListVO::of)
                .collect(Collectors.toList());
        return PageResult.of(items, pageParams.getPageNumber(), queryResult.getTotalElements(),
                pageParams.getPageSize());
    }

    @Override
    public String getEventUrl(String warningId) {
        return warningRepository.getById(warningId).getEventUrl();
    }

    @Override
    public List<WarningCarTrajectoryVO> getCarTrajectory(String warningId) {
        WarningTrajectoryEntity warningTrajectoryEntity = warningTrajectoryRepository.findAllByWarningId(warningId)
                .stream().findAny().orElse(new WarningTrajectoryEntity());
        String idValue = warningTrajectoryEntity.getIdValue();

//        return getWarningCarTrajectoryFromMoye(warningId, idValue);
        WarningEntity warning = warningRepository.getById(warningId);
        String warningLevel = warning.getWarningLevel();
        return getWarningCarTrajectoryFromHybase(idValue, warningLevel);
    }

    private List<WarningCarTrajectoryVO> getWarningCarTrajectoryFromHybase(String idValue, String warningLevel) {
        TimeParams timeParams = new TimeParams();
        timeParams.setRange("10");
        return BeanUtil.getBean(HybaseDao.class).getWarningCarTrajectory(idValue, warningLevel, timeParams, 100);
    }

    @NotNull
    private List<WarningCarTrajectoryVO> getWarningCarTrajectoryFromMoye(String warningId, String idValue) {
        Map<String, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("pageNum", 1);
        dataMap.put("pageSize", warningProperties.getTrackPageSize());
        dataMap.put("dataEnName", warningProperties.getTrackTableName());
        dataMap.put("condition", Collections.singleton("zjlid = '" + idValue + "'"));
        dataMap.put("requiredItems", Arrays.asList("wdwgs84", "jdwgs84", "hdsj", "gjdz"));
        log.info("请求体:" + JsonUtil.toJsonString(dataMap));
        JsonNode jsonNode = restTemplate.postForObject(warningProperties.getGeneralSearchUrl(),
                new HttpEntity<>(dataMap, warningProperties.getHeadersMap()),
                JsonNode.class);
        log.info("请求响应:" + JsonUtil.toJsonString(jsonNode));
        if (Objects.isNull(jsonNode)) {
            return new ArrayList<>();
        }
        String result = JsonUtil.toJsonString(jsonNode.get("data").get("data"));
        WarningEntity warning = warningRepository.getById(warningId);
        String warningLevel = warning.getWarningLevel();

        return JsonUtil.parseArray(result, WarningCarTrajectoryDto.class)
                .stream()
                .map(dto -> new WarningCarTrajectoryVO(dto.getGjdz(), warningLevel, dto.getHdsj(), dto.getJdwgs84(),
                        dto.getWdwgs84(), null))
                .collect(Collectors.toList());
    }


    @Override
    public CarWarningVO getCarDetail(String warningId) {
        Map<String, String> mapToChinese = new HashMap<>();
        mapToChinese.put("nightAvgDrivingCount", "日平均夜间被抓拍次数");
        mapToChinese.put("dailyAvgRouteLocationNum", "日平均行驶途径地点数");
        mapToChinese.put("dayTimeAvgDrivingCount", "日平均日间被抓拍次数");
        mapToChinese.put("dailyAvgDrivingDistance", "日平均行驶距离");
        mapToChinese.put("drivingDurationCoverage", "日平均出行时间覆盖率");
        mapToChinese.put("referenceValue", "参考标准值");

        List<WarningTrajectoryEntity> allByWarningId = warningTrajectoryRepository.findAllByWarningId(warningId);
        CarWarningVO carWarningDTO = new CarWarningVO();
        if (!allByWarningId.isEmpty()) {
            WarningTrajectoryEntity trajectory = allByWarningId.get(0);
            carWarningDTO.setPlateNo(trajectory.getIdValue());
            carWarningDTO.setResult(trajectory.getSimilarity());
            //设置车辆识别信息：中文-对应的值
            Map<String, String> explain = JsonUtil.parseMap(trajectory.getWarningExplain(), String.class);
            List<KeyNameVO> explains = mapToChinese.entrySet()
                    .stream()
                    .map(entrySet -> {
                        String value = explain.get(entrySet.getKey());
                        value = StringUtils.isBlank(value) ? "- -" : value;
                        return new KeyNameVO(entrySet.getValue(), value);
                    })
                    .collect(Collectors.toList());
            carWarningDTO.setExplain(explains);
            //研判结果
            WarningCarJudgeEntity judgeResult = warningCarJudgeRepository.getByPlateNumber(carWarningDTO.getPlateNo());
            carWarningDTO.setJudgeResult(Objects.isNull(judgeResult) ? null : judgeResult.getJudgeResult());
        }
        return carWarningDTO;
    }

    @Override
    public void judgeCar(WarningCarJudgeVO judgeVO) {
        WarningCarJudgeEntity carJudge = warningCarJudgeRepository.getByPlateNumber(judgeVO.getPlateNumber());
        if (Objects.isNull(carJudge)) {
            carJudge = new WarningCarJudgeEntity(judgeVO.getPlateNumber(), judgeVO.getJudgeResult(),
                    LocalDateTime.now());
        } else {
            carJudge.setJudgeResult(judgeVO.getJudgeResult());
            carJudge.setJudgeTime(LocalDateTime.now());
        }
        warningCarJudgeRepository.save(carJudge);
    }

    @Override
    public HomePageWarningVO getHomePageWarningList(String subjectId) {
        LoginUser user = AuthHelper.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new ParamValidationException("用户不存在！");
        }
        List<RoleDataEntity> roleData = roleDataRepository.findViewRangesByUserId(user.getId());
        List<String> operationRanges = roleData.stream().map(RoleDataEntity::getOperationRange).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> warningTypes = roleData.stream().map(RoleDataEntity::getWarningType).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<WarningEntity> vos = warningRepository.findUnsignedByWarningTypeIn(warningTypes).stream()
                .filter(warning -> checkCanOperate(warning, operationRanges, warningTypes))
                .filter(warning -> warning.getSubjectId().equals(subjectId))
                .sorted(Comparator.comparing(WarningEntity::getWarningTime).reversed()).collect(Collectors.toList());
        return vos.isEmpty() ? null : of(vos.get(0));
    }

    private HomePageWarningVO of(WarningEntity warning) {
        HomePageWarningVO vo = new HomePageWarningVO();
        vo.setWarningId(warning.getId());
        vo.setTime(warning.getWarningTime().format(DATE_TIME_FORMATTER));
        vo.setType(warning.getWarningLevel());
        vo.setMsg(warning.getWarningDetails());
        WarningTypeEntity type = warningTypeRepository.findById(warning.getWarningType()).orElse(null);
        vo.setTitle(Objects.nonNull(type) ? type.getCnName() : "");
        vo.setSubjectId(Objects.nonNull(type) ? type.getSubjectId() : "");
        return vo;
    }

    @NotNull
    private Sort getSort(SortParams sortParams) {
        if (Objects.nonNull(sortParams)) {
            String sortField = sortParams.getSortField();
            String sortDirection = sortParams.getSortDirection();
            if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(sortDirection)) {
                return Sort.by("desc".equals(sortDirection) ? Sort.Direction.DESC : Sort.Direction.ASC, sortField);
            }
        }
        return Sort.by(Sort.Direction.DESC, "warningTime");
    }

    @Override
    public WarningDetailVO getWarningDetail(String warningId) {
        WarningEntity warningEntity = warningRepository.findById(warningId).orElse(null);
        if (Objects.isNull(warningEntity)) {
            throw new ParamValidationException("预警信息不存在，请核实！");
        }

        LoginUser user = AuthHelper.getCurrentUser();
        if (Objects.isNull(user)) {
            throw new ParamValidationException("用户不存在！");
        }
        final List<RoleDataEntity> roleData = roleDataRepository.findViewRangesByUserId(user.getId());
        final List<String> operationRanges = roleData.stream().map(RoleDataEntity::getOperationRange)
                .filter(Objects::nonNull).collect(Collectors.toList());
        final List<String> warningTypes = roleData.stream().map(RoleDataEntity::getWarningType).filter(Objects::nonNull)
                .collect(Collectors.toList());

        WarningDetailVO vo = WarningDetailVO.of(warningEntity);
        DisplayTypeEnum displayType = getDisplayTypeByWarningType(warningEntity.getWarningType());
        vo.setDisplayType(displayType.getCode());
        WarningTypeEntity type = warningTypeRepository.getById(warningEntity.getWarningType());
        vo.setWarningType(type.getCnName());

        switch (displayType) {
            case SINGLE_PERSON:
            case MULTI_PERSON:
                vo.setWarningAddress(warningEntity.getAddress());
                break;
            case CALL_DETAIL_RECORD:
                vo.setWarningPhoneNumber(getWarningNumber(warningId));
                break;
            case CAR_DETAIL_RECORD:
                List<WarningTrajectoryEntity> allByWarningId = warningTrajectoryRepository.findAllByWarningId(
                        warningEntity.getId());
                if (!allByWarningId.isEmpty()) {
                    WarningTrajectoryEntity warningTrajectoryEntity = allByWarningId.get(0);
                    String carNumber = warningTrajectoryEntity.getIdValue();
                    vo.setWarningCarNumber(StringUtils.isBlank(carNumber) ? "- -" : carNumber);
                }
                break;
            case PROFESSIONAL:
            case CRJ:
                break;
            default:
        }
        vo.setCanOperate(checkCanOperate(warningEntity, operationRanges, warningTypes));
        return vo;
    }

    /**
     * 根据预警id查询预警电话，如不唯一则抛出异常
     *
     * @param warningId 预警id
     * @return 预警电话号码
     */
    private String getWarningNumber(String warningId) {
        try {
            return warningCallRepository.getWarningNumberByWarningId(warningId);
        } catch (Exception e) {
            throw new ParamValidationException("单人类型预警轨迹信息不唯一，请核实！");
        }
    }

    @Override
    public List<WarningPersonVO> getWarningPersonDetail(String warningId) {
        WarningEntity warningEntity = warningRepository.findById(warningId).orElse(null);
        if (Objects.isNull(warningEntity)) {
            throw new ParamValidationException("预警信息不存在，请核实！");
        }
        DisplayTypeEnum displayType = getDisplayTypeByWarningType(warningEntity.getWarningType());

        switch (displayType) {
            case SINGLE_PERSON:
                return generateSinglePersonDetail(warningEntity);
            case MULTI_PERSON:
                return generateMultiPersonDetail(warningEntity);
            case CALL_DETAIL_RECORD:
                return generateCdrDetail(warningEntity);
            case CRJ:
                return generateCrjDetail(warningEntity);
            default:
                throw new ParamValidationException("显示类型错误，请核实！");
        }
    }

    private List<WarningPersonVO> generateCrjDetail(WarningEntity warning) {
        List<WarningPersonVO> person = new ArrayList<>();
        try {
            WarningPersonVO personVO = new WarningPersonVO();
            WarningTrajectoryEntity trajectory = warningTrajectoryRepository.findTraceByWarningId(warning.getId());
            yjgjRepository.findById(trajectory.getRawData()).ifPresent(track -> {
                personVO.setName(track.getGlxm());
                personVO.setPersonType(Collections.singletonList(track.getRyxl()));
                personVO.setIdNumber(track.getGlsfzh());
            });
            personVO.setActivityDetail(trajectoryToActivity(trajectory));
            person.add(personVO);
        } catch (Exception e) {
            throw new ParamValidationException("获取预警人员信息失败，请核实！", e);
        }
        return person;
    }

    /**
     * 处理单人类型预警详情
     *
     * @param warning 预警
     * @return 处理完成vo
     */
    private List<WarningPersonVO> generateSinglePersonDetail(WarningEntity warning) {
        List<WarningPersonVO> person = new ArrayList<>();
        try {
            PersonEntity personEntity = getPersonEntity(warning);
            WarningPersonVO personVO = personEntityToWarning(personEntity, warning.getSubjectId());
            WarningTrajectoryEntity trajectory = warningTrajectoryRepository.findTraceByWarningId(warning.getId());
            trajectory.setSimilarity("99.31415926");
            personVO.setActivityDetail(trajectoryToActivity(trajectory));
            person.add(personVO);
        } catch (Exception e) {
            throw new ParamValidationException("获取预警人员信息失败，请核实！", e);
        }
        return person;
    }

    private PersonEntity getPersonEntity(WarningEntity warning) {
        return warningTraceRelationRepository.findPersonByWarning(warning.getId());
    }

    /**
     * 处理多人类型预警详情
     *
     * @param warning 预警
     * @return 处理完成vo
     */
    private List<WarningPersonVO> generateMultiPersonDetail(WarningEntity warning) {
        List<WarningTrajectoryEntity> trajectories = warningTrajectoryRepository.findTraceListByWarningId(
                warning.getId());
        List<WarningPersonVO> persons = new ArrayList<>();
        for (WarningTrajectoryEntity trajectory : trajectories) {
            PersonEntity personEntity = warningTrajectoryRepository.findPersonByTrajectoryId(trajectory.getId());
            if (Objects.isNull(personEntity)) {
                continue;
            }
            WarningPersonVO personVO = personEntityToWarning(personEntity, warning.getSubjectId());

            personVO.setActivityDetail(trajectoryToActivity(trajectory));

            persons.add(personVO);
        }

        return persons;
    }

    /**
     * 处理话单类型预警详情
     *
     * @param warning 预警
     * @return 处理完成vo
     */
    private List<WarningPersonVO> generateCdrDetail(WarningEntity warning) {
        List<WarningPersonVO> persons = new ArrayList<>();
        List<WarningCallEntity> calls = warningCallRepository.findAllByWarningId(warning.getId());

        if (Objects.isNull(calls)) {
            return persons;
        }

        List<String> idNumbers = new ArrayList<>();
        for (WarningCallEntity call : calls) {
            if (!idNumbers.contains(call.getIdNumber())) {
                idNumbers.add(call.getIdNumber());
            }
        }
        for (String idNumber : idNumbers) {
            PersonEntity person = personRepository.findByIdNumber(idNumber);
            if (Objects.isNull(person)) {
                continue;
            }
            WarningPersonVO warningPersonVO = personEntityToWarning(person, warning.getSubjectId());
            Integer contacts = warningCallRepository.countByIdNumberAndWarningId(person.getIdNumber(), warning.getId());
            warningPersonVO.setContactsCount(contacts);

            persons.add(warningPersonVO);
        }
        return persons;
    }

    /**
     * 根据轨迹entity生成活动vo
     *
     * @param trajectory entity {@link WarningTrajectoryEntity}
     * @return vo {@link WarningActivityVO}
     */
    private WarningActivityVO trajectoryToActivity(WarningTrajectoryEntity trajectory) {
        WarningActivityVO activity = new WarningActivityVO();
        activity.setActivityAddress(trajectory.getAddress());
        activity.setActivityTime(trajectory.getDateTime());
        activity.setActivityType(warningTrajectoryRepository.findSourceNameByTrajectoryId(trajectory.getId()));

        //活动照片
        List<ImageVO> images = new ArrayList<>();
        if (Objects.nonNull(trajectory.getImageUrl())) {
            ImageVO fullImage = new ImageVO();

            String fullImageUrl = trajectory.getImageUrl();
            if (StringUtils.contains(fullImageUrl, "***********")) {
                /*
                 * 泸州依图人像图片数据特殊处理  代码逻辑不会影响其他项目
                 * 依图人像图片地址***********代理的大图地址有问题  需要修正                 *
                 */
                fullImageUrl = "http://***********:11180/storage/v1/image/global?cluster_id=LUZHOU_NT&uri_base64="
                        + StringUtils.substringAfterLast(fullImageUrl, "uri_base64=");
            }
            fullImage.setUrl(fullImageUrl);
            images.add(fullImage);
        }
        if (Objects.nonNull(trajectory.getCropUrl())) {
            ImageVO cropImage = new ImageVO();
            cropImage.setUrl(trajectory.getCropUrl());
            images.add(cropImage);
        }

        ActivityImagesVO activityImage = new ActivityImagesVO();
        activityImage.setImages(images);
        activityImage.setSimilarity(trajectory.getSimilarity());
        activity.setActivityImages(activityImage);

        return activity;
    }

    /**
     * 由人员entity生成预警详情人员vo
     *
     * @param personEntity entity {@link PersonEntity}
     * @param subjectId    专题id
     * @return vo {@link WarningPersonVO}
     */
    private WarningPersonVO personEntityToWarning(PersonEntity personEntity, String subjectId) {
        WarningPersonVO personVO = new WarningPersonVO();
        personVO.setPersonId(personEntity.getId());
        personVO.setName(personEntity.getName());
        personVO.setGender(personEntity.getGender());
        personVO.setIdNumber(personEntity.getIdNumber());
        personVO.setPhoneNumber(personEntity.getContactInformation());
        personVO.setControlStatus(personEntity.getControlStatus());
        List<LabelEntity> types = labelRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), subjectId);
        if (!labelRepository.findAllBySubjectId(subjectId, "person").isEmpty()) {
            personVO.setPersonType(types.stream().map(LabelEntity::getName).collect(Collectors.toList()));
        }
        ImageVO imageVO = new ImageVO();
        imageVO.setImageId("");
        imageVO.setUrl("/dispatchWarning/getPhoto/" + personEntity.getIdNumber());
        personVO.setPhoto(imageVO);

        return personVO;
    }

    @Override
    public CarTrajectoryVO getCarTrajectoryByCarNumber(String carNumber, TimeParams timeParams) {
        CarTrajectoryVO carTrajectoryVO = new CarTrajectoryVO();
        List<TimeTrajectoryVO> timeTrajectory = new ArrayList<>();
        List<WarningCarTrajectoryVO> warningCarTrajectory = BeanUtil.getBean(HybaseDao.class)
                .getWarningCarTrajectory(carNumber, "蓝色", timeParams, 1000);
        carTrajectoryVO.setCount(warningCarTrajectory.size());
        warningCarTrajectory.stream().collect(Collectors.groupingBy(item -> item.getDate().format(DATE_FORMATTER)))
                .forEach((key, value) -> {
                    TimeTrajectoryVO timeTrajectoryVO = new TimeTrajectoryVO();
                    timeTrajectoryVO.setDate(key);
                    timeTrajectoryVO.setTrajectory(value);
                    timeTrajectory.add(timeTrajectoryVO);
                });
        timeTrajectory.sort((o1, o2) -> {
            try {
                Date date1 = dateFormat.parse(o1.getDate());
                Date date2 = dateFormat.parse(o2.getDate());
                return date2.compareTo(date1);
            } catch (ParseException e) {
                throw new IllegalArgumentException(e);
            }
        });
        carTrajectoryVO.setTimeTrajectory(timeTrajectory);
        return carTrajectoryVO;
    }

    @Override
    public void driveNoLicenseAnalysisByManual() {
        task.run();
        //处理人员历史数据
        task.dealHistoryPerson();
    }

    /**
     * testSqlInFile<BR>
     *
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/8/14 09:51
     */
    @Override
    public String testDriveNoLicenseAnalysisSql() {
        return task.testDriveNoLicenseAnalysisSql();
    }
}
