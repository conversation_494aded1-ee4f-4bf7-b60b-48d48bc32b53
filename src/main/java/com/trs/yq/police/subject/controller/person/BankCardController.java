package com.trs.yq.police.subject.controller.person;

import com.trs.yq.police.subject.domain.vo.BankCardVO;
import com.trs.yq.police.subject.service.BankCardService;
import com.trs.yq.police.subject.service.PersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 银行卡信息接口
 *
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("/person")
@Slf4j
public class BankCardController {

    @Resource
    private BankCardService bankCardService;
    @Resource
    private PersonService personService;

    /**
     * 获取人员银行卡信息
     *
     * @param personId 人员id
     * @return 工作信息 {@link BankCardVO}
     * <AUTHOR>
     */
    @GetMapping("/{personId}/bank-card")
    public List<BankCardVO> getAll(@PathVariable String personId) {
        return bankCardService.getAllByPersonId(personId);
    }


    /**
     * 根据id删除人员的银行卡信息
     *
     * @param personId   人员id
     * @param bankCardId 银行卡id
     * <AUTHOR>
     */
    @DeleteMapping("/{personId}/bank-card")
    public void deleteOne(@PathVariable String personId, @NotBlank(message = "银行卡主键为空") String bankCardId) {
        personService.checkPersonExist(personId);
        bankCardService.deleteOneById(personId, bankCardId);
    }

    /**
     * 增加一条银行卡信息
     *
     * @param personId   人员主键
     * @param bankCardVO 银行卡信息
     * <AUTHOR>
     */
    @PostMapping("/{personId}/bank-card")
    public void addOne(@PathVariable String personId, @Valid @RequestBody BankCardVO bankCardVO) {
        personService.checkPersonExist(personId);
        bankCardService.addOne(personId, bankCardVO);
    }

    /**
     * 更新一条银行卡信息
     *
     * @param personId   人员主键
     * @param bankCardVO 银行卡信息
     * <AUTHOR>
     */
    @PutMapping("/{personId}/bank-card")
    public void updateOne(@PathVariable String personId, @Valid @RequestBody BankCardVO bankCardVO) {
        personService.checkPersonExist(personId);
        bankCardService.updateOne(bankCardVO, personId);
    }


}
