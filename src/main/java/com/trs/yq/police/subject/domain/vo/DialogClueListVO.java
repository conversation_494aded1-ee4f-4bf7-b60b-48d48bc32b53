package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.ClueEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.stream.Collectors;


/**
 * 群体关联线索弹出框中线索列表的vo
 *
 * <AUTHOR>
 * @date 2021/09/06
 */
@Data
public class DialogClueListVO implements Serializable {

    private static final long serialVersionUID = 8876200542732178970L;

    /**
     * 线索id
     */
    private String clueId;
    /**
     * 线索名称
     */
    private String clueName;
    /**
     * 线索类别
     */
    private String clueType;
    /**
     * 录入单位
     */
    private String createDeptName;

    /**
     * 创建vo
     *
     * @param clueEntity {@link ClueEntity}
     * @return {@link DialogClueListVO}
     */
    public static DialogClueListVO of(ClueEntity clueEntity) {
        DialogClueListVO dialogClueListVO = new DialogClueListVO();
        dialogClueListVO.setClueId(clueEntity.getId());
        dialogClueListVO.setClueName(clueEntity.getName());
        LabelRepository labelRepository = BeanUtil.getBean(LabelRepository.class);
        String clueType = labelRepository.findByClueId(clueEntity.getId()).stream()
                .map(LabelEntity::getName)
                .collect(Collectors.joining("、"));
        dialogClueListVO.setClueType(clueType);
        dialogClueListVO.setCreateDeptName(clueEntity.getCrDept());
        return dialogClueListVO;
    }
}
