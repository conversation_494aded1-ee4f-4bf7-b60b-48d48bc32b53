package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.GroupEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.entity.PersonGroupRelationEntity;
import com.trs.yq.police.subject.repository.EventRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.repository.PersonGroupRelationRepository;
import com.trs.yq.police.subject.service.DictService;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DictTypeConstants.DICT_TYPE_CONTROL_STATUS;

/**
 * 群体关联的人员列表查询VO
 *
 * <AUTHOR>
 * @date 2021/09/08
 */
@Data
public class GroupRelatedPersonVO implements Serializable {

    private static final long serialVersionUID = -1860923329688015457L;

    /**
     * 人员id
     */
    private String personId;
    /**
     * 人员姓名
     */
    private String personName;
    /**
     * 设份证号码
     */
    private String idNumber;
    /**
     * 活跃程度
     */
    private String activityLevel;
    /**
     * 人员类别
     */
    private String personType;
    /**
     * 录入时间
     */
    private LocalDateTime createTime;
    /**
     * 关联id
     */
    private String relationId;

    /**
     * 管控状态
     */
    private String controlStatus;

    /**
     * 涉事数量
     */
    private Integer eventCount;


    /**
     * 构建vo
     *
     * @param personEntity {@link PersonEntity}
     * @param groupEntity  {@link GroupEntity}
     * @return {@link GroupRelatedPersonVO}
     */
    public static GroupRelatedPersonVO of(PersonEntity personEntity, GroupEntity groupEntity) {
        GroupRelatedPersonVO vo = new GroupRelatedPersonVO();
        vo.setPersonId(personEntity.getId());
        vo.setPersonName(personEntity.getName());
        vo.setIdNumber(personEntity.getIdNumber());

        LabelRepository personTypeRepository = BeanUtil.getBean(LabelRepository.class);
        String personType = personTypeRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), groupEntity.getSubjectId()).stream()
                .map(LabelEntity::getName).collect(Collectors.joining("、"));
        vo.setPersonType(personType);

        PersonGroupRelationRepository personGroupRelationRepository = BeanUtil.getBean(PersonGroupRelationRepository.class);
        PersonGroupRelationEntity relation = personGroupRelationRepository.findByGroupIdAndPersonId(groupEntity.getId(), personEntity.getId());
        vo.setActivityLevel(relation.getActivityLevel());
        vo.setCreateTime(relation.getCrTime());
        vo.setRelationId(relation.getId());

        //管控状态
        DictService dictService = BeanUtil.getBean(DictService.class);
        vo.setControlStatus(dictService.getDictEntityByTypeAndCode(DICT_TYPE_CONTROL_STATUS, personEntity.getControlStatus()).getName());

        //涉事数量
        EventRepository eventRepository = BeanUtil.getBean(EventRepository.class);
        vo.setEventCount(eventRepository.findAllByPersonIdAndSubjectId(personEntity.getId(), groupEntity.getSubjectId()).size());
        return vo;
    }
}
