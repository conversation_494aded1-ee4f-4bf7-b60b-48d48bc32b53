package com.trs.yq.police.subject.domain.entity;

import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * <AUTHOR>
 * @date 2021/11/24
 */
@Data
@Entity(name = "T_BATTLE_USER")
public class BattleUserEntity implements Serializable {

    private static final long serialVersionUID = -8271448695805019516L;

    /**
     * 数据主键
     */
    @Id
    @GenericGenerator(name = "uuid", strategy = "uuid")
    @GeneratedValue(generator = "uuid")
    private String id;

    /**
     * 合成作战ID
     */
    @Column(name = "RECORDID")
    private String recordId;

    /**
     * 合成作战ID
     */
    @Column(name = "USERID")
    private String userId;
    /**
     * 合成作战ID
     */
    @Column(name = "REALNAME")
    private String realName;
    /**
     * 合成作战ID
     */
    @Column(name = "IDCARD")
    private String idCard;
    /**
     * 合成作战ID
     */
    @Column(name = "UNITNAME")
    private String unitName;
    /**
     * 合成作战ID
     */
    @Column(name = "UNITCODE")
    private String unitCode;
    /**
     * 合成作战ID
     */
    @Column(name = "ISHOST")
    private Boolean isHost;
    /**
     * 合成作战ID
     */
    @Column(name = "SHOWORDER")
    private Integer showOrder;
    /**
     * 合成作战ID
     */
    @Column(name = "ADDTIME")
    private LocalDateTime addTime;
    /**
     * 合成作战ID
     */
    @Column(name = "INHOSTUNIT")
    private Boolean inHostUnit;
}
