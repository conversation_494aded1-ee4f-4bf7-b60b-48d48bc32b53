package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.domain.entity.BattleCommandEntity;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合成作战/指令列表VO
 *
 * <AUTHOR>
 * @date 2021/12/13 10:17
 */
@Data
public class BattleRecordCommandListVO implements Serializable {
    private static final long serialVersionUID = 1982224342181670463L;
    /**
     * 合成作战Id
     */
    private String id;
    /**
     * 合成作战标题
     */
    private String title;
    /**
     * 状态(1-待审核 2-进行中 3-已归档  4-已驳回)
     */
    private Integer status;
    /**
     * 发布单位
     */
    private String publishDepartment;
    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布人
     */
    private String publishPerson;

    /**
     * 获取BattleRecordCommandListVO
     *
     * @param battleCommandEntity 指令信息
     * @return {@link BattleRecordCommandListVO}
     */
    public static BattleRecordCommandListVO of(BattleCommandEntity battleCommandEntity) {
        BattleRecordCommandListVO battleRecordCommandListVO = new BattleRecordCommandListVO();
        battleRecordCommandListVO.setId(battleCommandEntity.getId());
        battleRecordCommandListVO.setTitle(battleCommandEntity.getTitle());
        battleRecordCommandListVO.setStatus(battleCommandEntity.getState());
        battleRecordCommandListVO.setPublishPerson(battleCommandEntity.getCrtbyname());
        battleRecordCommandListVO.setPublishDepartment(battleCommandEntity.getUnitName());
        battleRecordCommandListVO.setPublishTime(battleCommandEntity.getPublishTime());
        return battleRecordCommandListVO;
    }
}

