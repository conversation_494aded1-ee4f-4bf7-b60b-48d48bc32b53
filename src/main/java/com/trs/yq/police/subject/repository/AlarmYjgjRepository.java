package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.AlarmYjgjEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 云墙预警轨迹
 */
@Repository
public interface AlarmYjgjRepository extends BaseRepository<AlarmYjgjEntity, String> {

    /**
     * 查询预警轨迹
     *
     * @param ryxl 人员类型
     * @return {@link AlarmYjgjEntity}
     */
    @Query("select t from AlarmYjgjEntity t where t.ryxl = :ryxl")
    List<AlarmYjgjEntity> getYjgj(@Param("ryxl") String ryxl);

    /**
     * 查询预警轨迹
     *
     * @param ryxl      人员类型
     * @param startTime 活动数据范围开始时间
     * @return {@link AlarmYjgjEntity}
     */
    @Query("select t from AlarmYjgjEntity t where t.ryxl = :ryxl and t.hdfssj >= :startTime")
    List<AlarmYjgjEntity> getYjgjByTime(@Param("ryxl") String ryxl, @Param("startTime") LocalDateTime startTime);
}
