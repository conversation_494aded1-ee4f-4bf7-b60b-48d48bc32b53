package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.PersonSubjectRelationEntity;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 人员专题关系查询接口
 *
 * <AUTHOR>
 * @date 2021/07/27
 */
@Repository
public interface PersonSubjectRelationRepository extends BaseRepository<PersonSubjectRelationEntity, String> {

    /**
     * 查询与专题相关的所有人员
     *
     * @param subjectId 专题id
     * @return 与该主题关联的人员
     */
    List<PersonSubjectRelationEntity> findBySubjectId(@Param("subjectId") String subjectId);

    /**
     * 依据人员id删除所有关联关系
     *
     * @param personId  人员id
     * @param subjectId 专题主键
     */
    @Modifying
    void removeByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId);

    /**
     * 根据人员id和专题id查找
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @return 人员信息与专题关系
     */
    PersonSubjectRelationEntity findByPersonIdAndSubjectId(@Param("personId") String personId, @Param("subjectId") String subjectId);


    /**
     * 通过人员主键检索关联关系
     *
     * @param personId 人员主键
     * @return 关联关系
     */
    List<PersonSubjectRelationEntity> findAllByPersonId(@Param("personId") String personId);

    /**
     * 查询出身份证号码和专题id的对应关系
     *
     * @return 身份证号码:subjectId
     */
    @Query(nativeQuery = true, value = "SELECT t1.SUBJECT_ID," +
            "t2.ID_NUMBER," +
            "t2.CONTROL_STATUS," +
            "t2.MONITOR_STATUS, " +
            "t2.ID " +
            "FROM T_PS_PERSON_SUBJECT_RELATION t1 JOIN T_PS_PERSON t2 ON t1.PERSON_ID=t2.ID")
    List<Map<String, Object>> findSubjectIdAndIdNumber();

    /**
     * 统计数量
     *
     * @param subjectId 专题主键
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @return 统计数量
     */
    @SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
    @Query("select p.personId from PersonSubjectRelationEntity p where p.subjectId = ?1 and p.crTime between ?2 and ?3")
    List<String> findPersonIdBySubjectIdAndCrTimeIsBetween(String subjectId, LocalDateTime beginTime, LocalDateTime endTime);

    /**
     * 根据专题id查询人员id
     *
     * @param subjectId 专题id
     * @return 人员id
     */
    @SuppressWarnings("SpringDataRepositoryMethodReturnTypeInspection")
    @Query("select t1.id from PersonEntity t1 join PersonSubjectRelationEntity t2 on t1.id=t2.personId where t2.subjectId = ?1 ")
    List<String> findPersonIdBySubjectId(String subjectId);


    /**
     * 根据专题id查询人员id
     *
     * @param endTime   时间范围
     * @param subjectId 专题id
     * @return 人员id
     */
    @Query("select p from PersonSubjectRelationEntity p where p.subjectId = ?1 and p.crTime<?2 ")
    List<PersonSubjectRelationEntity> findBySubjectIdAndCrTime(String subjectId, LocalDateTime endTime);

    /**
     * 获取人员数量
     *
     * @param subjectId 专题id
     * @return 人员数量
     */
    @Query("select count(p) from PersonSubjectRelationEntity p where p.subjectId = ?1 ")
    long getPersonCountBySubjectId(String subjectId);
}
