package com.trs.yq.police.subject.domain.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * @version 1.0
 * @date 创建时间：2024/6/19 16:34
 * @since 1.0
 */
@Data
public class DrivingNoLicenseAnalyzeVO {

    /**
     * 预警编号（唯一）
     */
    private String yjbh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 证件号码（为空则无意义）
     */
    private String zjhm;

    /**
     * 车牌号码
     */
    private String hphm;

    /**
     * 准驾车型
     */
    private String zjcx;

    /**
     * 驾驶证状态
     */
    private String jszzt;

    /**
     * 违法时间
     */
    private String wfsj;

    /**
     * 违法地点
     */
    private String wfdd;

    /**
     * 违法地点坐标（经度在前维度在后逗号分隔）
     */
    private String wfddZt;

    /**
     * 违法地管辖单位编号
     * 不为空，根据这个信息推送到指定单位
     */
    private String gxdwbh;

    /**
     * 人脸图
     */
    private String rlt;

    /**
     * 场景图
     */
    private String cjt;

    /**
     * 预警信息唯一id
     */
    private String warningId;

    /**
     * splitTime<BR>
     *
     * @param sj 参数
     * @return 结果
     * <AUTHOR> chu.chuanbao E-mail: <EMAIL>
     * @date 创建时间：2024/6/24 20:48
     */
    public static String splitTime(String sj) {
        if (StringUtils.isEmpty(sj)) {
            throw new RuntimeException("时间不能为空");
        }
        String[] tmp = sj.split(" ");
        return tmp[0] + " " + tmp[1];
    }

}
