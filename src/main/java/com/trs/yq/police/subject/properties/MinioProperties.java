package com.trs.yq.police.subject.properties;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@Component
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "com.trs.minio")
public class MinioProperties {

    private String endpoint;

    private String accessKey;

    private String secretKey;


}
