package com.trs.yq.police.subject.domain.entity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.Hibernate;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/08/26
 */
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Table(name = "T_PS_TRAJECTORY_SOURCE")
@Entity
public class TrajectorySourceEntity implements Serializable {

    public static final String CAR_NUMBER = "carNumber";
    public static final String PHONE_NUMBER = "phoneNumber";
    private static final long serialVersionUID = 8899180537363877736L;

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 特征值字段名称
     */
    private String indexColumn;

    /**
     * 特征值类型
     */
    private String indexType;

    /**
     * 活动场所字段名称
     */
    private String placeColumn;

    /**
     * 活动地址字段名称
     */
    private String addressColumn;

    /**
     * 活动时间字段名称
     */
    private String timeColumn;

    /**
     * 活动时间格式
     */
    private String timeFormat;

    /**
     * 经度字段名称
     */
    private String lngColumn;

    /**
     * 纬度字段名称
     */
    private String latColumn;

    /**
     * 备注拼接字符串（英文代表字段名称）
     */
    private String template;

    /**
     * 图片字段名称逗号分割，列表
     */
    private String imageColumn;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || Hibernate.getClass(this) != Hibernate.getClass(o)) {
            return false;
        }
        TrajectorySourceEntity that = (TrajectorySourceEntity) o;

        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return 1913738757;
    }
}

