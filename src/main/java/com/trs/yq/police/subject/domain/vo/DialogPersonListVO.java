package com.trs.yq.police.subject.domain.vo;

import com.trs.yq.police.subject.constants.enums.FileModuleEnum;
import com.trs.yq.police.subject.constants.enums.FileTypeEnum;
import com.trs.yq.police.subject.domain.entity.FileStorageEntity;
import com.trs.yq.police.subject.domain.entity.LabelEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.repository.FileStorageRepository;
import com.trs.yq.police.subject.repository.LabelRepository;
import com.trs.yq.police.subject.utils.BeanUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 创建群体/线索->人员关联对话框的列表查询VO
 *
 * <AUTHOR>
 * @date 2021/09/04
 */
@Data
public class DialogPersonListVO implements Serializable {

    private static final long serialVersionUID = -6187837831067897969L;

    /**
     * 人员id
     */
    private String personId;
    /**
     * 姓名
     */
    private String personName;
    /**
     * 身份证号
     */
    private String idNumber;
    /**
     * 人员类别
     */
    private String personType;
    /**
     * 录入单位
     */
    private String createDeptName;
    /**
     * 图片
     */
    private List<ImageVO> images;

    /**
     * 构建vo
     *
     * @param person    {@link PersonEntity}
     * @param subjectId 专题id
     * @return {@link DialogPersonListVO}
     */
    public static DialogPersonListVO of(PersonEntity person, String subjectId) {
        DialogPersonListVO item = new DialogPersonListVO();
        item.setPersonId(person.getId());
        item.setPersonName(person.getName());
        item.setIdNumber(person.getIdNumber());

        LabelRepository personTypeRepository = BeanUtil.getBean(LabelRepository.class);
        String personType = personTypeRepository.findAllByPersonIdAndSubjectId(person.getId(), subjectId).stream()
                .map(LabelEntity::getName)
                .collect(Collectors.joining("、"));
        item.setPersonType(personType);

        item.setCreateDeptName(person.getCrDept());
        FileStorageRepository fileStorageRepository = BeanUtil.getBean(FileStorageRepository.class);
        List<FileStorageEntity> files = fileStorageRepository.findAllByPersonIdAndModule(person.getId(),
                FileTypeEnum.IMAGE.getCode(),
                FileModuleEnum.BASIC_INFO_PHOTO.getCode(),
                null);
        item.setImages(files.stream().map(ImageVO::of).collect(Collectors.toList()));
        return item;
    }
}
