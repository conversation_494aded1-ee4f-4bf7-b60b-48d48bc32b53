package com.trs.yq.police.subject.repository;

import com.trs.yq.police.subject.domain.entity.ApprovalEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * 审批表
 */
@Repository
public interface ApprovalRepository extends BaseRepository<ApprovalEntity, String> {

    /**
     * 分页查询待我审批
     *
     * @param approverId 审批人id
     * @param status     审批状态
     * @param pageable   分页参数
     * @return 待审批列表
     */
    Page<ApprovalEntity> findAllByApproverIdAndStatus(String approverId, String status, Pageable pageable);

    /**
     * 查找该人员操作的待审批记录
     *
     * @param personId  人员id
     * @param subjectId 专题id
     * @param type      审批类型
     * @param status    审批状态
     * @return 审批记录
     */
    ApprovalEntity findByPersonIdAndSubjectIdAndTypeAndStatus(String personId, String subjectId, String type, String status);
}
