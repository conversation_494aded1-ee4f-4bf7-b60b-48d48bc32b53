package com.trs.yq.police.subject.message.startegy;

import com.fasterxml.jackson.databind.JsonNode;
import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.constants.enums.*;
import com.trs.yq.police.subject.domain.dto.StreamWarningMessageDTO;
import com.trs.yq.police.subject.domain.dto.WarningTrajectoryListDTO;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.message.WarningPushService;
import com.trs.yq.police.subject.operation.event.OperationLogRecord;
import com.trs.yq.police.subject.operation.handler.OperationLogHandler;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.utils.BeanUtil;
import com.trs.yq.police.subject.utils.GeoUtil;
import com.trs.yq.police.subject.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.trs.yq.police.subject.constants.DateTimeConstants.DATE_TIME_FORMATTER;
import static com.trs.yq.police.subject.constants.PoliceSubjectConstants.*;
import static com.trs.yq.police.subject.constants.WarningConstants.*;
import static com.trs.yq.police.subject.constants.enums.WarningTypeEnum.*;

/**
 * 流式预警处理
 *
 * <AUTHOR>
 * @date 2021/9/11 14:40
 */
@Slf4j
@Service
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
public class StreamWarningProcess implements WarningProcess {

    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private LabelRepository labelRepository;
    @Resource
    private TrajectorySourceRepository trajectorySourceRepository;
    @Resource
    private MobilityRepository mobilityRepository;
    @Resource
    private PersonSubjectRelationRepository personSubjectRelationRepository;
    @Resource
    private OperationLogHandler operationLogHandler;
    @Resource
    private WarningPushService warningPushService;
    @Resource
    private WarningPushLogRepository warningPushLogRepository;
    @Resource
    private ControlRepository controlRepository;
    @Resource
    private AdjudicationRepository adjudicationRepository;

    /**
     * 预警来源：火车订票
     */
    private static final String TRAIN_TICKETS_TYPE_ID = "11";
    /**
     * 泸州火车站经纬度
     */
    private static final String TRAIN_STATION_LNG = "105.4505926";
    private static final String TRAIN_STATION_LAT = "28.9476817";
    /**
     * 预警来源：飞机订票
     */
    private static final String PLANE_TICKETS_TYPE_ID = "12";
    /**
     * 泸州飞机场经纬度
     */
    private static final String LZ_AIRPORT_LNG = "105.4635612";
    private static final String LZ_AIRPORT_LAT = "29.0230502";

    /**
     * 首都飞机场经纬度
     */
    private static final String BJ_AIRPORT_LNG = "116.610548";
    private static final String BJ_AIRPORT_LAT = "40.078495";

    /**
     * 成都飞机场经纬度
     */
    private static final String CD_AIRPORT_LNG = "103.9431095";
    private static final String CD_AIRPORT_LAT = "30.559930";

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void process(String message) {

        // 转换为流处理预警
        final StreamWarningMessageDTO streamMessage = JsonUtil.parseObject(message, StreamWarningMessageDTO.class);

        // 对数据进行验证
        if (Objects.isNull(streamMessage)) {
            log.error("record error warning message = {}!", message);
            return;
        }

        // 预备待处理的参数 去除已存在的轨迹
        final List<WarningTrajectoryListDTO> trajectories = streamMessage.getTrajectories();
//        if (trajectories.isEmpty()) {
//            return;
//        }

        final List<String> typeList = streamMessage.getType()
                .stream()
                .filter(type -> !type.startsWith("crj_"))
                .collect(Collectors.toList());

        //单独处理新流入人员
        if (typeList.contains(WARNING_TYPE_FK_XLRWARY)) {
            trajectories.forEach(this::processNewPerson);
        }

        // 存储轨迹
        final List<String> trajectoryIds = extractTrajectoryIds(trajectories);

        // 生成预警
        final List<String> warningIds = extractWarningIds(typeList, trajectories);

        // 生成关联关系
        establishRelationship(warningIds, trajectoryIds);

        //操作记录
        warningIds.forEach(warningId -> {
            final OperationLogRecord logRecord = OperationLogRecord.builder()
                    .operator(Operator.PUSH)
                    .module(OperateModule.WARNING)
                    .currentUser(AuthHelper.getCurrentUser())
                    .primaryKey(warningId)
                    .targetObjectType(TargetObjectTypeEnum.WARNING.getCode())
                    .desc("推送预警消息")
                    .build();
            if (Objects.nonNull(operationLogHandler)) {
                // 记录操作
                operationLogHandler.publishEvent(logRecord);
            }
        });
    }

    /**
     * 新流入危安人员入库
     *
     * @param trace 相关轨迹
     */
    private void processNewPerson(WarningTrajectoryListDTO trace) {
        //如果人员已存在则不入库
        PersonEntity existPerson = personRepository.findByIdNumber(trace.getIdNumber());
        if (Objects.nonNull(existPerson)) {
            return;
        }
        JsonNode raw = trace.getRaw();
        if (Objects.isNull(raw)) {
            return;
        }
        PersonEntity personEntity = new PersonEntity();
        personEntity.setAreaCode(raw.get("SSXQDM").asText());
        personEntity.setName(raw.get("XM").asText());
        personEntity.setNation(raw.get("MZDM").asText());
        personEntity.setRegisteredResidence(raw.get("SHENGSHIXIAN_ZH").asText());
        personEntity.setIdNumber(raw.get("ZJHM").asText());
        personEntity.setGender("男".equals(raw.get("XB_ZH").asText()) ? "0" : "1");
        personEntity.setControlStatus(ControlStatusEnum.IN_CONTROL.getCode());
        personRepository.saveAndFlush(personEntity);

        PersonSubjectRelationEntity relation = new PersonSubjectRelationEntity();
        relation.setPersonId(personEntity.getId());
        relation.setSubjectId(FK_SUBJECT);
        personSubjectRelationRepository.saveAndFlush(relation);

        MobilityEntity mobilityEntity = new MobilityEntity();
        mobilityEntity.setPersonId(personEntity.getId());
        mobilityEntity.setLocation(raw.get("address").asText());
        mobilityEntity.setMoveType("1");
        mobilityEntity.setMoveTime(
                LocalDateTime.parse(raw.get("RZSJ").asText(), DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss.SSS")));
        mobilityEntity.setNote("新流入危安人员预警自动生成");
        mobilityRepository.saveAndFlush(mobilityEntity);
    }

    /**
     * 处理失驾人员驾车，裁决信息结束时间前于当前时间时不入库
     */
    private Boolean processSjryjc(WarningTrajectoryListDTO trace) {
        final PersonEntity person = personRepository.findByIdNumber(trace.getIdNumber());
        AdjudicationEntity judgement = adjudicationRepository.findAllByPersonIdOrderByJudgementDateDesc(person.getId())
                .stream().max(Comparator.comparing(AdjudicationEntity::getEndDate)).orElse(null);
        return judgement == null || LocalDate.now().isBefore(judgement.getEndDate());
    }

    /**
     * 提取预警id
     *
     * @param typeList     预警类型
     * @param trajectories 轨迹表
     * @return 预警id
     */
    private List<String> extractWarningIds(List<String> typeList, List<WarningTrajectoryListDTO> trajectories) {
        // 生成预警类型列表
        final List<WarningTypeEntity> warningTypes = warningTypeRepository.findAllByEnNameIn(typeList);
        return warningTypes
                .stream()
                .map(type -> distributeExtractWarningId(type, trajectories))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 处理单个单人或多人预警入库，返回warningId
     *
     * @param type         预警类型
     * @param trajectories 轨迹列表
     * @return 预警id
     */
    private String distributeExtractWarningId(WarningTypeEntity type, List<WarningTrajectoryListDTO> trajectories) {
        switch (type.getDisplayType()) {
            case WARNING_DISPLAY_TYPE_SINGLE:
                return extractSingleWarningId(type, trajectories);
            case WARNING_DISPLAY_TYPE_MULTI:
                return extractMultiWarningId(type, trajectories);
            default:
                return Strings.EMPTY;
        }
    }

    /**
     * 生成单人预警
     *
     * @param type         预警类型
     * @param trajectories 轨迹列表
     * @return 预警id
     */
    private String extractSingleWarningId(WarningTypeEntity type, List<WarningTrajectoryListDTO> trajectories) {
        WarningTrajectoryListDTO warningTrajectory = trajectories.get(0);
        boolean warningIsExistedToday = warningIsExistedToday(type, warningTrajectory.getIdType(), warningTrajectory.getIdValue());
        if (warningIsExistedToday) {
            log.info("warningType:{};idType:{};idValue:{}今日已生成预警，不再生成新预警", type.getCnName(),
                    warningTrajectory.getIdType(), warningTrajectory.getIdValue());
            return Strings.EMPTY;
        }
        //跳过巡逻盘查人员
        if (warningTrajectory.getTrajectoryType().equals("xds.XunLuoPanCunRenYuanXinXi")) {
            return Strings.EMPTY;
        }
        //跳过技侦手段预警
        if (warningTrajectory.getTrajectoryType().equals("LIC_YIBIAO_3_SHI_LUZ")) {
            return Strings.EMPTY;
        }
        //判断失驾人员驾车
        if (type.getEnName().equals(WarningTypeEnum.JJ_SJRYJC.getEnName()) && !processSjryjc(warningTrajectory)) {
            return Strings.EMPTY;
        }
        WarningEntity warning = new WarningEntity();
        warning.setWarningKey(warningTrajectory.getIdNumber());
        warning.setWarningTime(LocalDateTime.now());
        warning.setAddress(warningTrajectory.getAddress());
        warning.setWarningLevel(type.getDefaultLevel());
        warning.setWarningSource(generateWarningResource(trajectories));
        warning.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
        warning.setAreaCode(generateAreaCode(type, warningTrajectory));
        String warningDetails = generateSingleWarningDetail(type, warningTrajectory);
        if (StringUtils.isBlank(warningDetails)) {
            return null;
        }
        warning.setWarningDetails(warningDetails);
        warning.setSubjectId(type.getSubjectId());
        warning.setWarningType(type.getId());
        warning.setPlace(warningTrajectory.getPlace());
        // 存储预警
        warningRepository.save(warning);
//        warningPushService.push(type, warning, warningTrajectory.getIdNumber());
        return warning.getId();
    }


    /**
     * 判断在当天内是否已经预警
     *
     * @param warningType 预警类型
     * @param idType      预警主键类型
     * @param idValue     预警主键值
     * @return true：已经预警 false：未预警
     */
    private boolean warningIsExistedToday(WarningTypeEntity warningType, String idType, String idValue) {
        return
                warningPushLogRepository.findIfExistedInToday(warningType.getEnName(), warningType.getSubjectId(), idType,
                        idValue) > 0;
    }

    /**
     * 处理areaCode
     *
     * @param type              预警类型
     * @param warningTrajectory 预警轨迹
     * @return areaCode
     */
    private String generateAreaCode(WarningTypeEntity type, WarningTrajectoryListDTO warningTrajectory) {
        final String idNumber = warningTrajectory.getIdNumber();
        //禁毒-涉毒人员驾车 推送预警人员户籍地
        if (StringUtils.equals(type.getEnName(), JD_SDRYJC.getEnName())) {
            String prefix = idNumber.substring(0, 6);
            if (StringUtils.equals(prefix, "510523")) {
                prefix = "510503";
            }
            return prefix;
            //政保-人员预警 推送管控派出所所在区县
        } else if (StringUtils.equals(type.getEnName(), ZB_RYYJ.getEnName()) || StringUtils.equals(type.getEnName(), FX_RYYJ.getEnName())) {
            PersonEntity person = personRepository.findByIdNumber(idNumber);
            if (Objects.nonNull(person)) {
                ControlEntity control = controlRepository.findByPersonIdAndSubjectId(person.getId(), getSubjectIdByRYYJ(type.getEnName()))
                        .orElse(null);
                if (Objects.nonNull(control)) {
                    return StringUtils.isNotBlank(control.getPoliceStationCode()) ? control.getPoliceStationCode()
                            .substring(0, 6) : "510500";
                }
            }
            return "510500";
        } else {
            return GeoUtil.getDistrictCode(warningTrajectory.getLng(), warningTrajectory.getLat());
        }
    }

    private String getSubjectIdByRYYJ(String ryyj) {
        if (ryyj.equals(ZB_RYYJ.getEnName())) {
            return ZB_SUBJECT;
        } else if (ryyj.equals(FX_RYYJ.getEnName())) {
            return FX_SUBJECT;
        } else {
            return "";
        }
    }

    /**
     * 生成单人预警详情
     *
     * @param warningType       预警类型
     * @param warningTrajectory 预警轨迹
     * @return 预警id
     */
    private String generateSingleWarningDetail(WarningTypeEntity warningType,
                                               WarningTrajectoryListDTO warningTrajectory) {

        final String contentTemplate = warningType.getContentTemplate();

        if (StringUtils.equals(contentTemplate, WARNING_DETAIL_TEMPLATE_MISSING)) {
            return generatePersonWarningDetail(warningType, warningTrajectory);
            //轨迹类型为火车订票
        } else if (StringUtils.equals(warningTrajectory.getTrajectoryType(), "xds.HuoCheDingPiao")) {
            return generateTrainBookingDetail(warningType, warningTrajectory);
            //轨迹类型为飞机订票
        } else if (StringUtils.equals(warningTrajectory.getTrajectoryType(), "xds.MingHangDingPiao")) {
            return generatePlaneBookingDetail(warningTrajectory);
        }

        // 利用SPEL 转化详情
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(contentTemplate);
        EvaluationContext context = new StandardEvaluationContext(warningTrajectory);

        // 地址
        final String address;
        if (StringUtils.isNotBlank(warningTrajectory.getAddress())) {
            address = warningTrajectory.getAddress();
        } else {
            final JsonNode raw = warningTrajectory.getRaw();
            String detailAddress = raw.has("XXDZ") ? StringUtils.trimToEmpty(raw.get("XXDZ").asText()) : "";
            String innName = raw.has("LDMC") ? StringUtils.trimToEmpty(raw.get("LDMC").asText()) : "";
            String roomNumber = raw.has("RZFH") ? StringUtils.trimToEmpty(raw.get("RZFH").asText()) : "";
            address = detailAddress + innName + roomNumber;
        }

        // 身份证号获取人员类型
        final String idNumber = warningTrajectory.getIdNumber();
        final String name = getPersonName(idNumber);
        final String personType = generateSinglePersonType(idNumber, warningType.getSubjectId());

        // 活动时间
        final LocalDateTime activeTime = warningTrajectory.getDateTime();


        // 拼接预警详情
        context.setVariable("personType", StringUtils.trimToEmpty(personType));
        context.setVariable("activeTime", StringUtils.trimToEmpty(DATE_TIME_FORMATTER.format(activeTime)));
        context.setVariable("idNumber", StringUtils.trimToEmpty(idNumber));
        context.setVariable("name", StringUtils.trimToEmpty(name));
        context.setVariable("address", address);
        return expression.getValue(context, String.class);
    }

    /**
     * 根据id number获取人员姓名
     *
     * @param idNumber 身份证号
     * @return 姓名
     */
    private String getPersonName(String idNumber) {
        final PersonEntity person = personRepository.findByIdNumber(idNumber);
        return person.getName();
    }

    /**
     * 单人预警构建人员类别
     *
     * @param idNumber 身份证号
     * @return 类别列表字符串
     */
    private String generateSinglePersonType(String idNumber, String subjectId) {
        return labelRepository.findAllByIdNumberAndSubjectId(idNumber, subjectId)
                .stream()
                .map(LabelEntity::getName)
                .collect(Collectors.joining("、"));
    }

    /**
     * 生成多人聚集预警
     *
     * @param type         预警类型
     * @param trajectories 预警轨迹
     * @return 预警id列表
     * <AUTHOR>
     * @since 2021/9/7 17:14
     */
    private String extractMultiWarningId(WarningTypeEntity type, List<WarningTrajectoryListDTO> trajectories) {
        WarningEntity warning = new WarningEntity();
        warning.setWarningTime(LocalDateTime.now());
        warning.setAddress(trajectories.get(0).getAddress());
        warning.setWarningLevel(type.getDefaultLevel());
        warning.setWarningSource(generateWarningResource(trajectories));
        warning.setWarningStatus(WarningStatusEnum.WAIT_SIGN.getCode());
        warning.setWarningDetails(generateWarningMultiDetails(type, trajectories));
        warning.setSubjectId(type.getSubjectId());
        warning.setWarningType(type.getId());
        warning.setPlace(trajectories.get(0).getPlace());
        // 存储预警
        warningRepository.save(warning);
//        warningPushService.push(type, warning);
        return warning.getId();
    }


    /**
     * 构建多人预警详情
     *
     * @param type         预警类型
     * @param trajectories 预警轨迹
     * @return 预警详情
     */
    private String generateWarningMultiDetails(WarningTypeEntity type, List<WarningTrajectoryListDTO> trajectories) {

        final String contentTemplate = type.getContentTemplate();

        if (StringUtils.equals(contentTemplate, WARNING_DETAIL_TEMPLATE_MISSING)) {
            return Strings.EMPTY;
        }

        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(contentTemplate);
        EvaluationContext context = new StandardEvaluationContext();
        final List<String> personIdNumbers = trajectories
                .stream()
                .map(WarningTrajectoryListDTO::getIdNumber)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        final String subjectId = type.getSubjectId();
        context.setVariable("address", trajectories.get(0).getAddress());
        context.setVariable("personType", generateMultiPersonType(personIdNumbers, subjectId));
        context.setVariable("groupNames", generateGroupNames(personIdNumbers, subjectId));
        context.setVariable("activeTime", trajectories.get(0).getDateTime().format(DATE_TIME_FORMATTER));
        context.setVariable("personCount", personIdNumbers.size());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 多人预警构建人员类别
     *
     * @param personIdNumbers 人员身份证号列表
     * @return 类别列表字符串
     */
    private String generateMultiPersonType(List<String> personIdNumbers, String subjectId) {
        List<PersonEntity> personEntities = personRepository.findByIdNumbers(personIdNumbers);
        if (Objects.isNull(personEntities) || personEntities.isEmpty()) {
            return "人员";
        } else {
            return personEntities.stream().flatMap(
                            personEntity -> personRepository.getPersonType(personEntity.getId(), subjectId)
                                    .stream()).distinct()
                    .collect(Collectors.joining("、"));
        }
    }

    /**
     * 构建群体名称
     *
     * @param personIdNumbers 人员身份证号列表
     * @return 群体列表字符串
     */
    private String generateGroupNames(List<String> personIdNumbers, String subjectId) {
        List<PersonEntity> personEntities = personRepository.findByIdNumbers(personIdNumbers);
        if (Objects.isNull(personEntities) || personEntities.isEmpty()) {
            return StringUtils.EMPTY;
        } else {
            return personEntities.stream().flatMap(
                            personEntity -> personRepository.getGroupType(personEntity.getId(), subjectId)
                                    .stream()).distinct()
                    .collect(Collectors.joining("、"));
        }
    }

    /**
     * 构建预警源
     *
     * @param warningTrajectories 预警消息
     * @return 预警源
     */
    private List<String> generateWarningResource(List<WarningTrajectoryListDTO> warningTrajectories) {

        final List<String> tableNames = warningTrajectories
                .stream()
                .map(WarningTrajectoryListDTO::getTrajectoryType)
                .distinct()
                .collect(Collectors.toList());
        return trajectorySourceRepository.findAllNameByTableNameIn(tableNames);
    }


    /**
     * 存储轨迹，提取轨迹id
     *
     * @param trajectories 轨迹列表
     * @return 轨迹id列表
     */
    private List<String> extractTrajectoryIds(List<WarningTrajectoryListDTO> trajectories) {
        final List<WarningTrajectoryEntity> warningTrajectories = trajectories.stream()
                .map(trajectory -> {
                    //跳过巡逻盘查人员
                    if (trajectory.getTrajectoryType().equals("xds.XunLuoPanCunRenYuanXinXi")) {
                        return null;
                    }

                    // 转换
                    WarningTrajectoryEntity warningTrajectory = new WarningTrajectoryEntity();
                    final TrajectorySourceEntity trajectorySource = trajectorySourceRepository.findByTableName(
                            trajectory.getTrajectoryType());
                    if (Objects.isNull(trajectorySource)) {
                        log.error("trajectory source is empty! trajectory = {}", trajectory);
                    } else {
                        warningTrajectory.setSourceId(trajectorySource.getId());
                        if (TRAIN_TICKETS_TYPE_ID.equals(trajectorySource.getId())) {
                            warningTrajectory.setLng(TRAIN_STATION_LNG);
                            warningTrajectory.setLat(TRAIN_STATION_LAT);
                        }
                        if (PLANE_TICKETS_TYPE_ID.equals(trajectorySource.getId())) {
                            warningTrajectory.setLng(LZ_AIRPORT_LNG);
                            warningTrajectory.setLat(LZ_AIRPORT_LAT);
                        }
                        if (trajectory.getTrajectoryType().equals("LIC_YIBIAO_3_SHI_LUZ")) {
                            JsonNode raw = trajectory.getRaw();
                            String data = raw.get("DATA").asText();
                            JsonNode dataNode = JsonUtil.parseJsonNode(data);
                            String lng = dataNode.has("LONGITUDE") ? StringUtils.trimToEmpty(dataNode.get("LONGITUDE").asText()) : "";
                            String lat = dataNode.has("LATITUDE") ? StringUtils.trimToEmpty(dataNode.get("LATITUDE").asText()) : "";
                            trajectory.setLat(lat);
                            trajectory.setLng(lng);
                        }
                    }
                    final String districtCode = GeoUtil.getDistrictCode(trajectory.getLng(), trajectory.getLat());
                    if (!StringUtils.isEmpty(districtCode)) {
                        warningTrajectory.setAreaCode(districtCode);
                    } else {
                        warningTrajectory.setAreaCode("510500");
                    }
                    BeanUtil.copyPropertiesIgnoreNull(trajectory, warningTrajectory);
                    warningTrajectory.setHybaseTable(trajectory.getTrajectoryType());
                    warningTrajectory.setHybaseId(trajectory.getTrsId());
                    warningTrajectory.setRawData(trajectory.getRaw().toString());
                    return warningTrajectory;
                }).filter(Objects::nonNull)
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(
                                () -> new TreeSet<>(Comparator.comparing(WarningTrajectoryEntity::getIdNumber))), ArrayList::new));
        // 存储
        warningTrajectoryRepository.saveAll(warningTrajectories);
        // 获取路径id
        return warningTrajectories.stream().map(WarningTrajectoryEntity::getId).collect(Collectors.toList());
    }

    /**
     * 检验轨迹是否已经入库, 轨迹不存在时返回true
     *
     * @param dto 轨迹dto
     * @return 查询结果
     */
    private Boolean checkTrajectoryExist(WarningTrajectoryListDTO dto) {
        final String sourceId = trajectorySourceRepository.findByTableName(dto.getTrajectoryType()).getId();
        final String idNumber = dto.getIdNumber();
        final LocalDateTime dateTime = dto.getDateTime();
        final String LNG = dto.getLng();
        final String LAT = dto.getLat();
        List<WarningTrajectoryEntity> result = warningTrajectoryRepository.findBySourceIdAndIdNumberAndDateTimeAndLngAndLat(
                sourceId, idNumber, dateTime, LNG, LAT);
        return result.isEmpty();
    }

    /**
     * 绑定预警与轨迹之间的关系
     *
     * @param warningIds    预警id列表
     * @param trajectoryIds 轨迹id列表
     */
    private void establishRelationship(List<String> warningIds, List<String> trajectoryIds) {
        final List<WarningTraceRelationEntity> entities = warningIds
                .stream()
                .map(warningId -> trajectoryIds
                        .stream()
                        .map(trajectoryId -> {
                            WarningEntity warningEntity = warningRepository.getById(warningId);
                            WarningTrajectoryEntity warningTrajectoryEntity = warningTrajectoryRepository.getById(trajectoryId);
                            if (PLANE_TICKETS_TYPE_ID.equals(warningTrajectoryEntity.getSourceId())
                                    //16： 重点人员进京     17：重点人员赴省
                                    && ("16".equals(warningEntity.getWarningType()) || "17".equals(
                                    warningEntity.getWarningType()))) {
                                String lat = "16".equals(warningEntity.getWarningType()) ? BJ_AIRPORT_LAT : CD_AIRPORT_LAT;
                                String lng = "16".equals(warningEntity.getWarningType()) ? BJ_AIRPORT_LNG : CD_AIRPORT_LNG;
                                warningTrajectoryEntity.setLat(lat);
                                warningTrajectoryEntity.setLng(lng);
                                warningTrajectoryRepository.save(warningTrajectoryEntity);
                            }
                            WarningTraceRelationEntity relation = new WarningTraceRelationEntity();
                            relation.setWarningId(warningId);
                            relation.setTrajectoryId(trajectoryId);
                            relation.setCreateTime(LocalDateTime.now());
                            return relation;
                        })
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        warningTraceRelationRepository.saveAll(entities);
    }

    /**
     * 生成人员预警详情
     *
     * @param warningTrajectory 预警轨迹列表
     * @return 预警详情
     */
    private String generatePersonWarningDetail(WarningTypeEntity warningType,
                                               WarningTrajectoryListDTO warningTrajectory) {
        final String trajectoryType = warningTrajectory.getTrajectoryType();

        switch (trajectoryType) {
            //人像
            case "yt.face_hit1":
                return generateFaceDetail(warningTrajectory);
            //民航订票
            case "xds.MingHangDingPiao":
                return generatePlaneBookingDetail(warningTrajectory);
            //民航进港
            case "xds.MingHangJinGangXinXi":
                return generateMinHangJinGangDetail(warningTrajectory);
            //民航机场安检
            case "lz.MH_JC_AJ1":
                return generateSafetyCheckDetail(warningTrajectory);
            //火车订票
            case "xds.HuoCheDingPiao":
                return generateTrainBookingDetail(warningType, warningTrajectory);
            //铁路进站卡口
            case "xds.TieLuJinZhanRenYuanXinXi":
                return generateTrainDetail(warningTrajectory);
            //wifi
            case "xds.yunfu":
                return generateWifiDetail(warningTrajectory);
            //网吧上网人员
            case "vw_share_wb_swry":
            case "xds.xds.WangBaShangWangRenYuanXinXi":
                return generateWbDetail(warningTrajectory);
            //交警卡口
            case "xds.JiaoJingKaKouXinXi":
                return generateTrafficPoliceDetail(warningTrajectory);
            //海康卡口
            case "hk.TrafficInfo":
                return generateHkDetail(warningTrajectory);
            //汽车订票
            case "hx.THPE_TICKETINFO":
                return generateBusTicketDetail(warningTrajectory);
            //新东盛客运汽车订票
            case "xds.KeYunQiCheDingPiao":
                return generateXdsBusDetail(warningTrajectory);
            //新东盛泸州客运汽车订票
            case "xds.LuZhouKeYunQiCheDingPiao":
                return generateXdsBusLuZhouDetail(warningTrajectory);
            //国内旅客
            case "xds.ZA_GNLKXX":
            case "vm_share_gnlkxx":
            case "xds.ZhiAnGuoNeiLvKe":
                return generateHotelDetail(warningTrajectory);
            //巡逻盘查车辆
            case "xds.XunLuoPanChaCheLiangXinXi":
                return generateCarPatrolDetail(warningTrajectory);
            //巡逻盘查人员
            case "xds.XunLuoPanCunRenYuanXinXi":
                return generatePersonPatrolDetail(warningTrajectory);
            default:
                return StringUtils.EMPTY;
        }
    }

    /**
     * 生成人脸识别预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateFaceDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:'+#name+',证件号码:'+#idNumber+',地址:'+#address+',活动时间:'+#activeTime+',相似度:'+#similarity";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        PersonEntity person = personRepository.findByIdNumber(warningTrajectory.getIdNumber());
        context.setVariable("name", Objects.nonNull(person) ? person.getName() : "");
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("address", warningTrajectory.getAddress());
        context.setVariable("activeTime", DATE_TIME_FORMATTER.format(warningTrajectory.getDateTime()));
        context.setVariable("similarity", warningTrajectory.getSimilarity());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成民航订票预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generatePlaneBookingDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "#name+'（'+#idNumber+'）购买了'+#qfsj+'由'+#departure+'飞往'+#arrival+'的'+#hbh+'次航班'";
        final JsonNode raw = warningTrajectory.getRaw();
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        String name = "";
        PersonEntity person = personRepository.findByIdNumber(warningTrajectory.getIdNumber());
        if (Objects.nonNull(person)) {
            name = person.getName();
        }
        context.setVariable("name", name);
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("departure", raw.get("CFD_CN").asText());
        context.setVariable("arrival", raw.get("MDD_CN").asText());
        context.setVariable("hbh", raw.get("HBH").asText());
        context.setVariable("qfsj",
                LocalDateTime.parse(raw.get("QFSJ").asText(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
                        .format(DATE_TIME_FORMATTER));
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成火车订票预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateTrainBookingDetail(WarningTypeEntity warningType,
                                              WarningTrajectoryListDTO warningTrajectory) {
        String template = "#personType+#name+'于'+#time+'购买'+#CCRQ+'由'+#FZ+'到'+#DZ+'的'+#CC+'次列车，车厢号为'+#CXH+'，座位号为'+#ZWH";
        final JsonNode raw = warningTrajectory.getRaw();
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        context.setVariable("personType",
                generateSinglePersonType(warningTrajectory.getIdNumber(), warningType.getSubjectId()));
        context.setVariable("name", raw.get("XM").asText());
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("FZ", raw.get("FZ").asText());
        context.setVariable("DZ", raw.get("DZ").asText());
        context.setVariable("CC", raw.get("CC").asText());
        context.setVariable("CXH", raw.get("CXH").asText());
        context.setVariable("ZWH", raw.get("ZWH").asText());
        context.setVariable("time", DATE_TIME_FORMATTER.format(
                LocalDateTime.parse(raw.get("TIME").asText(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))));
        context.setVariable("CCRQ", raw.get("CCRQ").asText());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成wifi预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateWifiDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'mac地址:'+#mac+',地址:'+#address+',抓拍时间:'+#time";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        context.setVariable("mac", warningTrajectory.getIdValue());
        context.setVariable("address", warningTrajectory.getAddress());
        context.setVariable("time", DATE_TIME_FORMATTER.format(warningTrajectory.getDateTime()));
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成网吧上网人员预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateWbDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:'+#name+',身份证号:'+#idNumber+',网吧名称:'+#place+',座位号:'+#seatNumber+',开始上网时间:'+#startTime+',下网时间:'+#endTime";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        PersonEntity person = personRepository.findByIdNumber(warningTrajectory.getIdNumber());
        context.setVariable("name", Objects.nonNull(person) ? person.getName() : "");
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("place", warningTrajectory.getPlace());
        context.setVariable("startTime", warningTrajectory.getStartTime());
        context.setVariable("endTime", warningTrajectory.getEndTime());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成民航进港预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateMinHangJinGangDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:'+#name+',证件号码:'+#idNumber+',航空公司代码:'+#airlcode+',航班号:'+#fltNumber+',出发地:'+#departure+',目的地:'+#destination+',离港时间:'+#departureTime+',进港时间:'+#arrivalTime";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        final JsonNode raw = warningTrajectory.getRaw();
        PersonEntity person = personRepository.findByIdNumber(warningTrajectory.getIdNumber());
        context.setVariable("name", Objects.nonNull(person) ? person.getName() : "");
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("airlcode", raw.get("FLT_AIRLCODE"));
        context.setVariable("fltNumber", raw.get("FLT_NUMBER"));
        context.setVariable("departure", raw.get("DEPT_CN"));
        context.setVariable("destination", raw.get("DEST_CN"));
        context.setVariable("departureTime", raw.get("STA_DEPTTM"));
        context.setVariable("arrivalTime", raw.get("STA_ARVETM"));
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成机场安检预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateSafetyCheckDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:'+#name+',证件号码:'+#idNumber+',安检地址:'+#address+',安检时间:'+#time";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        final JsonNode raw = warningTrajectory.getRaw();
        final String idNumber = raw.get("idcard").asText();
        PersonEntity person = personRepository.findByIdNumber(idNumber);
        context.setVariable("name", Objects.nonNull(person) ? person.getName() : "");
        context.setVariable("idNumber", idNumber);
        context.setVariable("address", "泸州云龙机场");
        context.setVariable("time", raw.get("dep_action_time"));
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成铁路进站卡口预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateTrainDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "姓名:{},身份证:{},通过卡口名称:{},通过时间:{}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成海康卡口预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateHkDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'车牌号码:{车牌号},车辆类型:{车辆类型},抓拍时间:{年/月/日 时间},抓拍地点:{卡口地址}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成汽车订票预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateBusTicketDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "姓名:{},身份证号:{},购票时间:{},出发地:{},发车时间:{},到达地:{}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成新东盛客运汽车订票预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateXdsBusDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:{},身份证号:{},购票时间:{},出发地:{},发车时间:{},到达地:{}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成新东盛泸州客运汽车订票预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateXdsBusLuZhouDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:{},身份证号:{},购票时间:{},出发地:{},发车时间:{},到达地:{}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成国内旅客信息预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateHotelDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "#name+'（'+#idNumber+'）于'+#time+'入住'+#address+#place+#roomNumber+'号房间'";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        final JsonNode raw = warningTrajectory.getRaw();
        context.setVariable("name", raw.get("XM").asText());
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("time", warningTrajectory.getDateTime().format(DATE_TIME_FORMATTER));
        context.setVariable("address",
                StringUtils.isNotBlank(warningTrajectory.getAddress()) ? warningTrajectory.getAddress()
                        : raw.get("address").asText());
        context.setVariable("place", StringUtils.isNotBlank(warningTrajectory.getPlace()) ? warningTrajectory.getPlace()
                : raw.get("QIYEMC").asText());
        context.setVariable("roomNumber", raw.get("RZFH").asText());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成巡逻盘查车辆预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateCarPatrolDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'车牌号码:{车牌号},车辆类型:{车辆类型},抓拍时间:{年/月/日 时间},抓拍地点:{卡口地址}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成巡逻盘查人员预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generatePersonPatrolDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "#name+'（'+#idNumber+'）'+#time+'于'+#address+'被盘查，盘查结果：'+#result";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        final JsonNode raw = warningTrajectory.getRaw();
        context.setVariable("name", raw.get("XM").asText());
        context.setVariable("idNumber", warningTrajectory.getIdNumber());
        context.setVariable("time", warningTrajectory.getDateTime().format(DATE_TIME_FORMATTER));
        context.setVariable("address", warningTrajectory.getPlace());
        context.setVariable("result", raw.get("PCCLJGMC").asText());
        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }

    /**
     * 生成交警卡口预警详情
     *
     * @param warningTrajectory 预警轨迹
     * @return 预警详情
     */
    private String generateTrafficPoliceDetail(WarningTrajectoryListDTO warningTrajectory) {
        String template = "'姓名:{},身份证:{},通过卡口名称:{},通过时间:{}";
        SpelExpressionParser parser = new SpelExpressionParser();
        final Expression expression = parser.parseExpression(template);
        EvaluationContext context = new StandardEvaluationContext();

        return StringUtils.trimToNull(expression.getValue(context, String.class));
    }
}
