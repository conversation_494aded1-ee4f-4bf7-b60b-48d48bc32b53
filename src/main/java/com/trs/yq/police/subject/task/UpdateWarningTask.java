package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/29 10:58
 */
@Slf4j
@Component
@Transactional(readOnly = true, rollbackFor = RuntimeException.class)
@ConditionalOnProperty(value = "com.trs.update.warning.task.enable", havingValue = "true")
public class UpdateWarningTask {
    @Resource
    private WarningTypeRepository warningTypeRepository;
    @Resource
    private PersonRepository personRepository;
    @Resource
    private BattleEventRepository battleEventRepository;
    @Resource
    private WarningRepository warningRepository;
    @Resource
    private WarningTrajectoryRepository warningTrajectoryRepository;
    @Resource
    private WarningTraceRelationRepository warningTraceRelationRepository;

    /**
     * warningSource: 高风险警情预警类型
     */
    private static final List<String> GFXJQYJ = Collections.singletonList("高风险警情预警");


    /**
     * warningSource:高风险警情预警类型Id
     */
    private static final String GFXJQYJ_ID = "20";

    /**
     * 更新高风险人员预警
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @Scheduled(cron = "${com.trs.update.warning.task}")
    public void addWarningInfo() {
        //筛选出未同步的高风险警情
        List<PersonEntity> jsb = personRepository.getPersonByPersonTypeName("肇事肇祸精神病人");
        log.info("开始同步高风险警情预警");

        List<Map<String, String>> gfx = personRepository.getGfxWarningPerson();
        for (Map<String, String> g : gfx) {
            String personId = g.get("person_id");
            String eventId = g.get("event_id");
            PersonEntity person = personRepository.getById(personId);
            BattleEventEntity battleEventEntity = battleEventRepository.getById(eventId);

            if (Objects.nonNull(warningRepository.findByWarningKey(eventId))) {
                log.info("该事件已预警！" + eventId);
                continue;
            }

            WarningTypeEntity warningType = jsb.contains(person)
                    ? warningTypeRepository.getById("26")
                    : warningTypeRepository.getById("25");
            //设置预警
            WarningEntity warningEntity = new WarningEntity();
            warningEntity.setAddress(battleEventEntity.getEventaddr());
            warningEntity.setWarningTime(battleEventEntity.getEventtime());
            warningEntity.setWarningKey(battleEventEntity.getId());
            warningEntity.setWarningType(warningType.getId());
            warningEntity.setSubjectId(warningType.getSubjectId());
            warningEntity.setWarningLevel(battleEventEntity.getWarnlevel());
            warningEntity.setWarningStatus("1");
            warningEntity.setWarningDetails(String.format("%s （身份证号码：%s, 电话号码：%s）触发高风险警情预警，预警详情：%s。", person.getName(), person.getIdNumber(), person.getContactInformation(), battleEventEntity.getDetail()));
            warningEntity.setWarningSource(GFXJQYJ);
            warningEntity.setAddress(battleEventEntity.getEventaddr());
            warningEntity.setAreaCode(battleEventEntity.getDistrictcode());
            warningEntity.setEventUrl(String.format("/battle/event/compareArchiveList?id=%s&table=%s&phone=%s", battleEventEntity.getKeyval(), battleEventEntity.getSrctable(), battleEventEntity.getCallphone()));
            final String warningId = warningRepository.save(warningEntity).getId();
            //设置轨迹表
            WarningTrajectoryEntity warningTrajectoryEntity = new WarningTrajectoryEntity();
            warningTrajectoryEntity.setAddress(battleEventEntity.getEventaddr());
            warningTrajectoryEntity.setDateTime(battleEventEntity.getEventtime());
            warningTrajectoryEntity.setIdNumber(person.getIdNumber());
            warningTrajectoryEntity.setHybaseTable("system.T_BATTLE_EVENT");
            warningTrajectoryEntity.setHybaseId("ID");
            warningTrajectoryEntity.setSourceId(GFXJQYJ_ID);

            Map<String, String> latLng = warningRepository.findLatLngFromJq(battleEventEntity.getKeyval());
            String lat = latLng.get("lat");
            if (lat.equals("0")) {
                warningTrajectoryEntity.setLat("28.877139");
            }
            String lng = latLng.get("lng");
            if (lng.equals("0")) {
                warningTrajectoryEntity.setLng("105.448391");
            }

            final String warningTrajectoryId = warningTrajectoryRepository.save(warningTrajectoryEntity).getId();
            //设置预警-轨迹关系表
            WarningTraceRelationEntity warningTraceRelationEntity = new WarningTraceRelationEntity();
            warningTraceRelationEntity.setWarningId(warningId);
            warningTraceRelationEntity.setTrajectoryId(warningTrajectoryId);
            warningTraceRelationEntity.setCreateTime(battleEventEntity.getEventtime());
            warningTraceRelationRepository.save(warningTraceRelationEntity);
        }
        log.info("高风险警情预警同步结束");
    }

}

