package com.trs.yq.police.subject.constants.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 虚拟身份枚举
 *
 * <AUTHOR>
 * @since 2021/10/15
 */

public enum VirtualIdentityEnum {
    /**
     * enum
     */
    MAC("1", "MAC"),
    IMSI("2", "IMSI"),
    IMEI("3", "IMEI"),
    QQ("4", "QQ"),
    WECHAT("5", "WECHAT"),
    TIKTOK("6", "TIKTOK");

    @Getter
    private final String code;

    @Getter
    private final String name;

    VirtualIdentityEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据类型名称匹配虚拟身份枚举
     *
     * @param name 名称
     * @return 枚举
     */
    public static VirtualIdentityEnum nameOf(String name) {

        if (StringUtils.isNotBlank(name)) {
            for (VirtualIdentityEnum typeEnum : VirtualIdentityEnum.values()) {
                if (StringUtils.equals(name, typeEnum.name)) {
                    return typeEnum;
                }
            }
        }
        return null;
    }

    /**
     * 根据类型名称返回编号
     *
     * @param name 名称
     * @return 编号
     */
    public static String getCode(String name) {

        if (StringUtils.isNotBlank(name)) {
            for (VirtualIdentityEnum typeEnum : VirtualIdentityEnum.values()) {
                if (StringUtils.equals(name, typeEnum.name)) {
                    return typeEnum.getCode();
                }
            }
        }
        return null;
    }
}
