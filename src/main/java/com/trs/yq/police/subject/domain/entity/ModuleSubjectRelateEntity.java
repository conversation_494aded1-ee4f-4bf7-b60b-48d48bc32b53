package com.trs.yq.police.subject.domain.entity;

import lombok.*;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 专题模关系
 *
 * <AUTHOR>
 * @date 2021/08/04
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "T_PS_MODULE_SUBJECT_RELATION")
public class ModuleSubjectRelateEntity {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 专题id
     */
    private String subjectId;

    /**
     * 档案id
     */
    private String moduleId;

    /**
     * 顺序
     */
    private Short showOrder;

    /**
     * 档案名称 person/group/clue
     */
    private String archiveName;
}

