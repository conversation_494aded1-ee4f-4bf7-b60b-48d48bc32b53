spring.application.name=police-subject
spring.cloud.nacos.server-addr=10.18.20.131:8848
spring.cloud.nacos.config.namespace=yq-lz
#spring.cloud.nacos.config.namespace=6bf89118-56cf-4645-8757-919d7f02c283
spring.cloud.nacos.discovery.namespace=${spring.cloud.nacos.config.namespace}
logging.file.path=./logs


spring.datasource.druid.filter.wall.config.function-check=false
spring.datasource.druid.filter.wall.config.permit-functions=dbms_random