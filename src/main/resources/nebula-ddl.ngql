create tag Person (
    name string null comment "姓名",
    gender string null comment "性别",
    nation string null comment "民族",
    nick_name string null comment "绰号",
    former_name string null comment "曾用名",
    education string null comment "文化程度",
    marital_status string null comment "婚姻状况",
    politics_status string null comment "政治面貌",
    religious_belief string null comment "宗教信仰",
    current_job string null comment "现职业",
    registered_residence string null comment "户籍地",
    current_residence string null comment "现住址"
) comment = "人员信息,主键为身份证号码";
create tag index if not exists person_index on Person();

create tag Phone(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间"
) comment = "手机号码,主键手机号码";
create tag index if not exists phone_index on Phone();

create tag QQ(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    nick_name string null comment "网名"
) comment = "qq号码,qq号码";
create tag index if not exists qq_index on QQ();

create tag Wechat(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    nick_name string null comment "网名"
) comment = "微信号码,微信号";
create tag index if not exists wechat_index on Wechat();

create tag Car(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    vehicle_modal string null comment "车型",
) comment = "车辆,车牌号码";
create tag index if not exists car_index on Car();

create tag BankCard(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
) comment = "银行卡,银行卡号码";
create tag index if not exists bankcard_index on BankCard();

create tag Place(
    location string null comment "地址经纬度",
    address string not null comment "地址",
    type string not null default 'unknown' comment "场所类型"
) comment = "地址,主键为地址的hash值";
create tag index if not exists place_index on Place();

create edge Relation(
    relation_type string not null default '[]' comment "人物关系类型，数组"
) comment = "人物关系";
create edge index if not exists relation_index on Relation();

create edge Call(
    first_call_time timestamp null default now() comment "首次通联时间",
    last_call_time timestamp null default now() comment "最后通联时间",
    call_times int not null default 1 comment "通联次数"
) comment = "通联关系";
create edge index if not exists call_index on Call();

create edge Own(
    obtain_time timestamp null comment "获得时间",
    miss_time timestamp null comment "失去时间"
) comment = "拥有关系";
create EDGE index if not exists own_index on Own() comment = '拥有关系索引';

create tag Imei(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    times int default 1 comment "捕获次数"
) comment = "imei机身码";
create tag index if not exists imei_index on Imei();

create tag Imsi(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    times int default 1 comment "捕获次数"
) comment = "sim卡编号";
create tag index if not exists imsi_index on Imsi();

create tag Mac(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    times int default 1 comment "捕获次数"
) comment = "无符号小写mac地址";
create tag index if not exists mac_index on Mac();

create edge Catch(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    times int default 1 comment "捕获次数"
) comment = "捕获关系，描述手机号码、imei、imsi、Mac等信息被同时采集的关系";
create edge index if not exists Catch_index on Catch();

create edge Foothold(
    first_capture_time timestamp null default now() comment "首次捕获时间",
    last_capture_time timestamp null default now() comment "最后捕获时间",
    times int default 1 comment "捕获次数"
) comment = "落脚点关系";
create edge index if not exists Foothold_index on Foothold();