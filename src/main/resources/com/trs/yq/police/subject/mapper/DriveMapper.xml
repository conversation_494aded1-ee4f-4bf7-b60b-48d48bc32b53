<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.trs.yq.police.subject.mapper.DriveMapper">

    <select id="customSql" resultType="com.trs.yq.police.subject.domain.vo.DrivingNoLicenseAnalyzeVO">
        ${customSql}
    </select>

    <select id="timingAnalyze" resultType="com.trs.yq.police.subject.domain.vo.DrivingNoLicenseAnalyzeVO">
        -- 驾驶证排重与码表翻译
        -- 字典准备
        with dict as (select dmz,d.dmsm1 as dmmc from mpp_new.public.jj_dmb d where d.dmlb = '2005'),
            -- 驾照数据去重
             t_distincted as (
                 select
                     *
                 from
                     (
                         select
                             xm,
                             sfzmhm,
                             zjcx,
                             zt,
                             row_number() over(partition by sfzmhm order by gxsj desc) as rn
                         from
                             mpp_new.public.jj_jsrxxb_lzyz )
                 where
                     rn = 1),
            -- 状态拆分字符数组
             t_array as (
                 select
                     j.xm,
                     j.sfzmhm,
                     j.zjcx,
                     j.zt,
                     transform(sequence(1,length(j.zt)), i->substr(j.zt,	i,1)) as zts
                 from
                     t_distincted j
             ),
            -- 拆分多行
             t_splited as (
                 select
                     xm,
                     sfzmhm,
                     zjcx,
                     zt as joined_zt,
                     splited_zt
                 from
                     t_array,
                     unnest(zts) as t(splited_zt)),
            -- 翻译码表
             t_splited_translated as (
                 select
                     t.xm,
                     t.sfzmhm,
                     t.zjcx,
                     t.joined_zt,
                     d.dmmc ztsm
                 from
                     t_splited t
                         left join dict d on
                         t.splited_zt = d.dmz),
             -- 多行合并一行
             t_jsz as (
                 select
                     xm,
                     sfzmhm,
                     zjcx,
                     joined_zt,
                     array_join(array_agg(ztsm),	',') as ztsm
                 from
                     t_splited_translated
                 group by
                     xm,
                     sfzmhm,
                     zjcx,
                     joined_zt),
             -- 轨迹按日排重
             t_gj as (
                 select
                     *
                 from
                     (
                         select
                             t.personid,
                             t.realname,
                             t.plateno,
                             parse_datetime(t.shottime,
                                            'yyyy-MM-dd HH:mm:ss') as shottime,
                             t.urlhost,
                             t.faceimageurl,
                             t.pictureurl,
                             t.cameraname,
                             t.longtitude,
                             t.latitude,
                             t.crossingindexcodes,
                             row_number() over(partition by personid,
		format_datetime(parse_datetime(shottime,
		'yyyy-MM-dd HH:mm:ss'),
		'yyyy-MM-dd')
	order by
		shottime asc nulls last) as rn
                         from
                             mpp_new.public.dsj_yt_wzjssbdaxx t
                         where
                             t.isdriver = '1'
                           -- 排除已经标记过的数据
                           and t.personid not in (
                             select
                                 tpw.WARNING_KEY
                             from
                                 yq_oracle.trs_bigdata.t_ps_warning tpw
                             where
                                 tpw.subject_id = '4'
                               and tpw.warning_type = '58'
                               and tpw.JUDGE_RESULT = '3')
                           and not exists (
                                 select
                                     1
                                 from
                                     t_jsz
                                 where
                                     t_jsz.sfzmhm = t.personid
                           -- 按照XMKFB-3028要求去除相关条件
                           --        and t_jsz.joined_zt in ('C', 'D', 'M', 'K', 'N', 'J', 'L', 'H', 'P', 'B', 'S', 'A', 'I', 'U', 'T')
                                            )
                           -- 时间范围限定
                           and parse_datetime(t.shottime, 'yyyy-MM-dd HH:mm:ss') between (
                             select
                                 coalesce((select max(tw.warning_time) from yq_oracle.trs_bigdata.t_ps_warning tw
                                           where
                                               tw.subject_id = '4'
                                             and tw.warning_type = '58') ,
                                          date_add('day',	-7,	current_date)))
                             and current_date
                     )
                 where
                     rn = 1)
        select
            -- 预警病号
            t.personid || '-' || format_datetime(t.shottime,
                                                 'yyyyMMdd') as yjbh,
            -- 姓名
            t.realname as xm ,
            -- 证件号码
            t.personid as zjhm,
            -- 车牌号
            t.plateno as hphm,
            -- 准驾车型
            j.zjcx ,
            -- 驾驶状态
            j.ztsm as jszzt,
            -- 违法时间
            t.shottime as wfsj,
            -- 违法地点
            t.cameraname as wfdd ,
            -- 违法地点坐标
            t.longtitude || ',' || t.latitude as wfdd_zt,
            -- 违法地管辖单位代码
            case
                -- 泸县
                when t.crossingindexcodes like '510521%' then '510521230000'
                -- 合江
                when t.crossingindexcodes like '510522%' then '510522190000'
                -- 叙永
                when t.crossingindexcodes like '510524%' then '510524180000'
                -- 古蔺
                when t.crossingindexcodes like '510525%' then '510525200000'
                when ST_contains(
                        ST_GeometryFromText('POLYGON((105.332507 28.872501,105.333097 28.872113,105.333705 28.872231,105.334042 28.872214,105.334228 28.871691,105.334717 28.871421,105.335494 28.871607,105.335544 28.871117,105.335578 28.870459,105.336202 28.87024,105.336658 28.870105,105.337063 28.86948,105.337232 28.869025,105.336945 28.868772,105.337468 28.868451,105.338042 28.86813,105.338143 28.867827,105.3387 28.867641,105.339172 28.868012,105.339594 28.868316,105.339712 28.868704,105.339982 28.869143,105.340353 28.869345,105.340927 28.869312,105.341501 28.869143,105.342142 28.869514,105.343357 28.869767,105.343981 28.869818,105.343728 28.86916,105.343576 28.868451,105.343677 28.867405,105.343407 28.866392,105.343593 28.866004,105.343964 28.865701,105.344589 28.865734,105.344808 28.865228,105.344572 28.864789,105.344386 28.864452,105.343779 28.864131,105.342952 28.863996,105.342446 28.864131,105.341906 28.864081,105.341383 28.863625,105.340471 28.863203,105.339982 28.862815,105.339206 28.862697,105.338919 28.863051,105.338582 28.862883,105.338227 28.862849,105.33821 28.862427,105.33902 28.862056,105.33897 28.861752,105.338649 28.8616,105.33794 28.861955,105.337502 28.862022,105.337282 28.861803,105.33794 28.86133,105.338565 28.860942,105.338295 28.86079,105.337991 28.860824,105.338058 28.86052,105.337687 28.860335,105.337569 28.85993,105.33762 28.859575,105.337097 28.859727,105.336455 28.860233,105.336084 28.860622,105.33546 28.860942,105.335021 28.860993,105.334565 28.86133,105.333958 28.861313,105.333739 28.861499,105.333435 28.861634,105.333283 28.861482,105.333553 28.861195,105.334329 28.86106,105.335089 28.860335,105.335375 28.860166,105.335612 28.860368,105.336017 28.860132,105.336304 28.85966,105.336962 28.859305,105.337198 28.859052,105.337147 28.858445,105.336692 28.858175,105.336995 28.857787,105.337383 28.856876,105.337316 28.856336,105.337974 28.856319,105.33924 28.855796,105.339459 28.855205,105.33902 28.853703,105.3387 28.852151,105.338835 28.851678,105.339611 28.850784,105.339425 28.850295,105.339172 28.850177,105.338953 28.84935,105.33897 28.84827,105.338126 28.847156,105.338278 28.846042,105.338851 28.844962,105.339408 28.844254,105.3401 28.844136,105.340809 28.844203,105.341298 28.843849,105.341383 28.843275,105.340623 28.842971,105.341113 28.842685,105.342125 28.842836,105.342901 28.842398,105.342986 28.842026,105.343931 28.84179,105.343998 28.840643,105.344386 28.83963,105.343509 28.838112,105.342749 28.837116,105.341298 28.83607,105.340657 28.835125,105.340505 28.834028,105.340421 28.833252,105.339898 28.832273,105.339864 28.831733,105.340455 28.83116,105.341062 28.830653,105.341956 28.830552,105.341956 28.829557,105.342243 28.829303,105.342935 28.829033,105.343475 28.829101,105.343897 28.829573,105.34442 28.829928,105.347018 28.829523,105.348824 28.82927,105.350579 28.832324,105.351254 28.833623,105.355017 28.837842,105.357717 28.840305,105.361986 28.844676,105.364382 28.846346,105.367925 28.849265,105.370693 28.851375,105.373241 28.853636,105.37643 28.855711,105.378134 28.856724,105.379028 28.858023,105.379822 28.859069,105.381846 28.861415,105.383416 28.862292,105.384614 28.862832,105.386723 28.863186,105.388697 28.863203,105.390941 28.86322,105.39381 28.863237,105.401083 28.863439,105.408018 28.863693,105.411258 28.86403,105.414599 28.864233,105.416927 28.8643,105.420252 28.864941,105.422141 28.86511,105.425331 28.865532,105.427879 28.866224,105.431118 28.867202,105.433801 28.867776,105.436062 28.867877,105.43861 28.868215,105.440737 28.868535,105.442525 28.868687,105.445748 28.869143,105.448111 28.869598,105.450355 28.87078,105.45162 28.871792,105.453662 28.873665,105.454438 28.874863,105.455299 28.876382,105.455906 28.878137,105.456126 28.880482,105.456362 28.883385,105.456463 28.885359,105.456362 28.887671,105.455974 28.889004,105.454911 28.892547,105.454371 28.895551,105.454185 28.898858,105.453881 28.901237,105.452869 28.903937,105.451198 28.904865,105.449275 28.904831,105.447959 28.904055,105.446879 28.902739,105.444786 28.90068,105.44347 28.899415,105.442221 28.897964,105.439471 28.895247,105.437952 28.89334,105.436062 28.891805,105.434358 28.890084,105.432063 28.888143,105.430528 28.886996,105.428385 28.886034,105.426984 28.885443,105.423677 28.884971,105.418564 28.884929,105.41348 28.886002,105.40914 28.887556,105.405765 28.888574,105.404587 28.890609,105.405872 28.892752,105.407533 28.894413,105.409462 28.895592,105.410855 28.89677,105.411069 28.898967,105.41048 28.900788,105.408497 28.902663,105.405872 28.904324,105.401587 28.905556,105.397837 28.906306,105.39414 28.907002,105.389587 28.907163,105.385194 28.907377,105.382301 28.907967,105.378015 28.908342,105.37598 28.90952,105.374212 28.911127,105.372497 28.912413,105.371694 28.913967,105.371212 28.915734,105.372765 28.918895,105.374908 28.921252,105.377265 28.925217,105.377533 28.927734,105.377051 28.92902,105.375819 28.929663,105.374587 28.929824,105.373247 28.930092,105.37239 28.930038,105.372122 28.929127,105.371533 28.924252,105.370783 28.920663,105.369444 28.91852,105.367301 28.916484,105.365319 28.915145,105.363819 28.913913,105.36264 28.913217,105.361087 28.911931,105.35964 28.910699,105.35889 28.910217,105.357765 28.908609,105.357176 28.907109,105.356533 28.904913,105.35589 28.902234,105.355247 28.899717,105.354337 28.897092,105.353624 28.896012,105.353405 28.895792,105.353017 28.895725,105.352763 28.895556,105.35224 28.895404,105.351649 28.894999,105.351008 28.89478,105.350518 28.894948,105.350062 28.895218,105.349589 28.895472,105.349201 28.895404,105.348779 28.895134,105.34834 28.894712,105.3478 28.894273,105.347175 28.894104,105.346534 28.894138,105.345791 28.893952,105.345369 28.893598,105.34466 28.893125,105.3439 28.893024,105.343309 28.893547,105.342767 28.893689,105.342585 28.893957,105.34214 28.894134,105.34156 28.89422,105.340761 28.894247,105.339973 28.89423,105.339291 28.8943,105.338326 28.894466,105.337864 28.894627,105.337752 28.894515,105.337661 28.894181,105.337669 28.893979,105.337926 28.893891,105.33799 28.893778,105.337886 28.893585,105.337902 28.893312,105.338047 28.893271,105.338328 28.8934,105.338369 28.893641,105.33861 28.893762,105.338924 28.893706,105.339141 28.893585,105.339214 28.893392,105.339181 28.893207,105.339318 28.893126,105.3396 28.893183,105.339777 28.893151,105.339769 28.892893,105.339632 28.892652,105.339415 28.892491,105.339197 28.892451,105.338964 28.892555,105.338811 28.892595,105.338513 28.892475,105.338264 28.892314,105.338208 28.89204,105.338296 28.891871,105.338127 28.89167,105.337878 28.891614,105.337948 28.891404,105.338334 28.891461,105.33843 28.891332,105.338318 28.891211,105.337964 28.891091,105.337441 28.891131,105.337296 28.89105,105.337328 28.890825,105.337328 28.890551,105.337046 28.890568,105.336692 28.890656,105.336378 28.890624,105.336362 28.890415,105.336547 28.890262,105.336652 28.890141,105.33658 28.889835,105.336491 28.889393,105.336547 28.889079,105.336668 28.888942,105.336797 28.889095,105.336982 28.889071,105.337111 28.888934,105.337207 28.888669,105.337167 28.888379,105.337111 28.888025,105.337014 28.887719,105.336901 28.887566,105.336676 28.887405,105.336668 28.887099,105.336322 28.886713,105.336081 28.886745,105.335944 28.886697,105.335863 28.886472,105.33567 28.886448,105.335517 28.886287,105.335534 28.886102,105.335815 28.885933,105.335984 28.885852,105.336201 28.885884,105.336217 28.886174,105.336258 28.88648,105.336322 28.886657,105.336459 28.886665,105.33666 28.886625,105.336789 28.886464,105.336644 28.886335,105.336539 28.886206,105.336555 28.885868,105.336547 28.885659,105.336386 28.885506,105.335888 28.885281,105.335646 28.885015,105.335703 28.884669,105.336024 28.884364,105.336032 28.884042,105.336081 28.883816,105.336378 28.883688,105.336306 28.883462,105.336443 28.883342,105.336636 28.883342,105.336805 28.883366,105.336982 28.883301,105.337087 28.883092,105.337191 28.882714,105.337239 28.882505,105.33736 28.882553,105.337481 28.882633,105.33769 28.882473,105.338004 28.882215,105.337891 28.882038,105.337714 28.881821,105.337682 28.881587,105.337811 28.881257,105.337859 28.880952,105.337569 28.880686,105.337247 28.880469,105.336966 28.880308,105.3367 28.880163,105.336346 28.880203,105.336073 28.880284,105.335582 28.88038,105.335284 28.880461,105.335139 28.880606,105.335011 28.880614,105.334842 28.880533,105.33485 28.880388,105.335019 28.880083,105.33489 28.87964,105.33456 28.879608,105.334327 28.879761,105.334174 28.880018,105.334069 28.880203,105.333828 28.880227,105.333449 28.880268,105.333232 28.880123,105.333087 28.880018,105.33291 28.880091,105.332822 28.880268,105.332774 28.880469,105.332782 28.881209,105.332733 28.881628,105.332669 28.881845,105.332524 28.881901,105.332259 28.881821,105.332057 28.881692,105.331719 28.88162,105.331478 28.881531,105.331164 28.881507,105.33085 28.881515,105.330722 28.881338,105.330569 28.881088,105.330545 28.880799,105.330416 28.880405,105.330456 28.880131,105.3307 28.879865,105.331054 28.879833,105.331481 28.879873,105.331666 28.880018,105.331835 28.88026,105.332092 28.880356,105.332318 28.880107,105.332479 28.879801,105.332833 28.87968,105.333026 28.879471,105.33342 28.879503,105.333581 28.879535,105.333919 28.879246,105.334257 28.879061,105.334555 28.878948,105.33478 28.879004,105.334941 28.878843,105.335142 28.878538,105.335086 28.87828,105.335214 28.878047,105.334876 28.877717,105.334635 28.877564,105.334595 28.877427,105.334732 28.877315,105.334901 28.877226,105.334997 28.87696,105.334941 28.876816,105.334884 28.876679,105.334828 28.876421,105.334579 28.876341,105.334289 28.876285,105.333935 28.876132,105.333685 28.876075,105.33342 28.87618,105.333235 28.87618,105.33301 28.876075,105.3328 28.875963,105.332623 28.875866,105.33239 28.875971,105.332092 28.876011,105.331875 28.875931,105.33169 28.87581,105.331537 28.875584,105.331352 28.875432,105.331062 28.875279,105.330966 28.875134,105.331062 28.874941,105.331078 28.874466,105.331191 28.874015,105.33136 28.87371,105.331609 28.873476,105.331996 28.873122,105.332318 28.872655,105.332417 28.872631,105.332507 28.872501))'),
                        ST_GeometryFromText('POINT(' || t.longtitude || ' ' || t.latitude || ')' )
                    ) then '510500260800'
                when ST_contains(
                        ST_GeometryFromText('POLYGON((105.39086 28.855867,105.380159 28.85474,105.370161 28.848826,105.366077 28.844038,105.358896 28.837702,105.352982 28.830239,105.351151 28.823761,105.354953 28.814327,105.356502 28.812074,105.358755 28.810102,105.364106 28.812637,105.378187 28.816439,105.377483 28.832773,105.380018 28.835026,105.384805 28.832351,105.393254 28.833618,105.402407 28.833759,105.426632 28.82074,105.427592 28.818605,105.432074 28.815724,105.440187 28.81243,105.444958 28.810979,105.451135 28.803349,105.464943 28.809707,105.485109 28.812796,105.509272 28.811887,105.526169 28.815521,105.538926 28.816391,105.543247 28.831509,105.548334 28.843318,105.531074 28.860396,105.533708 28.868996,105.530273 28.875867,105.528326 28.877127,105.52134 28.872546,105.51424 28.871057,105.503589 28.869568,105.491908 28.873118,105.483433 28.886861,105.481939 28.889315,105.480379 28.897449,105.47804 28.902797,105.471689 28.908368,105.467232 28.909594,105.461995 28.908814,105.457427 28.903466,105.45687 28.896446,105.456535 28.891321,105.458207 28.88965,105.458541 28.887644,105.459878 28.886196,105.45843 28.884079,105.45843 28.884079,105.457315 28.877728,105.454753 28.870151,105.448402 28.867589,105.434697 28.86614,105.39086 28.855867))'),
                        ST_GeometryFromText('POINT(' || t.longtitude || ' ' || t.latitude || ')' )
                    ) then '510500260900'
                when ST_contains(
                        ST_GeometryFromText('POLYGON((105.383918 28.947035,105.383787 28.94561,105.384153 28.93967,105.385652 28.930046,105.385265 28.925815,105.383282 28.922648,105.378447 28.91479,105.378616 28.910776,105.382605 28.909083,105.388336 28.908431,105.392253 28.908479,105.395589 28.907246,105.397911 28.907343,105.404511 28.90698,105.409782 28.903716,105.411934 28.900403,105.410073 28.899122,105.412708 28.898445,105.412007 28.896075,105.40896 28.893875,105.406373 28.890562,105.40751 28.888797,105.412515 28.887854,105.419212 28.886887,105.424 28.886259,105.429537 28.887443,105.433333 28.890683,105.436452 28.890853,105.436186 28.893077,105.44107 28.898106,105.442907 28.89813,105.443536 28.900911,105.448178 28.905843,105.451564 28.909712,105.456883 28.913919,105.460703 28.913484,105.466168 28.913822,105.470568 28.913726,105.474703 28.912082,105.478692 28.909277,105.484302 28.903305,105.486018 28.901056,105.486502 28.899968,105.488049 28.895858,105.489258 28.892835,105.490105 28.888676,105.49274 28.88459,105.494288 28.880649,105.501203 28.876587,105.507634 28.875717,105.511745 28.876176,105.517862 28.877723,105.524245 28.879634,105.525163 28.879914,105.526747 28.880188,105.529291 28.881818,105.532276 28.883402,105.536222 28.887258,105.537479 28.891852,105.537286 28.893593,105.52157 28.902055,105.520023 28.901772,105.518785 28.902794,105.517476 28.903887,105.516365 28.906362,105.513962 28.909052,105.506131 28.913181,105.500799 28.920478,105.493253 28.92398,105.491514 28.924409,105.486657 28.932926,105.484691 28.937512,105.487131 28.944584,105.495061 28.950209,105.496077 28.952852,105.489142 28.950051,105.479427 28.949034,105.477462 28.949848,105.473079 28.953214,105.47039 28.95719,105.468109 28.957709,105.463568 28.957235,105.457988 28.957597,105.452611 28.958026,105.449357 28.958252,105.450961 28.962431,105.451662 28.967672,105.452317 28.970496,105.448725 28.971039,105.44206 28.967944,105.431171 28.970564,105.428889 28.97018,105.405959 28.958432,105.419107 28.949938,105.411109 28.942844,105.398684 28.942573,105.390099 28.945013,105.383918 28.947035))'),
                        ST_GeometryFromText('POINT(' || t.longtitude || ' ' || t.latitude || ')' )
                    ) then '510500261000'
                when ST_contains(
                        ST_GeometryFromText('POLYGON((105.277722 28.738682,105.289431 28.731722,105.316 28.723562,105.347581 28.722535,105.376244 28.722364,105.386885 28.736608,105.395576 28.742733,105.392745 28.753556,105.385432 28.76478,105.386783 28.766186,105.391919 28.767429,105.398136 28.765916,105.401164 28.763375,105.40484 28.761051,105.409705 28.760997,105.41376 28.760078,105.421382 28.760564,105.417648 28.766084,105.420761 28.773904,105.420274 28.782662,105.418707 28.789851,105.426491 28.793905,105.439391 28.800335,105.4495 28.802281,105.451879 28.807471,105.446473 28.81266,105.43512 28.814174,105.432417 28.815201,105.429011 28.816714,105.426902 28.818228,105.424848 28.819958,105.42301 28.821255,105.42101 28.822985,105.413279 28.827201,105.411603 28.82812,105.409387 28.829255,105.404737 28.83158,105.404737 28.832444,105.405278 28.833472,105.406413 28.834607,105.407765 28.835742,105.408522 28.836282,105.408655 28.836665,105.408812 28.83681,105.408571 28.837088,105.408269 28.837305,105.407991 28.837607,105.407738 28.837969,105.40816 28.83862,105.408969 28.839332,105.406881 28.840889,105.405372 28.841674,105.403562 28.840829,105.399733 28.838885,105.39779 28.837231,105.396233 28.836145,105.394253 28.837026,105.390717 28.834033,105.387095 28.833128,105.384718 28.833562,105.383933 28.831245,105.385732 28.830123,105.3883 28.82991,105.386657 28.82294,105.384686 28.820171,105.382738 28.819232,105.384123 28.814961,105.381822 28.80785,105.379825 28.821638,105.378874 28.821244,105.379055 28.816912,105.377249 28.812399,105.375985 28.807795,105.376211 28.803463,105.371111 28.802515,105.368628 28.80098,105.361632 28.791729,105.361677 28.791142,105.367771 28.791593,105.370885 28.791277,105.370479 28.786313,105.368538 28.780762,105.363167 28.777332,105.35247 28.770382,105.346241 28.767493,105.330571 28.76086,105.315399 28.75559,105.30592 28.756357,105.304025 28.760689,105.294095 28.759877,105.29247 28.754552,105.290349 28.749858,105.283398 28.74363,105.282089 28.742276,105.277722 28.738682))'),
                        ST_GeometryFromText('POINT(' || t.longtitude || ' ' || t.latitude || ')' )
                    ) then '510500261100'
                -- 江阳
                when t.crossingindexcodes like '510502%' then '510502010000'
                -- 纳溪
                when t.crossingindexcodes like '510503%' then '510503180000'
                -- 龙马潭
                when t.crossingindexcodes like '510504%' then '510504010000'
                else
                    null
                end as gxdwbh,
            -- 人脸图
            t.urlhost || t.faceimageurl as rlt,
            -- 场景图
            t.urlhost || t.pictureurl as cjt
        from
            t_gj t
                left join t_jsz j on
                t.personid = j.sfzmhm
    </select>
</mapper>