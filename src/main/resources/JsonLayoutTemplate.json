{"date": {"$resolver": "timestamp", "pattern": {"format": "yyyy-MM-dd HH:mm:ss", "timeZone": "UTC", "locale": "zh_CN"}}, "thread": {"thread_name": {"$resolver": "thread", "field": "name"}, "thread_id": {"$resolver": "thread", "field": "id"}}, "level": {"$resolver": "level", "field": "name"}, "logger": {"$resolver": "logger", "field": "name"}, "host": "${hostName}", "message": {"$resolver": "message", "stringified": true}, "exception": {"exception_class": {"$resolver": "exception", "field": "className"}, "exception_message": {"$resolver": "exception", "field": "message", "stringified": true}, "stacktrace": {"$resolver": "exception", "field": "stackTrace", "stacktrace": {"stringified": true}}}, "class": {"$resolver": "source", "field": "className"}, "file": {"$resolver": "source", "field": "fileName"}, "method": {"$resolver": "source", "field": "methodName"}, "line_number": {"$resolver": "source", "field": "lineNumber"}}