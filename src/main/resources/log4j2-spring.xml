<?xml version="1.0" encoding="UTF-8"?>
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出-->
<!--monitorInterval：自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<Configuration monitorInterval="60" status="WARN">

    <!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
    <!-- 变量设置 -->
    <Properties>
        <!-- 格式化输出：%date表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度 %msg：日志消息，%n是换行符-->
        <!-- %logger{36} 表示 Logger 名字最长36个字符 -->
        <property name="LOG_PATTERN"
                  value="%yellow{%d{yyyy-MM-dd HH:mm:ss}} %red{[%thread]} %highlight{%-5level} %cyan{%logger{50}} - %magenta{%msg} %n"/>
        <!--日志编码-->
        <property name="CHARSET" value="utf-8"/>
    </Properties>

    <!--先定义所有的appender-->
    <Appenders>
        <!--输出控制台的配置-->
        <Console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式-->
            <PatternLayout pattern="${LOG_PATTERN}" charset="${CHARSET}"/>
            <!--            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>-->
        </Console>

        <RollingFile name="sqlLog" fileName="${sys:LOG_PATH}/sql/sql.log"
                     filePattern="${sys:LOG_PATH}/$${date:yyyy-MM}/log-%d{yyyy-MM-dd}-%i.log.gz">
            <Filters>
                <ThresholdFilter level="TRACE" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
            <JsonTemplateLayout eventTemplateUri="classpath:JsonLayoutTemplate.json"/>
            <Policies>
                <!-- 每天记录一次 -->
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <!-- 清除策略 -->
            <DefaultRolloverStrategy>
                <Delete basePath="${sys:LOG_PATH}" maxDepth="2">
                    <IfFileName glob="*/*.log.gz"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <!-- 滚动策略 -->
        <RollingFile name="fileInfoLog" fileName="${sys:LOG_PATH}/log.log"
                     filePattern="${sys:LOG_PATH}/$${date:yyyy-MM}/log-%d{yyyy-MM-dd}-%i.log.gz">
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
            <JsonTemplateLayout eventTemplateUri="classpath:JsonLayoutTemplate.json"/>
            <Policies>
                <!-- 每天记录一次 -->
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <!-- 清除策略 -->
            <DefaultRolloverStrategy>
                <Delete basePath="${sys:LOG_PATH}" maxDepth="2">
                    <IfFileName glob="*/*.log.gz"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

        <RollingFile name="fileErrorLog" fileName="${sys:LOG_PATH}/error.log"
                     filePattern="${sys:LOG_PATH}/$${date:yyyy-MM}/error-%d{yyyy-MM-dd}-%i.log.gz">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <JsonTemplateLayout eventTemplateUri="classpath:JsonLayoutTemplate.json"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
            </Policies>
            <!-- 清除策略 -->
            <DefaultRolloverStrategy>
                <Delete basePath="${sys:LOG_PATH}" maxDepth="2">
                    <IfFileName glob="*/*.log.gz"/>
                    <IfLastModified age="30d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>

    </Appenders>

    <Loggers>
        <logger name="org.hibernate" level="info"/>
        <logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="TRACE">
            <Appender-ref ref="sqlLog"/>
        </logger>

        <Root level="INFO">
            <Appender-ref ref="Console"/>
            <Appender-ref ref="fileInfoLog"/>
            <Appender-ref ref="fileErrorLog"/>
        </Root>
    </Loggers>
</Configuration>