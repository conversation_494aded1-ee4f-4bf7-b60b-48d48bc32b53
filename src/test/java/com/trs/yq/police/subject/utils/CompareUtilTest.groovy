package com.trs.yq.police.subject.utils

import com.google.common.collect.ImmutableList
import com.google.common.collect.ImmutableMap
import spock.lang.Specification

/**
 * 比较工具类单元测试
 *
 * <AUTHOR>
 * @date 2021/8/25 17:07
 */
class CompareUtilTest extends Specification {

    def "compare complex object"() {

        expect:
        CompareUtil.equals(newObj, oldObj) == result

        where:
        result << [true, false, true, false, true, false, false, false, true, true]
        newObj << [Collections.singletonList("1"),
                   Collections.singletonMap("1", "2"),
                   "1",
                   ImmutableList.of(ImmutableMap.of("id", "1", "name", "121"), ImmutableMap.of("id", "2", "name", "12")),
                   ImmutableList.of(ImmutableMap.of("id", "1", "name", "111"), ImmutableMap.of("id", "2", "name", "12")),
                   ImmutableList.of(ImmutableMap.of("id", "1", "name", "111"), ImmutableMap.of("id", "2", "name", "12")),
                   null,
                   "1",
                   null,
                   null]
        oldObj << [Collections.singletonList("1"),
                   Collections.singletonList("2"),
                   "1",
                   ImmutableList.of(ImmutableMap.of("id", "1", "name", "111"), ImmutableMap.of("id", "2", "name", "12")),
                   ImmutableList.of(ImmutableMap.of("id", "2", "name", "12"), ImmutableMap.of("id", "1", "name", "111")),
                   ImmutableList.of(ImmutableMap.of("id", "1", "name", "111"), ImmutableMap.of("id", "2", "name", "12"), ImmutableMap.of("id", "1", "name", "111")),
                   "1",
                   null,
                   null,
                   Collections.emptyList()]
    }
}
