package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.config.TestPersistenceConfig;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.MobilityEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.vo.MobilityPageWithTotal;
import com.trs.yq.police.subject.domain.vo.MobilityVO;
import com.trs.yq.police.subject.repository.MobilityRepository;
import com.trs.yq.police.subject.service.MobilityService;
import com.trs.yq.police.subject.service.PersonService;
import com.trs.yq.police.subject.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

/**
 * 流动信息测试类
 *
 * <AUTHOR>
 * @since 2021/8/13
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest(classes = TestPersistenceConfig.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class MobilityTest {

    @Resource
    private MobilityRepository mobilityRepository;

    private MobilityService mobilityService;

    @Mock
    private PersonService personService;

    private final String personId = "testPersonId";

    private final LocalDateTime mobilityTime = LocalDateTime.parse("2021-07-20T15:25:12");

    @BeforeAll
    void initTestData() {
        LoginUser testUser = new LoginUser();
        testUser.setId("6a94ce2b1062402ce055000000000001");
        testUser.setUserName("admin");
        testUser.setIdCard("51132419881210365X");
        try {
            Mockito.mockStatic(AuthHelper.class)
                    .when(AuthHelper::getCurrentUser)
                    .thenReturn(testUser);
        } catch (Exception ignored) {
        }
        when(personService.checkPersonExist(personId)).thenReturn(new PersonEntity());
        this.mobilityService = new MobilityServiceImpl(mobilityRepository, personService, null);
    }

    @Test
    @Order(1)
    void testAdd() {
        MobilityVO mobilityVO = new MobilityVO();
        mobilityVO.setMoveTime(DateUtil.dateTimeToUtc(mobilityTime));
        mobilityVO.setMoveType("1");
        mobilityVO.setLocation("成都");
        mobilityVO.setNote("备注");
        mobilityService.addMovement(personId, mobilityVO);
        final MobilityEntity result = mobilityRepository.findAll().get(0);

        MobilityEntity expect = new MobilityEntity();
        expect.setPersonId(personId);
        expect.setMoveTime(mobilityTime);
        expect.setMoveType("1");
        expect.setLocation("成都");
        expect.setNote("备注");

        assertThat(result).isEqualTo(expect);
    }

    @Test
    @Order(2)
    @Transactional
    @Rollback(value = false)
    void testModify() {
        String id = mobilityRepository.findAll().get(0).getId();
        MobilityVO mobilityVO = new MobilityVO();
        mobilityVO.setId(id);
        mobilityVO.setMoveTime(DateUtil.dateTimeToUtc(mobilityTime));
        mobilityVO.setMoveType("1");
        mobilityVO.setLocation("成都");
        mobilityVO.setNote("修改");
        mobilityService.updateMovement(personId, mobilityVO);
        final MobilityEntity result = mobilityRepository.findAll().get(0);

        MobilityEntity expect = new MobilityEntity();
        expect.setId(id);
        expect.setPersonId(personId);
        expect.setMoveTime(mobilityTime);
        expect.setMoveType("1");
        expect.setLocation("成都");
        expect.setNote("修改");

        assertThat(result).isEqualTo(expect);
    }

    @Test
    @Order(3)
    void testGetPageable() {
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(5);
        final MobilityPageWithTotal result = mobilityService.getMovement(personId, pageParams);

        final int pageNumber = 1;
        final int pageSize = 5;
        final int total = 1;
        final int totalIn = 1;
        MobilityVO mobilityVO = new MobilityVO();
        String id = mobilityRepository.findAll().get(0).getId();
        mobilityVO.setId(id);
        mobilityVO.setMoveTime(DateUtil.dateTimeToUtc(mobilityTime));
        mobilityVO.setMoveType("1");
        mobilityVO.setLocation("成都");
        mobilityVO.setNote("修改");
        List<MobilityVO> items = new ArrayList<>();
        items.add(mobilityVO);

        assertThat(result.getItems()).isEqualTo(items);
        assertThat(result.getPageNumber()).isEqualTo(pageNumber);
        assertThat(result.getPageSize()).isEqualTo(pageSize);
        assertThat(result.getTotal()).isEqualTo(total);
        assertThat(result.getTotalIn()).isEqualTo(totalIn);
    }

    @Test
    @Order(4)
    void testDelete() {
        String id = mobilityRepository.findAll().get(0).getId();
        mobilityService.deleteMovement(personId, id);
        assertThat(mobilityRepository.findAll().size()).isZero();
    }
}
