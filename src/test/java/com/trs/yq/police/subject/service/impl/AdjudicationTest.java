package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.config.TestPersistenceConfig;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.AdjudicationEntity;
import com.trs.yq.police.subject.domain.entity.PersonEntity;
import com.trs.yq.police.subject.domain.vo.AdjudicationVO;
import com.trs.yq.police.subject.exception.ParamValidationException;
import com.trs.yq.police.subject.repository.AdjudicationRepository;
import com.trs.yq.police.subject.service.AdjudicationService;
import com.trs.yq.police.subject.service.PersonService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.*;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

/**
 * 裁决信息测试类
 *
 * <AUTHOR>
 * @since 2021/8/12
 */
@Slf4j
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@SpringBootTest(classes = TestPersistenceConfig.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AdjudicationTest {

    @Resource
    private AdjudicationRepository adjudicationRepository;

    private AdjudicationService adjudicationService;

    @Mock
    private PersonService personService;

    private final String personId = "testPersonId";

    private final LocalDate adjudgeDate = LocalDate.parse("2021-08-06");

    private final LocalDate closingDate = LocalDate.parse("2021-12-31");

    @BeforeAll
    void initTestData() {
        LoginUser testUser = new LoginUser();
        testUser.setId("6a94ce2b1062402ce055000000000001");
        testUser.setUserName("admin");
        testUser.setIdCard("51132419881210365X");
        try {
            Mockito.mockStatic(AuthHelper.class)
                    .when(AuthHelper::getCurrentUser)
                    .thenReturn(testUser);
        } catch (Exception ignored) {
        }
        when(personService.checkPersonExist(personId)).thenReturn(new PersonEntity());
        this.adjudicationService = new AdjudicationServiceImpl(adjudicationRepository, personService, null);
    }

    @Test
    @Order(1)
    void testAdd() {
        AdjudicationVO adjudicationVO = new AdjudicationVO();
        adjudicationVO.setJudgementDate(adjudgeDate);
        adjudicationVO.setEndDate(closingDate);
        adjudicationVO.setLimitTime(3);
        adjudicationVO.setLimitUnit("MONTHS");
        adjudicationVO.setReason("reason");

        adjudicationService.saveJudgement(personId, adjudicationVO);
        List<AdjudicationEntity> list = adjudicationRepository.findAllByPersonIdOrderByJudgementDateDesc(personId);
        AdjudicationEntity entity = new AdjudicationEntity(
                personId,
                adjudgeDate,
                closingDate,
                3,
                "MONTHS",
                "reason"
        );
        assertThat(list.get(0)).isEqualTo(entity);
    }

    @Test
    void testException() {
        AdjudicationVO vo = new AdjudicationVO();
        vo.setJudgementDate(adjudgeDate);
        vo.setEndDate(closingDate);
        vo.setLimitTime(2);
        vo.setLimitUnit("MONTHS");
        vo.setReason("reason");
        assertThatThrownBy(() -> adjudicationService.saveJudgement(personId, vo)).isInstanceOf(ParamValidationException.class).hasMessage("截止日期与限制年限不匹配，请核实！");
    }

    @Test
    @Order(2)
    @Transactional
    @Rollback(value = false)
    void testModify() {
        String id = adjudicationRepository.findAllByPersonIdOrderByJudgementDateDesc(personId).get(0).getId();
        AdjudicationVO adjudicationVO = new AdjudicationVO();
        adjudicationVO.setId(id);
        adjudicationVO.setJudgementDate(adjudgeDate);
        adjudicationVO.setEndDate(closingDate);
        adjudicationVO.setLimitTime(3);
        adjudicationVO.setLimitUnit("MONTHS");
        adjudicationVO.setReason("modified");
        adjudicationService.updateJudgement(personId, adjudicationVO);
        AdjudicationEntity entity = adjudicationRepository.getById(id);
        AdjudicationEntity expect = new AdjudicationEntity(
                personId,
                adjudgeDate,
                closingDate,
                3,
                "MONTHS",
                "modified"
        );
        expect.setId(id);
        assertThat(entity).isEqualTo(expect);
    }

    @Test
    @Order(3)
    void testFind() {
        final List<AdjudicationVO> result = adjudicationService.getJudgement(personId);
        final List<AdjudicationVO> list = new ArrayList<>();
        AdjudicationVO adjudicationVO = new AdjudicationVO();
        adjudicationVO.setJudgementDate(adjudgeDate);
        adjudicationVO.setEndDate(closingDate);
        adjudicationVO.setLimitTime(3);
        adjudicationVO.setLimitUnit("MONTHS");
        adjudicationVO.setReason("modified");
        list.add(adjudicationVO);
        assertThat(result).usingRecursiveComparison().ignoringFields("id").isEqualTo(list);
    }

    @Test
    @Order(4)
    void testDelete() {
        String id = adjudicationRepository.findAllByPersonIdOrderByJudgementDateDesc(personId).get(0).getId();
        adjudicationService.deleteJudgement(personId, id);
        assertThat(adjudicationRepository.findAllByPersonIdOrderByJudgementDateDesc(personId).size()).isZero();
    }
}
