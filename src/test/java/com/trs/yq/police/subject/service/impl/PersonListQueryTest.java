package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.auth.AuthHelper;
import com.trs.yq.police.subject.config.TestPersistenceConfig;
import com.trs.yq.police.subject.domain.LoginUser;
import com.trs.yq.police.subject.domain.entity.*;
import com.trs.yq.police.subject.domain.params.PageParams;
import com.trs.yq.police.subject.domain.params.SearchParams;
import com.trs.yq.police.subject.domain.params.SortParams;
import com.trs.yq.police.subject.domain.vo.KeyValueVO;
import com.trs.yq.police.subject.domain.vo.ListRequestVO;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.repository.*;
import com.trs.yq.police.subject.service.PersonService;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest(classes = {TestPersistenceConfig.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class PersonListQueryTest {

    @Resource
    private PersonRepository personRepository;

    @Resource
    private PersonSubjectRelationRepository personSubjectRelationRepository;

    @Resource
    private GroupRepository groupRepository;

    @Resource
    private PersonGroupRelationRepository personGroupRelationRepository;

    @Resource
    private ControlRepository controlRepository;

    @Resource
    private MobilePhoneRepository mobilePhoneRepository;

    @Resource
    private VirtualIdentityRepository virtualIdentityRepository;

    @Resource
    private PersonService personService;

    @Resource
    private LabelRepository groupTypeRepository;

    @Resource
    private GroupLabelRelationRepository groupLabelRelationRepository;

    private List<GroupEntity> groupList;

    private List<LabelEntity> groupTypeList;

    private final List<PersonEntity> personList = new ArrayList<>();

    @BeforeAll
    public void generateTestData() {
        LoginUser testUser = new LoginUser();
        testUser.setId("6a94ce2b1062402ce055000000000001");
        testUser.setUserName("admin");
        testUser.setIdCard("51132419881210365X");
        try {
            Mockito.mockStatic(AuthHelper.class)
                    .when(AuthHelper::getCurrentUser)
                    .thenReturn(testUser);
        } catch (Exception ignored) {
        }

        GroupEntity group1 = new GroupEntity();
        group1.setSubjectId("1");
        group1.setName("group1");
        LabelEntity groupType1 = new LabelEntity();
        groupType1.setSubjectId("1");
        groupType1.setName("个人极端行为群体");
        GroupEntity group2 = new GroupEntity();
        group2.setSubjectId("1");
        group2.setName("group2");
        LabelEntity groupType2 = new LabelEntity();
        groupType2.setSubjectId("1");
        groupType2.setName("恐怖行为群体");
        GroupEntity group3 = new GroupEntity();
        group3.setSubjectId("1");
        group3.setName("group3");
        LabelEntity groupType3 = new LabelEntity();
        groupType3.setSubjectId("1");
        groupType3.setName("其他群体");
        groupList = Arrays.asList(group1, group2, group3);
        groupRepository.saveAll(groupList);
        groupTypeList = Arrays.asList(groupType1, groupType2, groupType3);
        groupTypeRepository.saveAll(groupTypeList);
        GroupLabelRelationEntity relation1 = new GroupLabelRelationEntity();
        relation1.setGroupId(group1.getId());
        relation1.setLabelId(groupType1.getId());
        GroupLabelRelationEntity relation2 = new GroupLabelRelationEntity();
        relation2.setGroupId(group2.getId());
        relation2.setLabelId(groupType2.getId());
        GroupLabelRelationEntity relation3 = new GroupLabelRelationEntity();
        relation3.setGroupId(group3.getId());
        relation3.setLabelId(groupType3.getId());
        List<GroupLabelRelationEntity> relations = Arrays.asList(relation1, relation2, relation3);
        groupLabelRelationRepository.saveAll(relations);

        for (int i = 0; i < 40; i++) {
            PersonEntity person = createPersonEntity(i);
            personList.add(person);
        }
        personRepository.saveAll(personList);

        createPersonSubject1Relations();
        createPersonSubject2Relations();
        createPersonGroup1Relations();
        createPersonGroup2Relations();
        createPersonControl();
        createMobilePhoneEntities();
        createVirtualIdentityEntities();
    }

    // write test cases here

    @Test
    @DisplayName("默认参数")
    void emptyParams() {
        ListRequestVO request = new ListRequestVO();
        request.setFilterParams(Collections.emptyList());
        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams);

        PageResult<PersonEntity> personList = personService.getGroupPersonList("1", request);
        assertThat(personList.getTotal()).isEqualTo(20);
        assertThat(personList.getItems()).hasSize(10);
    }

    @Test
    @DisplayName("管控状态筛选条件")
    void controlStatusFilter() {
        final ListRequestVO request = new ListRequestVO();

        List<KeyValueVO> filterParams = new ArrayList<>();
        KeyValueVO param = new KeyValueVO();
        param.setKey("controlStatus");
        param.setValue("1");
        filterParams.add(param);
        request.setFilterParams(filterParams);

        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams);

        SortParams sortParams = new SortParams();
        sortParams.setSortField("idNumber");
        sortParams.setSortDirection("ASC");
        request.setSortParams(sortParams);

        PageResult<PersonEntity> result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(Arrays.asList(personList.get(0), personList.get(3), personList.get(6), personList.get(9), personList.get(12), personList.get(15), personList.get(18)));
    }

    @Test
    @DisplayName("是否关联群体筛选条件")
    void groupStatusFilter() {
        final ListRequestVO request = new ListRequestVO();

        List<KeyValueVO> filterParams = new ArrayList<>();
        KeyValueVO param = new KeyValueVO();
        param.setKey("groupStatus");
        param.setValue("1");
        filterParams.add(param);
        request.setFilterParams(filterParams);

        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams);

        SortParams sortParams = new SortParams();
        sortParams.setSortField("idNumber");
        sortParams.setSortDirection("ASC");
        request.setSortParams(sortParams);

        PageResult<PersonEntity> result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 10));

        pageParams.setPageNumber(2);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(10, 12));
    }

    @Test
    @DisplayName("群体类别筛选条件")
    void groupTypeFilter() {
        final ListRequestVO request = new ListRequestVO();

        List<KeyValueVO> filterParams = new ArrayList<>();
        KeyValueVO param = new KeyValueVO();
        param.setKey("groupType");
        param.setValue(groupTypeList.get(0).getId());
        filterParams.add(param);
        request.setFilterParams(filterParams);

        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams);

        SortParams sortParams = new SortParams();
        sortParams.setSortField("idNumber");
        sortParams.setSortDirection("ASC");
        request.setSortParams(sortParams);

        PageResult<PersonEntity> result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 6));

        filterParams.clear();
        param.setKey("groupType");
        param.setValue(groupTypeList.get(1).getId());
        filterParams.add(param);
        request.setFilterParams(filterParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(6, 12));
    }

    @Test
    @DisplayName("管辖部门筛选")
    void controlDepartmentFilter() {
        final ListRequestVO request = new ListRequestVO();

        List<KeyValueVO> filterParams = new ArrayList<>();
        KeyValueVO param = new KeyValueVO();
        param.setKey("department");
        param.setValue("510522040000");
        filterParams.add(param);
        request.setFilterParams(filterParams);

        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams

        );

        SortParams sortParams = new SortParams();
        sortParams.setSortField("idNumber");
        sortParams.setSortDirection("ASC");
        request.setSortParams(sortParams);

        PageResult<PersonEntity> result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 1));

        filterParams.clear();
        param.setKey("department");
        param.setValue("510522000000");
        filterParams.add(param);
        request.setFilterParams(filterParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 1));

        filterParams.clear();
        param.setKey("department");
        param.setValue("510522000000");
        filterParams.add(param);
        request.setFilterParams(filterParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 1));

        filterParams.clear();
        param.setKey("department");
        param.setValue("510521000000");
        filterParams.add(param);
        request.setFilterParams(filterParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEmpty();
    }

    @Test
    @DisplayName("模糊搜索")
    public void searchFilterTest() {
        ListRequestVO request = new ListRequestVO();
        request.setFilterParams(Collections.emptyList());

        SearchParams searchParams = new SearchParams();
        searchParams.setSearchField("qq");
        searchParams.setSearchValue("12345");
        request.setSearchParams(searchParams);

        PageParams pageParams = new PageParams();
        pageParams.setPageNumber(1);
        pageParams.setPageSize(10);
        request.setPageParams(pageParams);

        SortParams sortParams = new SortParams();
        sortParams.setSortField("idNumber");
        sortParams.setSortDirection("ASC");
        request.setSortParams(sortParams);

        PageResult<PersonEntity> result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 1));

        searchParams = new SearchParams();
        searchParams.setSearchField("qq");
        searchParams.setSearchValue("1234");
        request.setSearchParams(searchParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEqualTo(personList.subList(0, 2));

        searchParams = new SearchParams();
        searchParams.setSearchField("qq");
        searchParams.setSearchValue("9999");
        request.setSearchParams(searchParams);

        result = personService.getGroupPersonList("1", request);
        assertThat(result.getItems()).isEmpty();

    }

    private void createPersonControl() {
        List<ControlEntity> controlEntities = personList.subList(0, 1).stream()
                .map(person -> createControlEntity(person.getId())).collect(Collectors.toList());
        controlRepository.saveAll(controlEntities);
    }

    private void createPersonGroup1Relations() {
        List<PersonGroupRelationEntity> personGroupRelations = personList.subList(0, 6).stream()
                .map(person -> createPersonGroupRelation(groupList.get(0).getId(), person.getId())).collect(Collectors.toList());
        personGroupRelationRepository.saveAll(personGroupRelations);
    }

    private void createPersonGroup2Relations() {
        List<PersonGroupRelationEntity> personGroupRelations = personList.subList(6, 12).stream()
                .map(person -> createPersonGroupRelation(groupList.get(1).getId(), person.getId())).collect(Collectors.toList());
        personGroupRelationRepository.saveAll(personGroupRelations);
    }

    private void createPersonSubject1Relations() {
        List<PersonSubjectRelationEntity> personSubjectRelations = personList.subList(0, 20).stream()
                .map(person -> createPersonSubjectRelation("1", person.getId())).collect(Collectors.toList());
        personSubjectRelationRepository.saveAll(personSubjectRelations);
    }

    private void createPersonSubject2Relations() {
        List<PersonSubjectRelationEntity> personSubjectRelations = personList.subList(20, 40).stream()
                .map(person -> createPersonSubjectRelation("2", person.getId())).collect(Collectors.toList());
        personSubjectRelationRepository.saveAll(personSubjectRelations);
    }

    private void createMobilePhoneEntities() {
        MobilePhoneEntity mobilePhoneEntity = new MobilePhoneEntity();
        mobilePhoneEntity.setPersonId(personList.get(0).getId());
        mobilePhoneEntity.setPhoneNumber("13911111111");
        mobilePhoneRepository.save(mobilePhoneEntity);

        mobilePhoneEntity = new MobilePhoneEntity();
        mobilePhoneEntity.setPersonId(personList.get(1).getId());
        mobilePhoneEntity.setPhoneNumber("13922222222");
        mobilePhoneRepository.save(mobilePhoneEntity);
    }

    private void createVirtualIdentityEntities() {
        VirtualIdentityEntity virtualIdentityEntity = new VirtualIdentityEntity();
        virtualIdentityEntity.setPersonId(personList.get(0).getId());
        virtualIdentityEntity.setVirtualType("4");
        virtualIdentityEntity.setVirtualNumber("1234567");
        virtualIdentityRepository.save(virtualIdentityEntity);

        virtualIdentityEntity = new VirtualIdentityEntity();
        virtualIdentityEntity.setPersonId(personList.get(1).getId());
        virtualIdentityEntity.setVirtualType("4");
        virtualIdentityEntity.setVirtualNumber("12344321");
        virtualIdentityRepository.save(virtualIdentityEntity);
    }

    private PersonGroupRelationEntity createPersonGroupRelation(String groupId, String personId) {
        PersonGroupRelationEntity personGroupRelationEntity = new PersonGroupRelationEntity();
        personGroupRelationEntity.setGroupId(groupId);
        personGroupRelationEntity.setPersonId(personId);
        return personGroupRelationEntity;
    }

    private ControlEntity createControlEntity(String personId) {
        ControlEntity controlEntity = new ControlEntity();
        controlEntity.setPersonId(personId);
        controlEntity.setSubjectId("1");
        controlEntity.setPoliceStationCode("510522040000");
        controlEntity.setPoliceStationName("四川省泸州市合江县公安局神臂城派出所");
        return controlEntity;
    }

    private PersonSubjectRelationEntity createPersonSubjectRelation(String subjectId, String personId) {
        PersonSubjectRelationEntity personSubjectRelation = new PersonSubjectRelationEntity();
        personSubjectRelation.setPersonId(personId);
        personSubjectRelation.setSubjectId(subjectId);
        return personSubjectRelation;
    }

    private PersonEntity createPersonEntity(Integer num) {
        String index = num.toString();
        PersonEntity person = new PersonEntity();
        person.setName("person" + index);
        person.setControlStatus(String.valueOf(num % 3 + 1));
        person.setIdNumber(String.format("510000000000000%03d", num));
        return person;
    }

}
