package com.trs.yq.police.subject.message;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class SyncServiceImplTest {

    private String message = "{\"trajectories\":[{\"address\":\"广元\",\"dateTime\":1631842497000,\"idNumber\":\"510525197809221139\",\"idType\":\"id_number\",\"idValue\":\"510525197809221139\",\"place\":\"广元\",\"raw\":{\"CC\":\"K423\",\"CCRQ\":\"20210917\",\"TRS_ID\":1439222154259992576,\"ZWH\":\"0070\",\"TIME\":\"20210917093457\",\"SPSJ\":\"20210917093457\",\"CXH\":\"12\",\"AREA\":\"\",\"FZ\":\"广元\",\"XM\":\"庞成\",\"DZ\":\"成都\",\"kafkatime\":\"1631972477028\",\"ID\":\"510525197809221139\",\"ZJHM\":\"510525197809221139\",\"TRS_IR_TIME\":\"20210918213755\"},\"trajectoryType\":\"xds.HuoCheDingPiao\",\"trsId\":\"1439222154259992576\"}],\"type\":[\"ww_zdryfs\"]}";

    @Test
    void syncWarning() {
    }
}