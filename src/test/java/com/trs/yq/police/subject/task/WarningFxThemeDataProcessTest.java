package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.PoliceSubjectApplication;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = PoliceSubjectApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WarningFxThemeDataProcessTest {

    @Autowired
    private WarningFxThemeDataTask process;

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void fxztWarningSyncTask() {
        process.fxztWarningSyncTask();
    }
}