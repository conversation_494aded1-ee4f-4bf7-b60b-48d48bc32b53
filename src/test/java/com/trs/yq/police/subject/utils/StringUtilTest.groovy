package com.trs.yq.police.subject.utils

import org.apache.commons.lang3.StringUtils
import spock.lang.Specification

/**
 * 字符串工具类单元测试
 *
 * <AUTHOR>
 * @date 2021/8/22 10:35
 */
class StringUtilTest extends Specification {

    def "CheckIdNumber"() {
        expect:
        StringUtil.checkIdNumber(idNumber as String) == checkFlag

        where:
        idNumber << ["1243252", "12352341412435134513421", "510103196502083435", "51070219741025112X", "51302919681005571X"]
        checkFlag << [false, false, true, false, true]
    }

    def "ParseBirthdayFromIdNumber"() {
        expect:
        StringUtil.parseBirthdayFromIdNumber(idNumber) == birthday

        where:
        idNumber << ["510103196502083435", "340203198409012838"]
        birthday << [-154512000000, 462816000000]
    }

    def "ParseGenderFromIdNumber"() {
        expect:
        StringUtils.equals(StringUtil.parseGenderFromIdNumber(idNumber, true), genderCn)
        StringUtils.equals(StringUtil.parseGenderFromIdNumber(idNumber, false), gender)

        where:
        idNumber << ["510103196502083435", "340203198409012838"]
        genderCn << ["男", "男"]
        gender << ["0", "0"]
    }

    def "GetDepartmentIdPrefix"() {
        expect:
        StringUtils.equals(StringUtil.getDepartmentIdPrefix(departmentCode as String), departmentCodePrefix as String)

        where:
        departmentCode << ["5105000000", "5105020000"]
        departmentCodePrefix << ["5105", "510502"]
    }

    def "GetPoliceStationPrefix"() {
        expect:
        StringUtils.equals(StringUtil.getPoliceStationPrefix(departmentCode as String), departmentCodePrefix as String)

        where:
        departmentCode << ["510500000000", "510500020000","510502020000"]
        departmentCodePrefix << ["510500", "51050002","51050202"]
    }
}
