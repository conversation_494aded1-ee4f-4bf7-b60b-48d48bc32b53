package com.trs.yq.police.subject.task;

import com.trs.yq.police.subject.PoliceSubjectApplication;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * 创建时间：2024/6/20 21:57
 * @version 1.0
 * @since 1.0
 */
@SpringBootTest(classes = PoliceSubjectApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class DriveNoLicenseAnalysisTaskTest {

    @Autowired
    private DriveNoLicenseAnalysisTask task;

    @Test
    void run() {
        task.run();
    }
}