package com.trs.yq.police.subject.service.impl;

import com.trs.yq.police.subject.PoliceSubjectApplication;
import com.trs.yq.police.subject.domain.vo.PageResult;
import com.trs.yq.police.subject.domain.vo.WarningListVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <p>Title:        TRS WCM</p>
 * <p>Copyright:    Copyright (c) 2004-2020</p>
 * <p>Company:      www.trs.com.cn</p>
 *
 * <AUTHOR> zhang.yang  E-mail: <EMAIL>
 * 创建时间：2024/6/20 17:48
 * @version 1.0
 * @since 1.0
 */
@SpringBootTest(classes = PoliceSubjectApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class WarningServiceImplTest {

    @Resource
    private WarningServiceImpl service;

    @Test
    void getWarningList() {
        PageResult<WarningListVO> warningList = service.getWarningList("4", null);
        System.out.println(warningList.getItems().size());
    }
}