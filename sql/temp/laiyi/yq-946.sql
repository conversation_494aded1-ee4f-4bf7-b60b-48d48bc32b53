INSERT INTO T_DICT ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('5b8a2e15f9934f52bce500a815adac3f', 'ps_event_person_source_group', '0', '事件人员来源', 'a59027d6defa4c4b9e917f05a91468', NULL, '0', NULL, NULL, NULL);
INSERT INTO T_DICT ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('ef17a7b8b8e648389d07c3167aebc654', 'ps_event_person_source', '1', '网安', '5b8a2e15f9934f52bce500a815adac3f', NULL, '0', NULL, NULL, NULL);
INSERT INTO T_DICT ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('a1f4b94ec102461bbadb5405c466dc9b', 'ps_event_person_source', '2', '视侦', '5b8a2e15f9934f52bce500a815adac3f', NULL, '0', NULL, NULL, NULL);

ALTER TABLE T_EVENT_PRESON_RELATION ADD SOURCE VARCHAR2(2) DEFAULT '1' NOT NULL;
COMMENT ON COLUMN T_EVENT_PRESON_RELATION.SOURCE is '人员来源';

UPDATE T_EVENT_PRESON_RELATION SET SOURCE='1' WHERE SOURCE IS NULL;

ALTER TABLE T_PS_PERSON_VISIT_RECORD ADD LAT NUMBER(20);
COMMENT ON COLUMN T_PS_PERSON_VISIT_RECORD.LAT is '纬度';

ALTER TABLE T_PS_PERSON_VISIT_RECORD ADD LNG NUMBER(20);
COMMENT ON COLUMN T_PS_PERSON_VISIT_RECORD.LNG is '经度';
