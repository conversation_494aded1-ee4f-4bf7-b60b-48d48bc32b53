/*
Navicat Oracle Data Transfer
Oracle Client Version : ********.0

Source Server         : **************
Source Server Version : 110200
Source Host           : **************:1522
Source Schema         : BIGDATA

Target Server Type    : ORACLE
Target Server Version : 110200
File Encoding         : 65001

Date: 2021-09-17 10:40:20
*/


-- ----------------------------
-- Table structure for T_PS_CRJ_FILE_RELATION
-- ----------------------------
CREATE TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" (
"ID" VARCHAR2(255 BYTE) NOT NULL ,
"CR_BY" VARCHAR2(255 BYTE) NULL ,
"CR_BY_NAME" VARCHAR2(255 BYTE) NULL ,
"CR_TIME" TIMESTAMP(6)  NULL ,
"UP_BY" VARCHAR2(255 BYTE) NULL ,
"UP_BY_NAME" VARCHAR2(255 BYTE) NULL ,
"UP_TIME" TIMESTAMP(6)  NULL ,
"CR_DEPT" VARCHAR2(255 BYTE) NULL ,
"CR_DEPT_CODE" VARCHAR2(255 BYTE) NULL ,
"CRJ_ID" VARCHAR2(255 BYTE) NOT NULL ,
"FILE_STORAGE_ID" VARCHAR2(255 BYTE) NOT NULL ,
"TYPE" VARCHAR2(2 BYTE) NOT NULL ,
"MODULE" VARCHAR2(2 BYTE) NOT NULL ,
"RECORD_ID" VARCHAR2(64 BYTE) NULL 
)
LOGGING
NOCOMPRESS
NOCACHE

;
COMMENT ON TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" IS '出入境文件关系表';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_FILE_RELATION"."CRJ_ID" IS '出入境信息id';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_FILE_RELATION"."FILE_STORAGE_ID" IS '文件存储id';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_FILE_RELATION"."TYPE" IS '文件类型，0-视频，1-文档，2-图片，3-其他';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_FILE_RELATION"."MODULE" IS '模块类型';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_FILE_RELATION"."RECORD_ID" IS '关联的记录主键，如果没有则为空';

-- ----------------------------
-- Checks structure for table T_PS_CRJ_FILE_RELATION
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" ADD CHECK ("CRJ_ID" IS NOT NULL);
ALTER TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" ADD CHECK ("FILE_STORAGE_ID" IS NOT NULL);
ALTER TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" ADD CHECK ("TYPE" IS NOT NULL);
ALTER TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" ADD CHECK ("MODULE" IS NOT NULL);

-- ----------------------------
-- Primary Key structure for table T_PS_CRJ_FILE_RELATION
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_CRJ_FILE_RELATION" ADD PRIMARY KEY ("ID");
