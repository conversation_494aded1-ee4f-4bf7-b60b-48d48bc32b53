CREATE TABLE T_PS_PERSON_APPROVAL (
                                              ID VARCHAR2(32) NOT NULL,
                                              CR_BY VARCHAR2(64),
                                              CR_BY_NAME NVARCHAR2(255),
                                              CR_TIME TIMESTAMP,
                                              UP_BY VARCHAR2(64),
                                              UP_BY_NAME NVARCHAR2(255),
                                              UP_TIME TIMESTAMP,
                                              CR_DEPT NVARCHAR2(255),
                                              CR_DEPT_CODE VARCHAR2(16),
                                              APPROVER_ID VARCHAR2(32),
                                              "TYPE" VARCHAR2(20),
                                              DETAIL NVARCHAR2(1000),
                                              STATUS VARCHAR2(2),
                                              PERSON_ID VARCHAR2(32),
                                              SUBJECT_ID VARCHAR2(32),
                                              CONTENT VARCHAR2(1000),
                                              CONSTRAINT T_PS_PERSON_APPROVAL_PK PRIMARY KEY (ID)
);
COMMENT ON TABLE T_PS_PERSON_APPROVAL IS '警种专题操作审批表';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL.APPROVER_ID IS '审批人id';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL."TYPE" IS '申请类型';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL.DETAIL IS '详情';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL.STATUS IS '审批状态';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL.PERSON_ID IS '人员id';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL.SUBJECT_ID IS '专题id';
COMMENT ON COLUMN T_PS_PERSON_APPROVAL.CONTENT IS '其他参数';
