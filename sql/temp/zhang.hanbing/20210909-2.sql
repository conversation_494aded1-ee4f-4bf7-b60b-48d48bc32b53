INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '32', '涉及人员', 'personId', '人员姓名', null, 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '33', '涉及群体', 'groupId', '群体名称', null, 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'clueName', '线索名称', null, 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'clueSource', '线索来源', 'ps_clue_source', 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'emergencyLevel', '紧急程度', 'ps_clue_emergency_level', 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'opennessLevel', '公开程度', 'ps_clue_openness_level', 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'clueDetail', '线索详情', null, 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'clueTypes', '线索类别', null, 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'relatedPersons', '人员', null, 2);
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'attachments', '附件', null, 2);