CREATE TABLE T_PS_GROUP_PARENT_RELATION (
                                                    ID VARCHAR2(32),
                                                    CR_BY NVARCHAR2(64),
                                                    CR_BY_NAME NVARCHAR2(255),
                                                    CR_TIME TIMESTAMP,
                                                    UP_BY NVARCHAR2(64),
                                                    UP_BY_NAME NVARCHAR2(255),
                                                    UP_TIME TIMESTAMP,
                                                    CR_DEPT NVARCHAR2(255),
                                                    CR_DEPT_CODE VARCHAR2(16),
                                                    GROUP_ID VARCHAR2(32),
                                                    PARENT_ID VARCHAR2(32)
);
COMMENT ON TABLE T_PS_GROUP_PARENT_RELATION IS '群体-上级群体关联表';
COMMENT ON COLUMN T_PS_GROUP_PARENT_RELATION.GROUP_ID IS '群体id';
COMMENT ON COLUMN T_PS_GROUP_PARENT_RELATION.PARENT_ID IS '上级群体id';
ALTER TABLE T_PS_GROUP_PARENT_RELATION
    ADD CONSTRAINT T_PS_GROUP_PARENT_RELATION_PK PRIMARY KEY (ID)
        ENABLE;

INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('24', '上级群体', null, 'group');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES ('111', '1', '24', 4, 'group');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES ('112', '3', '24', 4, 'group');
insert into T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
values ('1', '24', '上级群体', 'groupId', '群体名称', null, '1');
