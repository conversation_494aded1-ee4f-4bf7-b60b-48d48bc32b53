INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('50', '档案管理', null, 'event');
INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('51', '基本信息', null, 'event');
INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('52', '涉及人员', null, 'event');
INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('53', '涉及群体', null, 'event');
INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('54', '事件材料', null, 'event');
INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('55', '工作指令', null, 'event');
INSERT INTO T_PS_MODULE (ID, CN_NAME, EN_NAME, TYPE)
VALUES ('56', '合成作战', null, 'event');





INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'title', '事件名称', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'occurrenceTime', '事发时间', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'type', '事件类型', 'ps_event_type', '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'estimatePersonCount', '估计人数', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'controlDeptName', '主管单位', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'reportStatus', '上报状态', 'ps_report_status', '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'content', '事件详情', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'address', '事发地点', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'appealPlace', '诉求地', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'disposalStatus', '处置状态', 'ps_event_disposal_status', '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'riskLevel', '风险等级', 'risk_level', '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'createTime', '录入时间', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '51', '基本信息', 'claim', '相关要求', null, '4');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '52', '涉及人员', 'personId', '姓名', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '52', '涉及人员', 'idNumber', '身份证号', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '52', '涉及人员', 'personType', '人员类别', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '52', '涉及人员', 'createTime', '添加时间', null, '4');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '53', '涉及群体', 'groupId', '群体名称', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '53', '涉及群体', 'memberCount', '群体人数', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '53', '涉及群体', 'groupType', '群体类别', null, '4');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '54', '事件材料', 'type', '材料类型', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '54', '事件材料', 'name', '文件名', null, '4');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '54', '事件材料', 'size', '文件大小', null, '4');




INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'reportStatus', '上报状态', 'ps_report_status', '2');
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES (lower(sys_guid()), '31', '基本信息', 'disposalStatus', '处置状态', 'ps_clue_disposal_status', '2');



INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '50', 0, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '51', 1, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '52', 2, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '53', 3, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '54', 4, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '55', 5, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '1', '56', 6, 'event');

INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '50', 0, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '51', 1, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '52', 2, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '53', 3, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '54', 4, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '55', 5, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '2', '56', 6, 'event');

INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '50', 0, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '51', 1, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '52', 2, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '53', 3, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '54', 4, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '55', 5, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '3', '56', 6, 'event');

INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '50', 0, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '51', 1, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '52', 2, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '53', 3, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '54', 4, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '55', 5, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '4', '56', 6, 'event');

INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '50', 0, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '51', 1, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '52', 2, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '53', 3, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '54', 4, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '55', 5, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '5', '56', 6, 'event');

INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '50', 0, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '51', 1, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '52', 2, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '53', 3, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '54', 4, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '55', 5, 'event');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME)
VALUES (lower(sys_guid()), '6', '56', 6, 'event');