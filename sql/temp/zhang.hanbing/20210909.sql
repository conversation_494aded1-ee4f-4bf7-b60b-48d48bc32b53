/*
Navicat Oracle Data Transfer
Oracle Client Version : ********.0

Source Server         : **************
Source Server Version : 110200
Source Host           : **************:1522
Source Schema         : BIGDATA

Target Server Type    : ORACLE
Target Server Version : 110200
File Encoding         : 65001

Date: 2021-09-09 17:41:11
*/


-- ----------------------------
-- Table structure for T_PS_MODULE_FIELD
-- ----------------------------
DROP TABLE "BIGDATA"."T_PS_MODULE_FIELD";
CREATE TABLE "BIGDATA"."T_PS_MODULE_FIELD" (
"ID" VARCHAR2(255 BYTE) NOT NULL ,
"MODULE_CODE" VARCHAR2(255 BYTE) NULL ,
"MODULE_NAME" NVARCHAR2(20) NULL ,
"FIELD_NAME" VARCHAR2(50 BYTE) NULL ,
"FIELD_CN_NAME" NVARCHAR2(20) NULL ,
"DICT_TYPE" VARCHAR2(50 BYTE) NULL ,
"TYPE" VARCHAR2(20 BYTE) NULL 
)
LOGGING
NOCOMPRESS
NOCACHE

;
COMMENT ON TABLE "BIGDATA"."T_PS_MODULE_FIELD" IS '模块属性表';
COMMENT ON COLUMN "BIGDATA"."T_PS_MODULE_FIELD"."MODULE_CODE" IS '模块id';
COMMENT ON COLUMN "BIGDATA"."T_PS_MODULE_FIELD"."MODULE_NAME" IS '模块名';
COMMENT ON COLUMN "BIGDATA"."T_PS_MODULE_FIELD"."FIELD_NAME" IS '属性名';
COMMENT ON COLUMN "BIGDATA"."T_PS_MODULE_FIELD"."FIELD_CN_NAME" IS '属性中文名';
COMMENT ON COLUMN "BIGDATA"."T_PS_MODULE_FIELD"."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN "BIGDATA"."T_PS_MODULE_FIELD"."TYPE" IS '类型 person=0,group=1,clue=2';

-- ----------------------------
-- Records of T_PS_MODULE_FIELD
-- ----------------------------
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce04e09e055000000000001', '1', '基本信息', 'name', '姓名', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce14e09e055000000000001', '1', '基本信息', 'idNumber', '身份证号', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce24e09e055000000000001', '1', '基本信息', 'gender', '性别', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce34e09e055000000000001', '1', '基本信息', 'formerName', '曾用名', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce44e09e055000000000001', '1', '基本信息', 'nickName', '绰号', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce54e09e055000000000001', '1', '基本信息', 'nation', '民族', 'nation', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce64e09e055000000000001', '1', '基本信息', 'politicalStatus', '政治面貌', 'ps_political_status', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce74e09e055000000000001', '1', '基本信息', 'religiousBelief', '宗教信仰', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce84e09e055000000000001', '1', '基本信息', 'maritalStatus', '婚姻状况', 'ps_marital_status', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ce94e09e055000000000001', '1', '基本信息', 'currentJob', '现职业', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cea4e09e055000000000001', '1', '基本信息', 'contactInformation', '联系方式', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41ceb4e09e055000000000001', '1', '基本信息', 'registeredResidence', '户籍地', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cec4e09e055000000000001', '1', '基本信息', 'currentResidence', '现住址', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('ca4a8f0469a4e352e055000000000001', '1', '基本信息', 'groups', '群体类别', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cee4e09e055000000000001', '1', '基本信息', 'controlLevel', '管控级别', 'ps_zb_control_level', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cef4e09e055000000000001', '1', '基本信息', 'basicInfo', '基本情况', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf04e09e055000000000001', '2', '教育信息', 'beginTime', '开始时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf14e09e055000000000001', '2', '教育信息', 'endTime', '结束时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf24e09e055000000000001', '2', '教育信息', 'school', '毕业院校', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf34e09e055000000000001', '2', '教育信息', 'subject', '专业', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf44e09e055000000000001', '3', '工作信息', 'workBeginTime', '开始时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf54e09e055000000000001', '3', '工作信息', 'workEndTime', '结束时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf64e09e055000000000001', '3', '工作信息', 'workUnit', '工作单位', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf74e09e055000000000001', '3', '工作信息', 'workSituation', '工作地点', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf84e09e055000000000001', '3', '工作信息', 'post', '职务', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cf94e09e055000000000001', '4', '家庭关系', 'relation', '关系', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cfa4e09e055000000000001', '4', '家庭关系', 'name', '姓名', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cfb4e09e055000000000001', '4', '家庭关系', 'idNumber', '身份证号码', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cfc4e09e055000000000001', '4', '家庭关系', 'currentResidence', '现住址', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cfd4e09e055000000000001', '4', '家庭关系', 'contactInformation', '联系方式', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cfe4e09e055000000000001', '5', '社会关系', 'relation', '关系', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41cff4e09e055000000000001', '5', '社会关系', 'name', '姓名', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d004e09e055000000000001', '5', '社会关系', 'idNumber', '身份证号码', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d014e09e055000000000001', '5', '社会关系', 'currentResidence', '现住址', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d024e09e055000000000001', '5', '社会关系', 'contactInformation', '联系方式', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d034e09e055000000000001', '6', '车辆信息', 'type', '车辆类型', 'ps_vehicle_type', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d044e09e055000000000001', '6', '车辆信息', 'vehicleNumber', '车牌号', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d054e09e055000000000001', '6', '车辆信息', 'owner', '所属人', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d064e09e055000000000001', '7', '手机号', 'phoneNumber', '电话号码', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d074e09e055000000000001', '7', '手机号', 'phoneStatus', '电话状态', 'ps_phone_status', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d084e09e055000000000001', '7', '手机号', 'phoneUseStatus', '是否常用', 'ps_phone_use_status', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d094e09e055000000000001', '8', '虚拟身份', 'virtualType', '身份类型', 'ps_virtual_identity_type', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d0a4e09e055000000000001', '8', '虚拟身份', 'virtualNumber', '虚拟号码', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d0b4e09e055000000000001', '8', '虚拟身份', 'virtualTypeName', '自定义虚拟身份名称', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d0c4e09e055000000000001', '9', '走访记录', 'time', '走访时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d0d4e09e055000000000001', '9', '走访记录', 'method', '走访方式', 'ps_person_visit_method', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d0e4e09e055000000000001', '9', '走访记录', 'inControl', '是否在控', 'ps_person_in_control', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d0f4e09e055000000000001', '9', '走访记录', 'outOfControlTime', '失控时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d104e09e055000000000001', '9', '走访记录', 'destination', '当前去向', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d114e09e055000000000001', '9', '走访记录', 'info', '走访情况', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d124e09e055000000000001', '9', '走访记录', 'visitBy', '走访工作人员', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d134e09e055000000000001', '10', '案事件信息', 'name', '案事件名称', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d144e09e055000000000001', '10', '案事件信息', 'caseType', '案事件类型', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d154e09e055000000000001', '10', '案事件信息', 'happenTime', '发生时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d164e09e055000000000001', '10', '案事件信息', 'happenSituation', '发生地点', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d174e09e055000000000001', '10', '案事件信息', 'policeSituation', '派出所', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d184e09e055000000000001', '12', '落脚点地址', 'startTime', '开始时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d194e09e055000000000001', '12', '落脚点地址', 'endTime', '结束时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d1a4e09e055000000000001', '12', '落脚点地址', 'address', '居住地址', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d1b4e09e055000000000001', '12', '落脚点地址', 'state', '居住状态', 'ps_person_living_status', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d1c4e09e055000000000001', '12', '落脚点地址', 'note', '备注', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d1d4e09e055000000000001', '13', '流动信息', 'moveTime', '流动时间', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d1e4e09e055000000000001', '13', '流动信息', 'moveType', '流动类型', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d1f4e09e055000000000001', '13', '流动信息', 'location', '地址', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d204e09e055000000000001', '13', '流动信息', 'note', '备注', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d214e09e055000000000001', '14', '管控信息', 'policeStationName', '派出所名称', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d224e09e055000000000001', '14', '管控信息', 'leaderName', '派出所责任领导', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d234e09e055000000000001', '14', '管控信息', 'leaderContact', '责任领导联系方式', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d244e09e055000000000001', '14', '管控信息', 'responsibleName', '责任人', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d254e09e055000000000001', '14', '管控信息', 'responsibleContact', '责任人联系方式', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d264e09e055000000000001', '17', '银行卡信息', 'bankCardNumber', '银行卡号', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d274e09e055000000000001', '17', '银行卡信息', 'bankOfDeposit', '开户行', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d284e09e055000000000001', '17', '银行卡信息', 'useType', '使用状态', 'ps_bank_card_status', '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d294e09e055000000000001', '18', '裁决信息', 'judgementDate', '裁决日期', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d2a4e09e055000000000001', '18', '裁决信息', 'endDate', '截止日期', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d2b4e09e055000000000001', '18', '裁决信息', 'limitTime', '限制年限', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d2c4e09e055000000000001', '18', '裁决信息', 'limitUnit', '限制年限单位', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('c9e75bf41d2d4e09e055000000000001', '18', '裁决信息', 'reason', '裁决原因', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('ca4a8f0469a3e352e055000000000001', '1', '基本信息', 'types', '人员类别', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('ca4a8f0469a5e352e055000000000001', '1', '基本信息', 'labels', '标签', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb27829826ed6830e055000000000001', '21', '基本信息', 'groupName', '名称', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb27829826ee6830e055000000000001', '21', '基本信息', 'basicInfo', '基本信息', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb27829826ef6830e055000000000001', '22', '群体成员', 'personId', '人员姓名', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb27829826f06830e055000000000001', '23', '相关线索', 'clueId', '线索名称', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb27829826f16830e055000000000001', '20', '档案管理', 'groupId', '群体名称', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb508c133ae2ffe5e055000000000001', '22', '群体成员', 'activityLevel', '活跃程度', 'ps_activity_level', '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb8c0002d4b91332e055000000000001', '21', '基本信息', 'groupTypes', '群体类别', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('ca4c37e65820c9f6e055000000000001', '9', '走访记录', 'images', '走访图片', null, '0');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb2884420e30f5e3e055000000000001', '20', '档案管理', 'groupName', '名称', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb2884420e31f5e3e055000000000001', '20', '档案管理', 'basicInfo', '基本信息', null, '1');
INSERT INTO "BIGDATA"."T_PS_MODULE_FIELD" VALUES ('cb4abf2989b16044e055000000000001', '20', '档案管理', 'groupTypes', '群体类别', null, '1');

-- ----------------------------
-- Indexes structure for table T_PS_MODULE_FIELD
-- ----------------------------
CREATE INDEX "BIGDATA"."FIELD_NAME_INDEX"
ON "BIGDATA"."T_PS_MODULE_FIELD" ("FIELD_NAME" ASC)
LOGGING
VISIBLE;
CREATE INDEX "BIGDATA"."MODULE_ID_INDEX"
ON "BIGDATA"."T_PS_MODULE_FIELD" ("MODULE_CODE" ASC)
LOGGING
VISIBLE;

-- ----------------------------
-- Primary Key structure for table T_PS_MODULE_FIELD
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_MODULE_FIELD" ADD PRIMARY KEY ("ID");
