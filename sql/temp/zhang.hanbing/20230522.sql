INSERT INTO T_DICT
(ID, "TYP<PERSON>", CODE, NAM<PERSON>, P<PERSON>, DIC<PERSON><PERSON><PERSON>, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES('fc4010e35ca8cd6de0530dc8a8c0afd6', 'ps_zb_track_type_group', '0', 'zb专题轨迹类型', NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES('fc4010e35ca8cd6de0530dc8a8c0afd7', 'ps_zb_track_type', '1', '异常行为轨迹', 'fc4010e35ca8cd6de0530dc8a8c0afd6', NULL, NULL, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYP<PERSON>", CODE, NAME, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SHOWNU<PERSON>ER, <PERSON>ANDA<PERSON>, <PERSON><PERSON><PERSON>, FLAG)
VALUES('fc4010e35ca8cd6de0530dc8a8c0afd8', 'ps_zb_track_type', '2', '感知源轨迹', 'fc4010e35ca8cd6de0530dc8a8c0afd6', NULL, NULL, NULL, NULL, NULL);

INSERT INTO T_PS_MODULE_SUBJECT_RELATION
(ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME, MODULE_NAME)
VALUES('142', '3', '104', 14, 'person', '话单分析');
INSERT INTO T_PS_MODULE_SUBJECT_RELATION
(ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME, MODULE_NAME)
VALUES('143', '3', '105', 15, 'person', '资金分析');
INSERT INTO T_PS_MODULE
(ID, CN_NAME, EN_NAME, "TYPE")
VALUES('104', '资金分析', NULL, 'person');
INSERT INTO T_PS_MODULE
(ID, CN_NAME, EN_NAME, "TYPE")
VALUES('105', '话单分析', NULL, 'person');


UPDATE T_PS_SUBJECT
SET NAME='政保专题', PERSON_LIST_FILTERS='[ { "type": "option", "displayName": "管控状态", "key": "controlStatus", "property": "single", "value": [ "$$ps_control_status$$" ] }, { "type": "option", "displayName": "布控状态", "key": "monitorStatus", "property": "single", "value": [ "$$ps_monitor_status$$" ] }, { "type": "option", "displayName": "人员标签", "key": "personLabel", "property": "single", "value": [ "&&personLabel&&" ] }, { "type": "tree", "displayName": "管辖部门", "key": "department", "value": [ "&&department&&" ] }, { "type": "option", "displayName": "管控级别", "key": "controlLevel", "value": [ "$$ps_zb_control_level$$" ] } ]', PERSON_LIST_PROPERTY='{"name":"姓名","idNumber":"身份证号","gender":"性别","formerName":"曾用名","nickName":"绰号","nation":"民族","politicalStatus":"政治面貌","religiousBelief":"宗教信仰","maritalStatus":"婚姻状况","currentJob":"现职业","contactInformation":"联系方式","controlLevel":"管控级别","controlStatus":"管控状态","personLabel":"标签","registeredResidence":"户籍地","currentResidence":"现住址","basicInfo":"基本情况"}', GROUP_LIST_FILTERS='[{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&groupType&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', GROUP_LIST_PROPERTY='{"groupName":"群体名称","groupType":"群体类别","basicInfo":"基本情况","createDeptName":"录入单位","createTime":"录入时间","updateTime":"更新时间"}', CLUE_LIST_FILTERS='[{"type":"select","displayName":"线索类别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["&&clueSource&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', CLUE_LIST_PROPERTY='{"clueName":"线索名称","clueSource":"线索来源","clueType":"线索类别","emergencyLevel":"紧急程度","createTime":"创建时间","updateTime":"更新时间"}', WARNING_LIST_FILTERS='[{"type":"option","displayName":"预警状态","key":"warningStatus","value":["$$ps_warning_status$$"]},{"type":"time","displayName":"预警时间","key":"warningTime","value":["&&time&&"]},{"type":"option","displayName":"预警级别","key":"warningLevel","value":["$$ps_warning_level$$"]},{"type":"option","displayName":"预警类型","key":"warningType","value":["&&warningType&&"]},{"type":"select","displayName":"地区","key":"areaCode","value":["&&areaCode&&"]}]', WARNING_LIST_PROPERTY='{"warningTime":"预警时间","warningType":"预警类别","warningContent":"预警内容","warningSource":"预警来源","warningStatus":"预警状态"}', EVENT_LIST_FILTERS=NULL, EVENT_LIST_PROPERTY=NULL
WHERE ID='3';

create table T_PS_PERSON_CALL
(
    ID                  VARCHAR2(32) not null
        constraint T_PS_PERSON_CALL_PK
            primary key,
    CR_BY               NVARCHAR2(64),
    CR_BY_NAME          NVARCHAR2(255),
    CR_TIME             TIMESTAMP(6),
    UP_BY               NVARCHAR2(64),
    UP_BY_NAME          NVARCHAR2(255),
    UP_TIME             TIMESTAMP(6),
    CR_DEPT             NVARCHAR2(255),
    CR_DEPT_CODE        VARCHAR2(16),
    PERSON_ID           VARCHAR2(32) not null,
    TOPOLOGY_GRAPH      NVARCHAR2(1000),
    CALL_FILE           NVARCHAR2(1000),
    CALL_TIME_FEATURE   NVARCHAR2(1000),
    CALL_POSITION_TRACK NVARCHAR2(1000),
    ATTACHMENTS         NVARCHAR2(1000),
    REMARK              NVARCHAR2(1000)
)
/

comment on column T_PS_PERSON_CALL.PERSON_ID is '人员id'
/

comment on column T_PS_PERSON_CALL.TOPOLOGY_GRAPH is '人际关系拓扑图文件id'
/

comment on column T_PS_PERSON_CALL.CALL_FILE is '话单文件id'
/

comment on column T_PS_PERSON_CALL.CALL_TIME_FEATURE is '通话时间特点'
/

comment on column T_PS_PERSON_CALL.CALL_POSITION_TRACK is '通话位置轨迹'
/

comment on column T_PS_PERSON_CALL.ATTACHMENTS is '附件'
/

comment on column T_PS_PERSON_CALL.REMARK is '备注'
/

create index T_PS_PERSON_CALL_PERSON_ID_IDX
    on T_PS_PERSON_CALL (PERSON_ID)
/

create table T_PS_PERSON_FUND
(
    ID                  VARCHAR2(32) not null
        constraint T_PS_PERSON_FUND_PK
            primary key,
    CR_BY               NVARCHAR2(64),
    CR_BY_NAME          NVARCHAR2(255),
    CR_TIME             TIMESTAMP(6),
    UP_BY               NVARCHAR2(64),
    UP_BY_NAME          NVARCHAR2(255),
    UP_TIME             TIMESTAMP(6),
    CR_DEPT             NVARCHAR2(255),
    CR_DEPT_CODE        VARCHAR2(16),
    PERSON_ID           VARCHAR2(32) not null,
    TOPOLOGY_GRAPH      NVARCHAR2(1000),
    ATTACHMENTS         NVARCHAR2(1000),
    REMARK              NVARCHAR2(1000)
)
/

comment on column T_PS_PERSON_FUND.PERSON_ID is '人员id'
/

comment on column T_PS_PERSON_FUND.TOPOLOGY_GRAPH is '资金关系拓扑图文件id'
/

comment on column T_PS_PERSON_FUND.ATTACHMENTS is '附件'
/

comment on column T_PS_PERSON_FUND.REMARK is '备注'
/

create index T_PS_PERSON_FUND_PERSON_ID_IDX
    on T_PS_PERSON_FUND (PERSON_ID)
/

INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('8', '104', '话单关系', 'topologyGraph', '人际关系拓扑图', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('9', '104', '话单关系', 'callTimeFeature', '通话时段特点', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('10', '104', '话单关系', 'callPositionTrack', '通话位置轨迹', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('11', '104', '话单关系', 'callFile', '话单文件', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('12', '104', '话单关系', 'attachments', '附件', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('13', '104', '资金关系', 'remark', '备注', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('5', '105', '资金关系', 'topologyGraph', '资金关系拓扑图', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('6', '105', '资金关系', 'attachments', '附件', NULL, '0');
INSERT INTO T_PS_MODULE_FIELD
(ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, "TYPE")
VALUES('7', '105', '资金关系', 'remark', '备注', NULL, '0');

INSERT INTO T_PS_MODULE
(ID, CN_NAME, EN_NAME, "TYPE")
VALUES('104', '资金分析', NULL, 'person');
INSERT INTO T_PS_MODULE
(ID, CN_NAME, EN_NAME, "TYPE")
VALUES('105', '话单分析', NULL, 'person');

