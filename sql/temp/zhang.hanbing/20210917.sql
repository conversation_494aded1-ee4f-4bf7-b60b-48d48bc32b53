/*
Navicat Oracle Data Transfer
Oracle Client Version : ********.0

Source Server         : **************
Source Server Version : 110200
Source Host           : **************:1522
Source Schema         : BIGDATA

Target Server Type    : ORACLE
Target Server Version : 110200
File Encoding         : 65001

Date: 2021-09-17 09:09:30
*/


-- ----------------------------
-- Table structure for T_PS_CRJ_ACCOMMODATION
-- ----------------------------
CREATE TABLE "BIGDATA"."T_PS_CRJ_ACCOMMODATION" (
"ID" VARCHAR2(32 BYTE) NOT NULL ,
"CR_BY" NVARCHAR2(64) NULL ,
"CR_BY_NAME" NVARCHAR2(255) NULL ,
"CR_TIME" TIMESTAMP(6)  NULL ,
"UP_BY" NVARCHAR2(64) NULL ,
"UP_BY_NAME" NVARCHAR2(255) NULL ,
"UP_TIME" TIMESTAMP(6)  NULL ,
"CR_DEPT" NVARCHAR2(64) NULL ,
"CR_DEPT_CODE" VARCHAR2(12 BYTE) NULL ,
"NAME" NVARCHAR2(255) NULL ,
"NATIONALITY" VARCHAR2(12 BYTE) NULL ,
"CERTIFICATE_TYPE" VARCHAR2(12 BYTE) NULL ,
"CERTIFICATE_NUMBER" NVARCHAR2(64) NULL ,
"LIVING_INFO" NVARCHAR2(1000) NULL ,
"PHONE_NUMBER" VARCHAR2(32 BYTE) NULL ,
"DEPARTURE_DATE" TIMESTAMP(0)  NULL 
)
LOGGING
NOCOMPRESS
NOCACHE

;
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."ID" IS '主键';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."NAME" IS '姓名';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."NATIONALITY" IS '国籍';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CERTIFICATE_TYPE" IS '证件类型';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."CERTIFICATE_NUMBER" IS '证件号码';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."LIVING_INFO" IS '居住信息';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."PHONE_NUMBER" IS '联系电话';
COMMENT ON COLUMN "BIGDATA"."T_PS_CRJ_ACCOMMODATION"."DEPARTURE_DATE" IS '拟离开时间';

-- ----------------------------
-- Indexes structure for table T_PS_CRJ_ACCOMMODATION
-- ----------------------------

-- ----------------------------
-- Checks structure for table T_PS_CRJ_ACCOMMODATION
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_CRJ_ACCOMMODATION" ADD CHECK ("ID" IS NOT NULL);

-- ----------------------------
-- Primary Key structure for table T_PS_CRJ_ACCOMMODATION
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_CRJ_ACCOMMODATION" ADD PRIMARY KEY ("ID");
