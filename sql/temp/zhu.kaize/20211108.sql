CREATE TABLE "BIGDATA"."T_PS_WARNING_PUSH_CONFIG" (
    "ID" VARCHAR2(32 BYTE) ,
    "WARNING_TYPE" VARCHAR2(50 BYTE) ,
    "AREA_CODE" VARCHAR2(50 BYTE) ,
    "ROLE_ID" VARCHAR2(32 BYTE) ,
    "SMS_FLAG" NUMBER(2) ,
    "POP_FLAG" NUMBER(2)
);
COMMENT ON COLUMN "BIGDATA"."T_PS_WARNING_PUSH_CONFIG"."WARNING_TYPE" IS '预警类型';
COMMENT ON COLUMN "BIGDATA"."T_PS_WARNING_PUSH_CONFIG"."AREA_CODE" IS '区域代码分类';
COMMENT ON COLUMN "BIGDATA"."T_PS_WARNING_PUSH_CONFIG"."ROLE_ID" IS '绑定的角色id，拥有该角色的所有user都将被推送';
COMMENT ON COLUMN "BIGDATA"."T_PS_WARNING_PUSH_CONFIG"."SMS_FLAG" IS '是否发短信,1:发 0:不发';
COMMENT ON COLUMN "BIGDATA"."T_PS_WARNING_PUSH_CONFIG"."POP_FLAG" IS '是否弹窗,1:弹 0:不弹';
COMMENT ON TABLE "BIGDATA"."T_PS_WARNING_PUSH_CONFIG" IS '预警推送表';