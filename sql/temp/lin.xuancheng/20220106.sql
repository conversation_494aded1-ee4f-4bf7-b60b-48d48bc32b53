INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME, MODULE_NAME)
VALUES ('418', '6', '20', '0', 'group', '档案管理');

INSERT INTO T_PS_MODULE_SUBJECT_RELATION (ID, SUBJECT_ID, MODULE_ID, SHOW_ORDER, ARCHIVE_NAME, MODULE_NAME)
VALUES ('419', '6', '0', '0', 'person', '档案管理');

-- 政府主管部门
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentDepartment', '政府行业主管部门', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentLeaderName', '政府牵头领导-姓名', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentLeaderJob', '政府牵头领导-职务', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentLeaderTelephone', '政府牵头领导-联系方式', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentDutyName', '政府具体责任人-姓名', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentDutyJob', '政府具体责任人-职务', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '100', '政府行业主管部门', 'governmentDutyTelephone', '政府具体责任人-联系方式', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentDepartment', '政府行业主管部门', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentLeaderName', '政府牵头领导-姓名', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentLeaderJob', '政府牵头领导-职务', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentLeaderTelephone', '政府牵头领导-联系方式', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentDutyName', '政府具体责任人-姓名', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentDutyJob', '政府具体责任人-职务', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '25', '政府行业主管部门', 'governmentDutyTelephone', '政府具体责任人-联系方式', null, '1');

-- 关联关系
UPDATE T_PS_MODULE_FIELD t
SET t.MODULE_NAME = '相关线索'
WHERE t.ID LIKE 'd34ffbfb05183f9be055000000000001' ESCAPE '#';

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '15', '涉事相关群体', 'activityLevel', '活跃程度', 'ps_activity_level', '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '101', '相关事件', 'eventId', '事件名称', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '26', '相关事件', 'eventId', '事件名称', null, '1');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '15', '涉事相关群体', 'groupId', '群体名称', null, '0');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE, TYPE)
VALUES (lower(sys_guid()), '16', '相关线索', 'clueId', '线索名称', null, '0');

-- 变更字段长度
alter table T_PS_OPERATION_LOG
    modify OPERATE_MODULE VARCHAR2(50)
/

alter table T_PS_OPERATION_LOG
    modify OPERATOR VARCHAR2(10);




