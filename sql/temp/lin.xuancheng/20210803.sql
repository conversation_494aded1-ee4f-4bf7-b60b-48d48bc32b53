alter table T_PS_PERSON
    drop column DELETED;

CREATE INDEX TARGET_OBJECT_ID_IDX ON BIGDATA.T_PS_OPERATION_LOG (TARGET_OBJECT_ID);
alter table T_PS_OPERATION_LOG
    add CR_BY_USERNAME VARCHAR2(255);

-- ---------- --
--  20210811  --
-- ---------- --

-- auto-generated definition
create table T_PS_PERSON_IMPORT_HISTORY
(
    ID                 VARCHAR2(255) not null
        constraint T_PS_PERSON_IMPORT_HISTORY_PK
            primary key,
    CR_BY              VARCHAR2(255),
    CR_BY_NAME         VARCHAR2(255),
    CR_DEPT            VARCHAR2(255),
    CR_DEPT_CODE       VARCHAR2(255),
    CR_TIME            TIMESTAMP(6) default current_timestamp,
    FILE_NAME          NVARCHAR2(100),
    EXTENSION_NAME     VARCHAR2(20),
    PATH               VARCHAR2(255),
    TYPE               VARCHAR2(2),
    <PERSON>U<PERSON><PERSON>ECT_ID         VARCHAR2(255),
    UP_BY              VARCHAR2(255),
    UP_BY_NAME         VARCHAR2(255),
    UP_TIME            TIMESTAMP(6),
    INITIAL_HISTORY_ID VARCHAR2(255),
    IS_TEMPLATE        VARCHAR2(1)  default 0
)
/

comment on table T_PS_PERSON_IMPORT_HISTORY is '人员导入历史记录'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.CR_BY is '创建人'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.FILE_NAME is '文件名'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.EXTENSION_NAME is '文件扩展名'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.PATH is '文件路径'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.TYPE is '文件类型'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.SUBJECT_ID is '主题主键'
/

comment on column T_PS_PERSON_IMPORT_HISTORY.INITIAL_HISTORY_ID is '原始记录主键';

-- 插入数据

INSERT INTO T_PS_PERSON_IMPORT_HISTORY (ID, CR_BY, CR_BY_NAME, CR_DEPT, CR_DEPT_CODE, CR_TIME, FILE_NAME,
                                        EXTENSION_NAME, PATH, TYPE, SUBJECT_ID, UP_BY, UP_BY_NAME, UP_TIME,
                                        INITIAL_HISTORY_ID, IS_TEMPLATE)
VALUES (lower(sys_guid()), '6a94ce2b1062402ce055000000000001', '系统管理员', '四川省泸州市公安局大数据警察支队', '************', DEFAULT,
        '通用批量导入.xlsx', 'xlsx', 'M01/00/05/wKjI0mEQ8HKAZIAgAAAiUsmxZtc58.xlsx', '0', '1',
        '6a94ce2b1062402ce055000000000001',
        '系统管理员', DEFAULT, null, '1');

INSERT INTO T_PS_PERSON_IMPORT_HISTORY (ID, CR_BY, CR_BY_NAME, CR_DEPT, CR_DEPT_CODE, CR_TIME, FILE_NAME,
                                        EXTENSION_NAME, PATH, TYPE, SUBJECT_ID, UP_BY, UP_BY_NAME, UP_TIME,
                                        INITIAL_HISTORY_ID, IS_TEMPLATE)
VALUES (lower(sys_guid()), '6a94ce2b1062402ce055000000000001', '系统管理员', '四川省泸州市公安局大数据警察支队', '************', DEFAULT,
        '通用批量导入.xlsx', 'xlsx', 'M01/00/05/wKjI0mEQ8HKAZIAgAAAiUsmxZtc58.xlsx', '0', '2',
        '6a94ce2b1062402ce055000000000001',
        '系统管理员', DEFAULT, null, '1');

INSERT INTO T_PS_PERSON_IMPORT_HISTORY (ID, CR_BY, CR_BY_NAME, CR_DEPT, CR_DEPT_CODE, CR_TIME, FILE_NAME,
                                        EXTENSION_NAME, PATH, TYPE, SUBJECT_ID, UP_BY, UP_BY_NAME, UP_TIME,
                                        INITIAL_HISTORY_ID, IS_TEMPLATE)
VALUES (lower(sys_guid()), '6a94ce2b1062402ce055000000000001', '系统管理员', '四川省泸州市公安局大数据警察支队', '************', DEFAULT,
        '通用批量导入.xlsx', 'xlsx', 'M01/00/05/wKjI0mEQ8HKAZIAgAAAiUsmxZtc58.xlsx', '0', '3',
        '6a94ce2b1062402ce055000000000001',
        '系统管理员', DEFAULT, null, '1');

INSERT INTO T_PS_PERSON_IMPORT_HISTORY (ID, CR_BY, CR_BY_NAME, CR_DEPT, CR_DEPT_CODE, CR_TIME, FILE_NAME,
                                        EXTENSION_NAME, PATH, TYPE, SUBJECT_ID, UP_BY, UP_BY_NAME, UP_TIME,
                                        INITIAL_HISTORY_ID, IS_TEMPLATE)
VALUES (lower(sys_guid()), '6a94ce2b1062402ce055000000000001', '系统管理员', '四川省泸州市公安局大数据警察支队', '************', DEFAULT,
        '交警吊销人员.xlsx', 'xlsx', 'M01/00/05/wKjI0mEQ8HKAZIAgAAAiUsmxZtc58.xlsx', '0', '4',
        '6a94ce2b1062402ce055000000000001',
        '系统管理员', DEFAULT, null, '1');

INSERT INTO T_PS_PERSON_IMPORT_HISTORY (ID, CR_BY, CR_BY_NAME, CR_DEPT, CR_DEPT_CODE, CR_TIME, FILE_NAME,
                                        EXTENSION_NAME, PATH, TYPE, SUBJECT_ID, UP_BY, UP_BY_NAME, UP_TIME,
                                        INITIAL_HISTORY_ID, IS_TEMPLATE)
VALUES (lower(sys_guid()), '6a94ce2b1062402ce055000000000001', '系统管理员', '四川省泸州市公安局大数据警察支队', '************', DEFAULT,
        '通用批量导入.xlsx', 'xlsx', 'M01/00/05/wKjI0mEQ8HKAZIAgAAAiUsmxZtc58.xlsx', '0', '5',
        '6a94ce2b1062402ce055000000000001',
        '系统管理员', DEFAULT, null, '1');


-- ---------- --
--  20210812  --
-- ---------- --
alter table T_PS_PERSON_WORK_INFORMATION
    modify WORK_END_TIME null;
alter table T_PS_PERSON_EDUCATION
    modify END_TIME null;

-- ---------- --
--  20210819  --
-- ---------- --
create table T_PS_MODULE_FIELD
(
    ID            VARCHAR2(255)
        constraint T_PS_MODULE_FIELD_PK
            primary key,
    MODULE_CODE   varchar2(255),
    MODULE_NAME   nvarchar2(20),
    FIELD_NAME    varchar2(50),
    FIELD_CN_NAME nvarchar2(20),
    DICT_TYPE     varchar2(50)
)
/

comment on table T_PS_MODULE_FIELD is '模块属性表'
/

comment on column T_PS_MODULE_FIELD.MODULE_CODE is '模块编号'
/

comment on column T_PS_MODULE_FIELD.MODULE_NAME is '模块名'
/

comment on column T_PS_MODULE_FIELD.FIELD_NAME is '属性名'
/

comment on column T_PS_MODULE_FIELD.FIELD_CN_NAME is '属性中文名'
/

comment on column T_PS_MODULE_FIELD.DICT_TYPE is '字典类型';

create index FIELD_NAME_INDEX
    on T_PS_MODULE_FIELD (FIELD_NAME)
/

create index MODULE_CODE_INDEX
    on T_PS_MODULE_FIELD (MODULE_CODE);



-- ------------------
--        DML      --
-- ------------------
INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'name', '姓名', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'idNumber', '身份证号', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'gender', '性别', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'formerName', '曾用名', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'nickName', '绰号', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'nation', '民族', 'nation');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'politicalStatus', '政治面貌', 'ps_political_status');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'religiousBelief', '宗教信仰', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'maritalStatus', '婚姻状况', 'ps_marital_status');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'currentJob', '现职业', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'contactInformation', '联系方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'registeredResidence', '户籍地', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'currentResidence', '现住址', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'controlStatus', '管控状态', 'ps_control_status');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'controlLevel', '管控级别', 'ps_zb_control_level');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'basicInfo', '基本情况', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '2', '教育信息', 'beginTime', '开始时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '2', '教育信息', 'endTime', '结束时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '2', '教育信息', 'school', '毕业院校', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '2', '教育信息', 'subject', '专业', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '3', '工作信息', 'workBeginTime', '开始时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '3', '工作信息', 'workEndTime', '结束时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '3', '工作信息', 'workUnit', '工作单位', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '3', '工作信息', 'workSituation', '工作地点', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '3', '工作信息', 'post', '职务', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '4', '家庭关系', 'relation', '关系', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '4', '家庭关系', 'name', '姓名', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '4', '家庭关系', 'idNumber', '身份证号码', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '4', '家庭关系', 'currentResidence', '现住址', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '4', '家庭关系', 'contactInformation', '联系方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '5', '社会关系', 'relation', '与当前人物关系', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '5', '社会关系', 'name', '姓名', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '5', '社会关系', 'idNumber', '身份证号码', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '5', '社会关系', 'currentResidence', '现住址', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '5', '社会关系', 'contactInformation', '联系方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '6', '车辆信息', 'type', '车辆类型', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '6', '车辆信息', 'vehicleNumber', '车牌号', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '6', '车辆信息', 'owner', '所属人', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '7', '手机号', 'phoneNumber', '电话号码', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '7', '手机号', 'phoneStatus', '电话状态', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '7', '手机号', 'phoneUseStatus', '是否常用', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '8', '虚拟身份', 'virtualType', '身份类型', 'ps_virtual_identity_type');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '8', '虚拟身份', 'virtualNumber', '虚拟号码', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '8', '虚拟身份', 'virtualTypeName', '自定义虚拟身份名称', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'time', '走访时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'method', '走访方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'inControl', '是否在控', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'outOfControlTime', '失控时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'destination', '当前去向', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'info', '走访情况', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'visitBy', '走访工作人员', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '10', '案事件信息', 'name', '案事件名称', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '10', '案事件信息', 'caseType', '案事件类型', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '10', '案事件信息', 'happenTime', '发生时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '10', '案事件信息', 'happenSituation', '发生地点', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '10', '案事件信息', 'policeSituation', '派出所', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '12', '落脚点地址', 'startTime', '开始时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '12', '落脚点地址', 'endTime', '结束时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '12', '落脚点地址', 'address', '居住地址', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '12', '落脚点地址', 'state', '居住状态', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '12', '落脚点地址', 'note', '备注', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '13', '流动信息', 'moveTime', '流动时间', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '13', '流动信息', 'moveType', '流动类型', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '13', '流动信息', 'location', '地址', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '13', '流动信息', 'note', '备注', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '14', '管控信息', 'policeStationName', '派出所名称', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '14', '管控信息', 'leaderName', '派出所责任领导', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '14', '管控信息', 'leaderContact', '责任领导联系方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '14', '管控信息', 'responsibleName', '责任人', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '14', '管控信息', 'responsibleContact', '责任人联系方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '17', '银行卡信息', 'bankCardNumber', '银行卡号', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '17', '银行卡信息', 'bankOfDeposit', '开户行', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '17', '银行卡信息', 'useType', '使用状态', 'ps_bank_card_status');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '18', '裁决信息', 'judgementDate', '裁决日期', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '18', '裁决信息', 'endDate', '截止日期', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '18', '裁决信息', 'limitTime', '限制年限', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '18', '裁决信息', 'limitUnit', '限制年限单位', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '18', '裁决信息', 'reason', '裁决原因', null);

UPDATE T_PS_MODULE_FIELD t
SET t.DICT_TYPE = 'ps_phone_use_status'
WHERE t.ID LIKE 'c9e75bf41d084e09e055000000000001' ESCAPE '#';

UPDATE T_PS_MODULE_FIELD t
SET t.DICT_TYPE = 'ps_person_in_control'
WHERE t.ID LIKE 'c9e75bf41d0e4e09e055000000000001' ESCAPE '#';

UPDATE T_PS_MODULE_FIELD t
SET t.DICT_TYPE = 'ps_vehicle_type'
WHERE t.ID LIKE 'c9e75bf41d034e09e055000000000001' ESCAPE '#';

UPDATE T_PS_MODULE_FIELD t
SET t.DICT_TYPE = 'ps_person_visit_method'
WHERE t.ID LIKE 'c9e75bf41d0d4e09e055000000000001' ESCAPE '#';

UPDATE T_PS_MODULE_FIELD t
SET t.DICT_TYPE = 'ps_person_living_status'
WHERE t.ID LIKE 'c9e75bf41d1b4e09e055000000000001' ESCAPE '#';

UPDATE T_PS_MODULE_FIELD t
SET t.DICT_TYPE = 'ps_phone_status'
WHERE t.ID LIKE 'c9e75bf41d074e09e055000000000001' ESCAPE '#';

-- ------------------------------------------
--    fix operation log column data type   --
-- ------------------------------------------
alter table T_PS_OPERATION_LOG
    drop column DETAIL;

alter table T_PS_OPERATION_LOG
    add DETAIL NVARCHAR2(2000)
/

comment on column T_PS_OPERATION_LOG.DETAIL is '详情';


-- ---------- --
--  20210823  --
-- ---------- --

-- --------------------------------
--     批量导入的模块属性，暂不使用    --
-- --------------------------------

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'name', '姓名', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'idNumber', '身份证号', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'gender', '性别', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'formerName', '曾用名', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'politicalStatus', '政治面貌', 'ps_political_status');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'religiousBelief', '宗教信仰', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'maritalStatus', '婚姻状况', 'ps_marital_status');

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'contactInformation', '联系方式', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'registeredResidence', '户籍地', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '0', '档案管理', 'currentResidence', '现住址', null);


INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'types', '人员类型', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'groups', '群体类别', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '1', '基本信息', 'labels', '标签', null);

INSERT INTO T_PS_MODULE_FIELD (ID, MODULE_CODE, MODULE_NAME, FIELD_NAME, FIELD_CN_NAME, DICT_TYPE)
VALUES (lower(sys_guid()), '9', '走访记录', 'images', '走访图片', null);

UPDATE T_PS_MODULE_FIELD F
SET F.FIELD_CN_NAME = '人员类别'
WHERE F.FIELD_NAME = 'types';

