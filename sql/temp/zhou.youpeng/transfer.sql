ALTER TABLE T_PS_CLUE_EXTEND
    ADD REPORT_STATUS VARCHAR2(2) DEFAULT 0;
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.REPORT_STATUS IS '上报状态';
ALTER TABLE T_PS_CLUE_EXTEND
    ADD DISPOSAL_STATUS VARCHAR2(2);
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.DISPOSAL_STATUS IS '线索状态：0,1,2 待处置,预警,已处置,不处置)';

--  T_PS_COMMON_EXTEND definition

CREATE TABLE "T_PS_COMMON_EXTEND"
(
    "ID"                          VARCHAR2(32) NOT NULL ENABLE,
    "CR_BY"                       NVARCHAR2(64),
    "CR_BY_NAME"                  NVARCHAR2(255),
    "CR_TIME"                     TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"                       NVARCHAR2(64),
    "UP_BY_NAME"                  NVARCHAR2(255),
    "UP_TIME"                     TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"                     NVARCHAR2(64),
    "CR_DEPT_CODE"                VARCHAR2(12),
    "MODULE"                       VARCHAR2(20) NOT NULL ENABLE,
    "RECORD_ID"                   VARCHAR2(32) NOT NULL ENABLE,
    "MAIN_DEMAND"                 VARCHAR2(4000),
    "GOVERNMENT_DEPARTMENT"       VARCHAR2(50),
    "GOVERNMENT_LEADER_NAME"      VARCHAR2(20),
    "GOVERNMENT_LEADER_JOB"       VARCHAR2(20),
    "GOVERNMENT_LEADER_TELEPHONE" VARCHAR2(20),
    "GOVERNMENT_DUTY_NAME"        VARCHAR2(20),
    "GOVERNMENT_DUTY_JOB"         VARCHAR2(20),
    "GOVERNMENT_DUTY_TELEPHONE"   VARCHAR2(20),
    PRIMARY KEY ("ID")
);

COMMENT
    ON TABLE T_PS_COMMON_EXTEND IS '线索扩展表';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.ID IS '主键';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.CR_BY IS '创建人';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.CR_BY_NAME IS '创建人姓名';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.CR_TIME IS '创建时间';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.UP_BY IS '最后更新人';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.UP_BY_NAME IS '最后更新人姓名';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.UP_TIME IS '最后更新时间';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.CR_DEPT IS '创建部门';
COMMENT
    ON COLUMN T_PS_COMMON_EXTEND.CR_DEPT_CODE IS '创建部门编号';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."MODULE" IS '模块：group | person';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."RECORD_ID" IS 'ID';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."MAIN_DEMAND" IS '主要诉求';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_DEPARTMENT" IS '政府主管部门';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_LEADER_NAME" IS '政府领导姓名';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_LEADER_JOB" IS '政府领导职务';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_LEADER_TELEPHONE" IS '政府领导联系电话';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_DUTY_NAME" IS '政府责任人姓名';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_DUTY_JOB" IS '政府责任人职务';
COMMENT ON COLUMN T_PS_COMMON_EXTEND."GOVERNMENT_DUTY_TELEPHONE" IS '政府责任人联系电话';


CREATE TABLE "T_PS_CLUE_EXTEND"
(
    "ID"              VARCHAR2(32) NOT NULL ENABLE,
    "CR_BY"           NVARCHAR2(64),
    "CR_BY_NAME"      NVARCHAR2(255),
    "CR_TIME"         TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"           NVARCHAR2(64),
    "UP_BY_NAME"      NVARCHAR2(255),
    "UP_TIME"         TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"         NVARCHAR2(64),
    "CR_DEPT_CODE"    VARCHAR2(12),
    "CLUE_ID"         VARCHAR2(32) NOT NULL ENABLE,
    "TYPE"          VARCHAR2(2),
    "RELATED_PLACE"   NVARCHAR2(255),
    "METHOD"          VARCHAR2(2),
    "BEHAVIOUR"       VARCHAR2(2),
    "OCCURRENCE_TIME" TIMESTAMP(6) DEFAULT sysdate,
    "LAT"             VARCHAR2(255),
    "LNG"             VARCHAR2(255),
    PRIMARY KEY ("ID")
);

COMMENT
    ON TABLE T_PS_CLUE_EXTEND IS '线索扩展表';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.ID IS '主键';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.CR_BY IS '创建人';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.CR_BY_NAME IS '创建人姓名';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.CR_TIME IS '创建时间';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.UP_BY IS '最后更新人';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.UP_BY_NAME IS '最后更新人姓名';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.UP_TIME IS '最后更新时间';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.CR_DEPT IS '创建部门';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.CR_DEPT_CODE IS '创建部门编号';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND."RELATED_PLACE" IS '涉及市州';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.BEHAVIOUR IS '维权行为：0-网上煽动，1-电话串联，2-线下串联';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.METHOD IS '维权方式：0-上访，1-集权，2-维权，3-极端方式维权';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.OCCURRENCE_TIME IS '维权时间';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.CLUE_ID IS '线索Id';
COMMENT
    ON COLUMN T_PS_CLUE_EXTEND.TYPE IS '线索级别：0-本地，1-赴省，2-进京';

CREATE TABLE T_PS_CLUE_PARENT_RELATION
(
    ID           VARCHAR2(32),
    CR_BY        NVARCHAR2(64),
    CR_BY_NAME   NVARCHAR2(255),
    CR_TIME      TIMESTAMP,
    UP_BY        NVARCHAR2(64),
    UP_BY_NAME   NVARCHAR2(255),
    UP_TIME      TIMESTAMP,
    CR_DEPT      NVARCHAR2(255),
    CR_DEPT_CODE VARCHAR2(16),
    CLUE_ID      VARCHAR2(32),
    PARENT_ID    VARCHAR2(32)
);
COMMENT ON TABLE T_PS_CLUE_PARENT_RELATION IS '线索-上级线索关联表';
COMMENT ON COLUMN T_PS_CLUE_PARENT_RELATION.CLUE_ID IS '线索id';
COMMENT ON COLUMN T_PS_CLUE_PARENT_RELATION.PARENT_ID IS '上级线索id';
ALTER TABLE T_PS_CLUE_PARENT_RELATION
    ADD CONSTRAINT T_PS_CLUE_PARENT_RELATION_PK PRIMARY KEY (ID)
        ENABLE;

