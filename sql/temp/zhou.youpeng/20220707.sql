--  T_PS_CRJ_SFRY definition

-- DDL generated by <PERSON><PERSON><PERSON>
-- WARNING: It may differ from actual native database DDL

-- Drop table

-- DROP TABLE  T_PS_CRJ_SFRY;

CREATE TABLE T_PS_CRJ_SFRY (
                               "ID" VARCHAR2(32),
                               CR_BY NVARCHAR2(64),
                               CR_BY_NAME NVARCHAR2(255),
                               CR_TIME TIMESTAMP,
                               UP_BY NVARCHAR2(64),
                               UP_BY_NAME NVARCHAR2(255),
                               UP_TIME TIMESTAMP,
                               CR_DEPT NVARCHAR2(64),
                               CR_DEPT_CODE VARCHAR2(12),
                               "HOTEL_APPLY_ID" VARCHAR2(32),
                               "YWX" VARCHAR2(32),
                               "YWM" VARCHAR2(32),
                               "YWXM" VARCHAR2(32),
                               "ZWXM" VARCHAR2(32),
                               "XB" VARCHAR2(32),
                               "XB_DESC" VARCHAR2(32),
                               "<PERSON><PERSON>" VARCHAR2(32),
                               "<PERSON>Z_DESC" VARCHAR2(32),
                               "CSRQ" VARCHAR2(32),
                               "GJD<PERSON>" VARCHAR2(32),
                               "ZJZ<PERSON>" VARCHAR2(32),
                               "ZJZL_DESC" VARCHAR2(32),
                               "ZJHM" VARCHAR2(32),
                               "AREA_CODE" VARCHAR2(32),
                               "AREA_CODE_DESC" VARCHAR2(32),
                               "ADDRESS" VARCHAR2(32),
                               "QZZL" VARCHAR2(32),
                               "QZHM" VARCHAR2(32),
                               "TINGLIUQIXIAN" VARCHAR2(32),
                               "RJRQ" VARCHAR2(32),
                               "RJKA" VARCHAR2(32),
                               "JDDW" VARCHAR2(32),
                               "JDR" VARCHAR2(32),
                               "RZSJ" TIMESTAMP,
                               "RZFH" VARCHAR2(32),
                               "TFSJ" VARCHAR2(32),
                               "QIYEBIANMA" VARCHAR2(32),
                               "QIYEBMC" VARCHAR2(32),
                               "YEWULBBM" VARCHAR2(32),
                               "YEWULB" VARCHAR2(32),
                               "GXDWBM" VARCHAR2(32),
                               "GXDWMC" VARCHAR2(32),
                               "SJLX" VARCHAR2(32),
                               "STATE" VARCHAR2(32),
                               "YQCL_STATE" VARCHAR2(32),
                               "YQCL_MSG" VARCHAR2(32),
                               "YQCL_TIME" TIMESTAMP,
                               "DEP_ACTION_FLAG" VARCHAR2(32) DEFAULT 0,
                               "DEP_ACTION_TIME" TIMESTAMP,
                               "DEP_FIRSTENTER_TIME" TIMESTAMP
);
COMMENT ON COLUMN  T_PS_CRJ_SFRY."HOTEL_APPLY_ID" IS '境外住宿登记ID';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YWX" IS '英文姓';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YWM" IS '英文名';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YWXM" IS '英文姓名';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."ZWXM" IS '中文姓名';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."XB" IS '性别';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."XB_DESC" IS '性别名称';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."MZ" IS '民族';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."MZ_DESC" IS '民族名称';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."CSRQ" IS '出生日期';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."GJDQ" IS '国家地区';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."ZJZL" IS '证件种类';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."ZJZL_DESC" IS '证件种类';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."ZJHM" IS '证件号码';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."AREA_CODE" IS '省市区行政区划代码';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."AREA_CODE_DESC" IS '省市区行政区划代码中文';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."ADDRESS" IS '详细地址';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."QZZL" IS '签证种类（206）';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."QZHM" IS '签证号码';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."TINGLIUQIXIAN" IS '停留期限';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."RJRQ" IS '入境日期（YYYYMMDD格式）';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."RJKA" IS '入境口岸（字典907）';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."JDDW" IS '接待单位';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."JDR" IS '接待人';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."RZSJ" IS '入住时间';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."RZFH" IS '入住房号';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."TFSJ" IS '退房时间';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."QIYEBIANMA" IS '酒店旅馆编码';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."QIYEBMC" IS '企业名称';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YEWULBBM" IS '旅馆业务代码类别';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YEWULB" IS '旅馆业务代码名称';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."GXDWBM" IS '管辖单位编码';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."GXDWMC" IS '管辖单位名称';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."SJLX" IS '数据类型1.境外人员住宿登记 2.旅店业';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."STATE" IS '数据状态1.新增2.修改';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YQCL_STATE" IS '云墙处理状态0.未处理 1.已处理 -1.处理失败';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YQCL_MSG" IS '云墙处理失败原因';
COMMENT ON COLUMN  T_PS_CRJ_SFRY."YQCL_TIME" IS '云墙处理时间';