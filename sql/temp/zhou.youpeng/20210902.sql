-- BIGDATA.T_PS_GROUP_TYPE definition

CREATE TABLE "BIGDATA"."T_PS_GROUP_TYPE"
(	"ID" VARCHAR2(32) NOT NULL ENABLE,
     "CR_BY" NVARCHAR2(64),
     "CR_BY_NAME" NVARCHAR2(255),
     "CR_TIME" TIMESTAMP (6) DEFAULT sysdate,
     "UP_BY" NVARCHAR2(64),
     "UP_BY_NAME" NVARCHAR2(255),
     "UP_TIME" TIMESTAMP (6) DEFAULT sysdate,
     "CR_DEPT" NVARCHAR2(64),
     "CR_DEPT_CODE" VARCHAR2(12),
     "SUBJECT_ID" VARCHAR2(32) NOT NULL ENABLE,
     "NAME" NVARCHAR2(64) NOT NULL ENABLE
);

COMMENT ON TABLE BIGDATA.T_PS_PERSON_TYPE IS '群体类别表';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.CR_BY IS '创建人';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.CR_BY_NAME IS '创建人姓名';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.CR_TIME IS '创建时间';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.UP_BY IS '最后更新人';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.UP_BY_NAME IS '最后更新人姓名';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.UP_TIME IS '最后更新时间';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.CR_DEPT IS '创建部门';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.CR_DEPT_CODE IS '创建部门编号';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.SUBJECT_ID IS '主题编号';
COMMENT ON COLUMN BIGDATA.T_PS_PERSON_TYPE.NAME IS '类别名称';

-- BIGDATA.T_PS_GROUP_TYPE_RELATION definition

CREATE TABLE "BIGDATA"."T_PS_GROUP_TYPE_RELATION"
(	"ID" VARCHAR2(32) NOT NULL ENABLE,
     "CR_BY" NVARCHAR2(64),
     "CR_BY_NAME" NVARCHAR2(255),
     "CR_TIME" TIMESTAMP (6) DEFAULT sysdate,
     "UP_BY" NVARCHAR2(64),
     "UP_BY_NAME" NVARCHAR2(255),
     "UP_TIME" TIMESTAMP (6) DEFAULT sysdate,
     "CR_DEPT" NVARCHAR2(64),
     "CR_DEPT_CODE" VARCHAR2(12),
     "GROUP_ID" VARCHAR2(32) NOT NULL ENABLE,
     "TYPE_ID" VARCHAR2(32) NOT NULL ENABLE
) ;
COMMENT ON TABLE BIGDATA.T_PS_GROUP_TYPE_RELATION IS '群体-类别关联表';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.ID IS '主键';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.CR_BY IS '创建人';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.CR_BY_NAME IS '创建人姓名';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.CR_TIME IS '创建时间';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.UP_BY IS '最后更新人';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.UP_BY_NAME IS '最后更新人姓名';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.UP_TIME IS '最后更新时间';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.CR_DEPT IS '创建部门';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.CR_DEPT_CODE IS '创建部门编号';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.GROUP_ID IS '群体ID';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_TYPE_RELATION.TYPE_ID IS '类型ID';