-- Auto-generated SQL script #202112311148
UPDATE T_PS_SUBJECT x
SET x.EVENT_LIST_PROPERTY='{"title":"事件名称","source":"事件来源","emergencyLevel":"事件级别","occurrenceTime":"事发时间","address":"事发地点","appealPlace":"诉求地","groupNames":"涉事群体名称"}'
WHERE x.id = '6'
    INSERT
INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('ff80808176a869e50176b2f0e442', 'ps_ww_clue_source_group', '0', '线索来源', 'a59027d6defa4c4b9e917f05a91468', NULL, 0, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, <PERSON>AM<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>OW<PERSON>MBER, <PERSON><PERSON><PERSON>RD, PCODE, FLAG)
VALUES ('ff80808176a861e50176b25f87c2', 'ps_ww_clue_source', '1', '部省核查', 'ff80808176a869e50176b2f0e442', NULL, 0, NULL,
        NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('ff80808176a869e50176b25f87c20', 'ps_ww_clue_source', '0', '本地', 'ff80808176a869e50176b2f0e442', NULL, 0, NULL,
        NULL, NULL);


INSERT
INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('6403dd-6a93-4736-bdc6-c49754eb', 'ps_clue_action_behaviour_group', '0', '维权行为',
        'a59027d6defa4c4b9e917f05a91468', NULL, 0, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('c3aab7-028c-470c-ad92-edd3e16a', 'ps_clue_action_behaviour', '2', '线下串联', '6403dd-6a93-4736-bdc6-c49754eb',
        NULL, 0, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('72bf4d-73bb-4d68-b6e8-ac73e645', 'ps_clue_action_behaviour', '1', '电话串联', '6403dd-6a93-4736-bdc6-c49754eb',
        NULL, 0, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('069929-fc63-4234-9f50-189a579c', 'ps_clue_action_behaviour', '0', '网上煽动', '6403dd-6a93-4736-bdc6-c49754eb',
        NULL, 0, NULL, NULL, NULL);


INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('d89f7b-b9e2-48c7-894e-4bce16d', 'ps_clue_action_method_group', '0', '维权方式', 'a59027d6defa4c4b9e917f05a91468',
        NULL, 0, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('15777b-7bd8-4a20-8a75-35534b0', 'ps_clue_action_method', '3', '极端方式维权', 'd89f7b-b9e2-48c7-894e-4bce16d', NULL,
        0, NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('43f6a2-10de-4408-984b-1724340', 'ps_clue_action_method', '2', '维权', 'd89f7b-b9e2-48c7-894e-4bce16d', NULL, 0,
        NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('8aa2af-47c4-4c86-a055-09f4221', 'ps_clue_action_method', '1', '集权', 'd89f7b-b9e2-48c7-894e-4bce16d', NULL, 0,
        NULL, NULL, NULL);
INSERT INTO T_DICT
(ID, "TYPE", CODE, NAME, PID, DICTDESC, SHOWNUMBER, STANDARD, PCODE, FLAG)
VALUES ('3925e6-35fe-4d03-b149-35fa9af', 'ps_clue_action_method', '0', '上访', 'd89f7b-b9e2-48c7-894e-4bce16d', NULL, 0,
        NULL, NULL, NULL);


-- Auto-generated SQL script #202112311542
UPDATE T_PS_SUBJECT x
SET x.CLUE_LIST_FILTERS='[{"type":"option","displayName":"线索状态","key":"disposalStatus","value":["$$ps_clue_disposal_status$$"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["$$ps_ww_clue_source$$"]},{"type":"select","displayName":"线索级别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"维权方式","key":"clueActionMethod","value":["$$ps_clue_action_method$$"]},{"type":"select","displayName":"维权行为","key":"clueActionBehaviour","value":["$$ps_clue_action_behaviour$$"]},{"type":"time","displayName":"维权时间","key":"occurrenceTime","value":["&&time&&"]}]'
WHERE x.id = '6';

ALTER TABLE T_PS_CLUE MODIFY "SOURCE" VARCHAR2(2) NULL;
ALTER TABLE T_PS_CLUE MODIFY EMERGENCY_LEVEL VARCHAR2(2) NULL;
ALTER TABLE T_PS_CLUE MODIFY OPENNESS_LEVEL VARCHAR2(2) NULL;
