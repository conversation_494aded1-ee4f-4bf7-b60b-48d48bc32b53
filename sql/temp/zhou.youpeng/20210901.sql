-- BIGDATA.T_PS_GROUP_CLUE_RELATION definition

CREATE TABLE "BIGDATA"."T_PS_GROUP_CLUE_RELATION"
(	"ID" VARCHAR2(32) NOT NULL ENABLE,
     "CR_BY" NVARCHAR2(64),
     "CR_BY_NAME" NVARCHAR2(255),
     "CR_TIME" TIMESTAMP (6) DEFAULT sysdate,
     "UP_BY" NVARCHAR2(64),
     "UP_BY_NAME" NVARCHAR2(255),
     "UP_TIME" TIMESTAMP (6) DEFAULT sysdate,
     "CR_DEPT" NVARCHAR2(64),
     "CR_DEPT_CODE" VARCHAR2(12),
     "GROUP_ID" VARCHAR2(32) NOT NULL ENABLE,
     "CLUE_ID" VARCHAR2(32) NOT NULL ENABLE
)
/

COMMENT ON TABLE BIGDATA.T_PS_GROUP_CLUE_RELATION IS '群体-线索-关系表';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.ID IS '主键';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.CR_BY IS '创建人';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.CR_BY_NAME IS '创建人姓名';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.CR_TIME IS '创建时间';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.UP_BY IS '最后更新人';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.UP_BY_NAME IS '最后更新人姓名';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.UP_TIME IS '最后更新时间';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.CR_DEPT IS '创建部门';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.CR_DEPT_CODE IS '创建部门编号';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.GROUP_ID IS '群体ID';
COMMENT ON COLUMN BIGDATA.T_PS_GROUP_CLUE_RELATION.CLUE_ID IS '线索ID';

ALTER TABLE BIGDATA.T_PS_GROUP ADD BASIC_INFO VARCHAR2(100);
COMMENT ON COLUMN BIGDATA.T_PS_GROUP.BASIC_INFO IS '基本情况';


