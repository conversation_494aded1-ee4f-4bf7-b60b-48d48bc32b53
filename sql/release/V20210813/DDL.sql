-- ----------------------------
-- Table structure for T_PS_FILE_STORAGE
-- ----------------------------
DROP TABLE "T_PS_FILE_STORAGE";
CREATE TABLE "T_PS_FILE_STORAGE"
(
    "ID"             VARCHAR2(255 BYTE) NOT NULL,
    "CR_BY"          VARCHAR2(255 BYTE),
    "CR_BY_NAME"     VARCHAR2(255 BYTE),
    "CR_TIME"        TIMESTAMP(6),
    "CR_DEPT"        VARCHAR2(255 BYTE),
    "CR_DEPT_CODE"   VARCHAR2(255 BYTE),
    "PATH"           VARCHAR2(2000 BYTE),
    "URL"            VARCHAR2(2000 BYTE),
    "GROUP_NAME"     VARCHAR2(255 BYTE),
    "TYPE"           VARCHAR2(2 BYTE),
    "MD5"            VARCHAR2(255 BYTE),
    "FILE_SIZE"      NUMBER(19, 0),
    "EXTENSION_NAME" VARCHAR2(255 BYTE),
    "NAME"           VARCHAR2(255 BYTE),
    "UP_BY"          VARCHAR2(255 BYTE),
    "UP_BY_NAME"     VARCHAR2(255 BYTE),
    "UP_TIME"        TIMESTAMP(6)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."PATH" IS '文件存储路径';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."URL" IS '存储URL地址';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."GROUP_NAME" IS '存储分组名';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."TYPE" IS '文件类型，0-视频，1-文档，2-图片，3-其他';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."MD5" IS 'MD5值';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."FILE_SIZE" IS '文件大小，单位B';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."EXTENSION_NAME" IS '文件扩展名';
COMMENT ON COLUMN "T_PS_FILE_STORAGE"."NAME" IS '文件名';
COMMENT ON TABLE "T_PS_FILE_STORAGE" IS '警种专题文件存储表';

-- ----------------------------
-- Primary Key structure for table T_PS_FILE_STORAGE
-- ----------------------------
ALTER TABLE "T_PS_FILE_STORAGE"
    ADD CONSTRAINT "T_PS_FILE_STORAGE_PK" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_FILE_STORAGE
-- ----------------------------
ALTER TABLE "T_PS_FILE_STORAGE"
    ADD CONSTRAINT "SYS_C0012246" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_FILE_STORAGE"
    ADD CONSTRAINT "SYS_C0012380" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_GROUP
-- ----------------------------
DROP TABLE "T_PS_GROUP";
CREATE TABLE "T_PS_GROUP"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "NAME"         NVARCHAR2(64)     NOT NULL,
    "SUBJECT_ID"   VARCHAR2(32 BYTE) NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_GROUP"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_GROUP"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_GROUP"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_GROUP"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_GROUP"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_GROUP"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_GROUP"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_GROUP"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_GROUP"."NAME" IS '群体名称';
COMMENT ON COLUMN "T_PS_GROUP"."SUBJECT_ID" IS '主题编号';

-- ----------------------------
-- Primary Key structure for table T_PS_GROUP
-- ----------------------------
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012191" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_GROUP
-- ----------------------------
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012188" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012189" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012190" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012379" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012381" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_GROUP"
    ADD CONSTRAINT "SYS_C0012382" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_LABEL
-- ----------------------------
DROP TABLE "T_PS_LABEL";
CREATE TABLE "T_PS_LABEL"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6)     DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6)     DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "NAME"         NVARCHAR2(64)     NOT NULL,
    "SUBJECT_ID"   VARCHAR2(32 BYTE) NOT NULL,
    "REMARK"       NVARCHAR2(1000),
    "CREATE_TYPE"  VARCHAR2(2 BYTE) DEFAULT 0
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_LABEL"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_LABEL"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_LABEL"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_LABEL"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_LABEL"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_LABEL"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_LABEL"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_LABEL"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_LABEL"."NAME" IS '标签名称';
COMMENT ON COLUMN "T_PS_LABEL"."SUBJECT_ID" IS '主题编号';
COMMENT ON COLUMN "T_PS_LABEL"."REMARK" IS '备注';
COMMENT ON COLUMN "T_PS_LABEL"."CREATE_TYPE" IS '创建方式';

-- ----------------------------
-- Primary Key structure for table T_PS_LABEL
-- ----------------------------
ALTER TABLE "T_PS_LABEL"
    ADD CONSTRAINT "SYS_C0012202" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_LABEL
-- ----------------------------
ALTER TABLE "T_PS_LABEL"
    ADD CONSTRAINT "SYS_C0012199" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_LABEL"
    ADD CONSTRAINT "SYS_C0012200" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_LABEL"
    ADD CONSTRAINT "SYS_C0012201" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_MODULE
-- ----------------------------
DROP TABLE "T_PS_MODULE";
CREATE TABLE "T_PS_MODULE"
(
    "ID"      VARCHAR2(255 BYTE) NOT NULL,
    "CN_NAME" NVARCHAR2(50),
    "EN_NAME" VARCHAR2(100 BYTE),
    "TYPE"    VARCHAR2(20 BYTE) DEFAULT 'person'
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_MODULE"."CN_NAME" IS '中文名';
COMMENT ON COLUMN "T_PS_MODULE"."EN_NAME" IS '英文名';
COMMENT ON TABLE "T_PS_MODULE" IS '档案结构表';

-- ----------------------------
-- Primary Key structure for table T_PS_MODULE
-- ----------------------------
ALTER TABLE "T_PS_MODULE"
    ADD CONSTRAINT "T_PS_ARCHIVE_STRUCTURE_PK" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_MODULE
-- ----------------------------
ALTER TABLE "T_PS_MODULE"
    ADD CONSTRAINT "SYS_C0012377" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_MODULE_SUBJECT_RELATION
-- ----------------------------
DROP TABLE "T_PS_MODULE_SUBJECT_RELATION";
CREATE TABLE "T_PS_MODULE_SUBJECT_RELATION"
(
    "ID"         VARCHAR2(255 BYTE) NOT NULL,
    "SUBJECT_ID" VARCHAR2(255 BYTE) NOT NULL,
    "MODULE_ID"  VARCHAR2(255 BYTE) NOT NULL,
    "SHOW_ORDER" NUMBER(2, 0)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_MODULE_SUBJECT_RELATION"."SHOW_ORDER" IS '顺序';
COMMENT ON TABLE "T_PS_MODULE_SUBJECT_RELATION" IS '档案与警种关联表';

-- ----------------------------
-- Primary Key structure for table T_PS_MODULE_SUBJECT_RELATION
-- ----------------------------
ALTER TABLE "T_PS_MODULE_SUBJECT_RELATION"
    ADD CONSTRAINT "T_PS_ARCHIVE_SUBJECT_RELATE_PK" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_MODULE_SUBJECT_RELATION
-- ----------------------------
ALTER TABLE "T_PS_MODULE_SUBJECT_RELATION"
    ADD CONSTRAINT "SYS_C0012543" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_MODULE_SUBJECT_RELATION"
    ADD CONSTRAINT "SYS_C0012561" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_MODULE_SUBJECT_RELATION"
    ADD CONSTRAINT "SYS_C0012562" CHECK ("MODULE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_OPERATION_LOG
-- ----------------------------
DROP TABLE "T_PS_OPERATION_LOG";
CREATE TABLE "T_PS_OPERATION_LOG"
(
    "ID"               VARCHAR2(255 BYTE) NOT NULL,
    "CR_BY"            VARCHAR2(255 BYTE),
    "CR_BY_NAME"       VARCHAR2(255 BYTE),
    "CR_DEPT"          VARCHAR2(255 BYTE),
    "CR_DEPT_CODE"     VARCHAR2(255 BYTE),
    "CR_TIME"          TIMESTAMP(6) DEFAULT current_timestamp,
    "OPERATE_MODULE"   VARCHAR2(2 BYTE),
    "OPERATOR"         VARCHAR2(2 BYTE),
    "OVERVIEW"         NVARCHAR2(500),
    "DETAIL"           CLOB,
    "IP_ADDR"          VARCHAR2(255 BYTE),
    "TARGET_OBJECT_ID" VARCHAR2(255 BYTE),
    "CR_BY_USERNAME"   VARCHAR2(255 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_BY" IS '创建人ID';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_DEPT" IS '创建人所属部门';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_DEPT_CODE" IS '创建人所属部门编号';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."OPERATE_MODULE" IS '操作模块';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."OPERATOR" IS '操作类型';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."OVERVIEW" IS '操作概述';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."DETAIL" IS '操作详情';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."IP_ADDR" IS 'IP地址';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."TARGET_OBJECT_ID" IS '目标对象id';
COMMENT ON TABLE "T_PS_OPERATION_LOG" IS '操作日志';

-- ----------------------------
-- Checks structure for table T_PS_OPERATION_LOG
-- ----------------------------
ALTER TABLE "T_PS_OPERATION_LOG"
    ADD CONSTRAINT "SYS_C0012386" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table T_PS_OPERATION_LOG
-- ----------------------------
CREATE INDEX "TARGET_OBJECT_ID_IDX"
    ON "T_PS_OPERATION_LOG" ("TARGET_OBJECT_ID" ASC)
    LOGGING
    VISIBLE
    PCTFREE 10
    INITRANS 2
    STORAGE (
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
    );


-- ----------------------------
-- Table structure for T_PS_PERSON
-- ----------------------------
DROP TABLE "T_PS_PERSON";
CREATE TABLE "T_PS_PERSON"
(
    "ID"                   VARCHAR2(32 BYTE)          NOT NULL,
    "CR_BY"                NVARCHAR2(64),
    "CR_BY_NAME"           NVARCHAR2(255),
    "CR_TIME"              TIMESTAMP(6)     DEFAULT sysdate,
    "UP_BY"                NVARCHAR2(64),
    "UP_BY_NAME"           NVARCHAR2(255),
    "UP_TIME"              TIMESTAMP(6)     DEFAULT sysdate,
    "CR_DEPT"              NVARCHAR2(64),
    "CR_DEPT_CODE"         VARCHAR2(12 BYTE),
    "NAME"                 NVARCHAR2(64)              NOT NULL,
    "ID_NUMBER"            VARCHAR2(18 BYTE)          NOT NULL,
    "GENDER"               NVARCHAR2(2),
    "FORMER_NAME"          NVARCHAR2(64),
    "NICK_NAME"            NVARCHAR2(64),
    "NATION"               NVARCHAR2(64),
    "POLITICAL_STATUS"     NVARCHAR2(64),
    "RELIGIOUS_BELIEF"     VARCHAR2(64 BYTE),
    "CURRENT_JOB"          VARCHAR2(64 BYTE),
    "CONTACT_INFORMATION"  NVARCHAR2(500),
    "REGISTERED_RESIDENCE" NVARCHAR2(100),
    "CURRENT_RESIDENCE"    NVARCHAR2(100),
    "CONTROL_STATUS"       VARCHAR2(1 BYTE) DEFAULT 1 NOT NULL,
    "OUT_TIME"             TIMESTAMP(6),
    "OUT_DIRECTION"        VARCHAR2(255 BYTE),
    "MARITAL_STATUS"       VARCHAR2(2 BYTE),
    "BASIC_INFO"           NVARCHAR2(1000),
    "CONTROL_LEVEL"        VARCHAR2(2 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON"."NAME" IS '姓名';
COMMENT ON COLUMN "T_PS_PERSON"."ID_NUMBER" IS '身份证号码';
COMMENT ON COLUMN "T_PS_PERSON"."GENDER" IS '性别';
COMMENT ON COLUMN "T_PS_PERSON"."FORMER_NAME" IS '曾用名';
COMMENT ON COLUMN "T_PS_PERSON"."NICK_NAME" IS '绰号';
COMMENT ON COLUMN "T_PS_PERSON"."NATION" IS '民族';
COMMENT ON COLUMN "T_PS_PERSON"."POLITICAL_STATUS" IS '政治面貌';
COMMENT ON COLUMN "T_PS_PERSON"."RELIGIOUS_BELIEF" IS '宗教信仰';
COMMENT ON COLUMN "T_PS_PERSON"."CURRENT_JOB" IS '现职业';
COMMENT ON COLUMN "T_PS_PERSON"."CONTACT_INFORMATION" IS '联系方式';
COMMENT ON COLUMN "T_PS_PERSON"."REGISTERED_RESIDENCE" IS '户籍地';
COMMENT ON COLUMN "T_PS_PERSON"."CURRENT_RESIDENCE" IS '现住地';
COMMENT ON COLUMN "T_PS_PERSON"."CONTROL_STATUS" IS '管控状态';
COMMENT ON COLUMN "T_PS_PERSON"."OUT_TIME" IS '流出时间';
COMMENT ON COLUMN "T_PS_PERSON"."OUT_DIRECTION" IS '流出方向';
COMMENT ON COLUMN "T_PS_PERSON"."MARITAL_STATUS" IS '婚姻状态';
COMMENT ON COLUMN "T_PS_PERSON"."BASIC_INFO" IS '基本情况';
COMMENT ON COLUMN "T_PS_PERSON"."CONTROL_LEVEL" IS '管控级别';

-- ----------------------------
-- Checks structure for table T_PS_PERSON
-- ----------------------------
ALTER TABLE "T_PS_PERSON"
    ADD CONSTRAINT "SYS_C0012387" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON"
    ADD CONSTRAINT "SYS_C0012388" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON"
    ADD CONSTRAINT "SYS_C0012389" CHECK ("ID_NUMBER" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON"
    ADD CONSTRAINT "SYS_C0012390" CHECK ("CONTROL_STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_ASSIGN
-- ----------------------------
DROP TABLE "T_PS_PERSON_ASSIGN";
CREATE TABLE "T_PS_PERSON_ASSIGN"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6),
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6),
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "SUBJECT_ID"   VARCHAR2(32 BYTE),
    "BUREAU_CODE"  VARCHAR2(32 BYTE),
    "BUREAU_NAME"  NVARCHAR2(255)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."SUBJECT_ID" IS '专题id';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."BUREAU_CODE" IS '指派分局编号';
COMMENT ON COLUMN "T_PS_PERSON_ASSIGN"."BUREAU_NAME" IS '分局名称';
COMMENT ON TABLE "T_PS_PERSON_ASSIGN" IS '指派到区县分局';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_ASSIGN
-- ----------------------------
ALTER TABLE "T_PS_PERSON_ASSIGN"
    ADD CONSTRAINT "SYS_C0012396" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_ASSIGN"
    ADD CONSTRAINT "SYS_C0012397" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_BANK_CARD
-- ----------------------------
DROP TABLE "T_PS_PERSON_BANK_CARD";
CREATE TABLE "T_PS_PERSON_BANK_CARD"
(
    "ID"               VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"            NVARCHAR2(64),
    "CR_BY_NAME"       NVARCHAR2(255),
    "CR_TIME"          TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"            NVARCHAR2(64),
    "UP_BY_NAME"       NVARCHAR2(255),
    "UP_TIME"          TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"          NVARCHAR2(64),
    "CR_DEPT_CODE"     VARCHAR2(12 BYTE),
    "PERSON_ID"        VARCHAR2(32 BYTE)  NOT NULL,
    "BANK_CARD_NUMBER" VARCHAR2(255 BYTE) NOT NULL,
    "BANK_OF_DEPOSIT"  VARCHAR2(255 BYTE) NOT NULL,
    "USE_TYPE"         NUMBER             NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."BANK_CARD_NUMBER" IS '银行卡号';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."BANK_OF_DEPOSIT" IS '开户行';
COMMENT ON COLUMN "T_PS_PERSON_BANK_CARD"."USE_TYPE" IS '使用状态';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_BANK_CARD
-- ----------------------------
ALTER TABLE "T_PS_PERSON_BANK_CARD"
    ADD CONSTRAINT "SYS_C0012391" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_BANK_CARD"
    ADD CONSTRAINT "SYS_C0012392" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_BANK_CARD"
    ADD CONSTRAINT "SYS_C0012393" CHECK ("BANK_CARD_NUMBER" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_BANK_CARD"
    ADD CONSTRAINT "SYS_C0012394" CHECK ("BANK_OF_DEPOSIT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_BANK_CARD"
    ADD CONSTRAINT "SYS_C0012395" CHECK ("USE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_CASE_EVENT
-- ----------------------------
DROP TABLE "T_PS_PERSON_CASE_EVENT";
CREATE TABLE "T_PS_PERSON_CASE_EVENT"
(
    "ID"               VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"            NVARCHAR2(64),
    "CR_BY_NAME"       NVARCHAR2(255),
    "CR_TIME"          TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"            NVARCHAR2(64),
    "UP_BY_NAME"       NVARCHAR2(255),
    "UP_TIME"          TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"          NVARCHAR2(64),
    "CR_DEPT_CODE"     VARCHAR2(12 BYTE),
    "PERSON_ID"        VARCHAR2(32 BYTE)  NOT NULL,
    "NAME"             VARCHAR2(255 BYTE) NOT NULL,
    "CASE_TYPE"        VARCHAR2(255 BYTE) NOT NULL,
    "HAPPEN_TIME"      TIMESTAMP(6)       NOT NULL,
    "HAPPEN_SITUATION" VARCHAR2(255 BYTE) NOT NULL,
    "POLICE_SITUATION" VARCHAR2(255 BYTE) NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."NAME" IS '案事件名称';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."CASE_TYPE" IS '案事件类型';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."HAPPEN_TIME" IS '案件发生时间';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."HAPPEN_SITUATION" IS '发生地点';
COMMENT ON COLUMN "T_PS_PERSON_CASE_EVENT"."POLICE_SITUATION" IS '派出所';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_CASE_EVENT
-- ----------------------------
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012403" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012404" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012405" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012406" CHECK ("CASE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012407" CHECK ("HAPPEN_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012408" CHECK ("HAPPEN_SITUATION" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CASE_EVENT"
    ADD CONSTRAINT "SYS_C0012409" CHECK ("POLICE_SITUATION" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_CONTROL
-- ----------------------------
DROP TABLE "T_PS_PERSON_CONTROL";
CREATE TABLE "T_PS_PERSON_CONTROL"
(
    "ID"                  VARCHAR2(32 BYTE)           NOT NULL,
    "CR_BY"               NVARCHAR2(64),
    "CR_BY_NAME"          NVARCHAR2(255),
    "CR_TIME"             TIMESTAMP(6)      DEFAULT sysdate,
    "UP_BY"               NVARCHAR2(64),
    "UP_BY_NAME"          NVARCHAR2(255),
    "UP_TIME"             TIMESTAMP(6)      DEFAULT sysdate,
    "CR_DEPT"             NVARCHAR2(64),
    "CR_DEPT_CODE"        VARCHAR2(12 BYTE),
    "PERSON_ID"           VARCHAR2(32 BYTE)           NOT NULL,
    "POLICE_STATION_NAME" NVARCHAR2(255),
    "LEADER_NAME"         NVARCHAR2(64),
    "LEADER_JOB"          NVARCHAR2(64),
    "LEADER_CONTACT"      NVARCHAR2(64),
    "RESPONSIBLE_NAME"    NVARCHAR2(64),
    "RESPONSIBLE_JOB"     NVARCHAR2(64),
    "RESPONSIBLE_CONTACT" NVARCHAR2(64),
    "SUBJECT_ID"          VARCHAR2(32 BYTE) DEFAULT 1 NOT NULL,
    "POLICE_STATION_CODE" VARCHAR2(32 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."ID" IS '管控信息id';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."POLICE_STATION_NAME" IS '派出所';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."LEADER_NAME" IS '派出所责任领导';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."LEADER_JOB" IS '责任领导职务';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."LEADER_CONTACT" IS '责任领导联系方式';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_NAME" IS '责任人';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_JOB" IS '责任人职务';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_CONTACT" IS '责任人联系方式';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."SUBJECT_ID" IS '专题id';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."POLICE_STATION_CODE" IS '派出所code';
COMMENT ON TABLE "T_PS_PERSON_CONTROL" IS '管控信息';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_CONTROL
-- ----------------------------
ALTER TABLE "T_PS_PERSON_CONTROL"
    ADD CONSTRAINT "SYS_C0012410" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CONTROL"
    ADD CONSTRAINT "SYS_C0012411" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_CONTROL"
    ADD CONSTRAINT "SYS_C0012412" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_EDUCATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_EDUCATION";
CREATE TABLE "T_PS_PERSON_EDUCATION"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "BEGIN_TIME"   TIMESTAMP(6)      NOT NULL,
    "END_TIME"     TIMESTAMP(6),
    "SCHOOL"       NVARCHAR2(255)    NOT NULL,
    "SUBJECT"      NVARCHAR2(255)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."BEGIN_TIME" IS '起始时间';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."END_TIME" IS '结束时间';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."SCHOOL" IS '毕业院校';
COMMENT ON COLUMN "T_PS_PERSON_EDUCATION"."SUBJECT" IS '专业';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_EDUCATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_EDUCATION"
    ADD CONSTRAINT "SYS_C0012398" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_EDUCATION"
    ADD CONSTRAINT "SYS_C0012399" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_EDUCATION"
    ADD CONSTRAINT "SYS_C0012400" CHECK ("BEGIN_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_EDUCATION"
    ADD CONSTRAINT "SYS_C0012402" CHECK ("SCHOOL" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_FILE_RELATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_FILE_RELATION";
CREATE TABLE "T_PS_PERSON_FILE_RELATION"
(
    "ID"              VARCHAR2(255 BYTE) NOT NULL,
    "CR_BY"           VARCHAR2(255 BYTE),
    "CR_BY_NAME"      VARCHAR2(255 BYTE),
    "CR_TIME"         TIMESTAMP(6),
    "UP_BY"           VARCHAR2(255 BYTE),
    "UP_BY_NAME"      VARCHAR2(255 BYTE),
    "UP_TIME"         TIMESTAMP(6),
    "CR_DEPT"         VARCHAR2(255 BYTE),
    "CR_DEPT_CODE"    VARCHAR2(255 BYTE),
    "PERSON_ID"       VARCHAR2(255 BYTE) NOT NULL,
    "FILE_STORAGE_ID" VARCHAR2(255 BYTE) NOT NULL,
    "TYPE"            VARCHAR2(2 BYTE)   NOT NULL,
    "MODULE"          VARCHAR2(2 BYTE)   NOT NULL,
    "RECORD_ID"       VARCHAR2(64 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_FILE_RELATION"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_FILE_RELATION"."FILE_STORAGE_ID" IS '文件存储id';
COMMENT ON COLUMN "T_PS_PERSON_FILE_RELATION"."TYPE" IS '文件类型，0-视频，1-文档，2-图片，3-其他';
COMMENT ON COLUMN "T_PS_PERSON_FILE_RELATION"."MODULE" IS '模块类型';
COMMENT ON COLUMN "T_PS_PERSON_FILE_RELATION"."RECORD_ID" IS '关联的记录主键，如果没有则为空';
COMMENT ON TABLE "T_PS_PERSON_FILE_RELATION" IS '人员文件关系表';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_FILE_RELATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_FILE_RELATION"
    ADD CONSTRAINT "SYS_C0012413" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_FILE_RELATION"
    ADD CONSTRAINT "SYS_C0012414" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_FILE_RELATION"
    ADD CONSTRAINT "SYS_C0012415" CHECK ("FILE_STORAGE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_FILE_RELATION"
    ADD CONSTRAINT "SYS_C0012416" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_FILE_RELATION"
    ADD CONSTRAINT "SYS_C0012417" CHECK ("MODULE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_FOOTHOLD
-- ----------------------------
DROP TABLE "T_PS_PERSON_FOOTHOLD";
CREATE TABLE "T_PS_PERSON_FOOTHOLD"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6)     DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6)     DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "START_TIME"   TIMESTAMP(6),
    "END_TIME"     TIMESTAMP(6),
    "ADDRESS"      NVARCHAR2(255),
    "STATE"        VARCHAR2(2 BYTE) DEFAULT 1,
    "NOTE"         NVARCHAR2(500)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."START_TIME" IS '开始居住时间';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."END_TIME" IS '停止居住时间';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."ADDRESS" IS '居住地址';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."STATE" IS '居住状态 0=未住 1=在住';
COMMENT ON COLUMN "T_PS_PERSON_FOOTHOLD"."NOTE" IS '备注';
COMMENT ON TABLE "T_PS_PERSON_FOOTHOLD" IS '落脚点地址表';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_FOOTHOLD
-- ----------------------------
ALTER TABLE "T_PS_PERSON_FOOTHOLD"
    ADD CONSTRAINT "SYS_C0012418" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_FOOTHOLD"
    ADD CONSTRAINT "SYS_C0012419" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_GROUP_RELATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_GROUP_RELATION";
CREATE TABLE "T_PS_PERSON_GROUP_RELATION"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "GROUP_ID"     VARCHAR2(32 BYTE) NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."GROUP_ID" IS '群体ID';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_GROUP_RELATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_GROUP_RELATION"
    ADD CONSTRAINT "SYS_C0012420" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_GROUP_RELATION"
    ADD CONSTRAINT "SYS_C0012421" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_GROUP_RELATION"
    ADD CONSTRAINT "SYS_C0012422" CHECK ("GROUP_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_IMPORT_HISTORY
-- ----------------------------
DROP TABLE "T_PS_PERSON_IMPORT_HISTORY";
CREATE TABLE "T_PS_PERSON_IMPORT_HISTORY"
(
    "ID"                 VARCHAR2(255 BYTE) NOT NULL,
    "CR_BY"              VARCHAR2(255 BYTE),
    "CR_BY_NAME"         VARCHAR2(255 BYTE),
    "CR_DEPT"            VARCHAR2(255 BYTE),
    "CR_DEPT_CODE"       VARCHAR2(255 BYTE),
    "CR_TIME"            TIMESTAMP(6)     DEFAULT current_timestamp,
    "FILE_NAME"          NVARCHAR2(100),
    "EXTENSION_NAME"     VARCHAR2(20 BYTE),
    "PATH"               VARCHAR2(255 BYTE),
    "TYPE"               VARCHAR2(2 BYTE),
    "SUBJECT_ID"         VARCHAR2(255 BYTE),
    "UP_BY"              VARCHAR2(255 BYTE),
    "UP_BY_NAME"         VARCHAR2(255 BYTE),
    "UP_TIME"            TIMESTAMP(6),
    "INITIAL_HISTORY_ID" VARCHAR2(255 BYTE),
    "IS_TEMPLATE"        VARCHAR2(1 BYTE) DEFAULT 0
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."FILE_NAME" IS '文件名';
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."EXTENSION_NAME" IS '文件扩展名';
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."PATH" IS '文件路径';
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."TYPE" IS '文件类型';
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."SUBJECT_ID" IS '主题主键';
COMMENT ON COLUMN "T_PS_PERSON_IMPORT_HISTORY"."INITIAL_HISTORY_ID" IS '原始记录主键';
COMMENT ON TABLE "T_PS_PERSON_IMPORT_HISTORY" IS '人员导入历史记录';

-- ----------------------------
-- Primary Key structure for table T_PS_PERSON_IMPORT_HISTORY
-- ----------------------------
ALTER TABLE "T_PS_PERSON_IMPORT_HISTORY"
    ADD CONSTRAINT "T_PS_PERSON_IMPORT_HISTORY_PK" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_PERSON_IMPORT_HISTORY
-- ----------------------------
ALTER TABLE "T_PS_PERSON_IMPORT_HISTORY"
    ADD CONSTRAINT "SYS_C0012588" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_JUDGEMENT
-- ----------------------------
DROP TABLE "T_PS_PERSON_JUDGEMENT";
CREATE TABLE "T_PS_PERSON_JUDGEMENT"
(
    "ID"             VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"          NVARCHAR2(64),
    "CR_BY_NAME"     NVARCHAR2(255),
    "CR_TIME"        TIMESTAMP(6),
    "UP_BY"          NVARCHAR2(64),
    "UP_BY_NAME"     NVARCHAR2(255),
    "UP_TIME"        TIMESTAMP(6),
    "CR_DEPT"        NVARCHAR2(64),
    "CR_DEPT_CODE"   VARCHAR2(12 BYTE),
    "PERSON_ID"      VARCHAR2(32 BYTE) NOT NULL,
    "JUDGEMENT_DATE" TIMESTAMP(6),
    "END_DATE"       TIMESTAMP(6),
    "LIMIT_TIME"     NUMBER,
    "LIMIT_UNIT"     VARCHAR2(10 BYTE),
    "REASON"         NVARCHAR2(500)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."JUDGEMENT_DATE" IS '裁决时间';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."END_DATE" IS '截止时间';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."LIMIT_TIME" IS '限制年限';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."LIMIT_UNIT" IS '限制年限单位';
COMMENT ON COLUMN "T_PS_PERSON_JUDGEMENT"."REASON" IS '裁决原因';
COMMENT ON TABLE "T_PS_PERSON_JUDGEMENT" IS '人员档案-裁决信息';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_JUDGEMENT
-- ----------------------------
ALTER TABLE "T_PS_PERSON_JUDGEMENT"
    ADD CONSTRAINT "SYS_C0012423" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_JUDGEMENT"
    ADD CONSTRAINT "SYS_C0012424" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_LABEL_RELATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_LABEL_RELATION";
CREATE TABLE "T_PS_PERSON_LABEL_RELATION"
(
    "ID"           VARCHAR2(255 BYTE) NOT NULL,
    "CR_BY"        VARCHAR2(255 BYTE),
    "CR_BY_NAME"   VARCHAR2(255 BYTE),
    "CR_TIME"      TIMESTAMP(6),
    "UP_BY"        VARCHAR2(255 BYTE),
    "UP_BY_NAME"   VARCHAR2(255 BYTE),
    "UP_TIME"      TIMESTAMP(6),
    "CR_DEPT"      VARCHAR2(255 BYTE),
    "CR_DEPT_CODE" VARCHAR2(255 BYTE),
    "PERSON_ID"    VARCHAR2(255 BYTE),
    "LABEL_ID"     VARCHAR2(255 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON TABLE "T_PS_PERSON_LABEL_RELATION" IS '人员标签关系表';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_LABEL_RELATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_LABEL_RELATION"
    ADD CONSTRAINT "SYS_C0012425" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_MOBILE_PHONE
-- ----------------------------
DROP TABLE "T_PS_PERSON_MOBILE_PHONE";
CREATE TABLE "T_PS_PERSON_MOBILE_PHONE"
(
    "ID"               VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"            NVARCHAR2(64),
    "CR_BY_NAME"       NVARCHAR2(255),
    "CR_TIME"          TIMESTAMP(6),
    "UP_BY"            NVARCHAR2(64),
    "UP_BY_NAME"       NVARCHAR2(255),
    "UP_TIME"          TIMESTAMP(6),
    "CR_DEPT"          NVARCHAR2(64),
    "CR_DEPT_CODE"     VARCHAR2(255 BYTE),
    "PERSON_ID"        VARCHAR2(255 BYTE) NOT NULL,
    "PHONE_NUMBER"     VARCHAR2(20 BYTE)  NOT NULL,
    "PHONE_STATUS"     VARCHAR2(2 BYTE)   NOT NULL,
    "PHONE_USE_STATUS" VARCHAR2(2 BYTE)   NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."PHONE_NUMBER" IS '手机号';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."PHONE_STATUS" IS '0 停用 1 启用';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."PHONE_USE_STATUS" IS '0 不常用 1 常用';
COMMENT ON TABLE "T_PS_PERSON_MOBILE_PHONE" IS '车辆信息';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_MOBILE_PHONE
-- ----------------------------
ALTER TABLE "T_PS_PERSON_MOBILE_PHONE"
    ADD CONSTRAINT "SYS_C0012426" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILE_PHONE"
    ADD CONSTRAINT "SYS_C0012427" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILE_PHONE"
    ADD CONSTRAINT "SYS_C0012547" CHECK ("PHONE_NUMBER" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILE_PHONE"
    ADD CONSTRAINT "SYS_C0012548" CHECK ("PHONE_STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILE_PHONE"
    ADD CONSTRAINT "SYS_C0012549" CHECK ("PHONE_USE_STATUS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_MOBILITY
-- ----------------------------
DROP TABLE "T_PS_PERSON_MOBILITY";
CREATE TABLE "T_PS_PERSON_MOBILITY"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "MOVE_TIME"    TIMESTAMP(6)      NOT NULL,
    "MOVE_TYPE"    VARCHAR2(2 BYTE)  NOT NULL,
    "LOCATION"     NVARCHAR2(255),
    "NOTE"         NVARCHAR2(255)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."MOVE_TIME" IS '流动时间';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."MOVE_TYPE" IS '流动类型（流入=1，流出=2）';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."LOCATION" IS '流动地址';
COMMENT ON COLUMN "T_PS_PERSON_MOBILITY"."NOTE" IS '备注';
COMMENT ON TABLE "T_PS_PERSON_MOBILITY" IS '流动信息';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_MOBILITY
-- ----------------------------
ALTER TABLE "T_PS_PERSON_MOBILITY"
    ADD CONSTRAINT "SYS_C0012431" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILITY"
    ADD CONSTRAINT "SYS_C0012432" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILITY"
    ADD CONSTRAINT "SYS_C0012433" CHECK ("MOVE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOBILITY"
    ADD CONSTRAINT "SYS_C0012434" CHECK ("MOVE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_MOVEMENT
-- ----------------------------
DROP TABLE "T_PS_PERSON_MOVEMENT";
CREATE TABLE "T_PS_PERSON_MOVEMENT"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "MOVE_TIME"    TIMESTAMP(6)      NOT NULL,
    "MOVE_TYPE"    VARCHAR2(2 BYTE)  NOT NULL,
    "LOCATION"     NVARCHAR2(255)    NOT NULL,
    "NOTE"         NVARCHAR2(255)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."MOVE_TIME" IS '流动时间';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."MOVE_TYPE" IS '流动类型（流入=1，流出=2）';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."LOCATION" IS '流动地址';
COMMENT ON COLUMN "T_PS_PERSON_MOVEMENT"."NOTE" IS '备注';
COMMENT ON TABLE "T_PS_PERSON_MOVEMENT" IS '流动信息';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_MOVEMENT
-- ----------------------------
ALTER TABLE "T_PS_PERSON_MOVEMENT"
    ADD CONSTRAINT "SYS_C0012312" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOVEMENT"
    ADD CONSTRAINT "SYS_C0012313" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOVEMENT"
    ADD CONSTRAINT "SYS_C0012314" CHECK ("MOVE_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOVEMENT"
    ADD CONSTRAINT "SYS_C0012315" CHECK ("MOVE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_MOVEMENT"
    ADD CONSTRAINT "SYS_C0012316" CHECK ("LOCATION" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_RELATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_RELATION";
CREATE TABLE "T_PS_PERSON_RELATION"
(
    "ID"                  VARCHAR2(32 BYTE)  NOT NULL,
    "TYPE"                VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"               NVARCHAR2(64),
    "CR_BY_NAME"          NVARCHAR2(255),
    "CR_TIME"             TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"               NVARCHAR2(64),
    "UP_BY_NAME"          NVARCHAR2(255),
    "UP_TIME"             TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"             NVARCHAR2(64),
    "CR_DEPT_CODE"        VARCHAR2(255 BYTE),
    "PERSON_ID"           VARCHAR2(255 BYTE) NOT NULL,
    "RELATION"            VARCHAR2(100 BYTE),
    "NAME"                VARCHAR2(100 BYTE),
    "ID_NUMBER"           VARCHAR2(100 BYTE),
    "CURRENT_RESIDENCE"   NVARCHAR2(200),
    "CONTACT_INFORMATION" VARCHAR2(100 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."TYPE" IS '类型：family,society';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."RELATION" IS '关系';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."NAME" IS '名字';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."ID_NUMBER" IS '身份证号码';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CURRENT_RESIDENCE" IS '现住址';
COMMENT ON COLUMN "T_PS_PERSON_RELATION"."CONTACT_INFORMATION" IS '联系方式';
COMMENT ON TABLE "T_PS_PERSON_RELATION" IS '家庭关系';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_RELATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_RELATION"
    ADD CONSTRAINT "SYS_C0012428" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_RELATION"
    ADD CONSTRAINT "SYS_C0012429" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_RELATION"
    ADD CONSTRAINT "SYS_C0012430" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_STAY
-- ----------------------------
DROP TABLE "T_PS_PERSON_STAY";
CREATE TABLE "T_PS_PERSON_STAY"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6)     DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6)     DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "START_TIME"   TIMESTAMP(6),
    "END_TIME"     TIMESTAMP(6),
    "ADDRESS"      NVARCHAR2(255),
    "STATE"        VARCHAR2(2 BYTE) DEFAULT 1,
    "NOTE"         NVARCHAR2(500)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_STAY"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."START_TIME" IS '开始居住时间';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."END_TIME" IS '停止居住时间';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."ADDRESS" IS '居住地址';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."STATE" IS '居住状态 0=未住 1=在住';
COMMENT ON COLUMN "T_PS_PERSON_STAY"."NOTE" IS '备注';
COMMENT ON TABLE "T_PS_PERSON_STAY" IS '落脚点地址表';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_STAY
-- ----------------------------
ALTER TABLE "T_PS_PERSON_STAY"
    ADD CONSTRAINT "SYS_C0012317" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_STAY"
    ADD CONSTRAINT "SYS_C0012318" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_SUBJECT_RELATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_SUBJECT_RELATION";
CREATE TABLE "T_PS_PERSON_SUBJECT_RELATION"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE),
    "SUBJECT_ID"   VARCHAR2(32 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_SUBJECT_RELATION"."SUBJECT_ID" IS '主题ID';
COMMENT ON TABLE "T_PS_PERSON_SUBJECT_RELATION" IS '人员-主题 关系表';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_SUBJECT_RELATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_SUBJECT_RELATION"
    ADD CONSTRAINT "SYS_C0012436" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_TYPE
-- ----------------------------
DROP TABLE "T_PS_PERSON_TYPE";
CREATE TABLE "T_PS_PERSON_TYPE"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "SUBJECT_ID"   VARCHAR2(32 BYTE) NOT NULL,
    "NAME"         NVARCHAR2(64)     NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."SUBJECT_ID" IS '主题编号';
COMMENT ON COLUMN "T_PS_PERSON_TYPE"."NAME" IS '类别名称';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_TYPE
-- ----------------------------
ALTER TABLE "T_PS_PERSON_TYPE"
    ADD CONSTRAINT "SYS_C0012437" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_TYPE"
    ADD CONSTRAINT "SYS_C0012438" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_TYPE"
    ADD CONSTRAINT "SYS_C0012439" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_TYPE_RELATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_TYPE_RELATION";
CREATE TABLE "T_PS_PERSON_TYPE_RELATION"
(
    "ID"           VARCHAR2(32 BYTE) NOT NULL,
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PERSON_ID"    VARCHAR2(32 BYTE) NOT NULL,
    "TYPE_ID"      VARCHAR2(32 BYTE) NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_TYPE_RELATION"."TYPE_ID" IS '类型ID';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_TYPE_RELATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_TYPE_RELATION"
    ADD CONSTRAINT "SYS_C0012440" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_TYPE_RELATION"
    ADD CONSTRAINT "SYS_C0012441" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_TYPE_RELATION"
    ADD CONSTRAINT "SYS_C0012442" CHECK ("TYPE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_VEHICLE
-- ----------------------------
DROP TABLE "T_PS_PERSON_VEHICLE";
CREATE TABLE "T_PS_PERSON_VEHICLE"
(
    "ID"             VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"          NVARCHAR2(64),
    "CR_BY_NAME"     NVARCHAR2(255),
    "CR_TIME"        TIMESTAMP(6),
    "UP_BY"          NVARCHAR2(64),
    "UP_BY_NAME"     NVARCHAR2(255),
    "UP_TIME"        TIMESTAMP(6),
    "CR_DEPT"        NVARCHAR2(64),
    "CR_DEPT_CODE"   VARCHAR2(255 BYTE),
    "PERSON_ID"      VARCHAR2(255 BYTE) NOT NULL,
    "TYPE"           VARCHAR2(100 BYTE),
    "VEHICLE_NUMBER" VARCHAR2(100 BYTE),
    "OWNER"          VARCHAR2(100 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."UP_BY_NAME" IS '最后更新姓名';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."PERSON_ID" IS '相关人员人员ID';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."TYPE" IS '种类';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."VEHICLE_NUMBER" IS '车牌号';
COMMENT ON COLUMN "T_PS_PERSON_VEHICLE"."OWNER" IS '所属人';
COMMENT ON TABLE "T_PS_PERSON_VEHICLE" IS '车辆信息';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_VEHICLE
-- ----------------------------
ALTER TABLE "T_PS_PERSON_VEHICLE"
    ADD CONSTRAINT "SYS_C0012443" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VEHICLE"
    ADD CONSTRAINT "SYS_C0012444" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_VIRTUAL_IDENTITY
-- ----------------------------
DROP TABLE "T_PS_PERSON_VIRTUAL_IDENTITY";
CREATE TABLE "T_PS_PERSON_VIRTUAL_IDENTITY"
(
    "ID"                VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"             NVARCHAR2(64),
    "CR_BY_NAME"        NVARCHAR2(255),
    "CR_TIME"           TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"             NVARCHAR2(64),
    "UP_BY_NAME"        NVARCHAR2(255),
    "UP_TIME"           TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"           NVARCHAR2(64),
    "CR_DEPT_CODE"      VARCHAR2(12 BYTE),
    "PERSON_ID"         VARCHAR2(32 BYTE)  NOT NULL,
    "VIRTUAL_TYPE"      NUMBER             NOT NULL,
    "VIRTUAL_NUMBER"    VARCHAR2(255 BYTE) NOT NULL,
    "VIRTUAL_TYPE_NAME" VARCHAR2(255 BYTE)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."VIRTUAL_TYPE" IS '虚拟身份类型';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."VIRTUAL_NUMBER" IS '虚拟号码';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_IDENTITY"."VIRTUAL_TYPE_NAME" IS '自定义身份类型名称';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_VIRTUAL_IDENTITY
-- ----------------------------
ALTER TABLE "T_PS_PERSON_VIRTUAL_IDENTITY"
    ADD CONSTRAINT "SYS_C0012445" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VIRTUAL_IDENTITY"
    ADD CONSTRAINT "SYS_C0012446" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VIRTUAL_IDENTITY"
    ADD CONSTRAINT "SYS_C0012447" CHECK ("VIRTUAL_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VIRTUAL_IDENTITY"
    ADD CONSTRAINT "SYS_C0012448" CHECK ("VIRTUAL_NUMBER" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_VIRTUAL_TYPE
-- ----------------------------
DROP TABLE "T_PS_PERSON_VIRTUAL_TYPE";
CREATE TABLE "T_PS_PERSON_VIRTUAL_TYPE"
(
    "TYPE" NUMBER             NOT NULL,
    "NAME" VARCHAR2(255 BYTE) NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_TYPE"."TYPE" IS '类型编号';
COMMENT ON COLUMN "T_PS_PERSON_VIRTUAL_TYPE"."NAME" IS '类型名称';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_VIRTUAL_TYPE
-- ----------------------------
ALTER TABLE "T_PS_PERSON_VIRTUAL_TYPE"
    ADD CONSTRAINT "SYS_C0012449" CHECK ("TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VIRTUAL_TYPE"
    ADD CONSTRAINT "SYS_C0012450" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_PERSON_VISIT_RECORD
-- ----------------------------
DROP TABLE "T_PS_PERSON_VISIT_RECORD";
CREATE TABLE "T_PS_PERSON_VISIT_RECORD"
(
    "ID"                  VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"               NVARCHAR2(64),
    "CR_BY_NAME"          NVARCHAR2(255),
    "CR_TIME"             TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"               NVARCHAR2(64),
    "UP_BY_NAME"          NVARCHAR2(255),
    "UP_TIME"             TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"             NVARCHAR2(64),
    "CR_DEPT_CODE"        VARCHAR2(12 BYTE),
    "PERSON_ID"           VARCHAR2(32 BYTE)  NOT NULL,
    "TIME"                TIMESTAMP(6)       NOT NULL,
    "METHOD"              VARCHAR2(2 BYTE)   NOT NULL,
    "IN_CONTROL"          VARCHAR2(2 BYTE)   NOT NULL,
    "OUT_OF_CONTROL_TIME" TIMESTAMP(6),
    "DESTINATION"         VARCHAR2(255 BYTE),
    "INFO"                CLOB,
    "VISIT_BY"            VARCHAR2(255 BYTE) NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."TIME" IS '走访时间';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."METHOD" IS '走访方式';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."IN_CONTROL" IS '是否在控';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."OUT_OF_CONTROL_TIME" IS '失控时间';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."DESTINATION" IS '当前去向';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."INFO" IS '走访情况';
COMMENT ON COLUMN "T_PS_PERSON_VISIT_RECORD"."VISIT_BY" IS '走访工作人员';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_VISIT_RECORD
-- ----------------------------
ALTER TABLE "T_PS_PERSON_VISIT_RECORD"
    ADD CONSTRAINT "SYS_C0012451" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VISIT_RECORD"
    ADD CONSTRAINT "SYS_C0012452" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VISIT_RECORD"
    ADD CONSTRAINT "SYS_C0012453" CHECK ("TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VISIT_RECORD"
    ADD CONSTRAINT "SYS_C0012454" CHECK ("METHOD" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VISIT_RECORD"
    ADD CONSTRAINT "SYS_C0012455" CHECK ("IN_CONTROL" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_VISIT_RECORD"
    ADD CONSTRAINT "SYS_C0012456" CHECK ("VISIT_BY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;


-- ----------------------------
-- Table structure for T_PS_PERSON_WORK_INFORMATION
-- ----------------------------
DROP TABLE "T_PS_PERSON_WORK_INFORMATION";
CREATE TABLE "T_PS_PERSON_WORK_INFORMATION"
(
    "ID"              VARCHAR2(32 BYTE)  NOT NULL,
    "CR_BY"           NVARCHAR2(64),
    "CR_BY_NAME"      NVARCHAR2(255),
    "CR_TIME"         TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"           NVARCHAR2(64),
    "UP_BY_NAME"      NVARCHAR2(255),
    "UP_TIME"         TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"         NVARCHAR2(64),
    "CR_DEPT_CODE"    VARCHAR2(12 BYTE),
    "WORK_BEGIN_TIME" TIMESTAMP(6)       NOT NULL,
    "WORK_UNIT"       VARCHAR2(255 BYTE) NOT NULL,
    "WORK_SITUATION"  VARCHAR2(255 BYTE),
    "POST"            VARCHAR2(255 BYTE) NOT NULL,
    "PERSON_ID"       VARCHAR2(32 BYTE)  NOT NULL,
    "WORK_END_TIME"   TIMESTAMP(6)
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."WORK_BEGIN_TIME" IS '开始上班时间';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."WORK_UNIT" IS '工作单位';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."WORK_SITUATION" IS '工作地点';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."POST" IS '职务';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_WORK_INFORMATION"."WORK_END_TIME" IS '结束上班时间';

-- ----------------------------
-- Checks structure for table T_PS_PERSON_WORK_INFORMATION
-- ----------------------------
ALTER TABLE "T_PS_PERSON_WORK_INFORMATION"
    ADD CONSTRAINT "SYS_C0012457" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_WORK_INFORMATION"
    ADD CONSTRAINT "SYS_C0012458" CHECK ("WORK_BEGIN_TIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_WORK_INFORMATION"
    ADD CONSTRAINT "SYS_C0012459" CHECK ("WORK_UNIT" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_WORK_INFORMATION"
    ADD CONSTRAINT "SYS_C0012461" CHECK ("POST" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_PERSON_WORK_INFORMATION"
    ADD CONSTRAINT "SYS_C0012462" CHECK ("PERSON_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;



-- ----------------------------
-- Table structure for T_PS_SUBJECT
-- ----------------------------
DROP TABLE "T_PS_SUBJECT";
CREATE TABLE "T_PS_SUBJECT"
(
    "ID"            VARCHAR2(32 BYTE) NOT NULL,
    "NAME"          NVARCHAR2(255)    NOT NULL,
    "LIST_FILTERS"  CLOB              NOT NULL,
    "LIST_PROPERTY" CLOB              NOT NULL
)
    LOGGING
    NOCOMPRESS
    PCTFREE 10
    INITRANS 1
    STORAGE
(
    INITIAL 65536
    NEXT 1048576
    MINEXTENTS 1
    MAXEXTENTS **********
    BUFFER_POOL DEFAULT
)
    PARALLEL 1
    NOCACHE
    DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "T_PS_SUBJECT"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_SUBJECT"."NAME" IS '专题名称';
COMMENT ON COLUMN "T_PS_SUBJECT"."LIST_FILTERS" IS '列表页筛选条件配置';
COMMENT ON COLUMN "T_PS_SUBJECT"."LIST_PROPERTY" IS '批量导出人员时属性';

-- ----------------------------
-- Primary Key structure for table T_PS_SUBJECT
-- ----------------------------
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012095" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_SUBJECT
-- ----------------------------
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012092" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012093" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012094" CHECK ("LIST_FILTERS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012253" CHECK ("LIST_PROPERTY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012529" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012530" CHECK ("NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012531" CHECK ("LIST_FILTERS" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "T_PS_SUBJECT"
    ADD CONSTRAINT "SYS_C0012532" CHECK ("LIST_PROPERTY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;