CREATE TABLE "T_PS_WARNING_ACCOM_RELATION" (
    "ID" VARCHAR2(32 BYTE) NOT NULL,
    "WARNING_ID" VARCHAR2(32 BYTE),
    "ACCOMMODATION_ID" VARCHAR2(32 BYTE),
    "CREATE_TIME" TIMESTAMP(6),
    CONSTRAINT "SYS_C0013275" PRIMARY KEY ("ID"),
    CONSTRAINT "SYS_C0013274" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE
);

COMMENT ON COLUMN "T_PS_WARNING_ACCOM_RELATION"."ID" IS '主键';

COMMENT ON COLUMN "T_PS_WARNING_ACCOM_RELATION"."WARNING_ID" IS '预警ID';

COMMENT ON COLUMN "T_PS_WARNING_ACCOM_RELATION"."ACCOMMODATION_ID" IS '住宿ID';

COMMENT ON COLUMN "T_PS_WARNING_ACCOM_RELATION"."CREATE_TIME" IS '创建时间'