INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('21', '基本信息', NULL, 'group');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('22', '群体成员', NULL, 'group');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('23', '相关线索', NULL, 'group');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('1', '基本信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('2', '教育信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('3', '工作信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('4', '家庭关系', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('5', '社会关系', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('6', '车辆信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('7', '手机号', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('8', '虚拟身份', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('9', '走访记录', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('10', '案事件信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('11', '人员轨迹', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('12', '落脚点地址', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('13', '流动信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('14', '管控信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('15', '涉事相关群体', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('16', '线索', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('17', '银行卡信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('18', '裁决信息', NULL, 'person');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('31', '基本信息', NULL, 'clue');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('32', '涉及人员', NULL, 'clue');
INSERT INTO "T_PS_MODULE" ("ID", "CN_NAME", "EN_NAME", "TYPE") VALUES ('33', '涉及群体', NULL, 'clue');

INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce04e09e055000000000001', '1', '基本信息', 'name', '姓名', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce14e09e055000000000001', '1', '基本信息', 'idNumber', '身份证号', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce24e09e055000000000001', '1', '基本信息', 'gender', '性别', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce34e09e055000000000001', '1', '基本信息', 'formerName', '曾用名', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce44e09e055000000000001', '1', '基本信息', 'nickName', '绰号', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce54e09e055000000000001', '1', '基本信息', 'nation', '民族', 'nation', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce64e09e055000000000001', '1', '基本信息', 'politicalStatus', '政治面貌', 'ps_political_status', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce74e09e055000000000001', '1', '基本信息', 'religiousBelief', '宗教信仰', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce84e09e055000000000001', '1', '基本信息', 'maritalStatus', '婚姻状况', 'ps_marital_status', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ce94e09e055000000000001', '1', '基本信息', 'currentJob', '现职业', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cea4e09e055000000000001', '1', '基本信息', 'contactInformation', '联系方式', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41ceb4e09e055000000000001', '1', '基本信息', 'registeredResidence', '户籍地', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cec4e09e055000000000001', '1', '基本信息', 'currentResidence', '现住址', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('ca4a8f0469a4e352e055000000000001', '1', '基本信息', 'groups', '群体类别', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cee4e09e055000000000001', '1', '基本信息', 'controlLevel', '管控级别', 'ps_zb_control_level', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cef4e09e055000000000001', '1', '基本信息', 'basicInfo', '基本情况', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf04e09e055000000000001', '2', '教育信息', 'beginTime', '开始时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf14e09e055000000000001', '2', '教育信息', 'endTime', '结束时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf24e09e055000000000001', '2', '教育信息', 'school', '毕业院校', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf34e09e055000000000001', '2', '教育信息', 'subject', '专业', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf44e09e055000000000001', '3', '工作信息', 'workBeginTime', '开始时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf54e09e055000000000001', '3', '工作信息', 'workEndTime', '结束时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf64e09e055000000000001', '3', '工作信息', 'workUnit', '工作单位', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf74e09e055000000000001', '3', '工作信息', 'workSituation', '工作地点', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf84e09e055000000000001', '3', '工作信息', 'post', '职务', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cf94e09e055000000000001', '4', '家庭关系', 'relation', '关系', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cfa4e09e055000000000001', '4', '家庭关系', 'name', '姓名', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cfb4e09e055000000000001', '4', '家庭关系', 'idNumber', '身份证号码', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cfc4e09e055000000000001', '4', '家庭关系', 'currentResidence', '现住址', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cfd4e09e055000000000001', '4', '家庭关系', 'contactInformation', '联系方式', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cfe4e09e055000000000001', '5', '社会关系', 'relation', '关系', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41cff4e09e055000000000001', '5', '社会关系', 'name', '姓名', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d004e09e055000000000001', '5', '社会关系', 'idNumber', '身份证号码', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d014e09e055000000000001', '5', '社会关系', 'currentResidence', '现住址', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d024e09e055000000000001', '5', '社会关系', 'contactInformation', '联系方式', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d034e09e055000000000001', '6', '车辆信息', 'type', '车辆类型', 'ps_vehicle_type', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d044e09e055000000000001', '6', '车辆信息', 'vehicleNumber', '车牌号', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d054e09e055000000000001', '6', '车辆信息', 'owner', '所属人', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d064e09e055000000000001', '7', '手机号', 'phoneNumber', '电话号码', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d074e09e055000000000001', '7', '手机号', 'phoneStatus', '电话状态', 'ps_phone_status', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d084e09e055000000000001', '7', '手机号', 'phoneUseStatus', '是否常用', 'ps_phone_use_status', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d094e09e055000000000001', '8', '虚拟身份', 'virtualType', '身份类型', 'ps_virtual_identity_type', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d0a4e09e055000000000001', '8', '虚拟身份', 'virtualNumber', '虚拟号码', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d0b4e09e055000000000001', '8', '虚拟身份', 'virtualTypeName', '自定义虚拟身份名称', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d0c4e09e055000000000001', '9', '走访记录', 'time', '走访时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d0d4e09e055000000000001', '9', '走访记录', 'method', '走访方式', 'ps_person_visit_method', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d0e4e09e055000000000001', '9', '走访记录', 'inControl', '是否在控', 'ps_person_in_control', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d0f4e09e055000000000001', '9', '走访记录', 'outOfControlTime', '失控时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d104e09e055000000000001', '9', '走访记录', 'destination', '当前去向', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d114e09e055000000000001', '9', '走访记录', 'info', '走访情况', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d124e09e055000000000001', '9', '走访记录', 'visitBy', '走访工作人员', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d134e09e055000000000001', '10', '案事件信息', 'name', '案事件名称', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d144e09e055000000000001', '10', '案事件信息', 'caseType', '案事件类型', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d154e09e055000000000001', '10', '案事件信息', 'happenTime', '发生时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d164e09e055000000000001', '10', '案事件信息', 'happenSituation', '发生地点', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d174e09e055000000000001', '10', '案事件信息', 'policeSituation', '派出所', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d184e09e055000000000001', '12', '落脚点地址', 'startTime', '开始时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d194e09e055000000000001', '12', '落脚点地址', 'endTime', '结束时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d1a4e09e055000000000001', '12', '落脚点地址', 'address', '居住地址', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d1b4e09e055000000000001', '12', '落脚点地址', 'state', '居住状态', 'ps_person_living_status', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d1c4e09e055000000000001', '12', '落脚点地址', 'note', '备注', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d1d4e09e055000000000001', '13', '流动信息', 'moveTime', '流动时间', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d1e4e09e055000000000001', '13', '流动信息', 'moveType', '流动类型', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d1f4e09e055000000000001', '13', '流动信息', 'location', '地址', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d204e09e055000000000001', '13', '流动信息', 'note', '备注', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d214e09e055000000000001', '14', '管控信息', 'policeStationName', '派出所名称', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d224e09e055000000000001', '14', '管控信息', 'leaderName', '派出所责任领导', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d234e09e055000000000001', '14', '管控信息', 'leaderContact', '责任领导联系方式', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d244e09e055000000000001', '14', '管控信息', 'responsibleName', '责任人', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d254e09e055000000000001', '14', '管控信息', 'responsibleContact', '责任人联系方式', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d264e09e055000000000001', '17', '银行卡信息', 'bankCardNumber', '银行卡号', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d274e09e055000000000001', '17', '银行卡信息', 'bankOfDeposit', '开户行', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d284e09e055000000000001', '17', '银行卡信息', 'useType', '使用状态', 'ps_bank_card_status', '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d294e09e055000000000001', '18', '裁决信息', 'judgementDate', '裁决日期', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d2a4e09e055000000000001', '18', '裁决信息', 'endDate', '截止日期', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d2b4e09e055000000000001', '18', '裁决信息', 'limitTime', '限制年限', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d2c4e09e055000000000001', '18', '裁决信息', 'limitUnit', '限制年限单位', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('c9e75bf41d2d4e09e055000000000001', '18', '裁决信息', 'reason', '裁决原因', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('ca4a8f0469a3e352e055000000000001', '1', '基本信息', 'types', '人员类别', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('ca4a8f0469a5e352e055000000000001', '1', '基本信息', 'labels', '标签', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb27829826ed6830e055000000000001', '21', '基本信息', 'groupName', '名称', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb27829826ee6830e055000000000001', '21', '基本信息', 'basicInfo', '基本信息', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb27829826ef6830e055000000000001', '22', '群体成员', 'personId', '人员姓名', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb27829826f06830e055000000000001', '23', '相关线索', 'clueId', '线索名称', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb27829826f16830e055000000000001', '20', '档案管理', 'groupId', '群体名称', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb508c133ae2ffe5e055000000000001', '22', '群体成员', 'activityLevel', '活跃程度', 'ps_activity_level', '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb9019df9e4a70ece055000000000001', '33', '涉及群体', 'groupId', '群体名称', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb8c0002d4b91332e055000000000001', '21', '基本信息', 'groupTypes', '群体类别', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb9019df9e4970ece055000000000001', '32', '涉及人员', 'personId', '人员姓名', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c0494a9f6e055000000000001', '31', '基本信息', 'clueName', '线索名称', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c0495a9f6e055000000000001', '31', '基本信息', 'clueSource', '线索来源', 'ps_clue_source', '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c0496a9f6e055000000000001', '31', '基本信息', 'emergencyLevel', '紧急程度', 'ps_clue_emergency_level', '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c0497a9f6e055000000000001', '31', '基本信息', 'opennessLevel', '公开程度', 'ps_clue_openness_level', '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c0498a9f6e055000000000001', '31', '基本信息', 'clueDetail', '线索详情', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c049aa9f6e055000000000001', '31', '基本信息', 'clueTypes', '线索类别', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c049ba9f6e055000000000001', '31', '基本信息', 'relatedPersons', '人员', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb90930c049ca9f6e055000000000001', '31', '基本信息', 'attachments', '附件', NULL, '2');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('ca4c37e65820c9f6e055000000000001', '9', '走访记录', 'images', '走访图片', NULL, '0');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb2884420e30f5e3e055000000000001', '20', '档案管理', 'groupName', '名称', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb2884420e31f5e3e055000000000001', '20', '档案管理', 'basicInfo', '基本信息', NULL, '1');
INSERT INTO "T_PS_MODULE_FIELD" ("ID", "MODULE_CODE", "MODULE_NAME", "FIELD_NAME", "FIELD_CN_NAME", "DICT_TYPE", "TYPE") VALUES ('cb4abf2989b16044e055000000000001', '20', '档案管理', 'groupTypes', '群体类别', NULL, '1');

INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('69', '2', '21', '1', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('70', '2', '22', '2', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('71', '2', '23', '3', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('72', '3', '21', '1', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('73', '3', '22', '2', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('74', '3', '23', '3', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('75', '4', '21', '1', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('76', '4', '22', '2', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('77', '4', '23', '3', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('78', '5', '21', '1', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('79', '5', '22', '2', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('80', '5', '23', '3', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('82', '1', '32', '2', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('83', '1', '33', '3', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('84', '2', '31', '1', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('85', '2', '32', '2', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('86', '2', '33', '3', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('87', '3', '31', '1', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('88', '3', '32', '2', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('89', '3', '33', '3', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('90', '4', '31', '1', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('91', '4', '32', '2', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('92', '4', '33', '3', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('93', '5', '31', '1', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('94', '5', '32', '2', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('95', '5', '33', '3', 'clue');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('1', '1', '1', '1', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('2', '1', '14', '2', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('3', '1', '13', '3', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('4', '1', '12', '4', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('5', '1', '2', '5', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('6', '1', '3', '6', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('7', '1', '15', '7', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('8', '1', '4', '8', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('9', '1', '5', '9', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('10', '1', '6', '10', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('11', '1', '7', '11', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('12', '1', '8', '12', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('13', '1', '9', '13', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('14', '1', '10', '14', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('15', '1', '11', '15', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('16', '2', '1', '1', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('17', '2', '12', '2', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('18', '2', '2', '3', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('19', '2', '3', '4', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('20', '2', '4', '5', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('21', '2', '5', '6', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('22', '2', '6', '7', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('23', '2', '7', '8', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('24', '2', '8', '9', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('25', '2', '9', '10', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('26', '2', '16', '11', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('27', '2', '10', '12', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('28', '2', '11', '13', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('29', '3', '1', '1', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('30', '3', '14', '2', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('31', '3', '12', '3', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('32', '3', '2', '4', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('33', '3', '3', '5', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('34', '3', '4', '6', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('35', '3', '5', '7', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('36', '3', '6', '8', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('37', '3', '7', '9', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('38', '3', '8', '10', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('39', '3', '9', '11', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('40', '3', '10', '12', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('41', '3', '11', '13', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('42', '5', '1', '1', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('43', '5', '17', '2', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('44', '5', '2', '3', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('45', '5', '3', '4', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('46', '5', '4', '5', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('47', '5', '5', '6', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('48', '5', '6', '7', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('49', '5', '7', '8', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('50', '5', '8', '9', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('51', '5', '9', '10', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('52', '5', '10', '11', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('53', '5', '11', '12', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('54', '4', '1', '1', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('55', '4', '18', '2', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('56', '4', '2', '3', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('57', '4', '3', '4', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('58', '4', '4', '5', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('59', '4', '5', '6', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('60', '4', '6', '7', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('61', '4', '7', '8', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('62', '4', '8', '9', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('63', '4', '9', '10', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('64', '4', '10', '11', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('65', '4', '11', '12', 'person');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('66', '1', '21', '1', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('67', '1', '22', '2', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('68', '1', '23', '3', 'group');
INSERT INTO "T_PS_MODULE_SUBJECT_RELATION" ("ID", "SUBJECT_ID", "MODULE_ID", "SHOW_ORDER", "ARCHIVE_NAME") VALUES ('81', '1', '31', '1', 'clue');

INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('19', '云蝠wifi', 'xds.yunfu', 'ID', 'MAC', 'SBDZ', 'SBDZ', 'TIME', 'yyyyMMdd', 'GPS1', 'GPS2', NULL);
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('1', '交警卡口', 'xds.JiaoJingKaKouXinXi', 'CTHPHM', 'carNumber', 'KKMC', 'KKMC', 'GCSJ', 'yyyyMMdd', 'LONGITUDE', 'LATITUDE', '车牌颜色:HPYS;车牌号码:CTHPHM;通过卡口名称:KKMC;行驶方向:FXMC;通过卡口时间:GCSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('2', '民航进港', 'xds.MingHangJinGangXinXi', 'CERT_NO', 'idNumber', 'DEST_CN', 'DEST_CN', 'STA_ARVETM', 'yyyyMMdd', NULL, NULL, '姓名:PSR_CHNNAME;证件号码:CERT_NO;航空公司代码:FLT_AIRLCODE;航班号:FLT_NUMBER;出发地:DEPT_CN;目的地:DEST_CN;离港时间:STA_DEPTTM;进港时间:STA_ARVETM');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('3', '铁路进站卡口', 'xds.TieLuJinZhanRenYuanXinXi', 'SFZH', 'idNumber', 'TGKKMC', 'TGKKMC', 'TGSJ', 'yyyyMMdd', NULL, NULL, '姓名:XM;身份证:SFZH;通过卡口名称:TGKKMC;通过时间:TGSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('4', '网吧上网人员', 'xds.WangBaShangWangRenYuanXinXi', 'ZJHM', 'idNumber', 'YYCSMC', 'XXDZ', 'SWKSSJ', 'yyyyMMdd', 'LONGITUDE', 'LATITUDE', '姓名:SWRYXM;身份证号码:ZJHM;网吧名称:YYCSMC;座位号:SWZDH;开始上网时间:SWKSSJ;下网时间:XWSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('5', '巡逻盘查车辆', 'xds.XunLuoPanChaCheLiangXinXi', 'HPHM', 'carNumber', 'KKMC', 'KKSZMC', 'PCSJ', 'yyyyMMdd', 'PCDJD', 'PCDWD', '车牌号码:HPHM;卡口所在市州名称:KKSZMC;卡口名称:KKMC;盘查时间:PCSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('6', '巡逻盘查人员', 'xds.XunLuoPanCunRenYuanXinXi', 'SFZH', 'idNumber', 'KKMC', 'KKSZMC', 'PCSJ', 'yyyyMMdd', 'PCDJD', 'PCDWD', '姓名:XM;证件号码:SFZH;卡口所在市州名称:KKSZMC;卡口名称:KKMC;盘查时间:PCSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('7', '国内旅客', 'xds.ZhiAnGuoNeiLvKe', 'SFZMHM', 'idNumber', 'LDMC', 'XXDZ', 'RZSJ', 'yyyyMMdd', 'LONGITUDE', 'LATITUDE', '姓名:XM;证件号码:SFZMHM;旅店名称:LDMC;入住房号:RZFH;入住时间:RZSJ;退房时间:TFSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('8', '依图人像', 'yt.face_hit1', 'hit_person_id', 'idNumber', 'camera_name', 'camera_name', 'datetime', 'yyyyMMdd', 'camera_lng', 'camera_lat', '地点:camera_name;姓名:hit_name;证件号码:hit_person_id;抓拍时间:hit_datetime;研判结果:judgementCode;相似度:hit_similarity');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('9', '汽车订票', 'hx.THPE_TICKETINFO', 'FIDNumber', 'idNumber', 'FLineName', 'FLineName', 'FBookDate', 'yyyyMMdd', 'jd', 'wd', '订票时间:FBookDate;车次:FLeaveTime;发车时间:FLeaveDate;发站:FLineName;到站:FStationName');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('10', '新东盛客运汽车订票', 'xds.KeYunQiCheDingPiao', 'GPRHM', 'idNumber', 'CFD', 'CCCZ', 'GPSJ', 'yyyyMMdd', 'CCCZJD', 'CCCZWD', '姓名:GPRXM;身份证号:GPRHM;购票时间:GPSJ;出发地:CFD;发车时间:FCSJ;到达地:DDD');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('11', '火车订票', 'xds.HuoCheDingPiao', 'ZJHM', 'idNumber', 'FZ', 'FZ', 'SPSJ', 'yyyyMMdd', NULL, NULL, '姓名:XM;证件号码:ZJHM;出发站:FZ;到达站:DZ;车次:CC;时间:SPSJ;车厢号:CXH;座位号:ZWH');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('12', '民航订票', 'xds.MingHangDingPiao', 'ZJHM', 'idNumber', 'CFD_CN', 'CFD_CN', 'QFSJ', 'yyyyMMdd', NULL, NULL, '证件号码:ZJHM;出发地:CFD_CN;目的地:MDD_CN;航班号:HBH;起飞时间:QFSJ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('13', '民航机场安检', 'lz.MH_JC_AJ1', 'idcard', 'idNumber', NULL, NULL, 'dep_action_time', 'yyyyMMdd', 'longitude', 'latitude', '证件号码:idcard;活动时间:dep_action_time');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('14', '新东盛客运汽车检票', 'xds.KeYunQiCheJianPiao', 'GPRHM', 'idNumber', 'CFD', 'CCCZ', 'JPSJ', 'yyyyMMdd', 'CCCZJD', 'CCCZWD', '姓名:GPRXM;身份证号:GPRHM;购票时间:GPSJ;检票时间:JPSJ;出发地:CFD;发车时间:FCSJ;到达地:DDD');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('15', '新东盛泸州客运汽车订票', 'xds.LuZhouKeYunQiCheDingPiao', 'ID', 'idNumber', 'CFD', 'CCCZ', 'TIME', 'yyyyMMdd', 'JD', 'WD', '姓名:GPRXM;身份证号:ID;购票时间:TIME;出发地:CFD;发车时间:FCSJ;到达地:DDD');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('16', '海康卡口', 'hk.TrafficInfo', 'PlateNo', 'carNumber', 'DZ', 'DZ', 'PassTime', 'yyyyMMdd', 'LONGITUDE', 'LATITUDE', '车牌号码:PlateNo;车辆类型:PlateClass;抓拍时间:PassTime;抓拍地点:DZ');
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('17', '话单', 'system.HD', 'JFHM', 'phoneNumber', 'JFROADS', 'JFADDRESS', 'HJSJ', 'yyyyMMdd', 'JFLNG', 'JFLAT', NULL);
INSERT INTO "T_PS_TRAJECTORY_SOURCE" ("ID", "NAME", "TABLE_NAME", "INDEX_COLUMN", "INDEX_TYPE", "PLACE_COLUMN", "ADDRESS_COLUMN", "TIME_COLUMN", "TIME_FORMAT", "LNG_COLUMN", "LAT_COLUMN", "TEMPLATE") VALUES ('18', '走访记录', 'system.t_visit_record', 'ID_NUMBER', 'idNumber', 'WHEREABOUTS', 'WHEREABOUTS', 'VISIT_TIME', 'yyyyMMdd', 'LNG', 'LAT', '姓名:NAME;记录添加人员:CR_BY_NAME;走访时间:VISIT_TIME;走访去向:WHEREABOUTS');

INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008861', 'ps_activity_level', '1', '关注', 'c83e4d5d87e093bde055000000008860', NULL, NULL, NULL, '27', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008862', 'ps_activity_level', '2', '活跃', 'c83e4d5d87e093bde055000000008860', NULL, NULL, NULL, '27', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008863', 'ps_activity_level', '3', '挑头', 'c83e4d5d87e093bde055000000008860', NULL, NULL, NULL, '27', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008860', 'ps_activity_level_group', '27', '群体人员活跃程度', 'a59027d6defa4c4b9e917f05a91468', NULL, NULL, NULL, '0', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008881', 'ps_clue_emergency_level', '1', '紧急', 'c83e4d5d87e093bde055000000008880', NULL, NULL, NULL, '21', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008882', 'ps_clue_emergency_level', '2', '重要', 'c83e4d5d87e093bde055000000008880', NULL, NULL, NULL, '21', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008883', 'ps_clue_emergency_level', '3', '一般', 'c83e4d5d87e093bde055000000008880', NULL, NULL, NULL, '21', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008880', 'ps_clue_emergency_level_group', '21', '线索紧急程度', 'a59027d6defa4c4b9e917f05a91468', NULL, NULL, NULL, '0', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008871', 'ps_clue_openness_level', '1', '本单位公开', 'c83e4d5d87e093bde055000000008870', NULL, NULL, NULL, '23', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008872', 'ps_clue_openness_level', '2', '完全公开', 'c83e4d5d87e093bde055000000008870', NULL, NULL, NULL, '23', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008870', 'ps_clue_openness_level_group', '23', '线索公开程度', 'a59027d6defa4c4b9e917f05a91468', NULL, NULL, NULL, '0', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008891', 'ps_clue_source', '1', '上级通报', 'c83e4d5d87e093bde055000000008890', NULL, NULL, NULL, '22', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008892', 'ps_clue_source', '2', '群众举报', 'c83e4d5d87e093bde055000000008890', NULL, NULL, NULL, '22', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008893', 'ps_clue_source', '3', '人力情报', 'c83e4d5d87e093bde055000000008890', NULL, NULL, NULL, '22', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008894', 'ps_clue_source', '4', '网安情报', 'c83e4d5d87e093bde055000000008890', NULL, NULL, NULL, '22', NULL);
INSERT INTO "T_DICT" ("ID", "TYPE", "CODE", "NAME", "PID", "DICTDESC", "SHOWNUMBER", "STANDARD", "PCODE", "FLAG") VALUES ('c83e4d5d87e093bde055000000008890', 'ps_clue_source_group', '22', '线索来源', 'a59027d6defa4c4b9e917f05a91468', NULL, NULL, NULL, '0', NULL);

INSERT INTO "T_PS_SUBJECT" ("ID", "NAME", "PERSON_LIST_FILTERS", "PERSON_LIST_PROPERTY", "GROUP_LIST_FILTERS", "GROUP_LIST_PROPERTY", "CLUE_LIST_FILTERS", "CLUE_LIST_PROPERTY") VALUES ('5', '刑侦专题', '[{"type":"option","displayName":"管控状态","key":"controlStatus","property":"single","value":["$$ps_control_status$$"]},{"type":"option","displayName":"人员类别","key":"personType","property":"single","value":["&&personType&&"]}]', '{"name":"姓名","idNumber":"身份证号","gender":"性别","formerName":"曾用名","nickName":"绰号","nation":"民族","politicalStatus":"政治面貌","religiousBelief":"宗教信仰","maritalStatus":"婚姻状况","currentJob":"现职业","contactInformation":"联系方式","personType":"人员类别","personLabel":"标签","registeredResidence":"户籍地","currentResidence":"现住址","basicInfo":"基本情况","controlStatus":"管控状态"}', '[{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&groupType&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"groupName":"群体名称","groupType":"群体类别","basicInfo":"基本情况","createDeptName":"录入单位","createTime":"录入时间","updateTime":"更新时间"}', '[{"type":"select","displayName":"线索类别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["&&clueSource&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"clueName":"线索名称","clueSource":"线索来源","clueType":"线索类别","emergencyLevel":"紧急程度","createTime":"创建时间","updateTime":"更新时间"}');
INSERT INTO "T_PS_SUBJECT" ("ID", "NAME", "PERSON_LIST_FILTERS", "PERSON_LIST_PROPERTY", "GROUP_LIST_FILTERS", "GROUP_LIST_PROPERTY", "CLUE_LIST_FILTERS", "CLUE_LIST_PROPERTY") VALUES ('1', '反恐专题', '[{"type":"option","displayName":"是否分配","key":"distributeStatus","property":"single","value":["$$ps_distribute_status$$"]},{"type":"option","displayName":"管控状态","key":"controlStatus","property":"single","value":["$$ps_control_status$$"]},{"type":"option","displayName":"是否纳入群体","key":"groupStatus","property":"single","value":["$$ps_group_status$$"]},{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&group&&"]},{"type":"tree","displayName":"管辖部门","key":"department","value":["&&department&&"]}]', '{"name":"姓名","idNumber":"身份证号","gender":"性别","formerName":"曾用名","nickName":"绰号","nation":"民族","politicalStatus":"政治面貌","religiousBelief":"宗教信仰","maritalStatus":"婚姻状态","currentJob":"现职业","contactInformation":"联系方式","registeredResidence":"户籍地","currentResidence":"现住址","groupType":"群体类别","controlStatus":"管控状态"}', '[{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&groupType&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"groupName":"群体名称","groupType":"群体类别","basicInfo":"基本情况","createDeptName":"录入单位","createTime":"录入时间","updateTime":"更新时间"}', '[{"type":"select","displayName":"线索类别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["&&clueSource&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"clueName":"线索名称","clueSource":"线索来源","clueType":"线索类别","emergencyLevel":"紧急程度","createTime":"创建时间","updateTime":"更新时间"}');
INSERT INTO "T_PS_SUBJECT" ("ID", "NAME", "PERSON_LIST_FILTERS", "PERSON_LIST_PROPERTY", "GROUP_LIST_FILTERS", "GROUP_LIST_PROPERTY", "CLUE_LIST_FILTERS", "CLUE_LIST_PROPERTY") VALUES ('3', '政保专题', '[{"type":"option","displayName":"是否分配","key":"distributeStatus","property":"single","value":["$$ps_distribute_status$$"]},{"type":"option","displayName":"管控状态","key":"controlStatus","property":"single","value":["$$ps_control_status$$"]},{"type":"option","displayName":"人员标签","key":"personLabel","property":"single","value":["&&personLabel&&"]},{"type":"tree","displayName":"管辖部门","key":"department","value":["&&department&&"]},{"type":"option","displayName":"管控级别","key":"controlLevel","value":["$$ps_zb_control_level$$"]}]', '{"name":"姓名","idNumber":"身份证号","gender":"性别","formerName":"曾用名","nickName":"绰号","nation":"民族","politicalStatus":"政治面貌","religiousBelief":"宗教信仰","maritalStatus":"婚姻状况","currentJob":"现职业","contactInformation":"联系方式","controlLevel":"管控级别","controlStatus":"管控状态","personLabel":"标签","registeredResidence":"户籍地","currentResidence":"现住址","basicInfo":"基本情况"}', '[{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&groupType&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"groupName":"群体名称","groupType":"群体类别","basicInfo":"基本情况","createDeptName":"录入单位","createTime":"录入时间","updateTime":"更新时间"}', '[{"type":"select","displayName":"线索类别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["&&clueSource&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"clueName":"线索名称","clueSource":"线索来源","clueType":"线索类别","emergencyLevel":"紧急程度","createTime":"创建时间","updateTime":"更新时间"}');
INSERT INTO "T_PS_SUBJECT" ("ID", "NAME", "PERSON_LIST_FILTERS", "PERSON_LIST_PROPERTY", "GROUP_LIST_FILTERS", "GROUP_LIST_PROPERTY", "CLUE_LIST_FILTERS", "CLUE_LIST_PROPERTY") VALUES ('4', '交警专题', '[{"type":"option","displayName":"管控状态","key":"controlStatus","property":"single","value":["$$ps_control_status$$"]},{"type":"option","displayName":"人员类别","key":"personType","property":"single","value":["&&personType&&"]}]', '{"name":"姓名","idNumber":"身份证号","gender":"性别","formerName":"曾用名","nickName":"绰号","nation":"民族","politicalStatus":"政治面貌","religiousBelief":"宗教信仰","maritalStatus":"婚姻状况","currentJob":"现职业","contactInformation":"联系方式","personType":"人员类别","personLabel":"标签","registeredResidence":"户籍地","currentResidence":"现住址","controlStatus":"管控状态"}', '[{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&groupType&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"groupName":"群体名称","groupType":"群体类别","basicInfo":"基本情况","createDeptName":"录入单位","createTime":"录入时间","updateTime":"更新时间"}', '[{"type":"select","displayName":"线索类别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["&&clueSource&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"clueName":"线索名称","clueSource":"线索来源","clueType":"线索类别","emergencyLevel":"紧急程度","createTime":"创建时间","updateTime":"更新时间"}');
INSERT INTO "T_PS_SUBJECT" ("ID", "NAME", "PERSON_LIST_FILTERS", "PERSON_LIST_PROPERTY", "GROUP_LIST_FILTERS", "GROUP_LIST_PROPERTY", "CLUE_LIST_FILTERS", "CLUE_LIST_PROPERTY") VALUES ('2', '禁毒专题', '[{"type":"option","displayName":"管控状态","key":"controlStatus","property":"single","value":["$$ps_control_status$$"]},{"type":"option","displayName":"人员类别","key":"personType","property":"single","value":["&&personType&&"]}]', '{"name":"姓名","idNumber":"身份证号","gender":"性别","formerName":"曾用名","nickName":"绰号","nation":"民族","politicalStatus":"政治面貌","religiousBelief":"宗教信仰","maritalStatus":"婚姻状况","currentJob":"现职业","contactInformation":"联系方式","personType":"人员类别","personLabel":"标签","registeredResidence":"户籍地","currentResidence":"现住址","controlStatus":"管控状态"}', '[{"type":"select","displayName":"所属群体类别","key":"groupType","value":["&&groupType&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"groupName":"群体名称","groupType":"群体类别","basicInfo":"基本情况","createDeptName":"录入单位","createTime":"录入时间","updateTime":"更新时间"}', '[{"type":"select","displayName":"线索类别","key":"clueType","value":["&&clueType&&"]},{"type":"select","displayName":"线索来源","key":"clueSource","value":["&&clueSource&&"]},{"type":"tree","displayName":"录入单位","key":"department","value":["&&department&&"]},{"type":"time","displayName":"录入时间","key":"timeParams","value":["&&time&&"]}]', '{"clueName":"线索名称","clueSource":"线索来源","clueType":"线索类别","emergencyLevel":"紧急程度","createTime":"创建时间","updateTime":"更新时间"}');
