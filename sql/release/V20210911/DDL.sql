--------------------------------------------------------
--  DDL for Table T_PS_CLUE
--------------------------------------------------------
CREATE TABLE "T_PS_CLUE"
(
    "ID"              VARCHAR2(32 BYTE),
    "CR_BY"           NVARCHAR2(64),
    "CR_BY_NAME"      NVARCHAR2(255),
    "CR_TIME"         TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"           NVARCHAR2(64),
    "UP_BY_NAME"      NVARCHAR2(255),
    "UP_TIME"         TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"         NVARCHAR2(64),
    "CR_DEPT_CODE"    VARCHAR2(12 BYTE),
    "NAME"            VARCHAR2(255 BYTE),
    "SOURCE"          VARCHAR2(2 BYTE),
    "EMERGENCY_LEVEL" VARCHAR2(2 BYTE),
    "DETAIL"          VARCHAR2(2000 BYTE),
    "OPENNESS_LEVEL"  VARCHAR2(2 BYTE),
    "SUBJECT_ID"      VARCHAR2(2 BYTE)
);

COMMENT ON COLUMN "T_PS_CLUE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_CLUE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_CLUE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_CLUE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_CLUE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_CLUE"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_CLUE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_CLUE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_CLUE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_CLUE"."NAME" IS '线索标题';
COMMENT ON COLUMN "T_PS_CLUE"."SOURCE" IS '线索来源';
COMMENT ON COLUMN "T_PS_CLUE"."EMERGENCY_LEVEL" IS '紧急程度';
COMMENT ON COLUMN "T_PS_CLUE"."DETAIL" IS '线索详情';
COMMENT ON COLUMN "T_PS_CLUE"."OPENNESS_LEVEL" IS '公开程度';
COMMENT ON COLUMN "T_PS_CLUE"."SUBJECT_ID" IS '专题id';

ALTER TABLE T_PS_CLUE ADD CONSTRAINT T_PS_CLUE_PK PRIMARY KEY(ID) ENABLE;
--------------------------------------------------------
--  DDL for Table T_PS_CLUE_FILE_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_CLUE_FILE_RELATION"
(
    "ID"              VARCHAR2(255 BYTE),
    "CR_BY"           VARCHAR2(255 BYTE),
    "CR_BY_NAME"      VARCHAR2(255 BYTE),
    "CR_TIME"         TIMESTAMP(6),
    "UP_BY"           VARCHAR2(255 BYTE),
    "UP_BY_NAME"      VARCHAR2(255 BYTE),
    "UP_TIME"         TIMESTAMP(6),
    "CR_DEPT"         VARCHAR2(255 BYTE),
    "CR_DEPT_CODE"    VARCHAR2(255 BYTE),
    "CLUE_ID"         VARCHAR2(255 BYTE),
    "FILE_STORAGE_ID" VARCHAR2(255 BYTE),
    "TYPE"            VARCHAR2(2 BYTE),
    "MODULE"          VARCHAR2(2 BYTE),
    "RECORD_ID"       VARCHAR2(64 BYTE)
);
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."CLUE_ID" IS '线索id';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."FILE_STORAGE_ID" IS '文件存储id';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."TYPE" IS '文件类型，0-视频，1-文档，2-图片，3-其他';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."MODULE" IS '模块类型';
COMMENT ON COLUMN "T_PS_CLUE_FILE_RELATION"."RECORD_ID" IS '关联的记录主键，如果没有则为空';

ALTER TABLE T_PS_CLUE_FILE_RELATION ADD CONSTRAINT T_PS_CLUE_FILE_RELATION_PK PRIMARY KEY(ID) ENABLE;
--------------------------------------------------------
--  DDL for Table T_PS_CLUE_PERSON_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_CLUE_PERSON_RELATION"
(
    "ID"           VARCHAR2(255 BYTE),
    "CR_BY"        VARCHAR2(255 BYTE),
    "CR_BY_NAME"   VARCHAR2(255 BYTE),
    "CR_TIME"      TIMESTAMP(6),
    "UP_BY"        VARCHAR2(255 BYTE),
    "UP_BY_NAME"   VARCHAR2(255 BYTE),
    "UP_TIME"      TIMESTAMP(6),
    "CR_DEPT"      VARCHAR2(255 BYTE),
    "CR_DEPT_CODE" VARCHAR2(255 BYTE),
    "CLUE_ID"      VARCHAR2(255 BYTE),
    "PERSON_ID"    VARCHAR2(255 BYTE)
);
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."CLUE_ID" IS '线索id';
COMMENT ON COLUMN "T_PS_CLUE_PERSON_RELATION"."PERSON_ID" IS '人员id';

ALTER TABLE T_PS_CLUE_PERSON_RELATION ADD CONSTRAINT T_PS_CLUE_PERSON_RELATION_PK PRIMARY KEY(ID) ENABLE;
--------------------------------------------------------
--  DDL for Table T_PS_CLUE_TYPE
--------------------------------------------------------

CREATE TABLE "T_PS_CLUE_TYPE"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "SUBJECT_ID"   VARCHAR2(32 BYTE),
    "NAME"         NVARCHAR2(64)
);
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."SUBJECT_ID" IS '专题ID';
COMMENT ON COLUMN "T_PS_CLUE_TYPE"."NAME" IS '类型名称';
COMMENT ON TABLE "T_PS_CLUE_TYPE" IS '情报类别表';

ALTER TABLE T_PS_CLUE_TYPE ADD CONSTRAINT T_PS_CLUE_TYPE_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_CLUE_TYPE_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_CLUE_TYPE_RELATION"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "CLUE_ID"      VARCHAR2(32 BYTE),
    "TYPE_ID"      VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."CLUE_ID" IS '情报ID';
COMMENT ON COLUMN "T_PS_CLUE_TYPE_RELATION"."TYPE_ID" IS '类型ID';
COMMENT ON TABLE "T_PS_CLUE_TYPE_RELATION" IS '群体-类别关联表';

ALTER TABLE T_PS_CLUE_TYPE_RELATION ADD CONSTRAINT T_PS_CLUE_TYPE_RELATION_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_COMPOUND
--------------------------------------------------------

CREATE TABLE "T_PS_COMPOUND"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6),
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6),
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "WARNING_ID"   VARCHAR2(32 BYTE),
    "COMPOUND_ID"  VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_COMPOUND"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_COMPOUND"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_COMPOUND"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_COMPOUND"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_COMPOUND"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_COMPOUND"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_COMPOUND"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_COMPOUND"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_COMPOUND"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_COMPOUND"."WARNING_ID" IS '预警id';
COMMENT ON COLUMN "T_PS_COMPOUND"."COMPOUND_ID" IS '合成id';

ALTER TABLE T_PS_COMPOUND ADD CONSTRAINT T_PS_COMPOUND_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_COOPERATION
--------------------------------------------------------

CREATE TABLE "T_PS_COOPERATION"
(
    "ID"             VARCHAR2(32 BYTE),
    "CR_BY"          NVARCHAR2(64),
    "CR_BY_NAME"     NVARCHAR2(255),
    "CR_TIME"        TIMESTAMP(6),
    "UP_BY"          NVARCHAR2(64),
    "UP_BY_NAME"     NVARCHAR2(255),
    "UP_TIME"        TIMESTAMP(6),
    "CR_DEPT"        NVARCHAR2(64),
    "CR_DEPT_CODE"   VARCHAR2(12 BYTE),
    "WARNING_ID"     VARCHAR2(32 BYTE),
    "COOPERATION_ID" VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_COOPERATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_COOPERATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_COOPERATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_COOPERATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_COOPERATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_COOPERATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_COOPERATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_COOPERATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_COOPERATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_COOPERATION"."WARNING_ID" IS '预警id';
COMMENT ON COLUMN "T_PS_COOPERATION"."COOPERATION_ID" IS '协作id';

ALTER TABLE T_PS_COOPERATION ADD CONSTRAINT T_PS_COOPERATION_PK PRIMARY KEY(ID) ENABLE;
--------------------------------------------------------
--  DDL for Table T_PS_GROUP
--------------------------------------------------------
DROP TABLE "T_PS_GROUP";
CREATE TABLE "T_PS_GROUP"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "NAME"         NVARCHAR2(64),
    "SUBJECT_ID"   VARCHAR2(32 BYTE),
    "BASIC_INFO"   VARCHAR2(100 BYTE)
);

COMMENT ON COLUMN "T_PS_GROUP"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_GROUP"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_GROUP"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_GROUP"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_GROUP"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_GROUP"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_GROUP"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_GROUP"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_GROUP"."NAME" IS '群体名称';
COMMENT ON COLUMN "T_PS_GROUP"."SUBJECT_ID" IS '主题编号';
COMMENT ON COLUMN "T_PS_GROUP"."BASIC_INFO" IS '基本情况';

ALTER TABLE T_PS_GROUP ADD CONSTRAINT T_PS_GROUP_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_GROUP_CLUE_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_GROUP_CLUE_RELATION"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "GROUP_ID"     VARCHAR2(32 BYTE),
    "CLUE_ID"      VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."GROUP_ID" IS '群体ID';
COMMENT ON COLUMN "T_PS_GROUP_CLUE_RELATION"."CLUE_ID" IS '线索ID';
COMMENT ON TABLE "T_PS_GROUP_CLUE_RELATION" IS '群体-线索-关系表';

ALTER TABLE T_PS_GROUP_CLUE_RELATION ADD CONSTRAINT T_PS_GROUP_CLUE_RELATION_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_GROUP_TYPE
--------------------------------------------------------

CREATE TABLE "T_PS_GROUP_TYPE"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "SUBJECT_ID"   VARCHAR2(32 BYTE),
    "NAME"         NVARCHAR2(64)
);


COMMENT ON COLUMN "T_PS_GROUP_TYPE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."SUBJECT_ID" IS '专题id';
COMMENT ON COLUMN "T_PS_GROUP_TYPE"."NAME" IS '类型名称';
COMMENT ON TABLE "T_PS_GROUP_TYPE" IS '群体类别表';

ALTER TABLE T_PS_GROUP_TYPE ADD CONSTRAINT T_PS_GROUP_TYPE_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_GROUP_TYPE_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_GROUP_TYPE_RELATION"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6) DEFAULT sysdate,
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "GROUP_ID"     VARCHAR2(32 BYTE),
    "TYPE_ID"      VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."GROUP_ID" IS '群体ID';
COMMENT ON COLUMN "T_PS_GROUP_TYPE_RELATION"."TYPE_ID" IS '类型ID';
COMMENT ON TABLE "T_PS_GROUP_TYPE_RELATION" IS '群体-类别关联表';

ALTER TABLE T_PS_GROUP_TYPE_RELATION ADD CONSTRAINT T_PS_GROUP_TYPE_RELATION_PK PRIMARY KEY(ID) ENABLE;
--------------------------------------------------------
--  DDL for Table T_PS_MODULE
--------------------------------------------------------
DROP TABLE "T_PS_MODULE";
CREATE TABLE "T_PS_MODULE"
(
    "ID"      VARCHAR2(255 BYTE),
    "CN_NAME" NVARCHAR2(50),
    "EN_NAME" VARCHAR2(100 BYTE),
    "TYPE"    VARCHAR2(20 BYTE) DEFAULT 'person'
);
COMMENT ON COLUMN "T_PS_MODULE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_MODULE"."CN_NAME" IS '中文名';
COMMENT ON COLUMN "T_PS_MODULE"."EN_NAME" IS '英文名';
COMMENT ON TABLE "T_PS_MODULE" IS '档案结构表';

ALTER TABLE T_PS_MODULE ADD CONSTRAINT T_PS_MODULE_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_MODULE_FIELD
--------------------------------------------------------
DROP TABLE "T_PS_MODULE_FIELD";
CREATE TABLE "T_PS_MODULE_FIELD"
(
    "ID"            VARCHAR2(255 BYTE) DEFAULT '',
    "MODULE_CODE"   VARCHAR2(255 BYTE),
    "MODULE_NAME"   NVARCHAR2(20),
    "FIELD_NAME"    VARCHAR2(50 BYTE),
    "FIELD_CN_NAME" NVARCHAR2(20),
    "DICT_TYPE"     VARCHAR2(50 BYTE),
    "TYPE"          VARCHAR2(20 BYTE)
);
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."MODULE_CODE" IS '模块id';
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."MODULE_NAME" IS '模块名';
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."FIELD_NAME" IS '属性名';
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."FIELD_CN_NAME" IS '属性中文名';
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."DICT_TYPE" IS '字典类型';
COMMENT ON COLUMN "T_PS_MODULE_FIELD"."TYPE" IS '类型 person=0,group=1,clue=2';
COMMENT ON TABLE "T_PS_MODULE_FIELD" IS '模块属性表';

ALTER TABLE T_PS_MODULE_FIELD ADD CONSTRAINT T_PS_MODULE_FIELD_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_MODULE_SUBJECT_RELATION
--------------------------------------------------------
DROP TABLE "T_PS_MODULE_SUBJECT_RELATION";
CREATE TABLE "T_PS_MODULE_SUBJECT_RELATION"
(
    "ID"           VARCHAR2(255 BYTE),
    "SUBJECT_ID"   VARCHAR2(255 BYTE),
    "MODULE_ID"    VARCHAR2(255 BYTE),
    "SHOW_ORDER"   NUMBER(2, 0),
    "ARCHIVE_NAME" VARCHAR2(255 BYTE)
);
COMMENT ON COLUMN "T_PS_MODULE_SUBJECT_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_MODULE_SUBJECT_RELATION"."SUBJECT_ID" IS '专题id';
COMMENT ON COLUMN "T_PS_MODULE_SUBJECT_RELATION"."MODULE_ID" IS '模块ID';
COMMENT ON COLUMN "T_PS_MODULE_SUBJECT_RELATION"."SHOW_ORDER" IS '显示顺序';
COMMENT ON TABLE "T_PS_MODULE_SUBJECT_RELATION" IS '档案与警种关联表';

ALTER TABLE T_PS_MODULE_SUBJECT_RELATION ADD CONSTRAINT T_PS_MODULE_SUBJECT_REL_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_OPERATION_LOG
--------------------------------------------------------
DROP TABLE "T_PS_OPERATION_LOG";
CREATE TABLE "T_PS_OPERATION_LOG"
(
    "ID"                 VARCHAR2(255 BYTE),
    "CR_BY"              VARCHAR2(255 BYTE),
    "CR_BY_NAME"         VARCHAR2(255 BYTE),
    "CR_DEPT"            VARCHAR2(255 BYTE),
    "CR_DEPT_CODE"       VARCHAR2(255 BYTE),
    "CR_TIME"            TIMESTAMP(6) DEFAULT current_timestamp,
    "OPERATE_MODULE"     VARCHAR2(2 BYTE),
    "OPERATOR"           VARCHAR2(2 BYTE),
    "OVERVIEW"           NVARCHAR2(500),
    "IP_ADDR"            VARCHAR2(255 BYTE),
    "TARGET_OBJECT_ID"   VARCHAR2(255 BYTE),
    "CR_BY_USERNAME"     VARCHAR2(255 BYTE),
    "DETAIL"             NVARCHAR2(2000),
    "TARGET_OBJECT_TYPE" NUMBER(2, 0) DEFAULT 0
);

COMMENT ON COLUMN "T_PS_OPERATION_LOG"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_BY" IS '创建人ID';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_DEPT" IS '创建人所属部门';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_DEPT_CODE" IS '创建人所属部门编号';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."OPERATE_MODULE" IS '操作模块';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."OPERATOR" IS '操作类型';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."OVERVIEW" IS '操作概述';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."IP_ADDR" IS 'IP地址';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."TARGET_OBJECT_ID" IS '目标对象id';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."DETAIL" IS '详情';
COMMENT ON COLUMN "T_PS_OPERATION_LOG"."TARGET_OBJECT_TYPE" IS '目标对象类型 0-person,1-group,2-clue';
COMMENT ON TABLE "T_PS_OPERATION_LOG" IS '操作日志';

ALTER TABLE T_PS_OPERATION_LOG ADD CONSTRAINT T_PS_OPERATION_LOG_PK PRIMARY KEY(ID) ENABLE;



ALTER TABLE T_PS_PERSON DROP COLUMN OUT_TIME;
ALTER TABLE T_PS_PERSON DROP COLUMN OUT_DIRECTION;


--------------------------------------------------------
--  DDL for Table T_PS_PERSON_CONTROL
--------------------------------------------------------
DROP TABLE "T_PS_PERSON_CONTROL";
CREATE TABLE "T_PS_PERSON_CONTROL"
(
    "ID"                  VARCHAR2(32 BYTE),
    "CR_BY"               NVARCHAR2(64),
    "CR_BY_NAME"          NVARCHAR2(255),
    "CR_TIME"             TIMESTAMP(6)      DEFAULT sysdate,
    "UP_BY"               NVARCHAR2(64),
    "UP_BY_NAME"          NVARCHAR2(255),
    "UP_TIME"             TIMESTAMP(6)      DEFAULT sysdate,
    "CR_DEPT"             NVARCHAR2(64),
    "CR_DEPT_CODE"        VARCHAR2(12 BYTE),
    "PERSON_ID"           VARCHAR2(32 BYTE),
    "POLICE_STATION_NAME" NVARCHAR2(255),
    "LEADER_NAME"         NVARCHAR2(64),
    "LEADER_JOB"          NVARCHAR2(64),
    "LEADER_CONTACT"      NVARCHAR2(64),
    "RESPONSIBLE_NAME"    NVARCHAR2(64),
    "RESPONSIBLE_JOB"     NVARCHAR2(64),
    "RESPONSIBLE_CONTACT" NVARCHAR2(64),
    "SUBJECT_ID"          VARCHAR2(32 BYTE) DEFAULT 1,
    "POLICE_STATION_CODE" VARCHAR2(32 BYTE),
    "RESPONSIBLE_ID"      VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."ID" IS '管控信息id';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."PERSON_ID" IS '人员id';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."POLICE_STATION_NAME" IS '派出所';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."LEADER_NAME" IS '派出所责任领导';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."LEADER_JOB" IS '责任领导职务';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."LEADER_CONTACT" IS '责任领导联系方式';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_NAME" IS '责任人';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_JOB" IS '责任人职务';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_CONTACT" IS '责任人联系方式';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."SUBJECT_ID" IS '专题id';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."POLICE_STATION_CODE" IS '派出所code';
COMMENT ON COLUMN "T_PS_PERSON_CONTROL"."RESPONSIBLE_ID" IS '责任人id';
COMMENT ON TABLE "T_PS_PERSON_CONTROL" IS '管控信息';

ALTER TABLE T_PS_PERSON_CONTROL ADD CONSTRAINT T_PS_PERSON_CONTROL_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_PERSON_GROUP_RELATION
--------------------------------------------------------
DROP TABLE "T_PS_PERSON_GROUP_RELATION";
CREATE TABLE "T_PS_PERSON_GROUP_RELATION"
(
    "ID"             VARCHAR2(32 BYTE),
    "CR_BY"          NVARCHAR2(64),
    "CR_BY_NAME"     NVARCHAR2(255),
    "CR_TIME"        TIMESTAMP(6)       DEFAULT sysdate,
    "UP_BY"          NVARCHAR2(64),
    "UP_BY_NAME"     NVARCHAR2(255),
    "UP_TIME"        TIMESTAMP(6)       DEFAULT sysdate,
    "CR_DEPT"        NVARCHAR2(64),
    "CR_DEPT_CODE"   VARCHAR2(12 BYTE),
    "PERSON_ID"      VARCHAR2(32 BYTE),
    "GROUP_ID"       VARCHAR2(32 BYTE),
    "ACTIVITY_LEVEL" VARCHAR2(255 BYTE) DEFAULT 1
);

COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."PERSON_ID" IS '人员ID';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."GROUP_ID" IS '群体ID';
COMMENT ON COLUMN "T_PS_PERSON_GROUP_RELATION"."ACTIVITY_LEVEL" IS '活跃程度';

ALTER TABLE T_PS_PERSON_GROUP_RELATION ADD CONSTRAINT T_PS_PERSON_GROUP_RELATION_PK PRIMARY KEY(ID) ENABLE;


COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."PHONE_STATUS" IS '手机状态：0 停用 1 启用';
COMMENT ON COLUMN "T_PS_PERSON_MOBILE_PHONE"."PHONE_USE_STATUS" IS '使用状态：0 不常用 1 常用';

DROP TABLE T_PS_PERSON_MOVEMENT;

COMMENT ON TABLE "T_PS_PERSON_TYPE" IS '人员-类别-关系表';

--------------------------------------------------------
--  DDL for Table T_PS_PLACE
--------------------------------------------------------

CREATE TABLE "T_PS_PLACE"
(
    "ID"           VARCHAR2(32 BYTE),
    "CR_BY"        NVARCHAR2(64),
    "CR_BY_NAME"   NVARCHAR2(255),
    "CR_TIME"      TIMESTAMP(6),
    "UP_BY"        NVARCHAR2(64),
    "UP_BY_NAME"   NVARCHAR2(255),
    "UP_TIME"      TIMESTAMP(6),
    "CR_DEPT"      NVARCHAR2(64),
    "CR_DEPT_CODE" VARCHAR2(12 BYTE),
    "PLACE"        NVARCHAR2(255),
    "ADDRESS"      NVARCHAR2(255)
);

COMMENT ON COLUMN "T_PS_PLACE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_PLACE"."CR_BY" IS '创建人';
COMMENT ON COLUMN "T_PS_PLACE"."CR_BY_NAME" IS '创建人姓名';
COMMENT ON COLUMN "T_PS_PLACE"."CR_TIME" IS '创建时间';
COMMENT ON COLUMN "T_PS_PLACE"."UP_BY" IS '最后更新人';
COMMENT ON COLUMN "T_PS_PLACE"."UP_BY_NAME" IS '最后更新人姓名';
COMMENT ON COLUMN "T_PS_PLACE"."UP_TIME" IS '最后更新时间';
COMMENT ON COLUMN "T_PS_PLACE"."CR_DEPT" IS '创建部门';
COMMENT ON COLUMN "T_PS_PLACE"."CR_DEPT_CODE" IS '创建部门编号';
COMMENT ON COLUMN "T_PS_PLACE"."PLACE" IS '场所名';
COMMENT ON COLUMN "T_PS_PLACE"."ADDRESS" IS '地址';
COMMENT ON TABLE "T_PS_PLACE" IS '人员档案-裁决信息';

ALTER TABLE T_PS_PLACE ADD CONSTRAINT T_PS_PLACE_PK PRIMARY KEY(ID) ENABLE;


--------------------------------------------------------
--  DDL for Table T_PS_SUBJECT
--------------------------------------------------------
DROP TABLE "T_PS_SUBJECT";
CREATE TABLE "T_PS_SUBJECT"
(
    "ID"                   VARCHAR2(32 BYTE),
    "NAME"                 NVARCHAR2(255),
    "PERSON_LIST_FILTERS"  NVARCHAR2(2000),
    "PERSON_LIST_PROPERTY" NVARCHAR2(2000),
    "GROUP_LIST_FILTERS"   NVARCHAR2(2000),
    "GROUP_LIST_PROPERTY"  NVARCHAR2(2000),
    "CLUE_LIST_FILTERS"    NVARCHAR2(2000),
    "CLUE_LIST_PROPERTY"   NVARCHAR2(2000)
);

COMMENT ON COLUMN "T_PS_SUBJECT"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_SUBJECT"."NAME" IS '专题名称';
COMMENT ON COLUMN "T_PS_SUBJECT"."PERSON_LIST_FILTERS" IS '人员列表页筛选条件配置';
COMMENT ON COLUMN "T_PS_SUBJECT"."PERSON_LIST_PROPERTY" IS '批量导出人员时属性';
COMMENT ON COLUMN "T_PS_SUBJECT"."GROUP_LIST_FILTERS" IS '群体列表页筛选条件配置';
COMMENT ON COLUMN "T_PS_SUBJECT"."GROUP_LIST_PROPERTY" IS '批量导出群体时属性';
COMMENT ON COLUMN "T_PS_SUBJECT"."CLUE_LIST_FILTERS" IS '线索列表页筛选条件配置';
COMMENT ON COLUMN "T_PS_SUBJECT"."CLUE_LIST_PROPERTY" IS '批量导出线索时属性';

ALTER TABLE T_PS_SUBJECT ADD CONSTRAINT T_PS_SUBJECT_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_TRAJECTORY_SOURCE
--------------------------------------------------------

CREATE TABLE "T_PS_TRAJECTORY_SOURCE"
(
    "ID"             VARCHAR2(32 BYTE),
    "NAME"           VARCHAR2(255 BYTE),
    "TABLE_NAME"     VARCHAR2(255 BYTE),
    "INDEX_COLUMN"   VARCHAR2(255 BYTE),
    "INDEX_TYPE"     VARCHAR2(255 BYTE),
    "PLACE_COLUMN"   VARCHAR2(255 BYTE),
    "ADDRESS_COLUMN" VARCHAR2(255 BYTE),
    "TIME_COLUMN"    VARCHAR2(255 BYTE),
    "TIME_FORMAT"    VARCHAR2(255 BYTE),
    "LNG_COLUMN"     VARCHAR2(255 BYTE),
    "LAT_COLUMN"     VARCHAR2(255 BYTE),
    "TEMPLATE"       VARCHAR2(2000 BYTE)
);

COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."NAME" IS '名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."TABLE_NAME" IS '表名';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."INDEX_COLUMN" IS '特征值字段名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."INDEX_TYPE" IS '特征值类型';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."PLACE_COLUMN" IS '活动场所字段名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."ADDRESS_COLUMN" IS '活动地址字段名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."TIME_COLUMN" IS '活动时间字段名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."TIME_FORMAT" IS '活动时间格式';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."LNG_COLUMN" IS '经度字段名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."LAT_COLUMN" IS '纬度字段名称';
COMMENT ON COLUMN "T_PS_TRAJECTORY_SOURCE"."TEMPLATE" IS '备注拼接字符串（英文代表字段名称）';

ALTER TABLE T_PS_TRAJECTORY_SOURCE ADD CONSTRAINT T_PS_TRAJECTORY_SOURCE_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_WARNING
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING"
(
    "ID"               VARCHAR2(32 BYTE),
    "WARNING_TIME"     TIMESTAMP(0),
    "ADDRESS"          NVARCHAR2(255),
    "WARNING_LEVEL"    VARCHAR2(2 BYTE),
    "WARNING_SOURCE"   VARCHAR2(100 BYTE),
    "WARNING_STATUS"   VARCHAR2(2 BYTE),
    "WARNING_DETAILS"  NVARCHAR2(2000),
    "SUBJECT_ID"       VARCHAR2(32 BYTE),
    "WARNING_TYPE"     NVARCHAR2(64),
    "WARNING_KEY"      VARCHAR2(255 BYTE),
    "PLACE"            NVARCHAR2(255),
    "JUDGE_RESULT"     VARCHAR2(2 BYTE),
    "JUDGE_REMARK"     NVARCHAR2(1000),
    "JUDGE_TIME"       TIMESTAMP(0),
    "SIGN_TIME"        TIMESTAMP(0),
    "WARNING_KEY_TYPE" VARCHAR2(2 BYTE)
);

COMMENT ON COLUMN "T_PS_WARNING"."ID" IS 'id';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_TIME" IS '时间';
COMMENT ON COLUMN "T_PS_WARNING"."ADDRESS" IS '地点';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_LEVEL" IS '预警等级,码表：ps_warning_level';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_SOURCE" IS '预警来源';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_STATUS" IS '预警状态，码表：ps_warning_status';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_DETAILS" IS '预警详情';
COMMENT ON COLUMN "T_PS_WARNING"."SUBJECT_ID" IS '专题id';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_TYPE" IS '预警类型';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_KEY" IS '预警值';
COMMENT ON COLUMN "T_PS_WARNING"."PLACE" IS '场所';
COMMENT ON COLUMN "T_PS_WARNING"."JUDGE_RESULT" IS '研判结果，码表：ps_warning_judge_result';
COMMENT ON COLUMN "T_PS_WARNING"."JUDGE_REMARK" IS '研判备注';
COMMENT ON COLUMN "T_PS_WARNING"."JUDGE_TIME" IS '研判时间';
COMMENT ON COLUMN "T_PS_WARNING"."SIGN_TIME" IS '签收时间';
COMMENT ON COLUMN "T_PS_WARNING"."WARNING_KEY_TYPE" IS '预警主键类型';
COMMENT ON TABLE "T_PS_WARNING" IS '预警记录表';

ALTER TABLE T_PS_WARNING ADD CONSTRAINT T_PS_WARNING_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_WARNING_CALL
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING_CALL"
(
    "ID"             VARCHAR2(32 BYTE),
    "TRS_TABLE"      VARCHAR2(100 BYTE),
    "TRS_ID"         VARCHAR2(32 BYTE),
    "PHONE_NUMBER"   VARCHAR2(11 BYTE),
    "CALL_TIME"      TIMESTAMP(0),
    "CALL_DURATION"  NUMBER(10, 0),
    "CALL_TYPE"      VARCHAR2(2 BYTE),
    "ID_NUMBER"      VARCHAR2(18 BYTE),
    "WARNING_TIME"   TIMESTAMP(0),
    "WARNING_NUMBER" VARCHAR2(11 BYTE),
    "TYPE"           VARCHAR2(100 BYTE),
    "CREATE_TIME"    VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_WARNING_CALL"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."TRS_TABLE" IS 'TRS表';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."TRS_ID" IS 'trs主键';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."PHONE_NUMBER" IS '命中的电话号码';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."CALL_TIME" IS '通话时间';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."CALL_DURATION" IS '通话时长，单位：秒';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."CALL_TYPE" IS '呼叫类型, 0:主叫,1:被叫';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."ID_NUMBER" IS '涉毒人员的身份证号';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."WARNING_TIME" IS '预警时间, 计算命中时生成预警的当前时间';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."WARNING_NUMBER" IS '预警号码，数据挖掘出的号码';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."TYPE" IS '预警类型';
COMMENT ON COLUMN "T_PS_WARNING_CALL"."CREATE_TIME" IS '创建时间';
COMMENT ON TABLE "T_PS_WARNING_CALL" IS '预警话单表';

ALTER TABLE T_PS_WARNING_CALL ADD CONSTRAINT T_PS_WARNING_CALL_PK PRIMARY KEY(ID) ENABLE;


--------------------------------------------------------
--  DDL for Table T_PS_WARNING_CALL_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING_CALL_RELATION"
(
    "ID"           VARCHAR2(32 BYTE),
    "WARNING_ID"   VARCHAR2(32 BYTE),
    "CALL_LIST_ID" VARCHAR2(32 BYTE),
    "CREATE_TIME"  TIMESTAMP(0)
);

COMMENT ON COLUMN "T_PS_WARNING_CALL_RELATION"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_WARNING_CALL_RELATION"."WARNING_ID" IS '预警ID';
COMMENT ON COLUMN "T_PS_WARNING_CALL_RELATION"."CALL_LIST_ID" IS '话单ID';
COMMENT ON COLUMN "T_PS_WARNING_CALL_RELATION"."CREATE_TIME" IS '创建时间';
COMMENT ON TABLE "T_PS_WARNING_CALL_RELATION" IS '预警话单关系表';
--------------------------------------------------------
--  DDL for Table T_PS_WARNING_MODULE
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING_MODULE"
(
    "ID"         NUMBER(16, 0),
    "EN_NAME"    NVARCHAR2(255),
    "CN_NAME"    NVARCHAR2(255),
    "SUBJECT_ID" VARCHAR2(16 BYTE)
);
--------------------------------------------------------
--  DDL for Table T_PS_WARNING_TRACE_RELATION
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING_TRACE_RELATION"
(
    "ID"            VARCHAR2(32 BYTE),
    "CREATE_TIME"   TIMESTAMP(0),
    "WARNING_ID"    VARCHAR2(32 BYTE),
    "TRAJECTORY_ID" VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_WARNING_TRACE_RELATION"."ID" IS '关系表id';
COMMENT ON COLUMN "T_PS_WARNING_TRACE_RELATION"."CREATE_TIME" IS '记录时间';
COMMENT ON COLUMN "T_PS_WARNING_TRACE_RELATION"."WARNING_ID" IS '预警id';
COMMENT ON COLUMN "T_PS_WARNING_TRACE_RELATION"."TRAJECTORY_ID" IS '预警轨迹id';
COMMENT ON TABLE "T_PS_WARNING_TRACE_RELATION" IS '预警轨迹关系表';

ALTER TABLE T_PS_WARNING_TRACE_RELATION ADD CONSTRAINT T_PS_WARNING_TRACE_RELATION_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_WARNING_TRAJECTORY
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING_TRAJECTORY"
(
    "ID"           VARCHAR2(32 BYTE),
    "SOURCE_ID"    VARCHAR2(32 BYTE),
    "ID_NUMBER"    VARCHAR2(32 BYTE),
    "LAT"          VARCHAR2(32 BYTE),
    "LNG"          VARCHAR2(32 BYTE),
    "PLACE"        VARCHAR2(255 BYTE),
    "ADDRESS"      VARCHAR2(255 BYTE),
    "HYBASE_TABLE" VARCHAR2(255 BYTE),
    "HYBASE_ID"    VARCHAR2(255 BYTE),
    "IMAGE_URL"    VARCHAR2(2000 BYTE),
    "CROP_URL"     VARCHAR2(2000 BYTE),
    "DATE_TIME"    TIMESTAMP(6),
    "ID_VALUE"     VARCHAR2(100 BYTE),
    "ID_TYPE"      VARCHAR2(100 BYTE),
    "SIMILARITY"   VARCHAR2(20 BYTE)
);

COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."SOURCE_ID" IS '轨迹来源';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."ID_NUMBER" IS '身份证号码';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."LAT" IS '轨迹纬度';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."LNG" IS '轨迹经度';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."PLACE" IS '场所';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."ADDRESS" IS '地址';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."HYBASE_TABLE" IS '海贝表名';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."HYBASE_ID" IS '海贝主键';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."IMAGE_URL" IS '全景图url';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."CROP_URL" IS '裁剪图片URL';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."DATE_TIME" IS '轨迹时间';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."ID_VALUE" IS 'id的值';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."ID_TYPE" IS 'id的类型';
COMMENT ON COLUMN "T_PS_WARNING_TRAJECTORY"."SIMILARITY" IS '图片相似度';
COMMENT ON TABLE "T_PS_WARNING_TRAJECTORY" IS '预警轨迹表';

ALTER TABLE T_PS_WARNING_TRAJECTORY ADD CONSTRAINT T_PS_WARNING_TRAJECTORY_PK PRIMARY KEY(ID) ENABLE;

--------------------------------------------------------
--  DDL for Table T_PS_WARNING_TYPE
--------------------------------------------------------

CREATE TABLE "T_PS_WARNING_TYPE"
(
    "ID"               VARCHAR2(32 BYTE),
    "CN_NAME"          NVARCHAR2(50),
    "EN_NAME"          NVARCHAR2(50),
    "DISPLAY_TYPE"     VARCHAR2(2 BYTE) DEFAULT 2,
    "CONTENT_TEMPLATE" VARCHAR2(255 BYTE),
    "DEFAULT_LEVEL"    VARCHAR2(2 BYTE) DEFAULT 4,
    "SIGN_TIME_LIMIT"  NUMBER,
    "SUBJECT_ID"       VARCHAR2(32 BYTE)
);

COMMENT ON COLUMN "T_PS_WARNING_TYPE"."ID" IS '主键';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."CN_NAME" IS '中文名称';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."EN_NAME" IS '英文名称';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."DISPLAY_TYPE" IS '显示方式 1-单人，2-多人，3-话单';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."CONTENT_TEMPLATE" IS '预警详情的模板';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."DEFAULT_LEVEL" IS '默认预警等级，1-红色，2-橙色，3-黄色，4-蓝色';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."SIGN_TIME_LIMIT" IS '签收时限，单位：分钟';
COMMENT ON COLUMN "T_PS_WARNING_TYPE"."SUBJECT_ID" IS '专题id';

ALTER TABLE T_PS_WARNING_TYPE ADD CONSTRAINT T_PS_WARNING_TYPE_PK PRIMARY KEY(ID) ENABLE;
