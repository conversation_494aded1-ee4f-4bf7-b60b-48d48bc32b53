-- ----------------------------
-- Table structure for T_PS_PERSON_IMPORT_TEMPLATE
-- ----------------------------
DROP TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE";
CREATE TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"
(
    "ID"          VARCHAR2(255 BYTE) NOT NULL,
    "FILE_NAME"   NVARCHAR2(100)     NOT NULL,
    "PATH"        VARCHAR2(255 BYTE) NOT NULL,
    "SUBJECT_ID"  VARCHAR2(32 BYTE)  NOT NULL,
    "PERSON_TYPE" VARCHAR2(32 BYTE)
);
COMMENT ON COLUMN "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"."FILE_NAME" IS '文件名';
COMMENT ON COLUMN "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"."PATH" IS '文件路径';
COMMENT ON COLUMN "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"."SUBJECT_ID" IS '主题主键';
COMMENT ON COLUMN "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"."PERSON_TYPE" IS '人员类型';

-- ----------------------------
-- Primary Key structure for table T_PS_PERSON_IMPORT_TEMPLATE
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"
    ADD CONSTRAINT "SYS_C0012946" PRIMARY KEY ("ID");

-- ----------------------------
-- Checks structure for table T_PS_PERSON_IMPORT_TEMPLATE
-- ----------------------------
ALTER TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"
    ADD CONSTRAINT "SYS_C0012945" CHECK ("ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"
    ADD CONSTRAINT "SYS_C0012947" CHECK ("FILE_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"
    ADD CONSTRAINT "SYS_C0012948" CHECK ("PATH" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "BIGDATA"."T_PS_PERSON_IMPORT_TEMPLATE"
    ADD CONSTRAINT "SYS_C0012949" CHECK ("SUBJECT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

alter table T_PS_PERSON_IMPORT_HISTORY
    drop column IS_TEMPLATE;